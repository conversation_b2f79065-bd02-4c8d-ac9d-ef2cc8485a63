plugins {
    id("java")
    kotlin("jvm")
    id("org.jetbrains.kotlin.plugin.serialization")
    id("org.sonarqube")
    `jvm-test-suite`
    jacoco
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

kotlin { jvmToolchain(21) }

repositories {
    mavenCentral()
}

sonar {
    properties {
        property("sonar.projectKey", "devops-martinit_oneteam-ai-be")
        property("sonar.organization", "devops-martinit")
        property("sonar.host.url", "https://sonarcloud.io")
        property("sonar.java.binaries", ".")
//        property("sonar.junit.reportPaths", "${projectDir}/build/test-results/test")
    }
}

tasks {

    test {
        useJUnitPlatform()
        environment("WEBSITE_SITE_NAME", "test")
        finalizedBy(jacocoTestReport)
    }

    jacocoTestReport {
        dependsOn(test)
        reports {
            xml.required.set(true)
        }
    }
}