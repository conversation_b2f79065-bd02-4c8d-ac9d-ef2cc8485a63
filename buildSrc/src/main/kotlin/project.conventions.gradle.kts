plugins {
    id("java")
    kotlin("jvm")
    id("com.jaredsburrows.license")
    id("org.owasp.dependencycheck")
    id("org.jetbrains.kotlin.plugin.serialization")
    id("org.sonarqube")
    `jvm-test-suite`
    jacoco
}

dependencyCheck {
    formats = listOf("JSON")
    nvd {
        apiKey = System.getenv("NVD_API_KEY")
    }
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

kotlin { jvmToolchain(21) }

licenseReport {
    // Generate reports
    generateCsvReport = false
    generateHtmlReport = false
    generateJsonReport = true
    generateTextReport = false
}

repositories {
    mavenCentral()
}

sonar {
    properties {
        property("sonar.projectKey", "devops-martinit_oneteam-ai-be")
        property("sonar.organization", "devops-martinit")
        property("sonar.host.url", "https://sonarcloud.io")
        property("sonar.java.binaries", ".")
//        property("sonar.junit.reportPaths", "${projectDir}/build/test-results/test")
    }
}

tasks {

    test {
        useJUnitPlatform()
        environment("WEBSITE_SITE_NAME", "test")
        finalizedBy(jacocoTestReport)
    }

    jacocoTestReport {
        dependsOn(test)
        reports {
            xml.required.set(true)
        }
    }
}