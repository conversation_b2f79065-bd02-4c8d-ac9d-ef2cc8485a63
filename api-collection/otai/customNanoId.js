const {customAlphabet} = require("nanoid");

// Define our own alphabet, excluding `_` and `-`
const alphabet =
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

/**
 * Custom random ID generator using the `nanoid` library.
 * Restrict the alphabet to alphanumeric characters.
 *
 * @param size Size of the ID. The default size is 21.
 * @returns A random string.
 */
const customAlphabetNanoId = customAlphabet(alphabet, 20);

const customNanoId = (size = 21) => {
    return `a${customAlphabetNanoId(size - 1)}`;
};

module.exports = {customNanoId};