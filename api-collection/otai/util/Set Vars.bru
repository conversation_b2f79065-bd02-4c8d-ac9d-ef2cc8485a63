meta {
  name: Set Vars
  type: http
  seq: 1
}

get {
  url: {{Origin}}{{prefix}}/
  body: none
  auth: none
}

script:pre-request {
  bru.setVar("initials", 'PXR');
  bru.setVar("discriminator", 1);
  bru.setVar("workspaceNumber",1)
  
  bru.setVar("foundationId", 0);
  // bru.setVar("foundationConfigurationId", 1);
  bru.setVar("workspaceId", 0);
  // bru.setVar("workspaceKey", "KEY2");
  bru.setVar("foundationConfigurationId", '');
  bru.setVar("foundationName",'')
  bru.setVar("foundationKey",'')
  bru.setVar("allowNull", true);
  bru.setVar("createIfNotExists", true)
  bru.setVar("documentId", "26RAFf6iuhVowLyJr1ArXhj5bqpj")
  bru.setVar("formId", 1)
}

docs {
  This is a request to set variables to whatever you want them to be - uncomment the relevant lines and set the values appropriately.
}
