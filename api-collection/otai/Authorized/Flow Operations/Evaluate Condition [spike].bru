meta {
  name: Evaluate Condition [spike]
  type: http
  seq: 2
}

post {
  url: {{Origin}}{{prefix}}/internal/condition
  body: json
  auth: bearer
}

auth:bearer {
  token: {{JWT}}
}

body:json {
  {
    "AND": [
      {
        "lhs": 5000,
        "operator": ">",
        "rhs": 0
      },
      {
        "lhs": "",
        "operator": "isEmpty"
      },
      {
        "OR": [
          {
            "lhs": "hello world",
            "operator": "contains",
            "rhs": "hello"
          },
          {
            "lhs": "2025-01-01",
            "operator": ">",
            "rhs": "2020-01-01"
          },
          {
            "AND": [
              {
                "lhs": false,
                "operator": "=",
                "rhs": false
              },
              {
                "lhs": true,
                "operator": "!=",
                "rhs": false
              }
            ]
          }
        ]
      },
      {
        "AND": [
          {
            "lhs": 5000,
            "operator": "=",
            "rhs": 5000
          },
          {
            "lhs": "hello world",
            "operator": "doesNotContain",
            "rhs": "goodbye"
          }
        ]
      }
    ]
  }
}
