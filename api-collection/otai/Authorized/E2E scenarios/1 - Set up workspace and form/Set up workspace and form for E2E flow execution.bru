meta {
  name: Set up workspace and form for E2E flow execution
  type: http
  seq: 1
}

get {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms?pageSize=5&formConfigurationId=1132-E2E-form&foundationConfigurationId=emp
  body: none
  auth: none
}

params:query {
  pageSize: 5
  formConfigurationId: 1132-E2E-form
  foundationConfigurationId: emp
}

script:post-response {
  var item = res.body.items[0]
  bru.setVar("form",item)
  bru.setVar("formId",item.id)
  bru.setVar("workspaceId",item.workspaceId)
  bru.setVar("foundationConfigurationId",item.foundation.foundationConfigurationId)
  bru.setVar("parentId",item.foundation.parentId)

}
