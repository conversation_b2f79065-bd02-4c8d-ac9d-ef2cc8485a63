meta {
  name: 1 - Create foundation
  type: http
  seq: 1
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/foundations
  body: json
  auth: none
}

body:json {
  { 
    "name": "Scenario_1106",
    "key": "SCENARIO1106",
    "foundationConfigurationId": "{{foundationConfigurationId}}",
    "workspaceId": "{{workspaceId}}",
    "parentId": "{{parentId}}"
  }
}

script:post-response {
  let data = res.getBody();
  bru.setVar("foundationId", data.id);
  
}
