meta {
  name: validate - invalid
  type: http
  seq: 2
}

post {
  url: {{Origin}}{{prefix}}/validate
  body: json
  auth: bearer
}

auth:bearer {
  token: {{jwt.token}}
}

body:json {
  {
    "description": "",
    "flows": {
      "entities": {},
      "order": []
    },
    "forms": [
      {
        "description": "",
        "id": "q82dbzLJHfuTm4VWBeSCF",
        "key": "F",
        "metadata": {
          "createdAt": "2024-11-25T02:46:32.044Z",
          "updatedAt": "2024-11-25T02:46:32.044Z"
        },
        "name": "f1"
      },
      {
        "description": "",
        "id": "aZeyATyTd4BZTJqEl2WcR",
        "key": "F2",
        "metadata": {
          "createdAt": "2024-11-25T02:46:49.296Z",
          "updatedAt": "2024-11-25T02:46:49.296Z"
        },
        "name": "f2"
      },
      {
        "description": "",
        "id": "w2ld4aNdnwR35s1G1t27h",
        "key": "F3",
        "metadata": {
          "createdAt": "2024-11-25T02:46:52.821Z",
          "updatedAt": "2024-11-25T02:46:52.822Z"
        },
        "name": "f3"
      },
      {
        "description": "",
        "id": "7ObKehKhwJ2VqCBRIkdfh",
        "key": "F1",
        "metadata": {
          "createdAt": "2024-11-25T02:46:57.468Z",
          "updatedAt": "2024-11-25T02:46:57.468Z"
        },
        "name": "f4"
      }
    ],
    "foundations": {
      "entities": {
        "caf4a93b-93b1-4c44-8151-2563e2ec8ac7": {
          "description": null,
          "id": "caf4a93b-93b1-4c44-8151-2563e2ec8ac7",
          "metadata": {
            "createdAt": "2024-11-25T01:40:56.762790Z",
            "updatedAt": "2024-11-25T01:40:56.762790Z"
          },
          "name": "Workspace",
          "relationship": "OneToMany"
        },
        "aaWCYzyNq9uVbtZsLnqEl": {
          "description": "duplicate name",
          "id": "aaWCYzyNq9uVbtZsLnqEl",
          "metadata": {
            "createdAt": "2024-11-25T02:35:19.812Z",
            "updatedAt": "2024-11-25T02:35:19.812Z"
          },
          "name": "New foundation",
          "objectId": "",
          "relationship": "OneToMany",
          "workspaceId": 1
        },
        "HHWCYzyNq9uVbtZsLnqEl": {
          "description": "duplicate name",
          "id": "HHWCYzyNq9uVbtZsLnqEl",
          "metadata": {
            "createdAt": "2024-11-25T02:35:19.812Z",
            "updatedAt": "2024-11-25T02:35:19.812Z"
          },
          "name": "New foundation",
          "objectId": "",
          "relationship": "OneToMany",
          "workspaceId": 1
        }
      },
      "order": [
        "caf4a93b-93b1-4c44-8151-2563e2ec8ac7",
        "aaWCYzyNq9uVbtZsLnqEl",
        "HHWCYzyNq9uVbtZsLnqEl"
      ]
    },
    "id": 1,
    "key": "W2SDFSDFSDFF@",
    "metadata": {
      "createdAt": "2024-11-25T01:40:56.749007Z",
      "updatedAt": "2024-11-25T01:40:56.749009Z"
    },
    "name": "w1",
    "series": {},
    "labels": {}
  }
}
