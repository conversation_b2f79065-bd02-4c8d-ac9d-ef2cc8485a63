meta {
  name: validate - valid
  type: http
  seq: 1
}

post {
  url: {{Origin}}{{prefix}}/validate
  body: json
  auth: bearer
}

auth:bearer {
  token: {{jwt.token}}
}

body:json {
  {
    "description": "",
    "errors": [
      {
        "constraintDetail": "\"string\"",
        "key": "doc",
        "message": "$.doc: object found, string expected",
        "path": "$.doc",
        "type": "type"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "description",
        "message": "$: required property 'description' not found",
        "path": "$",
        "type": "required"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "flows",
        "message": "$: required property 'flows' not found",
        "path": "$",
        "type": "required"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "forms",
        "message": "$: required property 'forms' not found",
        "path": "$",
        "type": "required"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "foundations",
        "message": "$: required property 'foundations' not found",
        "path": "$",
        "type": "required"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "id",
        "message": "$: required property 'id' not found",
        "path": "$",
        "type": "required"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "key",
        "message": "$: required property 'key' not found",
        "path": "$",
        "type": "required"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "metadata",
        "message": "$: required property 'metadata' not found",
        "path": "$",
        "type": "required"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "name",
        "message": "$: required property 'name' not found",
        "path": "$",
        "type": "required"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "series",
        "message": "$: required property 'series' not found",
        "path": "$",
        "type": "required"
      },
      {
        "constraintDetail": "[\"description\",\"flows\",\"forms\",\"foundations\",\"id\",\"key\",\"metadata\",\"name\",\"series\",\"errors\"]",
        "key": "errors",
        "message": "$: required property 'errors' not found",
        "path": "$",
        "type": "required"
      }
    ],
    "flows": {
      "entities": {},
      "order": []
    },
    "forms": [
      {
        "description": "form\n",
        "id": "7cyjwdUBckFrcA2cqYglY",
        "key": "FORM1",
        "metadata": {
          "createdAt": "2024-11-27T01:50:16.127Z",
          "updatedAt": "2024-11-27T01:50:16.128Z"
        },
        "name": "form1"
      }
    ],
    "foundations": {
      "entities": {
        "e617f4d5-8cde-4014-bd87-84540fbb971c": {
          "description": null,
          "id": "e617f4d5-8cde-4014-bd87-84540fbb971c",
          "metadata": {
            "createdAt": "2024-11-27T01:49:53.277438Z",
            "updatedAt": "2024-11-27T01:49:53.277438Z"
          },
          "name": "Workspace",
          "relationship": "OneToMany"
        }
      },
      "order": [
        "e617f4d5-8cde-4014-bd87-84540fbb971c"
      ]
    },
    "id": 8,
    "key": "W5",
    "metadata": {
      "createdAt": "2024-11-27T01:49:53.258350Z",
      "updatedAt": "2024-11-27T01:49:53.258356Z"
    },
    "name": "w5",
    "series": {}
    "labels": {}
  }
}
