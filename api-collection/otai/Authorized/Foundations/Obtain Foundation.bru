meta {
  name: Obtain Foundation
  type: http
  seq: 5
}

post {
  url: {{Origin}}{{prefix}}/foundations/obtain
  body: json
  auth: bearer
}

auth:bearer {
  token: {{JWT}}
}

body:json {
  {
    "workspaceId": {{workspaceId}},
    "foundationConfigurationId": {{foundationConfigurationId}},
    "name": {{name}},
    "key": {{key}},
    "parentId": {{parentId}},
    "allowNull": {{allowNull}},
    "createIfNotExists": {{createIfNotExists}}
  }
}
