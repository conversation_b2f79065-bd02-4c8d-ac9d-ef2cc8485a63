meta {
  name: create foundation
  type: http
  seq: 2
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/foundations
  body: json
  auth: none
}

body:json {
  { 
    "name": "{{foundationName}}",
    "key": "{{foundationKey}}",
    "foundationConfigurationId": "{{foundationConfigurationId}}",
    "workspaceId": {{workspaceId}},
    "parentId": {{parentId}}
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body,
      name: customNanoId(20),
      key: customNanoId(20)
    })
  
}

script:post-response {
  let data = res.getBody();
  bru.setVar("foundationId", data.id);
  
}
