meta {
  name: Create Event
  type: http
  seq: 1
}

post {
  url: {{Origin}}{{prefix}}/internal/events
  body: json
  auth: none
}

body:json {
  {
    "workspaceId": {{workspaceId}},
    "eventProperties": {
      "key": "START_flow_manually_from_form",
      "buttonLabel": "Run Flow",
      "form": {
        "id": {{formId}},
        "foundationId": {{foundationId}},
        "formConfiguration": {
          "id": "{{formConfigurationId}}",
           "key": "unknown",  
          "name": "This is form name",
          "seriesId": "serialId1"
        },
        "documentId": "documentId1",
        "intervalId": "intervalId1"
      }
    }
  }
}
