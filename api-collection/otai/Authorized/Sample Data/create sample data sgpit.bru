meta {
  name: create sample data sgpit
  type: http
  seq: 1
}

post {
  url: {{Origin}}{{prefix}}/sample-data/SGPIT
  body: json
  auth: none
}

body:json {
  {
    "name": "{{initials}} SGPIT {{workspaceNumber}}",
    "key": "{{initials}}SGPIT{{workspaceNumber}}",
    "description": "SGPIT workspace"
  }
}

script:pre-request {
  if(!bru.getVar("workspaceNumber")) {
    bru.setVar("workspaceNumber",1)
  } else {
    bru.setVar("workspaceNumber",bru.getVar("workspaceNumber")+1)
  }
  
  if(!bru.getVar("initials")) {
    bru.setVar("initials","XXX")
  }
}

script:post-response {
  let data = res.getBody();
  if (data.id) {
    bru.setVar("workspaceId", data.id);
    bru.setVar("workspaceKey", data.key);
    bru.setVar("documentId", data.documentId);
    bru.setVar("discriminator", data.id + 1);
  }
  
  
}
