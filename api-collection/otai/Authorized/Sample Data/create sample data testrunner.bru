meta {
  name: create sample data testrunner
  type: http
  seq: 2
}

post {
  url: {{Origin}}{{prefix}}/sample-data/TESTRUNNER
  body: json
  auth: none
}

body:json {
  {
    "name": "{{initials}} TESTRUNNER {{workspaceNumber}}",
    "key": "{{initials}}TESTRUNNER{{workspaceNumber}}",
    "description": "TESTRUNNER workspace"
  }
}

script:pre-request {
  if(!bru.getVar("workspaceNumber")) {
    bru.setVar("workspaceNumber",1)
  } else {
    bru.setVar("workspaceNumber",bru.getVar("workspaceNumber")+1)
  }
  
  if(!bru.getVar("initials")) {
    bru.setVar("initials","XXX")
  }
}

script:post-response {
  let data = res.getBody();
  if (data.id) {
    bru.setVar("workspaceId", data.id);
    bru.setVar("workspaceKey", data.key);
    bru.setVar("documentId", data.documentId);
    bru.setVar("discriminator", data.id + 1);
  }
  
  
}
