meta {
  name: create form
  type: http
  seq: 1
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms
  body: json
  auth: none
}

body:json {
  { 
    "formConfigurationId": "{{formConfigurationId}}",
    "foundationId": "{{foundationId}}",
    "intervalId": "{{intervalId}}"
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body
    })
  
}

script:post-response {
  let data = res.getBody();
  bru.setVar("formId", data.id);
  
}
