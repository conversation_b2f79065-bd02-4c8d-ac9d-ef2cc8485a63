meta {
  name: update form properties by id
  type: http
  seq: 9
}

put {
  url: {{Origin}}{{prefix}}/forms/{{formId}}/properties
  body: json
  auth: none
}

body:json {
  {
    "properties": {
      "label": "abc",
      "nested": {
        "deep": 1232312
      }
    }
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body
    })
  
}

script:post-response {
  let data = res.getBody();
  
}
