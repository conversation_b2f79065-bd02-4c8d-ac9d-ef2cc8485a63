meta {
  name: update form by id
  type: http
  seq: 5
}

put {
  url: {{Origin}}{{prefix}}/forms/{{formId}}
  body: json
  auth: none
}

body:json {
  {
    "formConfigurationId": "{{formConfigurationId}}",
    "foundationId": "{{foundationId}}",
    "intervalId": "{{intervalId}}"
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body
    })
  
}

script:post-response {
  let data = res.getBody();
  
}
