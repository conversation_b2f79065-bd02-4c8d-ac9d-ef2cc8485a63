meta {
  name: Set answer
  type: http
  seq: 1
}

put {
  url: {{Origin}}{{prefix}}/forms/352/answer
  body: json
  auth: none
}

body:json {
  {
    "questionId": "P_l3oy7YWo",
  //   "value": "new value",
  //   "operation": "setCell",
  //   "rowId": "UWn3SPNGbK3eALbgbR8zi",
  //   "rowIndex": 1,
  //   "columnId": "xpnD2z9VKj"
    
    "value": {
      "DkElvJsJPx": "value 5",
      "IFJEHmqmlg": "new2"
    },
    "rowId": "AwqrP7ygXlOI6u5C5fZ4H",
    "operation": "setRow"
    
  //   "columnId": "RMCM-sJvqM",
  //   "value": ["111", "22", "33", "44"],
  //   "operation": "setColumn",
  //   "columnId": "DkElvJsJPx"
    
  //   "value": [
  //     {
  //       "DkElvJsJPx": "value",
  //     "IFJEHmqmlg": "11"
  //     },
  //     {
  //       "DkElvJsJPx": "value 0",
  //     "IFJEHmqmlg": "14"
  //     }
  //   ],
  //   "operation": "setTable"
  }
}
