meta {
  name: Set answer
  type: http
  seq: 1
}

put {
  url: {{Origin}}{{prefix}}/forms/292/answer
  body: json
  auth: none
}

body:json {
  {
    "questionId": "FPhq5-5V5O",
  //   "value": "new value",
  //   "operation": "setCell",
  //   "rowId": "UWn3SPNGbK3eALbgbR8zi",
  //   "rowIndex": 1,
  //   "columnId": "xpnD2z9VKj"
    
  //   "value": {
  //     "xpnD2z9VKj": "value 1",
  //     "RMCM-sJvqM": "new"
  //   },
  //   "rowId": "UWn3SPNGbK3eALbgbR8zi",
  //   "operation": "setRow"
    
  //   "columnId": "RMCM-sJvqM",
  //   "value": ["11", "22", "33", "44"],
  //   "operation": "setColumn"
    
    "columnId": "RMCM-sJvqM",
    "value": [
      {
        "xpnD2z9VKj": "value",
      "RMCM-sJvqM": "11"
      },
      {
        "xpnD2z9VKj": "value 0",
      "RMCM-sJvqM": "14"
      }
    ],
    "operation": "setTable"
  }
}
