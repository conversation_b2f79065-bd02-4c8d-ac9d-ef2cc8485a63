meta {
  name: Row BlobUpload
  type: http
  seq: 7
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms/answers/ABC/blob/r
  body: json
  auth: none
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body
    })
  
}

script:post-response {
  let data = res.getBody();
  bru.setVar("formId", data.id);
  
}
