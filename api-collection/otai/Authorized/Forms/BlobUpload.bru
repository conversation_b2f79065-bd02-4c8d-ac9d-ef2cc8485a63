meta {
  name: BlobUpload
  type: http
  seq: 6
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms/answers/ABC/blob
  body: json
  auth: none
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body
    })
  
}

script:post-response {
  let data = res.getBody();
  bru.setVar("formId", data.id);
  
}
