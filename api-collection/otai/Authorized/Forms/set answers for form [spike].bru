meta {
  name: set answers for form [spike]
  type: http
  seq: 3
}

put {
  url: {{Origin}}{{prefix}}/document/37uSvCAfUcv1cBYj6F1sBzeMTNGk/form/answer
  body: json
  auth: none
}

body:json {
  [
      {
        "path": ["answers", "MBeNvmBMj7"],
        "formAnswer": {
          "questionId": "MBeNvmBMj7",
          "type": "json",
          "value": {
            "Hq3M0ftG9-": {
              "questionId": "Hq3M0ftG9-",
              "type": "text",
              "value": "jon"
            },
            "R5GTqDUivP": {
              "questionId": "R5GTqDUivP",
              "type": "number",
              "value": "12"
            }
    }
        }
      }
  ]
}
