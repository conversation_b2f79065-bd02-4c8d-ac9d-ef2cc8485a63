meta {
  name: Setup - Find latest SGPIT
  type: http
  seq: 1
}

get {
  url: {{Origin}}{{prefix}}/workspaces/search?search=sgpit&sort=id,desc&pageSize=1
  body: none
  auth: none
}

params:query {
  search: sgpit
  sort: id,desc
  pageSize: 1
}

headers {
  : 
}

script:post-response {
  var data = res.body.items[0]
  bru.setVar("workspaceId", data.id)
  bru.setVar("workspaceKey", data.key)
  bru.setVar("documentId", data.documentId);
  
}
