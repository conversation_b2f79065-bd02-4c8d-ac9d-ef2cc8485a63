meta {
  name: create form - bad form configuration id
  type: http
  seq: 3
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms
  body: json
  auth: none
}

body:json {
  { 
    "formConfigurationId": "unknown",
    "foundationId": "{{foundationId}}",
    "intervalId": "{{intervalId}}"
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body
    })
  
}

script:post-response {
  let data = res.getBody();
  bru.setVar("formId", data.id);
  
}
