meta {
  name: create form - interval should not be specified
  type: http
  seq: 5
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms
  body: json
  auth: none
}

body:json {
  { 
    "formConfigurationId": "aqt",
    "foundationId": "1543",
    "intervalId": "interval"
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body
    })
  
}

script:post-response {
  let data = res.getBody();
  bru.setVar("formId", data.id);
  
}
