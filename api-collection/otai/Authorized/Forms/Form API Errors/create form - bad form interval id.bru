meta {
  name: create form - bad form interval id
  type: http
  seq: 4
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms
  body: json
  auth: none
}

body:json {
  { 
    "formConfigurationId": "1132-E2E-form",
    "foundationId": "1549",
    "intervalId": "unknown"
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body
    })
  
}

script:post-response {
  let data = res.getBody();
  bru.setVar("formId", data.id);
  
}
