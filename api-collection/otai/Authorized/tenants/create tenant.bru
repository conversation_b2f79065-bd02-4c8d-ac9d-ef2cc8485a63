meta {
  name: create tenant
  type: http
  seq: 2
}

post {
  url: {{Origin}}{{prefix}}/tenants
  body: json
  auth: none
}

body:json {
  {
    "name": "UNIQUE_NAME",
    "originUrl": "https://unique.origin.com"
  }
}

script:pre-request {
  
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body,
      name: customNanoId(),
      originUrl: "http://" + customNanoId() + ":8080"
    })
  
}
