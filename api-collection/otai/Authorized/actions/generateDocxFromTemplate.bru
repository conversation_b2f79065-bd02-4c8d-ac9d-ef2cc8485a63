meta {
  name: generateDocxFromTemplate
  type: http
  seq: 1
}

post {
  url: {{Origin}}{{prefix}}/internal/workspaces/29/actions/generateFromTemplate
  body: json
  auth: none
}

body:json {
  {
    "template": {  
      "url": "{{templateUrl}}"
    },
    "outputFilename": "myFilename",
    "replacements": {
      "first_name": "<PERSON>",
      "last_name": "<PERSON><PERSON>",
      "company": {
          "name": "ABC Corp",
          "address": "123 Main St, Springfield, USA"
      },
      "content_hello": "'hello'",
      "content_integer": 1234,
      "content_float": 567.89,
      "conditionalHidden": false,
      "conditionalVisible": true,
      "listOfItems": [
          {
              "name": "Item 1",
              "SKU": "SKU001"
          },
          {
              "name": "Item 2",
              "SKU": "SKU002"
          }
      ],
      "signature_first_name": "<PERSON>",
      "signature_last_name": "<PERSON><PERSON>"
    }
  }
}

vars:pre-request {
  templateUrl: ****************************************************************************?sp=r&st=2025-04-15T01:47:49Z&se=2025-04-15T09:47:49Z&spr=https&sv=2024-11-04&sr=b&sig=%2Bugiycx6deQtRs0HQv58kkfNsFhOXrA5DuJSgRTWWEY%3D
}
