meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}
  body: json
  auth: none
}

body:json {
  {
    "id": -1,
    "name": "1",
    "key": "1",
    "description":"test",
    "documentId": "",
    "configuration": {
    "description": "",
    "flows": [],
    "forms": [],
    "foundations": [],
    "id": 44,
    "key": "NEW123",
    "metadata": {
      "createdAt": "2024-11-28T05:27:34.993509Z",
      "updatedAt": "2024-11-28T05:27:34.993512Z"
    },
    "name": "new123",
    "series": {}
    "labels": {}
  }
  
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
    ...body,
    })
  
}

script:post-response {
  if (res.status==200) {
  let data = res.getBody();
  bru.setVar("workspaceId", data.id);
  bru.setVar("workspaceKey", data.key);
  }
}
