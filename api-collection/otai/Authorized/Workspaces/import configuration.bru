meta {
  name: import configuration
  type: http
  seq: 10
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/configuration/import
  body: json
  auth: none
}

body:json {
  {"importConfig":"{\"description\":\"\",\"errors\":[],\"flows\":{\"entities\":{\"aMtScDOMgaxzxWwLDc6Qo\":{\"name\":\"flow-with-code\",\"description\":\"\",\"id\":\"aMtScDOMgaxzxWwLDc6Qo\",\"startingVariables\":[{\"type\":\"foundation.a00WftdxjQ6nsOvvWMfos\",\"identifier\":\"foundation\",\"properties\":{\"required\":true}}],\"endingVariables\":[],\"triggers\":{\"ayO5Bqon6SeUbQWLcD50G\":{\"id\":\"ayO5Bqon6SeUbQWLcD50G\",\"variant\":\"trigger\",\"name\":\"Manually trigger from a foundation\",\"properties\":{\"typePrimaryIdentifier\":\"manualTriggerFromFoundation\",\"inputs\":{\"foundationVariableName\":\"foundation\",\"foundationConfigurationId\":\"a00WftdxjQ6nsOvvWMfos\",\"buttonLabel\":\"press me\"}},\"next\":\"\",\"metadata\":{\"createdAt\":\"2025-05-30T00:58:43.474Z\",\"updatedAt\":\"2025-05-30T00:58:43.474Z\"}}},\"steps\":{\"aKWUerX8XuEi8mAk12BdP\":{\"id\":\"aKWUerX8XuEi8mAk12BdP\",\"variant\":\"setVariables\",\"name\":\"this gets gets waf blocked unless exempted\",\"properties\":{\"variables\":[{\"identifier\":\"customjson\",\"value\":\"$IS_NOT_EMPTY({\\\\\\\"name\\\\\\\": \\\\\\\"Easton\\\\\\\"})\",\"type\":\"boolean\"}]},\"next\":\"\",\"metadata\":{\"createdAt\":\"2025-05-30T00:58:50.539Z\",\"updatedAt\":\"2025-05-30T00:58:50.539Z\"}}},\"labels\":[],\"status\":\"active\",\"metadata\":{\"createdAt\":\"2025-05-30T00:58:35.305Z\",\"updatedAt\":\"2025-05-30T00:58:35.305Z\"},\"hasErrors\":false,\"start\":\"aKWUerX8XuEi8mAk12BdP\"}},\"order\":[\"aMtScDOMgaxzxWwLDc6Qo\"]},\"forms\":{},\"foundations\":{\"entities\":{\"a00WftdxjQ6nsOvvWMfos\":{\"id\":\"a00WftdxjQ6nsOvvWMfos\",\"metadata\":{\"createdAt\":\"2025-05-30T00:58:14.770048155Z\",\"updatedAt\":\"2025-05-30T00:58:14.770048155Z\"},\"name\":\"Workspace\",\"relationship\":\"OneToMany\"},\"aDTGRpfZZT2JCLWlqws9Z\":{\"id\":\"aDTGRpfZZT2JCLWlqws9Z\",\"name\":\"Level 1\",\"description\":\"\",\"relationship\":\"OneToMany\",\"metadata\":{\"createdAt\":\"2025-05-30T01:03:21.509Z\",\"updatedAt\":\"2025-05-30T01:03:21.509Z\"}}},\"order\":[\"a00WftdxjQ6nsOvvWMfos\",\"aDTGRpfZZT2JCLWlqws9Z\"]},\"id\":884,\"key\":\"AT2025053010\",\"labels\":{},\"metadata\":{\"createdAt\":\"2025-05-30T00:58:14.776365024Z\",\"updatedAt\":\"2025-05-30T00:58:14.776369658Z\"},\"name\":\"at2025053010\",\"series\":{},\"type\":\"WORKSPACE_CONFIGURATION\"}"}
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body,
    })
  
}

docs {
  The workspace config here only contains a Flow with a SetVaraible step that could be picked up by a WAF rule.
}
