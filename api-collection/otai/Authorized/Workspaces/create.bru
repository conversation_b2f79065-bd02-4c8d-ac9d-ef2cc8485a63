meta {
  name: create
  type: http
  seq: 1
}

post {
  url: {{Origin}}{{prefix}}/workspaces
  body: json
  auth: none
}

body:json {
  {
    "name": "UNIQUE NAME",
    "key": "UNIQUE KEY",
    "description":""
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body,
      name: customNanoId(3),
      key: customNanoId(5).toUpperCase()
    })
  
}

script:post-response {
  let data = res.getBody();
  bru.setVar("workspaceId", data.id);
  bru.setVar("workspaceKey", data.key);
  bru.setVar("documentId", data.documentId);
}
