meta {
  name: workspace
  type: http
  seq: 1
}

post {
  url: {{Origin}}{{prefix}}/webhooks/workspaces/862
  body: json
  auth: none
}

body:json {
  {"request":{"id":"Half-Yearly-Review-2025-111","name":"Manager Review for <PERSON>","assignee":"<EMAIL>","copiedTo":[],"entities":[1],"projectId":"Half-Yearly-Review-2025-Chris-Dam","requestId":"Half-Yearly-Review-2025-111","requestor":"<EMAIL>","requiredBy":"2025-06-12","description":"Manager Review for <PERSON>","requestType":"SMARTFORM","reminderDate":"2025-06-10","workflowStep":"QUESTIONS AVAILABLE","answersByEntity":[{"answers":[{"id":1,"value":"<PERSON>","secondaryId":1},{"id":2,"value":"Business Analyst","secondaryId":2},{"id":3,"value":"Senior Manager","secondaryId":3},{"id":4,"value":"Manager Review","secondaryId":4},{"id":5,"value":"Working Towards","secondaryId":5},{"id":6,"value":"could do better!13123","secondaryId":6},{"id":81,"value":"Chris Dam","secondaryId":81},{"id":82,"value":"Chris Dam","secondaryId":82},{"id":83,"value":"No","secondaryId":83},{"id":86,"value":"Yes","secondaryId":86}],"entityId":1}],"smartFormTypeId":1010},"triggeredByUser":{"email":"<EMAIL>"}}
}
