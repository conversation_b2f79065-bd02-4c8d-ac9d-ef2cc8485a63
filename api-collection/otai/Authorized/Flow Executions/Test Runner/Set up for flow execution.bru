meta {
  name: Set up for flow execution
  type: http
  seq: 1
}

get {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms?pageSize=5&formConfigurationId=EISiO632dEeNfgsIyqykP&foundationConfigurationId=a3290a72ad744a4d84ab3b6a12e5461c4eb8
  body: none
  auth: none
}

params:query {
  pageSize: 5
  formConfigurationId: EISiO632dEeNfgsIyqykP
  foundationConfigurationId: a3290a72ad744a4d84ab3b6a12e5461c4eb8
}

script:post-response {
  var item = res.body.items[0]
  bru.setVar("form",item)
}
