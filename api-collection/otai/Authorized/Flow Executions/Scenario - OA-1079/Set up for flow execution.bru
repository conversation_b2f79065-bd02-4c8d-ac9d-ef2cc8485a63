meta {
  name: Set up for flow execution
  type: http
  seq: 1
}

get {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms?pageSize=5&formConfigurationId=ef&foundationConfigurationId=emp
  body: none
  auth: none
}

params:query {
  pageSize: 5
  formConfigurationId: ef
  foundationConfigurationId: emp
}

script:post-response {
  var item = res.body.items[0]
  bru.setVar("form",item)
  
  const requestResponse = await bru.runRequest(
    "Authorized/Flow Executions/Scenario1 - Manual from form/Set up flow - search workspace versions"
  );
}
