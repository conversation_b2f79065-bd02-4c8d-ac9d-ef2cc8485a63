meta {
  name: set variables by workspace versions
  type: http
  seq: 4
}

get {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/configuration/versions?sort=id,desc&pageSize=1
  body: none
  auth: none
}

params:query {
  sort: id,desc
  pageSize: 1
}

script:post-response {
  // allows you to set all the variables based of a workspace version, using the first value for each object - workspace has to be set up properly for this to work
  
  let data = res.getBody().items[0].configuration;
  bru.setVar("workspaceId", data.id);
  bru.setVar("workspaceKey", data.key);
  bru.setVar("foundationConfigurationId", data.foundations.order[0]);
  bru.setVar("formConfigurationId", Object.values(data.forms)[0].id);
  
  const series = Object.values(data.series)[0]
  
  bru.setVar("seriesId", series.id);
  bru.setVar("intervalId", series.intervals.order[0]);
  
  
  
  
}
