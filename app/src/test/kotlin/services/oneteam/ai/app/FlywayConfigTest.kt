package services.oneteam.ai.app

import io.kotest.assertions.throwables.shouldThrow
import io.ktor.server.config.*
import io.ktor.server.testing.*
import kotlin.test.Test
import kotlin.test.assertEquals


class FlywayConfigTest() {

    @Test
    fun `should throw exception if website host is blank`() = testApplication {
        environment {
            config = ApplicationConfig("application.yaml")

            // should throw exception if website host is blank
            shouldThrow<ApplicationConfigurationException> {
                FlywayConfigBuilder.fromConfig(config.config("flyway"), "", "dbUser", "dbSuperUser")
            }
        }
    }

    @Test
    fun `should have properties for website host`() = testApplication {
        environment {
            config = ApplicationConfig("application.yaml")
            val flywayConfig = FlywayConfigBuilder.fromConfig(config.config("flyway"), "test", "dbUser", "dbSuperUser")
            assertEquals("localhost", flywayConfig.properties["domain"])
            assertEquals("http", flywayConfig.properties["protocol"])
        }
    }

    @Test
    fun `should replace variables in locations`() = testApplication {
        environment {
            config = ApplicationConfig("application.yaml")
            val flywayConfig = FlywayConfigBuilder.fromConfig(config.config("flyway"), "test", "dbUser", "dbSuperUser")
            assertEquals("classpath:database/h2/migration", flywayConfig.locations[0])
        }
    }
}
