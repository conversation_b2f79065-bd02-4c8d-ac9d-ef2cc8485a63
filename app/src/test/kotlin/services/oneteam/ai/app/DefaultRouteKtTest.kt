package services.oneteam.ai.app

import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.config.*
import io.ktor.server.testing.*
import kotlin.test.Test
import kotlin.test.assertEquals

class DefaultRouteKtTest {

    private fun ApplicationTestBuilder.setupConfig(): ApplicationConfig {
        return ApplicationConfig("application.yaml")
    }

    @Test
    fun testGetNakedOK() = testApplication {
        val setupConfig = setupConfig()
        environment {
            config = setupConfig
        }
        application { setup() }

        println(setupConfig)
        client.get("/") {
            headers {
                append(HttpHeaders.ContentType, ContentType.Application.Json)
                append(HttpHeaders.Referrer, originUrl)
            }
        }.apply {
            assertEquals(HttpStatusCode.OK, status)
            assertEquals("OK", bodyAsText())
        }
    }

    @Test
    fun testGetAiOK() = testApplication {
        environment {
            config = ApplicationConfig("application.yaml")
        }
        application { setup() }

        client.get("/ai") {
            headers {
                append(HttpHeaders.ContentType, ContentType.Application.Json)
                append(HttpHeaders.Referrer, originUrl)
            }
        }.apply {
            assertEquals(HttpStatusCode.OK, status)
            assertEquals("OK", bodyAsText())
        }
    }

    @Test
    fun testGetAisOK() = testApplication {
        environment {
            config = ApplicationConfig("application.yaml")
        }
        application { setup() }
        client.get("/ai/") {
            headers {
                append(HttpHeaders.ContentType, ContentType.Application.Json)
                append(HttpHeaders.Referrer, originUrl)
            }
        }.apply {
            assertEquals(HttpStatusCode.OK, status)
            assertEquals("OK", bodyAsText())
        }
    }

    @Test
    fun testGetAiApiOK() = testApplication {
        environment {
            config = ApplicationConfig("application.yaml")
        }
        application { setup() }
        client.get("/ai/api") {
            headers {
                append(HttpHeaders.ContentType, ContentType.Application.Json)
                append(HttpHeaders.Referrer, originUrl)
            }
        }.apply {
            assertEquals(HttpStatusCode.OK, status)
            assertEquals("OK", bodyAsText())
        }
    }

    @Test
    fun testGetAiApiSOK() = testApplication {
        environment {
            config = ApplicationConfig("application.yaml")
        }
        application { setup() }
        client.get("/ai/api/") {
            headers {
                append(HttpHeaders.ContentType, ContentType.Application.Json)
                append(HttpHeaders.Referrer, originUrl)
            }
        }.apply {
            assertEquals(HttpStatusCode.OK, status)
            assertEquals("OK", bodyAsText())
        }
    }
}