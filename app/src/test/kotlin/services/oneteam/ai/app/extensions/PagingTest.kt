package services.oneteam.ai.app.extensions

import io.ktor.http.*
import io.ktor.server.routing.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import services.oneteam.ai.flow.execution.FlowExecutionRepository
import services.oneteam.ai.shared.domains.Sort

class PagingTest {

    @Test
    fun `sort should use default fields when none are specified`() {
        val routingRequest = mock(RoutingRequest::class.java)
        `when`(routingRequest.queryParameters).thenReturn(ParametersBuilder().apply {
            append("page", "1")
            append("pageSize", "10")
        }.build())

        val pageRequest = routingRequest.buildPageRequest(
            allowedSortableFields = FlowExecutionRepository.SORTABLE_FIELDS,
            defaultSortableFields = listOf("id,desc")
        )

        assertEquals(1, pageRequest.pageNumber)
        assertEquals(10, pageRequest.pageSize)
        assertEquals(1, pageRequest.sort.fields.size)
        assertEquals("id", pageRequest.sort.fields[0].field)
        assertEquals(Sort.DESC, pageRequest.sort.fields[0].direction)
    }

    @Test
    fun `sort should use specified fields`() {
        val routingRequest = mock(RoutingRequest::class.java)
        `when`(routingRequest.queryParameters).thenReturn(ParametersBuilder().apply {
            append("sort", "result,asc")
            append("sort", "status,desc")
            append("page", "1")
            append("pageSize", "10")
        }.build())

        val pageRequest = routingRequest.buildPageRequest(
            allowedSortableFields = FlowExecutionRepository.SORTABLE_FIELDS,
            defaultSortableFields = listOf("id,desc")
        )

        assertEquals(1, pageRequest.pageNumber)
        assertEquals(10, pageRequest.pageSize)
        assertEquals(2, pageRequest.sort.fields.size)
        assertEquals("result", pageRequest.sort.fields[0].field)
        assertEquals(Sort.ASC, pageRequest.sort.fields[0].direction)
        assertEquals("status", pageRequest.sort.fields[1].field)
        assertEquals(Sort.DESC, pageRequest.sort.fields[1].direction)
    }
}