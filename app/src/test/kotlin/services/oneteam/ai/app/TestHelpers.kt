package services.oneteam.ai.app

import io.ktor.server.application.*
import org.jetbrains.exposed.sql.transactions.transaction
import services.oneteam.ai.shared.database.DatabaseLive
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.tenant.TenantRepository

const val originUrl = "http://testorigin"
fun Application.setup() {

    val dbConfig = DatabaseConfigBuilder.fromConfig(environment.config.config("ktor.database"))
    val live = DatabaseLive(dbConfig)
    live.connectSuperUser()
    val tenantRepository = TenantRepository()

    transaction {
        val maybeTenant = tenantRepository.getByOriginUrl(originUrl)
        if (maybeTenant == null) {
            tenantRepository.create(
                Tenant(
                    0,
                    name = "testTenant",
                    originUrl = originUrl,
                    internalUrl = originUrl,
                    internalSyncUrl = originUrl
                )
            )
        }
    }
    transaction {
        tenantRepository.getByOriginUrl(originUrl)!!
    }
}

