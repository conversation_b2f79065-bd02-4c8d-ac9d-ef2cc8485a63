package services.oneteam.ai.app

import io.kotest.matchers.shouldBe
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.routing.*
import io.ktor.server.testing.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.jetbrains.exposed.sql.Transaction
import org.mockito.Mockito.mock
import services.oneteam.ai.shared.domains.ApplicationException
import services.oneteam.ai.shared.domains.NotFoundException
import services.oneteam.ai.shared.domains.ValidationErrors
import kotlin.test.Test

class StatusPagesConfigurationTest {

    @OptIn(ExperimentalSerializationApi::class)
    @Test
    fun `test ExposedSQLException`() = testApplication {
        install(StatusPages) {
            configureStatusPages(
                arrayOf(
                    ConstraintMapping(
                        "constraint name",
                        "localization key",
                        "field name"
                    )
                )
            )
        }
        install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
            json(Json { explicitNulls = false })
        }

        routing {
            route("/ExposedSQLException") {
                get {
                    throw ExposedSQLException(
                        Throwable("constraint name"),
                        listOf(),
                        mock(Transaction::class.java)
                    )
                }
            }
        }

        val client = createClient { install(ContentNegotiation) { json() } }

        client.get("/ExposedSQLException").let {
            it.status.value shouldBe 400
            // {"statusCode":400,"message":"localization key","debug":"java.lang.Throwable: constraint name","errors":[{"message":"localization key","key":"localization key","field":"field name"}],"key":"localization key","context":"field name"}
            Json.decodeFromStream<ApiError>(it.bodyAsText().byteInputStream(Charsets.UTF_8)).apply {
                statusCode shouldBe 400
                message shouldBe "localization key"
                debug shouldBe "java.lang.Throwable: constraint name"
                (errors!!).size shouldBe 1
                errors[0].key shouldBe "localization key"
                errors[0].field shouldBe "field name"
            }
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    @Test
    fun `test ExposedSQLException constraint not mapped`() = testApplication {
        install(StatusPages) {
            configureStatusPages(
                arrayOf()
            )
        }
        install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
            json(Json { explicitNulls = false })
        }

        routing {
            route("/ExposedSQLException") {
                get {
                    throw ExposedSQLException(
                        Throwable("constraint name"),
                        listOf(),
                        mock(Transaction::class.java)
                    )
                }
            }
        }

        val client = createClient { install(ContentNegotiation) { json() } }

        client.get("/ExposedSQLException").let {
            it.status.value shouldBe 500
            println(it.bodyAsText())
            // {"statusCode":500,"message":"Something went wrong","debug":"java.lang.Throwable: constraint name"}
            Json.decodeFromStream<ApiError>(it.bodyAsText().byteInputStream(Charsets.UTF_8)).apply {
                statusCode shouldBe 500
                message shouldBe "Something went wrong"
                debug shouldBe "java.lang.Throwable: constraint name"
                errors shouldBe null
            }
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    @Test
    fun `test HttpStatusCode NotFound`() = testApplication {
        install(StatusPages) { configureStatusPages(arrayOf<ConstraintMapping>()) }
        install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
            json(Json { explicitNulls = false })
        }

        routing {
            get("/DoesNotExist") {
                throw NotFoundException("test")
            }
        }

        val client = createClient { install(ContentNegotiation) { json() } }

        client.get("/DoesNotExist").let {
            it.status.value shouldBe 404
            // {"statusCode":404,"message":"Not found","debug":"services.oneteam.domains.NotFoundException: test"}

            Json.decodeFromStream<ApiError>(it.bodyAsText().byteInputStream(Charsets.UTF_8)).apply {
                statusCode shouldBe 404
                message shouldBe "Not found"
                debug shouldBe "services.oneteam.ai.shared.domains.NotFoundException: test"
            }
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    @Test
    fun `test BadRequestException`() = testApplication {
        install(StatusPages) { configureStatusPages(arrayOf<ConstraintMapping>()) }
        install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
            json(Json { explicitNulls = false })
        }

        routing {
            route("/BadRequestException") {
                get {
                    throw services.oneteam.ai.shared.domains.BadRequestException(
                        ValidationErrors().add(
                            "key",
                            "fieldName"
                        )
                    )
                }
            }
        }

        val client = createClient { install(ContentNegotiation) { json() } }

        client.get("/BadRequestException").let {
            it.status.value shouldBe 400
            // {"statusCode":400,"message":"Request validation failed","debug":"Bad request: fieldName: key","errors":[{"message":"key","key":"key","field":"fieldName"}],"key":"key","context":"fieldName"}

            Json.decodeFromStream<ApiError>(it.bodyAsText().byteInputStream(Charsets.UTF_8)).apply {
                statusCode shouldBe 400
                message shouldBe "Request validation failed"
                debug shouldBe "Bad request: fieldName: key"
                (errors!!).size shouldBe 1
                errors[0].message shouldBe "key"
                errors[0].key shouldBe "key"
                errors[0].field shouldBe "fieldName"
                key shouldBe "errors.common.badRequest"
            }
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    @Test
    fun `test IllegalArgumentException`() = testApplication {
        install(StatusPages) { configureStatusPages(arrayOf<ConstraintMapping>()) }
        install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
            json(Json { explicitNulls = false })
        }

        routing {
            route("/IllegalArgumentException") {
                get {
                    throw IllegalArgumentException("test")
                }
            }
        }

        val client = createClient { install(ContentNegotiation) { json() } }

        client.get("/IllegalArgumentException").let {
            it.status.value shouldBe 400
            // {"statusCode":400,"message":"java.lang.IllegalArgumentException: test","debug":"test"}

            Json.decodeFromStream<ApiError>(it.bodyAsText().byteInputStream(Charsets.UTF_8)).apply {
                statusCode shouldBe 400
                message shouldBe "java.lang.IllegalArgumentException: test"
                debug shouldBe "test"
            }
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    @Test
    fun `test ApplicationException`() = testApplication {
        install(StatusPages) { configureStatusPages(arrayOf<ConstraintMapping>()) }
        install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
            json(Json { explicitNulls = false })
        }

        routing {
            route("/ApplicationException") {
                get {
                    throw ApplicationException("test")
                }
            }
        }

        val client = createClient { install(ContentNegotiation) { json() } }

        client.get("/ApplicationException").let {
            it.status.value shouldBe 400
            // {"statusCode":400,"message":"test","debug":"services.oneteam.domains.ApplicationException: test"}

            Json.decodeFromStream<ApiError>(it.bodyAsText().byteInputStream(Charsets.UTF_8)).apply {
                statusCode shouldBe 400
                message shouldBe "test"
                debug shouldBe "services.oneteam.ai.shared.domains.ApplicationException: test"
            }
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    @Test
    fun `test generic exception`() = testApplication {
        install(StatusPages) { configureStatusPages(arrayOf<ConstraintMapping>()) }
        install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
            json(Json { explicitNulls = false })
        }

        routing {
            route("/NullPointerException") {
                get {
                    throw NullPointerException("test")
                }
            }
        }

        val client = createClient { install(ContentNegotiation) { json() } }

        client.get("/NullPointerException").let {
            it.status.value shouldBe 500
            // {"statusCode":500,"message":"NullPointerException","debug":"test"}
            Json.decodeFromStream<ApiError>(it.bodyAsText().byteInputStream(Charsets.UTF_8)).apply {
                statusCode shouldBe 500
                message shouldBe "NullPointerException"
                debug shouldBe "test"
            }
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    @Test
    fun `test no route`() = testApplication {
        install(StatusPages) { configureStatusPages(arrayOf<ConstraintMapping>()) }
        install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
            json(Json { explicitNulls = false })
        }

        routing {
            // no routes exist
        }

        val client = createClient { install(ContentNegotiation) { json() } }

        client.get("/doesNotExist").let {
            it.status.value shouldBe 404
            // {"statusCode":404,"message":"Not Found /doesNotExist"}

            Json.decodeFromStream<ApiError>(it.bodyAsText().byteInputStream(Charsets.UTF_8)).apply {
                statusCode shouldBe 404
                message shouldBe "Not Found /doesNotExist"
            }
        }

    }
}