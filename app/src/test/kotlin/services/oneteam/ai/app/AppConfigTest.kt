package services.oneteam.ai.app

import io.ktor.server.config.ApplicationConfig
import io.ktor.server.testing.*
import java.util.Locale
import kotlin.test.*


class AppConfigTest() {

    @Test
    fun `app config should load`() = testApplication {

        environment {
            config = ApplicationConfig("application.yaml")

            val appConfig = AppConfig()
            appConfig.fromConfig(config)

            assertNotNull(appConfig.databaseConfig)
            assertNotNull(appConfig.flyway)
            assertNotNull(appConfig.cookie)
            assertNotNull(appConfig.jwt)
            assertNotNull(appConfig.frontendUrl)
            assertNotNull(appConfig.websiteName)
            assertEquals("en-AU", Locale.getDefault().toLanguageTag())

        }
    }

}
