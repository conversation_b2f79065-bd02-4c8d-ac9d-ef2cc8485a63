package services.oneteam.ai.app

import io.kotest.matchers.string.shouldContain
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import java.io.ByteArrayOutputStream
import java.io.OutputStream
import java.io.PrintStream

class LoggingTest {

    var sysOut: PrintStream? = null
    var outContent: ByteArrayOutputStream? = null

    @BeforeEach
    fun setUpStreams() {
        sysOut = System.out;
        outContent = ByteArrayOutputStream()
        System.setOut(PrintStream(outContent as OutputStream))
    }

    @AfterEach
    fun revertStreams() {
        System.setOut(sysOut);
    }

    @Test
    fun `tenant should be present on log statement`() {

        val logger: Logger = LoggerFactory.getLogger(javaClass)
        runBlocking {
            MDC.put("tenant", "TENANT")
            withContext(MDCContext()) {
                logger.info("Hello, world!")
                // expect something like: {"@timestamp":"2025-07-08T15:56:40.323666+10:00","@version":"1","message":"Hello, world!","logger_name":"services.oneteam.ai.app.LoggingTest","thread_name":"Test worker @coroutine#2","level":"INFO","level_value":20000,"tenant":"TENANT"}
                outContent.toString().shouldContain(""","tenant":"TENANT"""")
                outContent.toString().shouldContain(""""message":"Hello, world!"""")
            }
        }
    }
}