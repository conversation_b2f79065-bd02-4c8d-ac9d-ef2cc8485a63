package services.oneteam.ai.app

import io.kotest.matchers.string.shouldContain
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import java.io.ByteArrayOutputStream
import java.io.OutputStream
import java.io.PrintStream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

class LoggingTest {

    var sysOut: PrintStream? = null
    var outContent: ByteArrayOutputStream? = null

    @BeforeEach
    fun setUpStreams() {
        sysOut = System.out;
        outContent = ByteArrayOutputStream()
        System.setOut(PrintStream(outContent as OutputStream))
    }

    @AfterEach
    fun revertStreams() {
        System.setOut(sysOut);
    }

    @Test
    fun `tenant should be present on log statement`() {

        val logger: Logger = LoggerFactory.getLogger(javaClass)
        runBlocking {
            MDC.put("tenant", "TENANT")
            withContext(MDCContext()) {
                logger.info("Hello, world!")
                // expect something like: 2024-10-02 12:08:58.419 [TENANT] [Test worker @coroutine#1] INFO  services.oneteam.ai.shared.LoggingTest - Hello, world!
                outContent.toString().shouldContain("[TENANT]")
                outContent.toString().shouldContain(".LoggingTest - Hello, world!")
            }
        }
    }
}