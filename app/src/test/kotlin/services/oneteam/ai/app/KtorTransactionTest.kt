package services.oneteam.ai.app

import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.config.*
import io.ktor.server.testing.*
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import kotlin.test.assertEquals

@Disabled
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class KtorTransactionTest {

    private fun setupConfig(): ApplicationConfig {
        return ApplicationConfig("application.yaml")
    }

    data class Spec(
        val path: String,
    )

    fun provider(): Stream<Spec> {
        return Stream.of(
            Spec("/ai/api/tools/debug/transactions/6"),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun testPath(spec: Spec) = testApplication {
        val setupConfig = setupConfig()
        environment {
            config = setupConfig
        }
        application { setup() }

        client.get(spec.path) {
            headers {
                append(HttpHeaders.ContentType, ContentType.Application.Json)
                append(HttpHeaders.Referrer, originUrl)
            }
        }.apply {
            assertEquals(HttpStatusCode.OK, status)
            assertEquals("OK", bodyAsText())
        }
    }


}