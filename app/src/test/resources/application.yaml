ktor:
  locale: "en-AU"
  deployment:
    port: 8080
  development: true
  websiteName: "$WEBSITE_SITE_NAME:"
  application:
    modules:
      - services.oneteam.ai.app.ApplicationKt.module
  database:
    audit: false
    application:
      maximumPoolSize: "6"
      driverClassName: "org.h2.Driver"
      jdbcUrl: "jdbc:h2:mem:otai_development;MODE=PostgreSQL;DATABASE_TO_UPPER=false;INIT=SET SCHEMA TESTING;"
      username: "otai_admin"
      password: "otai_admin"
      rls: false
    privileged:
      maximumPoolSize: "$DB_SUPER_POOL_SIZE:3"
      driverClassName: "org.h2.Driver"
      jdbcUrl: "jdbc:h2:mem:otai_development;MODE=PostgreSQL;DATABASE_TO_UPPER=false;INIT=RUNSCRIPT FROM 'classpath:database/h2/init.sql'"
      username: "otai_superuser"
      password: "otai_superuser"
      poolName: "PrivilegedPool"
      rls: false
  cookie:
    name: "test_session"
    secretEncryptKey: 00112233445566778899aabbccddeeff
    secretSignKey: 1111222233334444aabbccddeeff
    timeout: "$TIMEOUT:15m"
  jwt:
    publicKey: |
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqxPdT2kgzHnfxsVOUZgB
      2f9KlO/nIi5ZE95br1365jToyXJpKQTLRBeIY/+5dRHIJe0D1HQe4dZihNRXajOd
      PGqot5jsRH0JH/IenMOtxRJf35AXNTeyqEvdwKxL8HsY9MPonZKcy7lQpHeauWQs
      amJpm20zOEnlx+41P5c7230oanHtYxkig+L+cG1It55NgzcvIwqqmJOf0yP4bkjE
      U4F2qMTh+VdAAABb9XhwqQszXFqttUshec2nN7tkS2g+ErSscGLZbXwKJj771gmX
      vyvKxUSzmpianOAJolmu9IY+9Z93TR2vEyAAkqr91EqLMmefF054OVQS45L04gHS
      pQIDAQAB
      -----END PUBLIC KEY-----
  otaiServiceAccount:
    "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.JeDsE42JUezvo4bkJWLcxh4Mz_aNmnMaGL0McsjrKGg6-w0Dyt5rwXqJQl6RdWIr152tgWt_8h-esZkbA7aikrCiH53YBoTqiNMdkajG-aOreYHpj4SozuiFwVhA8caLhCCFvoOIv5YNICf_BAq45TefNNaBvBRt8Ec-R8VwDzdogaHJaa8lQBPOZ66pfpH76Z5y1eO_zKTXCFZxNe-zZ7hhuPq_NbE92lot1fZ8c5Ur6RFxehApYWcYiT5DiF_CgeIoLTR7fpWVhXRsNhWmBeehcSQhvccw7G8uQWjTItNnImsYjxtwbhDN5W2Ypsupn6i4AGxwr5y80gb3-tNO5g"
  storage:
    accountName: "azurite"
    uploadContainerName: "upload"
    accessKey: "Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw=="
  cors:
    hosts: "https://local;http://localhost:3000;http://localhost:4000;http://localhost:8000;http://localhost:8080"
telemetry:
  enabled: false
flyway:
  enabled: true
  locations: [ "classpath:database/h2/migration" ]
  properties: {
    "test": {
      "domain": "localhost",
      "protocol": "http"
    }
  }

seedData: false

flows:
  runExecutionImmediately: "$FLOW_RUN_EXECUTION_IMMEDIATELY:false"
  flowExecutionMaxDurationMins: "$FLOW_EXECUTION_MAX_DURATION_MINS:2"
  numberOfThreads: "$NUMBER_OF_THREADS:4"
  maxConcurrentCoroutines: "$MAX_CONCURRENT_COROUTINES:30"
  includeLogging: "$INCLUDE_LOGGING:true"
  skipStepUpdates: "$FLOWS_SKIP_STEP_UPDATES:true"
  skipVariableUpdates: "$FLOWS_SKIP_VARIABLE_UPDATES:false"
  skipSubFlowFlowUpdates: "$FLOWS_SKIP_SUB_FLOW_FLOW_UPDATES:true"
  fedStorageType: "$FLOWS_FED_STORAGE_TYPE:AUTOMERGE"
azureWebPubSub:
  connectionString: "$AZURE_WEB_PUB_SUB_CONNECTION_STRING:"
  hubName: "$AZURE_WEB_PUB_SUB_HUB_NAME:otaiPubSubHub"
  tokenExpirySeconds: "$AZURE_WEB_PUB_SUB_TOKEN_EXPIRY_SECONDS:60"
filePress:
  url: "$FILE_PRESS_URL:"
oneTeamStorage:
  storageName: "$ONETEAM_STORAGE_STORAGE_NAME:"
  containerName: "$ONETEAM_STORAGE_CONTAINER_NAME:"
  sasToken: "$ONETEAM_STORAGE_SAS_TOKEN:"
toggles:
  useExecutionStepFactoryV1: "$USE_EXECUTION_STEP_FACTORY_V1:false"
passVault:
  keyVault:
    serviceProvider: "$PASS_VAULT_KEY_VAULT_PROVIDER:IN_MEMORY"
    azure:
      name: "$PASS_VAULT_KEY_VAULT_NAME:"
      secretNamePrefix: "$PASS_VAULT_KEY_VAULT_SECRET_NAME_PREFIX:"
      credentialStrategy: "$PASS_VAULT_KEY_VAULT_AZURE_CREDENTIAL_STRATEGY:"
    inMemory:
      name: "$PASS_VAULT_KEY_VAULT_NAME:test-vault"
      secretNamePrefix: "$PASS_VAULT_KEY_VAULT_SECRET_NAME_PREFIX:test"
      loadSeedData: "$PASS_VAULT_KEY_VAULT_LOAD_SEED_DATA:false"
debug:
  enableEndpoints: "$DEBUG_ENABLE_ENDPOINTS:true"
