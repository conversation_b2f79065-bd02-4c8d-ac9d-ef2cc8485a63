CREATE TABLE tenants
(
    id         bigint GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    created_at timestamp    NOT NULL,
    updated_at timestamp,
    name       text         NOT NULL,
    origin_url text         NOT NULL,
    internal_url text       NOT NULL,
    internal_sync_url text  NOT NULL,
        CONSTRAINT tenants_origin_url_unique
            UNIQUE (origin_url)
);

CREATE TABLE users
(
    id         bigint GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    created_at timestamp NOT NULL,
    updated_at timestamp,
    email      text      NOT NULL,
    tenant_id  bigint    NOT NULL,
    CONSTRAINT users_email_tenant_id_unique
        UNIQUE (email, tenant_id)
);

INSERT INTO tenants (name, origin_url, created_at, updated_at, internal_url, internal_sync_url)
VALUES ('OT', 'http://localhost:8000', NOW(), NOW(), 'http://localhost:8000', 'http://localhost:8000');
