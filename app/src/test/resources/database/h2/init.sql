CREATE USER IF NOT EXISTS otai_superuser PASSWORD 'otai_superuser';
CREATE USER IF NOT EXISTS otai_admin PASSWORD 'otai_admin';
-- https://stackoverflow.com/a/75737605
CREATE DOMAIN IF NOT EXISTS jsonb AS JSON;

GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA PUBLIC TO otai_superuser;
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA PUBLIC TO otai_admin;

CREATE SCHEMA IF NOT EXISTS TESTING;
GRANT SELECT, INSERT, UPDATE, DELETE ON SC<PERSON>EMA TESTING TO otai_superuser;
GRANT SELECT, INSERT, UPDATE, <PERSON>LETE ON SCHEMA TESTING TO otai_admin;

SET SCHEMA TESTING;