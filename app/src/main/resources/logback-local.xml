<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%X{tenant}] [%X{flowExecutionId}] [%thread] %-5level %logger{36} -
                %msg%n
            </pattern>
        </encoder>
    </appender>
    <root level="trace">
        <appender-ref ref="STDOUT"/>
    </root>
    <logger name="org.eclipse.jetty" level="INFO"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    <logger name="io.netty" level="INFO"/>
    <logger name="io" level="INFO"/>
    <logger name="com" level="INFO"/>
    <logger name="org" level="INFO"/>
    <logger name="EXPOSED" level="INFO"/>
    <logger name="Exposed" level="INFO"/>
    <logger name="services.oneteam.ai.shared.database" level="WARN"/>
    <logger name="services.oneteam.ai.shared" level="WARN"/>
    <logger name="services.oneteam.ai.flow" level="TRACE"/>
</configuration>