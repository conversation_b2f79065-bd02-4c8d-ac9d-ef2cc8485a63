<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!--
        https://github.com/logfellow/logstash-logback-encoder#readme
        Use the logstash logback encoder to configure the stacktraces to be smaller.
        This is required because Azure truncates the log messages at 16385 characters and stacktraces easily puts us over this.
        -->
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                <maxLength>2048</maxLength>
                <maxDepthPerThrowable>30</maxDepthPerThrowable>
                <rootCauseFirst>true</rootCauseFirst>
                <shortenClassName>true</shortenClassName>

                <exclude>io\.ktor\..*</exclude>
                <exclude>kotlinx\..*</exclude>
            </throwableConverter>
        </encoder>
    </appender>

    <root level="${LOGGER_ROOT_LEVEL:-DEBUG}">
        <appender-ref ref="STDOUT"/>
    </root>
    <logger name="org.eclipse.jetty" level="INFO"/>
    <logger name="com.zaxxer.hikari" level="INFO"/>
    <logger name="io.netty" level="INFO"/>
    <logger name="reactor.netty.channel" level="INFO"/>
    <logger name="com.jayway.jsonpath" level="INFO"/>
    <logger name="${LOGGER_OTAI_NAME:-}" level="${LOGGER_OTAI_LEVEL:-${LOGGER_ROOT_LEVEL:-DEBUG}}"/>

</configuration>