ktor:
  locale: "en-AU"
  deployment:
    port: 8080
    # https://stackoverflow.com/a/78182668
    connectionGroupSize: "$CONNECTION_GROUP_SIZE:10"
    workerGroupSize: "$WORKER_GROUP_SIZE:10"
    callGroupSize: "$CALL_GROUP_SIZE:16"
  development: "$DEVELOPMENT:false"
  frontendUrl: "$FRONTEND_URL:" # for development only
  websiteName: "$WEBSITE_SITE_NAME:"
  application:
    modules:
      - services.oneteam.ai.app.ApplicationKt.module
  database:
    audit: true
    application:
      maximumPoolSize: "$DB_POOL_SIZE:6"
      driverClassName: "org.postgresql.Driver"
      jdbcUrl: "$DB_URL:**************************************************"
      username: "$DB_USER:otai_admin"
      password: "$DB_PASSWORD:otai_admin"
    privileged:
      maximumPoolSize: "$DB_SUPER_POOL_SIZE:3"
      driverClassName: "org.postgresql.Driver"
      jdbcUrl: "$DB_URL:**************************************************"
      username: "$DB_SUPER_USER:otai_superuser"
      password: "$DB_SUPER_PASSWORD:otai_superuser"
      poolName: "PrivilegedPool"
  cookie:
    name: "user_session"
    secretEncryptKey: "$SESSION_ENCRYPT" #32 bytes
    secretSignKey: "$SESSION_SIGN" # 32 bytes
    timeout: "$TIMEOUT:15m"
  jwt:
    publicKey: "$JWT_PUBLIC_KEY:"
  otaiServiceAccount:
    token: "$SERVICE_JWT_TOKEN"
  storage:
    accountName: "$STORAGE_ACCOUNT_NAME:"
    uploadContainerName: "$STORAGE_CONTAINER_UPLOAD:upload"
    accessKey: "$STORAGE_ACCESS_KEY:"
telemetry:
  enabled: "$TELEMETRY_ENABLED:false"
loadSampleData: "$LOAD_SAMPLE_DATA:false"
flyway:
  enabled: true
  locations: [ "classpath:db/migration/postgres-only", "classpath:db/migration/common", "classpath:db/migration/${websiteName}" ]
  # properties keyed off the environment variable WEBSITE_SITE_NAME
  properties: {
    "local": {

    },
    "otai-dev": {

    }
  }
seedData: true
syncServerUrl: "$SYNC_SERVER_URL:ws://localhost:3030"
documentStrategy: "$DOCUMENT_STRATEGY:API"
flows:
  runExecutionImmediately: "$FLOW_RUN_EXECUTION_IMMEDIATELY:true"
  flowExecutionMaxDurationMins: "$FLOW_EXECUTION_MAX_DURATION_MINS:2"
  numberOfThreads: "$NUMBER_OF_THREADS:4"
  maxConcurrentCoroutines: "$MAX_CONCURRENT_COROUTINES:30"
  includeLogging: "$FLOWS_INCLUDE_LOGGING:false"
  skipStepUpdates: "$FLOWS_SKIP_STEP_UPDATES:true"
  skipVariableUpdates: "$FLOWS_SKIP_VARIABLE_UPDATES:true"
  skipSubFlowFlowUpdates: "$FLOWS_SKIP_SUB_FLOW_FLOW_UPDATES:true"
  fedStorageType: "$FLOWS_FED_STORAGE_TYPE:BLOB"
  jsonataTimeoutMs: "$JSONATA_TIMEOUT_MS:20001"
  jsonataMaxRecursionDepth: "$JSONATA_MAX_RECURSION_DEPTH:1001"
artificialDelayMs: "$ARTIFICIAL_DELAY_MS:0"
azureWebPubSub:
  connectionString: "$AZURE_WEB_PUB_SUB_CONNECTION_STRING:"
  hubName: "$AZURE_WEB_PUB_SUB_HUB_NAME:otaiPubSubHub"
  tokenExpirySeconds: "$AZURE_WEB_PUB_SUB_TOKEN_EXPIRY_SECONDS:60"
filePress:
  url: "$FILE_PRESS_URL:"
sampleDataRequestTimeoutMs: "$SAMPLE_DATA_REQUEST_TIMEOUT_MS:60000"
oneTeamStorage:
  storageName: "$ONETEAM_STORAGE_STORAGE_NAME:"
  containerName: "$ONETEAM_STORAGE_CONTAINER_NAME:"
  sasToken: "$ONETEAM_STORAGE_SAS_TOKEN:"
toggles:
  useExecutionStepFactoryV1: "$USE_EXECUTION_STEP_FACTORY_V1:false"
passVault:
  keyVault:
    serviceProvider: "$PASS_VAULT_KEY_VAULT_PROVIDER:AZURE" # See KeyVaultServiceProvider
    azure:
      name: "$PASS_VAULT_KEY_VAULT_NAME:"
      secretNamePrefix: "$PASS_VAULT_KEY_VAULT_SECRET_NAME_PREFIX:"
      credentialStrategy: "$PASS_VAULT_KEY_VAULT_AZURE_CREDENTIAL_STRATEGY:" # See AzureCredentialStrategy
    inMemory:
      name: "$PASS_VAULT_KEY_VAULT_NAME:"
      secretNamePrefix: "$PASS_VAULT_KEY_VAULT_SECRET_NAME_PREFIX:"
      loadSeedData: "$PASS_VAULT_KEY_VAULT_MEMORY_LOAD_SEED_DATA:false"

debug:
  enableEndpoints: "$DEBUG_ENABLE_ENDPOINTS:false"