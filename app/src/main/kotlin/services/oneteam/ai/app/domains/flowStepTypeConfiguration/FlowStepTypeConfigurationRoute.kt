package services.oneteam.ai.app.domains.flowStepTypeConfiguration

import io.ktor.resources.Resource
import io.ktor.server.request.receive
import io.ktor.server.resources.delete
import io.ktor.server.resources.get
import io.ktor.server.resources.post
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import io.ktor.util.*
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService

@Resource("/flowStepTypeConfiguration")
class FlowStepTypeConfigurationRoute() {
    @Resource("{id}")
    class Id(val parent: FlowStepTypeConfigurationRoute = FlowStepTypeConfigurationRoute(), val id: Long)
}

fun Route.flowStepTypeConfigurationEndpoints(
    flowStepTypeConfigurationService: FlowStepTypeConfigurationService,
) {
    get<FlowStepTypeConfigurationRoute> {
        val queryParameters = call.request.queryParameters.toMap()
        val c = if (queryParameters.isNotEmpty()) {
            flowStepTypeConfigurationService.getAllByQuery(queryParameters)
        } else {
            flowStepTypeConfigurationService.getAll()
        }
        call.respond(c)
    }

    get<FlowStepTypeConfigurationRoute.Id> { cId ->
        val c = flowStepTypeConfigurationService.getById(cId.id)
        call.respond(c)
    }

    delete<FlowStepTypeConfigurationRoute.Id> { cId ->
        val c = flowStepTypeConfigurationService.delete(cId.id)
        call.respond(c)
    }

    post<FlowStepTypeConfigurationRoute> {
        val flowStepTypeConfiguration = call.receive<FlowStepTypeConfiguration>()
        val c = flowStepTypeConfigurationService.create(flowStepTypeConfiguration)
        call.respond(c)
    }
}