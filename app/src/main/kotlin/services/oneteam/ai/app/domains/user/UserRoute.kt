package services.oneteam.ai.app.domains.user

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.resources.*
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.user.UserSearchCriteria
import services.oneteam.ai.shared.domains.user.UserService

@Suppress("unused")
@Resource("/users")
private class UserRoute() {
    @Resource("{id}")
    class Id(val users: UserRoute, val id: User.Id)
}

fun Route.userEndpoints(
    userService: UserService
) {
    get<UserRoute> {
        val pageRequest = call.request.buildPageRequest(UserRepository.SORTABLE_FIELDS)
        val searchCriteria = UserSearchCriteria.fromRequest(call.request.queryParameters)

        val user = userService.search(pageRequest, searchCriteria)
        call.respond(user)
    }

    get<UserRoute.Id> { params ->
        val user = userService.findById(params.id)
        call.respond(user)
    }
}

fun UserSearchCriteria.Companion.fromRequest(
    parameters: Parameters
): UserSearchCriteria {
    val searchTerm = parameters["search"] ?: ""

    return UserSearchCriteria(
        searchTerm = searchTerm,
    )

}
