package services.oneteam.ai.app.internal

import io.ktor.http.*
import io.ktor.server.engine.*
import io.ktor.server.request.*
import io.ktor.utils.io.*

class InternalRequest(call: InternalCall) : BaseApplicationRequest(call) {
    var method: HttpMethod = HttpMethod.Get
    var uri: String = "/"
    var scheme: String = "http"
    var version: String = "HTTP/1.1"
    var port: Int = 8080
    var host: String = "localhost"
    var queryParams: Map<String, String> = emptyMap()
    var requestHeaders: Map<String, String> = emptyMap()
    var body: ByteReadChannel = ByteReadChannel.Empty

    override val local: RequestConnectionPoint = object : RequestConnectionPoint {
        override val uri: String get() = <EMAIL>
        override val method: HttpMethod get() = <EMAIL>
        override val scheme: String get() = <EMAIL>
        override val version: String get() = <EMAIL>
        override val port: Int get() = <EMAIL>
        override val host: String get() = <EMAIL>
        override val localPort: Int get() = <EMAIL>
        override val serverPort: Int get() = <EMAIL>
        override val localHost: String get() = <EMAIL>
        override val serverHost: String get() = <EMAIL>
        override val localAddress: String get() = <EMAIL>
        override val remoteHost: String get() = <EMAIL>
        override val remotePort: Int get() = <EMAIL>
        override val remoteAddress: String get() = <EMAIL>
    }

    override val queryParameters: Parameters = object : Parameters {
        override val caseInsensitiveName: Boolean get() = true
        override fun getAll(name: String) = queryParams[name]?.let { listOf(it) } ?: emptyList()
        override fun names() = queryParams.keys
        override fun entries() = queryParams.mapValues { listOf(it.value) }.entries
        override fun isEmpty() = queryParams.isEmpty()
    }

    override val rawQueryParameters: Parameters = queryParameters
    override val cookies: RequestCookies = RequestCookies(this)
    override val engineHeaders: Headers get() = Headers.build {
        requestHeaders.entries.forEach { append(it.key, it.value) }
    }
    override val engineReceiveChannel: ByteReadChannel get() = body
}