package services.oneteam.ai.app.middlewares

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.application.hooks.MonitoringEvent
import io.ktor.server.request.*
import io.ktor.server.routing.*
import io.ktor.util.*
import services.oneteam.ai.app.AppConfig
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.tenant.TenantService

val tenantKey = AttributeKey<Tenant>("tenant")
val tenantServiceKey = AttributeKey<TenantService>("tenantService")
val appConfigKey = AttributeKey<AppConfig>("appConfig")

class CurrentTenantConfiguration {
    lateinit var tenantServiceConfig: TenantService
    lateinit var applicationConfig: AppConfig
}

val CurrentTenantPlugin = createApplicationPlugin(
    name = "CurrentTenantPlugin",
    createConfiguration = ::CurrentTenantConfiguration
) {
    val tenantService = pluginConfig.tenantServiceConfig
    val appCfg = pluginConfig.applicationConfig

    on(MonitoringEvent(ApplicationStarted)) { application ->
        application.attributes.put(tenantServiceKey, tenantService)
        application.attributes.put(appConfigKey, appCfg)
    }
}

/**
 * strip path from referrer
 */
fun cleanReferrer(url: String?): String? {
    if (url.isNullOrEmpty()) return null
    val referrer = Url(url)
    return referrer.protocol.name + "://" + referrer.host + if (referrer.specifiedPort != 0) ":" + referrer.specifiedPort else ""
}

/**
 * add protocol to host
 */
fun cleanHost(host: String?, appConfig: AppConfig): String? {
    if (host.isNullOrEmpty()) return null
    val protocol = if (appConfig.development) "http" else "https"
    return "$protocol://$host"
}


val TenantPlugin = createRouteScopedPlugin("TenantPlugin") {
    onCall { call ->
        // The browser will set the origin header if the request is cross-origin - but we aren't in a cross-origin situation
        // so we need to fall back to the host or referrer header instead
        // https://javascript.info/fetch-crossorigin
        val tenantService = call.application.attributes[tenantServiceKey]
        val appConfig = call.application.attributes[appConfigKey]

        val originUrl =
            call.request.header(HttpHeaders.Origin)
                ?: cleanReferrer(call.request.header(HttpHeaders.Referrer))
                ?: cleanHost(call.request.header(HttpHeaders.Host), appConfig)
                ?: throw IllegalArgumentException("Missing Origin Header")
        val tenant =
            tenantService.getByInternalUrlOrOriginUrl(originUrl)
                ?: throw IllegalArgumentException("Unknown Tenant for $originUrl")
        call.attributes.put(tenantKey, tenant)
    }
}

fun Route.withTenant(build: Route.() -> Unit): Route {
    val tenantedRoute = createChild(WithTenantSelector())
    tenantedRoute.install(TenantPlugin)
    tenantedRoute.build()
    return tenantedRoute
}

class WithTenantSelector() : RouteSelector() {
    override suspend fun evaluate(context: RoutingResolveContext, segmentIndex: Int): RouteSelectorEvaluation {
        return RouteSelectorEvaluation.Transparent
    }

    override fun toString(): String = "(withTenant)"
}
