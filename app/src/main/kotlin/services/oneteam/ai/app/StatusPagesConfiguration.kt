package services.oneteam.ai.app

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.plugins.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.ApplicationException

val logger: Logger = LoggerFactory.getLogger("StatusPagesConfiguration")

/**
 * Configure status pages for the application.
 *
 * This catches application exceptions and converts them to a structured response that the client can understand.
 * It is very selective about what it logs - for example there is no point in logging a stacktrace for a 404. The intention is to log only the most important information and reduce noise.
 */
fun StatusPagesConfig.configureStatusPages(constraintMapping: Array<ConstraintMapping>) {
    exception<Throwable> { call, cause ->
        when (cause) {
            is services.oneteam.ai.shared.domains.BadRequestException -> handleBadRequestException(call, cause)
            is IllegalArgumentException, is BadRequestException -> handleIllegalArgumentException(call, cause)
            is NotFoundException, is services.oneteam.ai.shared.domains.NotFoundException -> handleNotFoundException(
                call,
                cause
            )

            is ApplicationException -> handleApplicationException(call, cause)
            is ExposedSQLException -> handleExposedSQLException(call, cause, constraintMapping)
            else -> handleGenericException(call, cause)
        }
    }
    status(HttpStatusCode.NotFound) { call, _ ->
        call.respond(HttpStatusCode.NotFound, ApiError(HttpStatusCode.NotFound.value, "Not Found ${call.request.uri}"))
    }
}

private suspend fun handleExposedSQLException(
    call: ApplicationCall, cause: ExposedSQLException, constraintMapping: Array<ConstraintMapping>
) {
    logger.warn("ExposedSQLException ${cause.message}", cause)

    constraintMapping.forEach() {
        if (cause.message?.contains(it.constraint) == true) {
            call.respond(
                HttpStatusCode.BadRequest, ApiError(
                    statusCode = HttpStatusCode.BadRequest.value,
                    message = it.localizationKey,
                    debug = cause.message,
                    errors = listOf(
                        ApiValidationError(
                            message = it.localizationKey,
                            key = it.localizationKey,
                            field = it.context
                        ),
                    ),
                    key = it.localizationKey,
                    context = it.context
                )
            )
            return
        }
    }

    call.respond(
        HttpStatusCode.InternalServerError,
        ApiError(
            HttpStatusCode.InternalServerError.value,
            "Something went wrong",
            cause.message,
            key = "errors.common.unexpected"
        )
    )
}

private suspend fun handleApplicationException(
    call: ApplicationCall,
    cause: ApplicationException
) {
    logger.warn("ApplicationException ${cause.message}")
    call.respond(
        HttpStatusCode.BadRequest,
        ApiError(HttpStatusCode.BadRequest.value, cause.message ?: "Bad Request", cause.toString())
    )
}

private suspend fun handleNotFoundException(
    call: ApplicationCall,
    cause: Throwable,
) {
    logger.warn("NotFound ${cause.message}")
    logger.trace("NotFound ${cause.message}", cause) // show stacktrace only in trace log level

    call.respond(
        HttpStatusCode.NotFound,
        ApiError(HttpStatusCode.NotFound.value, "Not found", cause.toString(), key = "errors.common.notFound")
    )
}

private suspend fun handleIllegalArgumentException(call: ApplicationCall, cause: Throwable) {
    logger.warn("BadRequest ${cause.message}", cause)
    call.respond(
        HttpStatusCode.BadRequest,
        ApiError(
            HttpStatusCode.BadRequest.value,
            cause.toString(),
            debug = cause.message,
            key = "errors.common.badRequest"
        )
    )
    return
}

private suspend fun handleBadRequestException(
    call: ApplicationCall,
    cause: services.oneteam.ai.shared.domains.BadRequestException,
) {
    logger.warn("BadRequest ${cause.message}")
    val errors = cause.errors?.errors?.map {
        ApiValidationError(
            it.localizationKey, it.localizationKey, it.field, it.context
        )
    }
    call.respond(
        HttpStatusCode.BadRequest, ApiError(
            HttpStatusCode.BadRequest.value,
            if (errors.isNullOrEmpty()) "Bad Request" else "Request validation failed",
            debug = cause.message,
            errors = errors,
            key = "errors.common.badRequest"
        )
    )
}

private suspend fun handleGenericException(call: ApplicationCall, cause: Throwable) {
    logger.error("Error processing request", cause)
    call.respond(
        HttpStatusCode.InternalServerError,
        ApiError(
            HttpStatusCode.InternalServerError.value,
            cause.javaClass.simpleName,
            debug = cause.message,
            key = "errors.common.unexpected"
        )
    )
}

@Serializable
class ConstraintMapping(
    val constraint: String, val localizationKey: String, val context: String
)