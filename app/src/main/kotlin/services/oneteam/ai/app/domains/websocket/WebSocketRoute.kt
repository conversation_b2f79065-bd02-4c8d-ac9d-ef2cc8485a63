package services.oneteam.ai.app.domains.websocket

import io.ktor.server.routing.Route
import io.ktor.util.reflect.TypeInfo
import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.pubsub.PubSubService

import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.encoding.decodeStructure
import kotlinx.serialization.encoding.encodeStructure
import com.azure.messaging.webpubsub.models.WebPubSubClientAccessToken
import io.ktor.resources.Resource
import io.ktor.server.request.receive
import io.ktor.server.resources.post
import kotlinx.serialization.SerializationException
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.encoding.CompositeDecoder
import kotlinx.serialization.descriptors.element
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

@Serializable(with = WebPubSubClientAccessTokenJsonSerializer::class)
data class WebPubSubClientAccessTokenJson(val token: WebPubSubClientAccessToken)

@Suppress("unused")
@Resource("/pubSub")
class PubSub() {
    @Resource("/generateToken")
    class GenerateToken(val parent: PubSub) {
        @Serializable
        data class RequestBody(
            val workspaceId: Workspace.Id,
        )
    }
}

fun Route.webSocketEndpoints() {
    post<PubSub.GenerateToken> {
        val principalId =
            coroutineContext[RequestContext]!!.principalId()!! // no system user here, so userId is always present
        val tenantId = coroutineContext[RequestContext]!!.tenant.id
        val generateTokenRequestBody = call.receive<PubSub.GenerateToken.RequestBody>()

        val token = PubSubService.generateToken(principalId, tenantId, generateTokenRequestBody.workspaceId)
        if (token == null) {
            throw Exception("Token generation failed")
        }
        call.respond(
            WebPubSubClientAccessTokenJson(token), TypeInfo(WebPubSubClientAccessTokenJson::class)
        )
    }
}

object WebPubSubClientAccessTokenJsonSerializer : KSerializer<WebPubSubClientAccessTokenJson> {
    override val descriptor: SerialDescriptor = buildClassSerialDescriptor("WebPubSubClientAccessToken") {
        element<String>("token")
        element<String>("url")
    }

    override fun serialize(encoder: Encoder, value: WebPubSubClientAccessTokenJson) {
        encoder.encodeStructure(descriptor) {
            encodeStringElement(descriptor, 0, value.token.token)
            encodeStringElement(descriptor, 1, value.token.url)
        }
    }

    override fun deserialize(decoder: Decoder): WebPubSubClientAccessTokenJson {
        return decoder.decodeStructure(descriptor) {
            var token = ""
            var url = ""
            while (true) {
                when (val index = decodeElementIndex(descriptor)) {
                    0 -> token = decodeStringElement(descriptor, 0)
                    1 -> url = decodeStringElement(descriptor, 1)
                    CompositeDecoder.DECODE_DONE -> break
                    else -> throw SerializationException("Unknown index $index")
                }
            }
            WebPubSubClientAccessTokenJson(WebPubSubClientAccessToken(token, url))
        }
    }
}