package services.oneteam.ai.app.internal

import io.ktor.http.*
import io.ktor.http.content.*
import io.ktor.server.engine.*
import io.ktor.server.response.*
import io.ktor.utils.io.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.runBlocking

class InternalResponse(
    call: InternalCall,
) : BaseApplicationResponse(call), CoroutineScope by call {
    private var responseChannel: ByteReadChannel? = null
    val byteContent: ByteArray?
        get() = when {
            responseChannel == null -> null
            else -> runBlocking { responseChannel!!.toByteArray() }
        }

    override val headers: ResponseHeaders = object : ResponseHeaders() {
        private val builder = HeadersBuilder()

        override fun engineAppendHeader(name: String, value: String) {
            builder.append(name, value)
        }

        override fun getEngineHeaderNames(): List<String> = builder.names().toList()
        override fun getEngineHeaderValues(name: String): List<String> = builder.getAll(name).orEmpty()
    }
    override val cookies: ResponseCookies = ResponseCookies(this)

    override suspend fun respondUpgrade(upgrade: OutgoingContent.ProtocolUpgrade) {
        throw UnsupportedOperationException("Upgrade not supported in internal engine")
    }

    override suspend fun respondOutgoingContent(content: OutgoingContent) {
        super.respondOutgoingContent(content)
    }

    override suspend fun responseChannel(): ByteWriteChannel {
        val result = ByteChannel(autoFlush = true)
        responseChannel = result
        return result
    }

    override fun setStatus(statusCode: HttpStatusCode) {
        // Implement the method to set the status code
    }
}