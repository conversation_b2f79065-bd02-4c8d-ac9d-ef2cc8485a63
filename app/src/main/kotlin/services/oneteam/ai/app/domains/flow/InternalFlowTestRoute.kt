package services.oneteam.ai.app.domains.flow

import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.conditional.Condition

fun Route.internalFlowTestEndpoints() {
    // Internal endpoint to test JSONConditionStructure
    post("/internal/condition") {
        val condition = call.receive<Condition>()
        val result = JsonataExpressionEvaluator().evaluate(condition.toExpression(), emptyMap<String, String>())
        call.respond(
            mapOf(
                "result" to result
            )
        )
    }
}