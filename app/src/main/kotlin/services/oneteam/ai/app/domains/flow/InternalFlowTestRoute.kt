package services.oneteam.ai.app.domains.flow

import io.ktor.http.HttpStatusCode
import io.ktor.http.content.PartData
import io.ktor.http.content.forEachPart
import io.ktor.http.content.streamProvider
import io.ktor.server.http.content.file
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.cio.writeChannel
import io.ktor.utils.io.copyAndClose
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.conditional.Condition
import java.io.File

fun Route.internalFlowTestEndpoints() {
    // Internal endpoint to test JSONConditionStructure
    post("/internal/condition") {
        val condition = call.receive<Condition>()
        val result = JsonataExpressionEvaluator().evaluate(condition.toExpression(), emptyMap<String, String>())
        call.respond(
            mapOf(
                "result" to result
            )
        )
    }

    post("/internal/test-upload-file") {
        val multipart = call.receiveMultipart()

        multipart.forEachPart { part ->
            when (part) {
                is PartData.FileItem -> {
                    val fileBytes = part.streamProvider().readBytes()
                    println("received fileBytes: ${fileBytes.size}")
                }

                else -> {}
            }
            part.dispose()
        }

        call.respond(HttpStatusCode.OK, mapOf("message" to "File uploaded successfully"))
    }
}