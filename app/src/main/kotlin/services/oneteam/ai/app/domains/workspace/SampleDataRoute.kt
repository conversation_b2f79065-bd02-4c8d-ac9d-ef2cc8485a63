package services.oneteam.ai.app.domains.workspace

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import services.oneteam.ai.shared.data.SampleDataLoader
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceService
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService

@Suppress("unused")
@Resource("/sample-data")
private class SampleDataRoute() {
    @Resource("{key}")
    class Key(val parent: SampleDataRoute = SampleDataRoute(), val key: String)

}

fun Route.sampleDataEndpoints(
    workspaceService: WorkspaceService,
    workspaceVersionService: WorkspaceVersionService,
    foundationRepository: FoundationRepository,
    foundationService: FoundationService,
    formService: FormService,
    requestTimeoutMs: Long
) {


    post<SampleDataRoute.Key> { key ->
        val workspaceForCreate = call.receive<Workspace.ForCreate>()

        val lowerCaseKey = key.key.lowercase()
        val workspace: Workspace.ForApi? = SampleDataLoader(
            workspaceService,
            workspaceVersionService,
            foundationRepository,
            foundationService,
            formService,
            call.request.headers["Cookie"]!!,
            workspaceForCreate,
            "/sample-data/$lowerCaseKey/$lowerCaseKey",
            key.key,
            requestTimeoutMs = requestTimeoutMs
        ).load()

        if (workspace != null) {
            call.respond(workspace)
        } else {
            call.respond(HttpStatusCode.NoContent)
        }
    }


}