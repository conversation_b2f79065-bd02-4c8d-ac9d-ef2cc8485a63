package services.oneteam.ai.app.domains.auth

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.auth.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import io.ktor.server.routing.application
import io.ktor.server.sessions.*
import kotlinx.datetime.Clock
import org.koin.ktor.ext.inject
import services.oneteam.ai.app.AppConfig
import services.oneteam.ai.shared.UserPrincipal
import services.oneteam.ai.shared.UserSession

@Suppress("unused")
@Resource("/auth")
class AuthEndpoints() {
    @Resource("/login")
    class Login(val parent: AuthEndpoints = AuthEndpoints())

    @Resource("/logout")
    class Logout(val parent: AuthEndpoints = AuthEndpoints())

    @Resource("/me")
    class Myself(val parent: AuthEndpoints = AuthEndpoints())
}

fun Route.unauthenticatedEndpoints() {
    val appConfig by application.inject<AppConfig>()
    post<AuthEndpoints.Login> {
        val user = call.principal<UserPrincipal>()!!.user
        //set the session
        call.sessions.set(
            UserSession(
                user,
                (Clock.System.now() + appConfig.cookie.timeoutDuration).toEpochMilliseconds()
            )
        )
        //return the user
        call.respond(user)
    }
}

fun Route.authenticatedEndpoints() {
    val appConfig by application.inject<AppConfig>()
    get<AuthEndpoints.Myself> {
        val principal = call.sessions.get<UserSession>()
        call.respond(principal!!.user)
    }

    post<AuthEndpoints.Logout> {
        call.sessions.clear(appConfig.cookie.name)
        call.respondText(text = "{\"message\": \"Logged out\"}", contentType = ContentType.Application.Json)
    }
}

