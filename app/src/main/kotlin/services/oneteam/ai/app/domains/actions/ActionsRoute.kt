package services.oneteam.ai.app.domains.actions

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement
import services.oneteam.ai.shared.domains.actions.DownloadUrlSource
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.actions.OutputFileFormat
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

@Suppress("unused")
@Resource("/internal/workspaces")
private class WorkspaceActionsRoute() {
    @Resource("{workspaceId}/actions")
    class Id(val parent: WorkspaceActionsRoute, val workspaceId: Workspace.Id) {
        @Resource("/generateFromTemplate")
        class GenerateFromTemplate(val parent: Id)

        @Resource("/downloadFile")
        class DownloadFile(val parent: Id)

        @Resource("/convertFile")
        class ConvertFile(val parent: Id)
    }
}

fun Route.internalActionEndpoints(
    filePressService: FilePressService
) {
    post<WorkspaceActionsRoute.Id.GenerateFromTemplate> { routeParams ->
        val requestBody = call.receive<GenerateFromTemplateBody>()

        if (requestBody.templateUrl.isBlank() == true) {
            call.respond(HttpStatusCode.BadRequest, "template url is required")
            return@post
        }

        val templateUrl = requestBody.templateUrl

        val tenantId = coroutineContext[RequestContext]!!.tenant.id
        val generatedFile = filePressService.generateDocxFromRemoteTemplate(
            tenantId = tenantId,
            workspaceId = routeParams.parent.workspaceId,
            templateUrl = templateUrl,
            replacements = requestBody.replacements,
            filename = requestBody.outputFilename
        )

        call.respond(
            HttpStatusCode.OK,
            generatedFile
        )
    }

    post<WorkspaceActionsRoute.Id.DownloadFile> { routeParams ->
        val requestBody = call.receive<DownloadFileBody>()

        if (requestBody.fileUrl.isBlank() == true) {
            call.respond(HttpStatusCode.BadRequest, "file url is required")
            return@post
        }

        val fileUrl = requestBody.fileUrl

        val tenantId = coroutineContext[RequestContext]!!.tenant.id
        val file = filePressService.downloadFile(
            tenantId = tenantId,
            workspaceId = routeParams.parent.workspaceId,
            fileUrl = fileUrl,
            filename = requestBody.outputFilename,
            urlSource = requestBody.urlSource
        )

        call.respond(
            HttpStatusCode.OK,
            file
        )
    }

    post<WorkspaceActionsRoute.Id.ConvertFile> { routeParams ->
        val requestBody = call.receive<ConvertFileBody>()

        if (requestBody.fileUrl.isEmpty()) {
            call.respond(HttpStatusCode.BadRequest, "file url is required")
            return@post
        }

        val fileUrl = requestBody.fileUrl

        val tenantId = coroutineContext[RequestContext]!!.tenant.id
        val convertedFile = filePressService.convertFile(
            tenantId = tenantId,
            workspaceId = routeParams.parent.workspaceId,
            fileUrl = fileUrl,
            outputFilename = requestBody.outputFilename ?: requestBody.fileName,
            outputFileFormat = requestBody.outputFormat,
        )

        call.respond(
            HttpStatusCode.OK,
            convertedFile
        )
    }
}

@Serializable
data class GenerateFromTemplateBody(
    val templateUrl: String,
    val replacements: JsonElement,
    val outputFilename: String? = null,
)

@Serializable
data class DownloadFileBody(
    val fileUrl: String,
    val outputFilename: String? = null,
    val urlSource: DownloadUrlSource? = DownloadUrlSource.INTERNET
)

@Serializable
data class ConvertFileBody(
    val fileUrl: String,
    val fileName: String,
    val outputFilename: String? = null,
    val outputFormat: OutputFileFormat,
)
