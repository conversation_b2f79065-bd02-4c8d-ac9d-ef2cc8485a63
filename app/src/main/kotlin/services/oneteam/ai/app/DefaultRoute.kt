package services.oneteam.ai.app

import io.ktor.http.ContentType
import io.ktor.server.response.*
import io.ktor.server.routing.*

fun Route.defaultEndpoints() {

    get("/") {
        call.respondText("OK")
    }
    get("/build") {
        val jsonString =
            this::class.java.classLoader.getResource("build.json")?.readText() ?: """{"version": "local"}"""
        call.respondText(text = jsonString, contentType = ContentType.Text.JavaScript)
    }
}
