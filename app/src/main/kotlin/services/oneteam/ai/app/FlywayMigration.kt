package services.oneteam.ai.app

import org.flywaydb.core.Flyway
import services.oneteam.ai.shared.FlywayConfig
import javax.sql.DataSource

class FlywayMigration {
    fun migrate(config: FlywayConfig, dataSource: DataSource) {

        val flyway = Flyway.configure()
            .defaultSchema(config.schema)
            .baselineOnMigrate(true) // create the flyway_schema_history table if it doesn't exist
            .baselineVersion("0") // so our migrations start at version 1
            .placeholders(config.properties) // to handle environmental variables in migrations - use ${key} in migrations as placeholder
            .outOfOrder(true)
            .locations(*config.locations.toTypedArray())
            .dataSource(dataSource).load()

        flyway.migrate()

    }
}