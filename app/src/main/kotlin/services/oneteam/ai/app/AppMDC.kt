package services.oneteam.ai.app

import org.slf4j.MDC
import services.oneteam.ai.shared.domains.tenant.Tenant

/**
 * AppMDC is a utility object for managing the MDC (Mapped Diagnostic Context) in the application.
 * It is a centralized place to manage context-specific data that can be used for logging purposes.
 */
object AppMDC {
    fun withTenant(tenant: Tenant) {
        MDC.put("tenant", tenant.name)
    }

    fun withRequestId(requestId: String) {
        MDC.put("requestId", requestId)
    }
}