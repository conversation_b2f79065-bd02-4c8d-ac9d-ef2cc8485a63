package services.oneteam.ai.app

import io.ktor.server.config.*
import services.oneteam.ai.shared.FlywayConfig

class FlywayConfigBuilder {
    companion object {
        fun fromConfig(
            config: ApplicationConfig,
            websiteName: String,
            dbUser: String,
            dbSuperUser: String
        ): FlywayConfig {
            if (websiteName.isBlank()) throw ApplicationConfigurationException("Application must be run with a WEBSITE_SITE_NAME environment variable so that it knows which set of flyway properties to use from application.yaml")

            val enabled = config.propertyOrNull("enabled")?.getString()?.toBoolean() == true
            val schema = config.propertyOrNull("schema")?.getString()

            // load locations for website
            val locations =
                config.propertyOrNull("locations")?.getList()
                    ?.map { replaceVariables(it, mapOf<String, String>("websiteName" to websiteName)) }
                    ?: listOf("classpath:db/migration")

            // environment specific properties, which can be used in migrations as placeholders using ${key}
            val properties = if (config.config("properties").toMap().containsKey(websiteName)) {
                config.config("properties").config(websiteName).toMap().mapValues { it.value.toString() }
            } else {
                emptyMap()
            }
            val fullProperties = properties + mapOf(
                "DB_USER" to dbUser,
                "DB_SUPER_USER" to dbSuperUser,
            )
            return FlywayConfig(enabled, locations, fullProperties, schema)
        }

        private fun replaceVariables(string: String, variables: Map<String, String>): String =
            variables.entries.fold(string) { acc, (key, value) -> acc.replace("\${$key}", value, false) }
    }
}
