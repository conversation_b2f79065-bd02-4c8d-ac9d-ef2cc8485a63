package services.oneteam.ai.app.domains.flow

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import services.oneteam.ai.app.middlewares.addEventToCallAttributes
import services.oneteam.ai.flow.execution.FlowExecutionService
import services.oneteam.ai.flow.expression.functions.ComputeFunction
import services.oneteam.ai.flow.expression.functions.ComputeFunctions
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.form.constructFormMinimalFromForm
import services.oneteam.ai.shared.domains.collection.form.getFormConfiguration
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.collection.foundation.constructFoundationMinimalFromFoundation
import services.oneteam.ai.shared.domains.collection.foundation.getFoundationConfiguration
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.EventKey
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

@Suppress("unused")
@Resource("/flows")
private class FlowRoute() {

    @Resource("/manual")
    class Manual(val parent: FlowRoute = FlowRoute())

    @Resource("/buttons")
    class Buttons(val parent: FlowRoute = FlowRoute())

    @Resource("/definitions/functions")
    class FunctionDefinitions(val parent: FlowRoute = FlowRoute())
}

fun Route.flowEndpoints(
    formService: FormService,
    flowExecutionService: FlowExecutionService,
    foundationService: FoundationService,
    workspaceVersionService: WorkspaceVersionService
) {
    get<FlowRoute.FunctionDefinitions> {
        val definitions = ComputeFunctions.definitions.map { ComputeFunction.convertForJson(it.value) };
        call.respond(HttpStatusCode.Created, definitions)
    }

    post<FlowRoute.Manual> { _ ->
        val manualTriggerOperation = call.receive<ManualTriggerOperation>()
        val eventPayload: Event.ForCreate

        when(manualTriggerOperation.location) {
            ManualTriggerButtonLocation.FORM -> {
                val formEntity = formService.get(manualTriggerOperation.properties.formId!!)
                val formConfiguration = getFormConfiguration(workspaceVersionService, formEntity)

                // verify form is in workspace

                eventPayload =  Event.ForCreate(
                    workspaceId = formEntity.workspaceId,
                    eventProperties = Event.EventProperties.StartFlowManuallyFromFormProperties(
                        buttonLabel = manualTriggerOperation.properties.buttonLabel,
                        form = constructFormMinimalFromForm(formEntity, formConfiguration!!, getFoundationConfiguration(workspaceVersionService, formEntity.foundation)!!),
                        userId = coroutineContext[RequestContext]!!.principalId()!!
                    )
                )
            }
            ManualTriggerButtonLocation.FOUNDATION -> {
                val foundationEntity = foundationService.get(manualTriggerOperation.properties.foundationId!!)
                val foundationConfiguration = getFoundationConfiguration(workspaceVersionService, foundationEntity)

                eventPayload = Event.ForCreate(
                    workspaceId = foundationEntity.workspaceId,
                    eventProperties = Event.EventProperties.StartFlowManuallyFromFoundationProperties(
                        buttonLabel = manualTriggerOperation.properties.buttonLabel,
                        foundation = constructFoundationMinimalFromFoundation(foundationEntity, foundationConfiguration!!),
                        userId = coroutineContext[RequestContext]!!.principalId()!!
                    )
                )

            }
        }

        addEventToCallAttributes(call, eventPayload)

        call.respond(HttpStatusCode.Created, eventPayload)
    }

    get<FlowRoute.Buttons> {
        try {
            val location = call.request.queryParameters["location"]
                ?.let { ManualTriggerButtonLocation.valueOf(it.uppercase()) }
                ?: throw BadRequestException("Missing or invalid 'location' parameter")

            when (location) {
                ManualTriggerButtonLocation.FORM -> {
                    val formId = call.request.queryParameters["formId"]?.toLongOrNull()
                        ?: throw BadRequestException("Missing or invalid 'formId' parameter")
                    val form = formService.get(Form.Id(formId))
                    call.respond(flowExecutionService.getFlowButtons(form.formConfigurationId.value,"formConfigurationId", form.workspaceId,
                        EventKey.START_FLOW_MANUALLY_FROM_FORM))
                }
                ManualTriggerButtonLocation.FOUNDATION -> {
                    val foundationId = call.request.queryParameters["foundationId"]?.toLongOrNull()
                        ?: throw BadRequestException("Missing or invalid 'foundationId' parameter")
                    val foundation = foundationService.get(Foundation.Id(foundationId))
                    call.respond(flowExecutionService.getFlowButtons(foundation.foundationConfigurationId.value,"foundationConfigurationId", foundation.workspaceId, EventKey.START_FLOW_MANUALLY_FROM_FOUNDATION))
                }
            }
        } catch (e: Exception) {
            call.respond(HttpStatusCode.BadRequest, e.localizedMessage)
        }
    }
}

@Serializable
data class ManualTriggerOperation(
    val location: ManualTriggerButtonLocation,
    val properties: ManualTriggerOperationProperties
)

@Serializable
data class ManualTriggerOperationProperties(
    val buttonLabel: String,
    val formId: Form.Id? = null,
    val foundationId: Foundation.Id? = null
)

@Serializable
enum class ManualTriggerButtonLocation {
    @SerialName("form")
    FORM,

    @SerialName("foundation")
    FOUNDATION,
}
