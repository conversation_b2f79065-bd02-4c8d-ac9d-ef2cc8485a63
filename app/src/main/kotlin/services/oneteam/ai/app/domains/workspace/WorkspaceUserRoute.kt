package services.oneteam.ai.app.domains.workspace

import io.ktor.http.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.flow.pubsub.*
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceUserRepository
import services.oneteam.ai.shared.domains.workspace.WorkspaceUserSearchCriteria
import services.oneteam.ai.shared.domains.workspace.WorkspaceUserService

fun Route.workspaceUserEndpoints(
    workspaceUserService: WorkspaceUserService
) {

    get<Workspaces.Id.Users> { params ->
        val pageRequest = call.request.buildPageRequest(WorkspaceUserRepository.SORTABLE_FIELDS)
        val searchCriteria = WorkspaceUserSearchCriteria.fromRequest(workspaceId = params.parent.id, parameters = call.request.queryParameters)
        val pageResult = workspaceUserService.search(
            pageRequest, searchCriteria
        )
        call.respond(pageResult)

    }
}

fun WorkspaceUserSearchCriteria.Companion.fromRequest(
    workspaceId: Workspace.Id,
    parameters: Parameters
): WorkspaceUserSearchCriteria {
    val searchTerm = parameters["search"] ?: ""

    return WorkspaceUserSearchCriteria(
        workspaceId = workspaceId,
        searchTerm = searchTerm,
    )

}