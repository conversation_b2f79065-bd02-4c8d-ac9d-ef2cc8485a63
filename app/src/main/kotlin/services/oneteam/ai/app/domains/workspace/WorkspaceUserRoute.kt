package services.oneteam.ai.app.domains.workspace

import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.shared.domains.workspace.*

fun Route.workspaceUserEndpoints(
    workspaceUserService: WorkspaceUserService
) {

    get<Workspaces.Id.Users.UserId> { params ->
        val workspaceUser = workspaceUserService.findByWorkspaceAndUserId(params.parent.parent.id, params.userId)
        call.respond(workspaceUser)
    }

    get<Workspaces.Id.Users> { params ->
        val pageRequest = call.request.buildPageRequest(WorkspaceUserRepository.SORTABLE_FIELDS)
        val searchCriteria = WorkspaceUserSearchCriteria.fromRequest(workspaceId = params.parent.id, parameters = call.request.queryParameters)
        val pageResult = workspaceUserService.search(
            pageRequest, searchCriteria
        )
        call.respond(pageResult)

    }

    post<Workspaces.Id.Users> {
        val workspaceUsers = call.receive<List<WorkspaceUser.ForCreate>>()
        val createdWorkspaceUsers = workspaceUsers.map { workspaceUserService.create(it) }
        call.respond(createdWorkspaceUsers)
    }

    put<Workspaces.Id.Users> {
        val workspace = call.receive<WorkspaceUser.ForUpdate>()
        val w = workspaceUserService.update(workspace)
        call.respond(w)
    }
}

fun WorkspaceUserSearchCriteria.Companion.fromRequest(
    workspaceId: Workspace.Id,
    parameters: Parameters
): WorkspaceUserSearchCriteria {
    val searchTerm = parameters["search"] ?: ""

    return WorkspaceUserSearchCriteria(
        workspaceId = workspaceId,
        searchTerm = searchTerm,
    )

}