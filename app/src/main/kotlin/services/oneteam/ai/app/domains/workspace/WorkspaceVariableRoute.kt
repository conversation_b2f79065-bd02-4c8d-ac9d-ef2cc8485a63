package services.oneteam.ai.app.domains.workspace

import io.ktor.http.HttpStatusCode
import io.ktor.server.request.receive
import io.ktor.server.resources.get
import io.ktor.server.resources.delete
import io.ktor.server.resources.post
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariableService
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariable

fun Route.sessionAuthWorkspaceVariablesEndpoints(
    workspaceVariableService: WorkspaceVariableService,
) {
    post<Workspaces.Id.Variables> { routingParams ->
        val workspaceId = routingParams.workspace.id
        val createParams = call.receive<WorkspaceVariable.ForCreate>()
        val result = workspaceVariableService.create(workspaceId, createParams)
        call.respond(HttpStatusCode.OK, result)
    }

    delete<Workspaces.Id.Variables.VariableId> { routingParams ->
        val workspaceId = routingParams.variables.workspace.id
        val variableId = WorkspaceVariable.Id(routingParams.variableId)
        workspaceVariableService.delete(
            workspaceId, variableId
        )
        call.respond(HttpStatusCode.OK, mapOf<String, String>())
    }
}

fun Route.workspaceVariablesEndpoints(
    workspaceVariableService: WorkspaceVariableService,
) {
    get<Workspaces.Id.Variables> { routingParams ->
        val workspaceId = routingParams.workspace.id
        val maybeWorkspaceVersionId = call.request.queryParameters["workspaceVersionId"]?.let { WorkspaceVersion.Id(it.toLong()) }
        val result = if (maybeWorkspaceVersionId == null) {
            workspaceVariableService.getAllWithoutVersion(workspaceId)
        } else {
            workspaceVariableService.getAllByVersion(workspaceId, maybeWorkspaceVersionId)
        }
        call.respond(HttpStatusCode.OK, result)
    }
}

