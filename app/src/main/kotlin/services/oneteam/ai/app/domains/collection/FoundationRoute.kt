package services.oneteam.ai.app.domains.collection

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.resources.put
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.ExperimentalSerializationApi
import services.oneteam.ai.app.domains.workspace.Workspaces
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.app.middlewares.addEventToCallAttributes
import services.oneteam.ai.shared.domains.collection.foundation.*
import services.oneteam.ai.shared.domains.workspace.FoundationConfigurationService
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService

@Suppress("unused")
@Resource("/foundations")
private class FoundationsRoute() {
    @Resource("{id}")
    class Id(val parent: FoundationsRoute = FoundationsRoute(), val id: Foundation.Id){
        @Resource("/hierarchy")
        class Hierarchy( val foundation:Id)

        @Resource("/properties")
        class FoundationProperties(val foundationId: Id)
    }

    @Resource("/select-many")
    class SelectMany(val foundations: FoundationsRoute)

    @Resource("/select-or-create")
    class SelectOrCreate(val foundations: FoundationsRoute)
}

@OptIn(ExperimentalSerializationApi::class)
fun Route.foundationEndpoints(foundationService: FoundationService, workspaceVersionService: WorkspaceVersionService, foundationConfigurationService: FoundationConfigurationService
) {


    get<Workspaces.Id.Foundations.Key.Config> { foundationParams ->
        val foundation = foundationService.get(
            foundationParams.parent.parent.parent.id,
            foundationParams.parent.key,
            foundationParams.configurationId
        )
        call.respond<Foundation>(foundation)
    }


    get<Workspaces.Id.Foundations.ConfigurationId.CanDelete> { foundationParams ->
        val workspaceId = foundationParams.parent.parent.parent.id
        val foundationConfigurationId = foundationParams.parent.configurationId
        val foundationPageRequest = call.request.buildPageRequest(FoundationRepository.SORTABLE_FIELDS)
        val canDelete = foundationConfigurationService.canDeleteFoundationLevel(workspaceId, foundationConfigurationId, foundationPageRequest)
        call.respond<Boolean>(canDelete)
    }

    get<Workspaces.Id.FoundationsRoot> { workspaceParams ->
        val foundation = foundationService.root(workspaceParams.parent.id)
        call.respond<Foundation>(foundation)
    }

    get<FoundationsRoute.Id> { foundationParams ->
        val foundation = foundationService.get(foundationParams.id)
        call.respond<Foundation>(foundation)
    }

    get<FoundationsRoute.Id.Hierarchy> { foundationParams ->
        val foundationId = foundationParams.foundation.id
        val hierarchy = foundationService.getFoundationHierarchy(foundationId)
        call.respond(mapOf("hierarchy" to hierarchy))
    }

    get<Workspaces.Id.Foundations> { workspaceParams ->
        val pageRequest = call.request.buildPageRequest(FoundationRepository.SORTABLE_FIELDS)
        val searchTerm = call.request.queryParameters["search"] ?: ""
        val parentKey = call.request.queryParameters["parentKey"] ?: ""
        val parentConfigurationId = call.request.queryParameters["parentConfigurationId"] ?: ""

        val pageResult = foundationService.search(
            workspaceParams.parent.id,
            searchTerm,
            parentKey,
            parentConfigurationId,
            pageRequest
        )
        call.respond(pageResult)
    }

    put<FoundationsRoute.Id> { foundationParams ->
        val foundationForUpdate = call.receive<Foundation.ForUpdate>()

        val foundationConfiguration = getFoundationConfiguration(workspaceVersionService, foundationForUpdate)
        if (foundationConfiguration == null) {
            call.respond(HttpStatusCode.BadRequest, "Foundation configuration not found")
            return@put
        }

        assert(foundationForUpdate.id == foundationParams.id)
        val updatedFoundation = foundationService.update(foundationForUpdate)

        val eventPayload = Event.ForCreate(
            workspaceId = updatedFoundation.workspaceId,
            eventProperties = Event.EventProperties.UpdateCollectionFoundationProperties(
                foundation = constructFoundationMinimalFromFoundation(updatedFoundation, foundationConfiguration)
            ),
        )

        addEventToCallAttributes(call, eventPayload)

        call.respond(updatedFoundation)
    }

    delete<FoundationsRoute.Id> { foundationParams ->
        val targetFoundation = foundationService.get(foundationParams.id)

        val foundationConfiguration = getFoundationConfiguration(workspaceVersionService, targetFoundation)
        if (foundationConfiguration == null) {
            call.respond(HttpStatusCode.BadRequest, "Foundation configuration not found")
            return@delete
        }

        foundationService.delete(foundationParams.id)

        val eventPayload = Event.ForCreate(
            workspaceId = targetFoundation.workspaceId,
            eventProperties = Event.EventProperties.DeleteCollectionFoundationProperties(
                foundation = constructFoundationMinimalFromFoundation(targetFoundation, foundationConfiguration)
            ),
        )

        addEventToCallAttributes(call, eventPayload)

        call.respond(HttpStatusCode.NoContent)
    }
}

fun Route.twoTypeAuthFoundationEndpoints(
    foundationService: FoundationService,
    workspaceVersionService: WorkspaceVersionService
) {
    /**
     * Create a new foundation for a workspace
     */
    post<Workspaces.Id.Foundations> { workspaceParams ->
        val foundationForCreate = call.receive<Foundation.ForCreate>()

        val foundationConfiguration = getFoundationConfiguration(workspaceVersionService, foundationForCreate)
        if (foundationConfiguration == null) {
            call.respond(HttpStatusCode.BadRequest, "Foundation configuration not found")
            return@post
        }
        assert(workspaceParams.parent.id == foundationForCreate.workspaceId)
        assert(foundationForCreate.parentId != null)
        val newFoundation = foundationService.create(foundationForCreate)

        val eventPayload = Event.ForCreate(
            workspaceId = newFoundation.workspaceId,
            eventProperties = Event.EventProperties.CreateCollectionFoundationProperties(
                foundation = constructFoundationMinimalFromFoundation(newFoundation, foundationConfiguration)
            ),
        )

        addEventToCallAttributes(call, eventPayload)

        call.respond(mapOf("foundation" to newFoundation))
    }

    put<FoundationsRoute.Id.FoundationProperties> { foundationParams ->
        val requestBody = call.receive<FoundationPropertiesRequestBody>()
        require(requestBody.properties.isNotEmpty()) { "Foundation properties cannot be empty" }

        val updatedFoundation = foundationService.updateProperties(
            foundationParams.foundationId.id,
            requestBody.properties,
            requestBody.workspaceId
        )

        val foundationConfiguration = getFoundationConfiguration(workspaceVersionService, updatedFoundation)
        if (foundationConfiguration == null) {
            call.respond(HttpStatusCode.BadRequest, "Foundation configuration not found")
            return@put
        }

        val eventPayload = Event.ForCreate(
            workspaceId = updatedFoundation.workspaceId,
            eventProperties = Event.EventProperties.UpdateCollectionFoundationProperties(
                foundation = constructFoundationMinimalFromFoundation(updatedFoundation, foundationConfiguration)
            ),
        )

        addEventToCallAttributes(call, eventPayload)

        call.respond(updatedFoundation)
    }

}

fun Route.internalFoundationEndpoints(
    foundationService: FoundationService,
    workspaceVersionService: WorkspaceVersionService
) {
    get<FoundationsRoute.SelectMany> {
        val queryBody = call.receive<SelectManyFoundationsParams>()
        val result = foundationService.selectManyFoundations(queryBody)
        call.respond(mapOf("foundations" to result.foundations))
    }

    post<FoundationsRoute.SelectOrCreate> {
        val queryBody = call.receive<SelectOrCreateFoundationParams>()
        val result = foundationService.selectOrCreateFoundation(queryBody)
        if(!result.alreadyExists && result.foundation != null) {
            val foundationConfiguration = getFoundationConfiguration(workspaceVersionService, result.foundation!!)
            if (foundationConfiguration == null) {
                call.respond(HttpStatusCode.BadRequest, "Foundation configuration not found")
                return@post
            }
            val eventPayload = Event.ForCreate(
                workspaceId = result.foundation!!.workspaceId,
                eventProperties = Event.EventProperties.CreateCollectionFoundationProperties(
                    foundation = constructFoundationMinimalFromFoundation(result.foundation!!, foundationConfiguration)
                ),
            )

            addEventToCallAttributes(call, eventPayload)
        }
        call.respond(mapOf("foundation" to result.foundation))
    }
}
