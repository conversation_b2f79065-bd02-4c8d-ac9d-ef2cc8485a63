package services.oneteam.ai.app

import io.ktor.server.application.*
import io.ktor.server.config.*
import org.koin.ktor.ext.inject
import services.oneteam.ai.flow.execution.FEDRepositoryType
import services.oneteam.ai.shared.DBConnectionConfig
import services.oneteam.ai.shared.DatabaseConfig
import services.oneteam.ai.shared.FlywayConfig
import java.util.*
import kotlin.properties.Delegates
import kotlin.time.Duration

class DatabaseConfigBuilder {

    companion object {

        fun fromConfig(config: ApplicationConfig): DatabaseConfig {
            val application = parseDbConfig(config.config("application"))
            val privileged = parseDbConfig(config.config("privileged"))
            val audit = config.property("audit").getString().toBoolean()

            return DatabaseConfig(audit, application, privileged)
        }

        private fun parseDbConfig(config: ApplicationConfig): DBConnectionConfig {
            val maximumPoolSize = config.property("maximumPoolSize").getString().toInt()
            val jdbcUrl = config.property("jdbcUrl").getString()
            val driverClassName = config.property("driverClassName").getString()
            val username = config.property("username").getString()
            val password = config.property("password").getString()
            val poolName = config.propertyOrNull("poolName")?.getString()
            val rls = config.propertyOrNull("rls")?.getString()?.toBoolean() != false
            return DBConnectionConfig(
                maximumPoolSize, jdbcUrl, driverClassName, username, password, poolName, rls
            )
        }
    }
}

class StorageConfig(
    config: ApplicationConfig,
    val accountName: String = config.property("accountName").getString(),
    val uploadContainerName: String = config.property("uploadContainerName").getString(),
    val accessKey: String = config.property("accessKey").getString()
)


class Cookie(
    config: ApplicationConfig,
    val name: String = config.property("name").getString(),
    val secretEncryptKey: String = config.property("secretEncryptKey").getString(),
    val secretSignKey: String = config.property("secretSignKey").getString(),
    val timeoutDuration: Duration = Duration.parse(config.property("timeout").getString()),
)

class Jwt(
    config: ApplicationConfig,
    val publicKey: String = config.property("publicKey").getString()
)

class OtaiServiceAccount(
    config: ApplicationConfig,
    val token: String = config.property("token").getString()
)

class TogglesConfig(
    config: ApplicationConfig,
    /**
     * A temporary toggle to use the old execution step factory, which
     */
    val useExecutionStepFactoryV1: Boolean = config.property("useExecutionStepFactoryV1").getString()
        .toBoolean(),
)

class FlowsConfig(
    config: ApplicationConfig,
    val runExecutionImmediately: Boolean = config.property("runExecutionImmediately").getString().toBoolean(),
    val timeoutMins: Long = config.property("flowExecutionMaxDurationMins").getString().toLong(),
    val numberOfThreads: Int = config.property("numberOfThreads").getString().toInt(),
    val maxConcurrentCoroutines: Int = config.property("maxConcurrentCoroutines").getString().toInt(),
    val includeLogging: Boolean = config.property("includeLogging").getString().toBoolean(),
    val skipStepUpdates: Boolean = config.property("skipStepUpdates").getString().toBoolean(),
    val skipVariableUpdates: Boolean = config.property("skipVariableUpdates").getString().toBoolean(),
    val skipSubFlowFlowUpdates: Boolean = config.property("skipSubFlowFlowUpdates").getString().toBoolean(),
    var fedStorageType: FEDRepositoryType = FEDRepositoryType.valueOf(config.property("fedStorageType").getString()),
    val jsonataTimeoutMs: Long = config.property("jsonataTimeoutMs").getString().toLong(),
    val jsonataMaxRecursionDepth: Int = config.property("jsonataMaxRecursionDepth").getString().toInt(),
)

class TelemetryConfig(
    config: ApplicationConfig,
    val enabled: Boolean = config.property("enabled").getString().toBoolean(),
)

class AzureWebPubSubConfig(
    config: ApplicationConfig,
    val connectionString: String = config.property("connectionString").getString(),
    val hubName: String = config.property("hubName").getString(),
    val tokenExpirySeconds: Long = config.property("tokenExpirySeconds").getString().toLong(),
)

class OneTeamStorageConfig(
    config: ApplicationConfig,
    val storageName: String = config.property("storageName").getString(),
    val containerName: String = config.property("containerName").getString(),
    val sasToken: String = config.property("sasToken").getString(),
)

class FilePressConfig(
    config: ApplicationConfig,
    val url: String = config.property("url").getString(),
)

class AzureKeyVaultConfig(
    config: ApplicationConfig,
    val keyVaultName: String = config.property("name").getString(),
    val secretNamePrefix: String? = config.propertyOrNull("secretNamePrefix")?.getString(),
    val credentialStrategy: String? = config.propertyOrNull("credentialStrategy")?.getString()
)

class InMemoryKeyVaultConfig(
    config: ApplicationConfig,
    val keyVaultName: String = config.property("name").getString(),
    val secretNamePrefix: String? = config.propertyOrNull("secretNamePrefix")?.getString(),
    val loadSeedData: Boolean = config.propertyOrNull("loadSeedData")?.getString()?.toBoolean() == true
)

class KeyVaultConfig(
    config: ApplicationConfig,
    val keyVaultServiceProvider: String = config.property("serviceProvider").getString(),
    val azure: AzureKeyVaultConfig = AzureKeyVaultConfig(config.config("azure")),
    val inMemory: InMemoryKeyVaultConfig = InMemoryKeyVaultConfig(config.config("inMemory")),
)

class PassVaultConfig(
    config: ApplicationConfig,
    val keyVaultConfig: KeyVaultConfig = KeyVaultConfig(config.config("keyVault"))
)

class AppConfig {
    lateinit var databaseConfig: DatabaseConfig
    lateinit var cookie: Cookie
    lateinit var jwt: Jwt
    lateinit var otaiServiceAccount: OtaiServiceAccount
    lateinit var storage: StorageConfig
    lateinit var frontendUrl: String
    lateinit var websiteName: String
    lateinit var flyway: FlywayConfig
    lateinit var flows: FlowsConfig
    lateinit var telemetry: TelemetryConfig
    var loadSampleData: Boolean = false
    var seedData: Boolean = true
    var artificialDelayMs: Long? = null
    var syncServerUrl: String = "ws://localhost:3030"
    lateinit var azureWebPubSub: AzureWebPubSubConfig
    lateinit var filePress: FilePressConfig
    lateinit var passVault: PassVaultConfig
    lateinit var oneTeamStorage: OneTeamStorageConfig
    var sampleDataRequestTimeoutMs: Long = 60000L
    lateinit var toggles: TogglesConfig

    var development by Delegates.notNull<Boolean>()

    fun fromConfig(config: ApplicationConfig) {
        databaseConfig = DatabaseConfigBuilder.fromConfig(config.config("ktor.database"))
        storage = StorageConfig(config.config("ktor.storage"))
        cookie = Cookie(config.config("ktor.cookie"))
        jwt = Jwt(config.config("ktor.jwt"))
        otaiServiceAccount = OtaiServiceAccount(config.config("ktor.otaiServiceAccount"))
        flows = FlowsConfig(config.config("flows"))
        telemetry = TelemetryConfig(config.config("telemetry"))
        toggles = TogglesConfig(config.config("toggles"))

        frontendUrl = config.propertyOrNull("ktor.frontendUrl")?.getString() ?: ""
        development = config.propertyOrNull("ktor.development")?.getString().toBoolean()
        websiteName = config.propertyOrNull("ktor.websiteName")?.getString() ?: ""

        loadSampleData = config.propertyOrNull("loadSampleData")?.getString()?.toBoolean() == true
        seedData = config.propertyOrNull("seedData")?.getString()?.toBoolean() == true
        syncServerUrl = config.propertyOrNull("syncServerUrl")?.getString() ?: "ws://localhost:3030"
        artificialDelayMs = config.propertyOrNull("artificialDelayMs")?.getString()?.toLong()
        azureWebPubSub = AzureWebPubSubConfig(config.config("azureWebPubSub"))
        filePress = FilePressConfig(config.config("filePress"))
        passVault = PassVaultConfig(config.config("passVault"))
        oneTeamStorage = OneTeamStorageConfig(config.config("oneTeamStorage"))
        sampleDataRequestTimeoutMs =
            config.propertyOrNull("sampleDataRequestTimeoutMs")?.getString()?.toLong() ?: 60000L

        flyway = FlywayConfigBuilder.fromConfig(
            config.config("flyway"),
            websiteName,
            databaseConfig.application.username,
            databaseConfig.privileged.username
        )
        setDefaultLocale(config)
    }

    private fun setDefaultLocale(config: ApplicationConfig) {
        var localeString = config.propertyOrNull("ktor.locale")?.getString()
        var locale = Locale.forLanguageTag(if (localeString.isNullOrEmpty()) "en-AU" else localeString)
        Locale.setDefault(locale)
    }
}

fun Application.setupConfig() {
    val appConfig by inject<AppConfig>()
    appConfig.fromConfig(environment.config)
}
