package services.oneteam.ai.app

import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService

fun Route.systemEndpoints(documentService: IDocumentService) {
    post("/validate") {
        val document = call.receive<String>()
        val d = documentService.validate(document)
        call.respond(d)
    }
}
