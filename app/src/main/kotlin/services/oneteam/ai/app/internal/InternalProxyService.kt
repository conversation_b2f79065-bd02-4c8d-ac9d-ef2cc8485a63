package services.oneteam.ai.app.internal

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.utils.io.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import runInternalRequest
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

class InternalProxyService(
    val application: Application,
    private val serviceAccountToken: String
) : ProxyService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun call(
        body: ProxyService.ProxyEndpointBody,
        isExternalResponse: Boolean?,
        timeoutMillis: Long? // only used in ExternalProxyService for now
    ): ProxyService.ProxyEndpointResponse {
        try {
            val tenant = coroutineContext[RequestContext]!!.tenant
            val url = Url(body.url)
            val call = application.runInternalRequest {
                method = HttpMethod.parse(body.method)
                uri = url.fullPath
                scheme = url.protocol.name
                version = "HTTP/1.1"
                port = url.port
                host = url.host
                <EMAIL> = ByteReadChannel(body.body.toString().toByteArray())

                requestHeaders = mutableMapOf<String, String>().apply {
                    putAll(body.headers ?: emptyMap())
                    putAll(body.authentication ?: emptyMap())
                    putAll(body.options ?: emptyMap())
                    put(HttpHeaders.ContentType, body.contentType.toString())
                    put(HttpHeaders.Referrer, tenant.originUrl)

                    if (body.authentication?.get("useOtaiServiceAccount") == "true") {
                        put(HttpHeaders.Authorization, "Bearer $serviceAccountToken")
                    }
                }
            }
            val response = call.response

            val isSuccess = response.status()?.value.toString().substring(0, 1) == "2"
            if (isSuccess) {
                logger.info("Internal call to ${body.url} was successful")
            } else {
                logger.error("Internal call to ${body.url} failed with status ${response.status()?.value}")
            }

            val responseContent = call.response.byteContent?.decodeToString()
            return ProxyService.ProxyEndpointResponse(
                response = responseContent,
                status = if (isSuccess) ProxyService.ProxyEndpointResponseStatus.SUCCESS else ProxyService.ProxyEndpointResponseStatus.FAIL,
                error = if (!isSuccess) responseContent else null
            )
        } catch (e: Exception) {
            logger.error("Internal call to ${body.url} failed with error: ${e.message}", e)
            return ProxyService.ProxyEndpointResponse(
                response = null,
                status = ProxyService.ProxyEndpointResponseStatus.FAIL,
                error = e.message
            )
        }
    }
}