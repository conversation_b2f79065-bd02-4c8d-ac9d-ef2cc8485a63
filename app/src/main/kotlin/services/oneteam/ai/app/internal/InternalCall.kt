package services.oneteam.ai.app.internal

import io.ktor.server.application.*
import io.ktor.server.engine.*
import kotlinx.coroutines.CoroutineScope
import kotlin.coroutines.CoroutineContext

class InternalCall(
    application: Application,
    override val coroutineContext: CoroutineContext,
) : BaseApplicationCall(application), CoroutineScope {
    override val request: InternalRequest = InternalRequest(this)
    override val response: InternalResponse = InternalResponse(this)

    init {
        putResponseAttribute()
    }
}