package services.oneteam.ai.app.domains.passVault

import services.oneteam.ai.app.KeyVaultConfig
import services.oneteam.ai.shared.domains.passvault.AzureKeyVaultService
import services.oneteam.ai.shared.domains.passvault.InMemoryKeyVaultService
import services.oneteam.ai.shared.domains.passvault.KeyVaultServiceFactory

fun KeyVaultConfig.toFactoryOptions(): KeyVaultServiceFactory.KeyVaultServiceFactoryOptions {
    return KeyVaultServiceFactory.KeyVaultServiceFactoryOptions(
        provider = this.keyVaultServiceProvider,
        azureConfig = AzureKeyVaultService.AzureKeyVaultServiceConfig(
            keyVaultName = this.azure.keyVaultName,
            secretNamePrefix = this.azure.secretNamePrefix,
            credentialStrategy = this.azure.credentialStrategy
        ),
        inMemoryConfig = InMemoryKeyVaultService.InMemoryKeyVaultServiceConfig(
            keyVaultName = this.inMemory.keyVaultName,
            secretNamePrefix = this.inMemory.secretNamePrefix,
            loadSeedData = this.inMemory.loadSeedData
        )
    )
}
