package services.oneteam.ai.app

import kotlinx.serialization.Serializable
import services.oneteam.ai.shared.domains.workspace.validation.Bounds

/*
Current error looks like
{
  "statusCode": 400,
  "message": "Request validation failed",
  "debug": "Validation failed for ForCreate(name=Name(value=), key=Key(value=aaaaaaaaaaaaaasdsdsdsssssssssssss), description=Description(value=)). Reasons: errors.workspace.name.required.name",
  "key": "errors.workspace.name.required",
  "context": "name"
}

New error looks like
{
  "statusCode": 400,
  "message": "Bad Request",
  "debug": "Bad request: name: errors.workspace.name.required, name: errors.workspace.name.length, key: errors.workspace.key.length, key: errors.workspace.key.uppercase",
  "key": "errors.workspace.name.required",
  "context": "name",
  "errors": [
    {
      "message": "errors.workspace.name.required",
      "key": "errors.workspace.name.required",
      "field": "name",
      "context": {
        "type": "services.oneteam.domains.RequiredValidationContext"
      }
    },
    {
      "message": "errors.workspace.name.length",
      "key": "errors.workspace.name.length",
      "field": "name",
      "context": {
        "type": "services.oneteam.domains.BoundedValidationContext",
        "min": 2,
        "max": 255
      }
    },
    {
      "message": "errors.workspace.key.length",
      "key": "errors.workspace.key.length",
      "field": "key",
      "context": {
        "type": "services.oneteam.domains.BoundedValidationContext",
        "min": 2,
        "max": 20
      }
    },
    {
      "message": "errors.workspace.key.uppercase",
      "key": "errors.workspace.key.uppercase",
      "field": "key",
      "context": {
        "type": "services.oneteam.domains.UppercaseValidationContext"
      }
    }
  ]
}


 */

@Serializable
data class ApiError(
    val statusCode: Int,
    val message: String,
    val debug: String? = null,
    val errors: List<ApiValidationError>? = null,
    val key: String? = null,
    var context: String? = null
)

@Serializable
data class ApiValidationError(
    val message: String, // for logs
    val key: String? = null, // localization key
    val field: String? = null, // field name
    val context: Bounds? = null
)