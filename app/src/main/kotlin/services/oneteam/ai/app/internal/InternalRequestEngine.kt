import io.ktor.events.*
import io.ktor.server.application.*
import io.ktor.server.engine.*
import io.ktor.util.pipeline.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.runBlocking
import services.oneteam.ai.app.internal.InternalCall
import services.oneteam.ai.app.internal.InternalRequest
import services.oneteam.ai.app.middlewares.OTAI_REQUEST_MODE_INTERNAL
import services.oneteam.ai.app.middlewares.OTAI_REQUEST_MODE_KEY
import kotlin.coroutines.CoroutineContext

fun Application.runInternalRequest(setup: InternalRequest.() -> Unit): InternalCall {
    val engine = InternalRequestEngine(this)
    return runBlocking {
        engine.handleRequest(setup)
    }
}


class InternalRequestEngine(
    val application: Application
) : BaseApplicationEngine(application.environment, Events(), application.developmentMode), CoroutineScope {

    private val job = Job()
    override val coroutineContext: CoroutineContext = Dispatchers.Default + job

    override fun start(wait: Boolean): ApplicationEngine {
        return this
    }

    override fun stop(gracePeriodMillis: Long, timeoutMillis: Long) {
        job.cancel()
    }

    suspend fun handleRequest(setup: InternalRequest.() -> Unit): InternalCall {
        val call = createCall(setup)
        pipeline.execute(call)
        return call
    }

    private fun createCall(setup: InternalRequest.() -> Unit): InternalCall {
        val call = InternalCall(application, coroutineContext)
        call.request.setup()
        call.attributes.put(OTAI_REQUEST_MODE_KEY, OTAI_REQUEST_MODE_INTERNAL)
        return call
    }
}
