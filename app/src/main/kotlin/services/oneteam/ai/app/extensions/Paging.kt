package services.oneteam.ai.app.extensions

import io.ktor.server.routing.*
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.Sort
import services.oneteam.ai.shared.domains.SortField
import services.oneteam.ai.shared.domains.SortableFields

fun RoutingRequest.buildPageRequest(
    allowedSortableFields: SortableFields,
    defaultSortableFields: List<String>? = null
): PageRequest {
    val specifiedSortFields = this.queryParameters.getAll("sort")

    val actualSortFields = if (specifiedSortFields.isNullOrEmpty()) defaultSortableFields else specifiedSortFields

    return PageRequest(
        pageNumber = this.queryParameters["page"]?.toInt() ?: PageRequest.DEFAULT_PAGE,
        pageSize = (this.queryParameters["pageSize"]?.toInt() ?: PageRequest.DEFAULT_PAGE_SIZE).coerceAtMost(
            PageRequest.MAX_PAGE_SIZE
        ).coerceAtLeast(PageRequest.MIN_PAGE_SIZE),
        keyword = this.queryParameters["keyword"]?.toString() ?: "",

        sort = Sort(
            fields = actualSortFields?.map { SortField.fromString(it) }
                ?.filter { allowedSortableFields.contains(it.field) }?.map { sort ->
                    SortField(
                        field = sort.field,
                        direction = sort.direction
                    )
                } ?: emptyList(),
        )
    )
}

