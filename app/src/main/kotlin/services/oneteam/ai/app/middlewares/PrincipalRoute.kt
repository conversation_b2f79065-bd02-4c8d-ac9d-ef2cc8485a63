package services.oneteam.ai.app.middlewares

import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*
import kotlinx.coroutines.withContext
import services.oneteam.ai.shared.domains.auth.ApiKeyPrincipal
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

/**
 * When we run flows we don't have a user principal available - flows can be run by the system/headless/scheduled.
 *
 * When a user does run a flow, we want to know that it is running as the system, so that we can configure the
 * database connection properly to handle row level security.
 *
 * We expect user-less requests to come with the JWT system token, which is used to identify the system user.
 * This plugin copies the JWT principal into the request context, so that it can be used in the flow.
 *
 * The database connection logic can then use the presence of this principal to configure the connection properly.
 *
 * @See services.oneteam.ai.shared.database.DatabaseUtils
 */
internal object PrincipalRouteHook : Hook<suspend (ApplicationCall, suspend () -> Unit) -> Unit> {
    internal val PrincipalPhase: PipelinePhase = PipelinePhase("PrincipalPhase")

    override fun install(
        pipeline: ApplicationCallPipeline,
        handler: suspend (ApplicationCall, suspend () -> Unit) -> Unit,
    ) {
        pipeline.insertPhaseAfter(ApplicationCallPipeline.Plugins, PrincipalPhase)
        pipeline.intercept(PrincipalPhase) { handler(call, ::proceed) }
    }
}

class PrincipalRouteConfig

val PrincipalRoutePlugin: RouteScopedPlugin<PrincipalRouteConfig> =
    createRouteScopedPlugin("PrincipalRoutePlugin", ::PrincipalRouteConfig) {
        on(PrincipalRouteHook) { call, block -> withPrincipal(call, block) }
    }

fun Route.withPrincipal(build: Route.() -> Unit): Route {
    val route = this
    route.install(PrincipalRoutePlugin)
    route.build()
    return route
}

internal suspend inline fun withPrincipal(
    call: ApplicationCall,
    crossinline block: suspend () -> Unit,
) {

    val jwtPrincipal = call.principal<JWTPrincipal>()
    val apiKeyPrincipal = call.principal<ApiKeyPrincipal>()

    val tenant = coroutineContext[RequestContext]?.tenant
    val principal = coroutineContext[RequestContext]?.principal

    // if we don't already have a principal, we use the JWTPrincipal or ApiKeyPrincipal
    val systemContext = if (principal == null) coroutineContext + RequestContext(
        tenant = tenant!!,
        principal = jwtPrincipal ?: apiKeyPrincipal
    ) else coroutineContext

    withContext(systemContext) {
        block()
    }

}




