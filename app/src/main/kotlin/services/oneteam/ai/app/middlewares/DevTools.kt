package services.oneteam.ai.app.middlewares

import io.ktor.server.application.*
import io.ktor.server.application.hooks.MonitoringEvent
import io.ktor.util.AttributeKey
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import services.oneteam.ai.app.AppConfig

val OTAI_REQUEST_MODE_KEY = AttributeKey<String>("otaiRequestMode")
val OTAI_REQUEST_MODE_INTERNAL = "internal"

class DevToolsConfiguration {
    lateinit var applicationConfig: AppConfig
}

val DevToolsPlugin = createApplicationPlugin(
    name = "DevToolsPlugin",
    createConfiguration = ::DevToolsConfiguration
) {
    val appConfig = pluginConfig.applicationConfig

    on(MonitoringEvent(ApplicationStarted)) { application ->
        application.attributes.put(appConfigKey, appConfig)
    }

    onCall { call ->
        if (!shouldDelayCall(call)) { return@onCall }

        if (appConfig.development && appConfig.artificialDelayMs != null && appConfig.artificialDelayMs!! > 0) {
            runBlocking {
                // make every API slower to let us see the impacts of HTTP requests
                delay(appConfig.artificialDelayMs!!)
            }
        }
    }
}

private fun shouldDelayCall(call: ApplicationCall): Boolean {
    val hasOtaiInternalRequestKey = call.attributes.contains(OTAI_REQUEST_MODE_KEY)
    if (!hasOtaiInternalRequestKey) {
        return true
    }
    if (call.attributes[OTAI_REQUEST_MODE_KEY] == OTAI_REQUEST_MODE_INTERNAL) {
        return false
    }
    return true
}