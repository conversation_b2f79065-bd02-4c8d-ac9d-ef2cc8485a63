package services.oneteam.ai.app

import io.ktor.server.plugins.requestvalidation.*
import services.oneteam.ai.shared.domains.ValidationErrors
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.workspace.FormConfiguration
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.SeriesConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace

fun RequestValidationConfig.configureRequestValidation() {
    validate<Workspace> { handle(it.validate()) }
    validate<FoundationConfiguration> { handle(it.validate()) }
    validate<FormConfiguration> { handle(it.validate()) }
    validate<SeriesConfiguration> { handle(it.validate()) }
    validate<Foundation> { handle(it.validate()) }
}

fun handle(errors: ValidationErrors): ValidationResult {
    errors.throwIfInvalid()
    return ValidationResult.Valid
}