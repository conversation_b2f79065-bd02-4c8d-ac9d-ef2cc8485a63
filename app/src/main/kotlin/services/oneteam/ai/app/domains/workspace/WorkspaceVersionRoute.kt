package services.oneteam.ai.app.domains.workspace

import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import org.koin.core.component.getScopeId
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.flow.pubsub.*
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersions

fun Route.workspaceVersionEndpoints(
    workspaceVersionService: WorkspaceVersionService
) {

    post<Workspaces.Id.Versions> { wId ->
        val id = wId.parent.id
        val cookie = call.request.headers["Cookie"]!!

        fun notify(key: String, variant: ToastNotificationVariant) {
            PubSubService.sendToWorkspace(
                id, WebSocketMessage(
                    MessageType.Notification, NotificationMessage(
                        heading = Message(key = "ui.notifications.notification"),
                        description = Message(key = key),
                        toastNotificationVariant = variant
                    )
                )
            )
        }

        runCatching {
            workspaceVersionService.createVersion(id, cookie)
        }.onFailure { e ->
            notify("ui.configuration.dashboard.alert.publish.error", ToastNotificationVariant.DANGER)
            throw e
        }.onSuccess { version ->
            notify("ui.configuration.dashboard.alert.publish.success", ToastNotificationVariant.INFO)
            call.respond(version)
        }
    }


    get<Workspaces.Id.Versions.Version> { wId ->
        val w = workspaceVersionService.findVersion(wId.parent.parent.id, wId.versionId)
        call.respond(w)
    }

    get<Workspaces.Id.Versions> { wId ->
        val pageRequest = call.request.buildPageRequest(
            // allowed sortable fields, mapped to their respective columns
            SortableFields(
                mapOf(
                    "id" to WorkspaceVersions.id,
                )
            )
        )

        val pageResult = workspaceVersionService.search(
            wId.parent.id, pageRequest
        )

        call.respond(pageResult)
    }


}