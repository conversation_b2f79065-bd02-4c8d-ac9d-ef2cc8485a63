package services.oneteam.ai.app.domains.auth

import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.resources.Resource
import io.ktor.server.request.receive
import io.ktor.server.resources.get
import io.ktor.server.response.respond
import io.ktor.server.resources.delete
import io.ktor.server.resources.post
import io.ktor.server.routing.Route
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.auth.ApiKey
import services.oneteam.ai.shared.domains.auth.ApiKeySearchCriteria
import services.oneteam.ai.shared.domains.auth.ApiKeyService

@Resource("/api-management")
class ApiManagement() {
    @Resource("/api-keys")
    class ApiKey(val apiManagement: ApiManagement = ApiManagement()) {

        @Resource("{apiKeyId}")
        class ApiKeyId(val apiKey: <PERSON>pi<PERSON>ey, val apiKeyId: Long)
    }
}

fun Route.tenantApiKeyEndpoints(
    apiKeyService: ApiKeyService
) {
    get<ApiManagement.ApiKey> {
        val searchCriteria = ApiKeySearchCriteria.fromRequest(call.request.queryParameters)
        val pageRequest = call.request.buildPageRequest(SortableFields(emptyMap()))
        val result = apiKeyService.search(
            pageRequest, searchCriteria
        )
        call.respond(result)
    }

    post<ApiManagement.ApiKey> {
        val token = call.receive<ApiKey.ForCreate>()
        val result = apiKeyService.create(token)
        call.respond(result)
    }

    delete<ApiManagement.ApiKey.ApiKeyId> { requestParams ->
        val tokenId = requestParams.apiKeyId
        apiKeyService.delete(ApiKey.Id(tokenId))
        call.respond(HttpStatusCode.OK, mapOf<String, String>())
    }
}

fun ApiKeySearchCriteria.Companion.fromRequest(
    parameters: Parameters
): ApiKeySearchCriteria {
    val searchTerm = parameters["search"] ?: ""

    return ApiKeySearchCriteria(
        searchTerm = searchTerm,
    )
}
