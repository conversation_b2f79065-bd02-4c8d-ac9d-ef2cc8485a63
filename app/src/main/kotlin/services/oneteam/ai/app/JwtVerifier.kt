package services.oneteam.ai.app

import com.auth0.jwt.algorithms.Algorithm
import java.security.KeyFactory
import java.security.interfaces.RSAPublicKey
import java.security.spec.X509EncodedKeySpec
import java.util.Base64
import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier

class JwtConfigurer(publicKey: String) {
    val verifier: JWTVerifier

    init {
        val publicKeyContent = publicKey
            .replace("-----BEGIN PUBLIC KEY-----", "")
            .replace("-----END PUBLIC KEY-----", "")
            .replace("\n", "")
            .replace("\\s+".toRegex(), "")
        val keyBytes = Base64.getDecoder().decode(publicKeyContent)
        val keyFactory = KeyFactory.getInstance("RSA")
        val processedPublicKey = keyFactory.generatePublic(X509EncodedKeySpec(keyBytes)) as RSAPublic<PERSON>ey
        val algorithm = Algorithm.RSA256(processedPublic<PERSON>ey, null)
        verifier = JWT.require(algorithm).build()
    }
}