package services.oneteam.ai.app.domains.document

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.resources.post
import io.ktor.server.resources.put
import io.ktor.server.routing.*
import services.oneteam.ai.app.middlewares.addEventToCallAttributes
import services.oneteam.ai.shared.domains.collection.form.*
import services.oneteam.ai.shared.domains.collection.foundation.getFoundationConfiguration
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.EventKey
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.domains.workspace.document.DocumentId
import services.oneteam.ai.shared.helpers.CustomNanoId
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService


@Resource("/document")
class DocumentRoute() {
    @Resource("/events")
    class Events(val parent: DocumentRoute)

    @Resource("{id}")
    class Id(val parent: DocumentRoute, val id: DocumentId) {

        @Resource("/form/answer")
        class FormAnswer(val documentId: Id)
    }
}

fun Route.documentEventsEndpoints(formService: FormService, workspaceVersionService: WorkspaceVersionService) {
    post<DocumentRoute.Events> { _ ->
        val eventResponseBody = call.receive<List<FormAnswerChangedRequestSyncServerBody>>()
        val eventGroupId = CustomNanoId.generate();
        for (event in eventResponseBody) {
            when (event.eventType) {
                EventKey.UPDATE_COLLECTION_FORM_ANSWER -> {
                    val formId = event.formId
                    val questionId = event.formAnswer.questionId

                    val formEntity = formService.get(formId)
                    val formConfiguration = getFormConfiguration(workspaceVersionService, formEntity)
                    if (formConfiguration == null) {
                        call.respond(HttpStatusCode.BadRequest, "Foundation configuration not found")
                        return@post
                    }
                    val question = getQuestionById(questionId, formConfiguration.content)
                    if (question == null) {
                        call.respond(HttpStatusCode.BadRequest, "Question not found")
                        return@post
                    }
                    val eventPayload = Event.ForCreate(
                        workspaceId = formEntity.workspaceId,
                        eventProperties = Event.EventProperties.UpdateCollectionFormAnswerProperties(
                            form = constructFormMinimalFromForm(
                                formEntity,
                                formConfiguration,
                                getFoundationConfiguration(workspaceVersionService, formEntity.foundation)!!
                            ),
                            changes = Event.EventProperties.UpdateCollectionFormAnswerProperties.Changes(
                               questionIds = listOf(question.id.value)
                            )
                        ),
                        eventGroupId = eventGroupId,
                    )
                    addEventToCallAttributes(call, eventPayload)
                }

                else -> {
                    call.respond(HttpStatusCode.BadRequest, "Unsupported event type")
                    return@post
                }
            }
        }

        call.respond(eventResponseBody)
    }
}

fun Route.documentEndpoints(
    documentService: IDocumentService,
) {
    /**
     * FE set answer for a form
     */
    put<DocumentRoute.Id.FormAnswer> { docParams ->
        val formAnswer = call.receive<List<SetAnswerParams>>()
        val result =
            documentService.answerQuestion(docParams.documentId.id, call.request.headers["Cookie"]!!, formAnswer)
        call.respond(result)
    }
}