package services.oneteam.ai.app.domains.collection

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.plugins.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.resources.put
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import services.oneteam.ai.app.domains.workspace.Workspaces
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.app.middlewares.addEventToCallAttributes
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.collection.form.*
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.getFoundationConfiguration
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.includeInternalServiceAccount
import services.oneteam.ai.shared.domains.workspace.*
import kotlin.text.toLong


@Suppress("unused")
@Resource("/forms")
private class FormsRoute() {

    @Resource("/obtain")
    class Obtain(val forms: FormsRoute)

    @Resource("{id}")
    class Id(val forms: FormsRoute, val id: Form.Id) {

        @Resource("/answers/{questionId}")
        class Answers(val form: Id, val questionId: String) {
            @Resource("/blob/{rowId?}") //optional path mapping needs to be last
            class Blob(val answer: Answers, val rowId: String? = null)

            @Resource("/prefill")
            class Prefill(val answer: Answers)

            @Resource("/download")
            class Download(val answer: Answers)
        }

        @Resource("/answer")
        class FormAnswer(val formId: Id)

        @Resource("/answers")
        class FormAnswers(val formId: Id)

        @Resource("/alert")
        class FormAlert(val formId: Id)

        @Resource("/properties")
        class FormProperties(val formId: Id)
    }
}


fun Route.formEndpoints(
    formService: FormService,
    workspaceVersionService: WorkspaceVersionService,
    proxyService: ProxyService,
) {
    get<FormsRoute.Id> { formParams ->
        val form = formService.get(formParams.id)
        call.respond<Form>(form)
    }

    get<Workspaces.Id.Forms> { workspaceParams ->
        val pageRequest = call.request.buildPageRequest(FormRepository.SORTABLE_FIELDS)
        val searchCriteria = FormSearchCriteria.fromRequest(workspaceParams.workspace.id, call.request.queryParameters)

        val pageResult = formService.search(
            pageRequest, searchCriteria
        )

        call.respond(pageResult)
    }

    put<FormsRoute.Id> { formParams ->
        val formForUpdate = call.receive<Form.ForUpdate>()
        val updatedForm = formService.update(formParams.id, formForUpdate)

        val formConfiguration = getFormConfiguration(workspaceVersionService, updatedForm)
        if (formConfiguration == null) {
            call.respond(HttpStatusCode.BadRequest, "Form configuration not found")
            return@put
        }

        val eventPayload = Event.ForCreate(
            workspaceId = updatedForm.workspaceId,
            eventProperties = Event.EventProperties.UpdateCollectionFormProperties(
                form = constructFormMinimalFromForm(
                    updatedForm,
                    formConfiguration,
                    getFoundationConfiguration(workspaceVersionService, updatedForm.foundation)!!
                )
            )
        )

        addEventToCallAttributes(call, eventPayload)

        call.respond(updatedForm)
    }

    delete<FormsRoute.Id> { formParams ->
        val targetForm = formService.get(formParams.id)
        formService.delete(formParams.id)

        val formConfiguration = getFormConfiguration(workspaceVersionService, targetForm)
        if (formConfiguration == null) {
            call.respond(HttpStatusCode.BadRequest, "Form configuration not found")
            return@delete
        }

        val eventPayload = Event.ForCreate(
            workspaceId = targetForm.workspaceId,
            eventProperties = Event.EventProperties.DeleteCollectionFormProperties(
                form = constructFormMinimalFromForm(
                    targetForm,
                    formConfiguration,
                    getFoundationConfiguration(workspaceVersionService, targetForm.foundation)!!
                )
            )
        )

        addEventToCallAttributes(call, eventPayload)

        call.respond(HttpStatusCode.NoContent)
    }

    @Serializable
    data class Path(val path: String)

    post<FormsRoute.Id.Answers.Blob> { answersParams ->
        val formId = answersParams.answer.form.id
        val questionId = answersParams.answer.questionId
        val rowId = answersParams.rowId
        var pathObject: Path? = null
        try {
            pathObject = call.receive<Path>()
        } catch (e: BadRequestException) {
            //ignore if there is no path Object
        }
        call.respond(mapOf("url" to formService.getSignedUrl(formId, questionId, rowId, pathObject?.path)))
    }

    post<FormsRoute.Id.Answers.Prefill> { answersParams ->
        val formId = answersParams.answer.form.id
        val questionId = answersParams.answer.questionId
        val prefillPayload = call.receive<PrefillRequestBody>()

        //get file from blob
        val signedUrl = formService.getSignedUrl(formId, questionId, null, prefillPayload.path)

        val df = formService.readFile(signedUrl, prefillPayload.isCsvFile)
        val value = formService.matchFileDataWithForm(formId, questionId, df)

        if (value !== null) {
            val requestBodyPayload = FormAnswerRequestBody(
                questionId = BaseSection.Id(questionId),
                value = TypeToJsonElementConverter.toJsonElement(value),
                operation = SetAnswerOperation.SET_TABLE
            )

            val payload = Json.encodeToJsonElement(FormAnswerRequestBody.serializer(), requestBodyPayload);

            val body = ProxyService.ProxyEndpointBody(
                ProxyService.buildInternalTenantUrl("/ai/api/forms/${formId.value}/answer"),
                "PUT",
                payload.jsonObject
            ).includeInternalServiceAccount()
            val response = proxyService.call(body).response.toString()
            val setAnswerResponse = Json.decodeFromString<SetAnswerResponse>(response)
            if (setAnswerResponse.documentId == null) {
                throw Exception("Failed to prefill answers")
            }
        }
        call.respond(mapOf("url" to signedUrl))
    }

    get<FormsRoute.Id.Answers.Download> { answersParams ->
        val formId = answersParams.answer.form.id
        val questionId = BaseSection.Id(answersParams.answer.questionId)
        val fileType = call.request.queryParameters["fileType"] ?: "xlsx"

        val signedUrl = formService.downloadTableData(formId, questionId, fileType)
        call.respond(mapOf("url" to signedUrl))
    }
}

fun Route.twoTypeAuthFormEndpoints(formService: FormService, workspaceVersionService: WorkspaceVersionService) {
    /**
     * Create a new form for a workspace
     */
    post<Workspaces.Id.Forms> { workspaceParams ->
        val formForCreate = call.receive<Form.ForCreate>()
        formService.checkDuplicatedForm(workspaceParams.id, formForCreate)
        val newForm = formService.create(workspaceParams.workspace.id, formForCreate)

        val formConfiguration = getFormConfiguration(workspaceVersionService, newForm)
        if (formConfiguration == null) {
            call.respond(HttpStatusCode.BadRequest, "Form configuration not found")
        }

        val eventPayload = Event.ForCreate(
            workspaceId = workspaceParams.workspace.id,
            eventProperties = Event.EventProperties.CreateCollectionFormProperties(
                form = constructFormMinimalFromForm(
                    newForm,
                    formConfiguration!!, getFoundationConfiguration(workspaceVersionService, newForm.foundation)!!
                )
            )
        )

        addEventToCallAttributes(call, eventPayload)

        call.respond(mapOf("form" to newForm))
    }

    /**
     * Set answer for a form
     */
    put<FormsRoute.Id.FormAnswer> { formParams ->
        val formAnswer = call.receive<FormAnswerRequestBody>()
        val response = formService.setAnswer(formParams.formId.id, call.request.headers["Cookie"], formAnswer)

        call.respond(response)
    }

    /**
     * Set answers for a form
     */
    put<FormsRoute.Id.FormAnswers> { formParams ->
        val formAnswers = call.receive<FormAnswersRequestBody>()
        val responses = formAnswers.answers.map { formAnswer ->
            formService.setAnswer(formParams.formId.id, call.request.headers["Cookie"], formAnswer, formAnswers.workspaceId)
        }

        call.respond(mapOf("responses" to responses))
    }

    /**
     * Set alert for a form
     */
    put<FormsRoute.Id.FormAlert> { formParams ->
        val formAlert = call.receive<FormAlertRequestBody>()
        val response = formService.setAlert(formParams.formId.id, call.request.headers["Cookie"], formAlert)
        call.respond(response)
    }

    put<FormsRoute.Id.FormProperties> { formParams ->
        val newFormProperties = call.receive<JsonObject>()
        require(newFormProperties.isNotEmpty()) { "Form properties cannot be empty" }
        require(newFormProperties.containsKey("properties")) { "Form properties cannot be empty" }
        val updatedForm = formService.updateProperties(formParams.formId.id,
            newFormProperties["properties"]!! as JsonObject
        )

        val formConfiguration = getFormConfiguration(workspaceVersionService, updatedForm)
        if (formConfiguration == null) {
            call.respond(HttpStatusCode.BadRequest, "Form configuration not found")
            return@put
        }

        val eventPayload = Event.ForCreate(
            workspaceId = updatedForm.workspaceId,
            eventProperties = Event.EventProperties.UpdateCollectionFormProperties(
                form = constructFormMinimalFromForm(
                    updatedForm,
                    formConfiguration,
                    getFoundationConfiguration(workspaceVersionService, updatedForm.foundation)!!
                )
            )
        )

        addEventToCallAttributes(call, eventPayload)
        call.respond(updatedForm)
    }

}

fun Route.internalFormEndpoints(
    formService: FormService,
    workspaceVersionService: WorkspaceVersionService
) {
    post<FormsRoute.Obtain> {
        val queryBody = call.receive<ObtainFormParams>();
        val result = formService.obtain(queryBody)
        if (!result.alreadyExists && result.form != null) {
            val formConfiguration = getFormConfiguration(workspaceVersionService, result.form!!)
            if (formConfiguration == null) {
                call.respond(HttpStatusCode.BadRequest, "Form configuration not found")
            }

            val eventPayload = Event.ForCreate(
                workspaceId = result.form!!.workspaceId,
                eventProperties = Event.EventProperties.CreateCollectionFormProperties(
                    form = constructFormMinimalFromForm(
                        result.form!!,
                        formConfiguration!!,
                        getFoundationConfiguration(workspaceVersionService, result.form!!.foundation)!!
                    )
                )
            )

            addEventToCallAttributes(call, eventPayload)
        }

        call.respond(mapOf("form" to result.form))
    }
}

fun FormSearchCriteria.Companion.fromRequest(
    workspaceId: Workspace.Id, parameters: Parameters
): FormSearchCriteria {
    val searchTerm = parameters["search"] ?: ""
    val foundationParent = parameters["foundationParent"]
    val foundation = parameters["foundation"]
    val foundationConfigurationId = parameters["foundationConfigurationId"]
    val formConfigurationId = parameters["formConfigurationId"]

    return FormSearchCriteria(
        workspaceId = workspaceId,
        searchTerm = searchTerm,
        foundationParentId = if (foundationParent != null) Foundation.Id(foundationParent.toLong()) else null,
        foundationId = if (foundation != null) Foundation.Id(foundation.toLong()) else null,
        foundationConfigurationId = if (foundationConfigurationId != null) FoundationConfiguration.Id(
            foundationConfigurationId
        ) else null,
        formConfigurationId = if (formConfigurationId != null) FormConfiguration.Id(formConfigurationId) else null
    )

}