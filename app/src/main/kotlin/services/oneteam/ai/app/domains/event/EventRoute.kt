package services.oneteam.ai.app.domains.event

import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.*
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.EventService


@Suppress("unused")
@Resource("/internal/events")
class EventRoute

fun Route.eventEndpoints(
    eventService: EventService,
) {
    post<EventRoute> {
        val event = call.receive<Event.ForCreate>()
        val e = eventService.create(event)
        call.respond(e)
    }
}