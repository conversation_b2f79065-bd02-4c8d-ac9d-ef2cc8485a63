# Database Migrations

Migrations are used for managing the schema and data. The database and user setup is in the [scripts](../../scripts)
folder - see the "preseed" sql files. These should be applied when provisioning a new database.

Migrations are applied using Flyway.

- https://documentation.red-gate.com/flyway/flyway-cli-and-api/concepts/migrations

The SQL migrations are located in the [src/main/resources/db/migration](../../src/main/resources/db/migration)
directory.

```sql
-- Sample: src/main/resources/db/migration/V7__add-sample.sql

CREATE TABLE sample
(
    id          bigserial PRIMARY KEY,
    name        varchar(20) NOT NULL,
    description text        NOT NULL
);
```

Migrations can also be implemented in Kotlin (in src/main/kotlin/db/migration):

```kotlin
// Sample: src/main/kotlin/db/migration/V5__Sample.kt

package db.migration

import org.flywaydb.core.api.migration.BaseJavaMigration
import org.flywaydb.core.api.migration.Context

class V5__Sample : BaseJavaMigration() {
    override fun migrate(context: Context?) {
        val statement = context?.connection?.prepareStatement(
            """
        CREATE TABLE article (
          id bigserial primary key,
          name varchar(20) NOT NULL,
          description text NOT NULL
        );
      """
        )
        statement.use { it?.execute() }
    }
}
```

## Configuration

Configuration for Flyway is in [src/main/resources/application.yaml](../../src/main/resources/application.yaml):

```yaml
flyway:
  enabled: true
  locations: [ "classpath:db/migration/postgres-only", "classpath:db/migration/common" ]
  # properties keyed off the environment variable WEBSITE_SITE_NAME
  properties: {
    "local": {
      "domain": "localhost",
      "protocol": "http"
    },
    "otai-dev": {
      "domain": "innovation.dev.oneteam.services",
      "protocol": "https"
    }
  }
```

The application must be run with a WEBSITE_SITE_NAME environment variable so that it knows which set of flyway
properties to use from `application.yaml`.

## Rebuilding the database locally

If you want to start from a clean slate, you can drop the database and recreate it by running the following command:

```shell
./scripts/db-recreate-local.mjs
```

When you start the application migrations will be applied to the database leaving it in a ready state.

## Running locally with H2

If you want to test locally in H2 you can use this configuration:

```yaml
  database:
    application:
      maximumPoolSize: "6"
      driverClassName: "org.h2.Driver"
      jdbcUrl: "jdbc:h2:tcp://localhost/~/oneteam/h2database/otai-dev-database;MODE=PostgreSQL;DATABASE_TO_UPPER=false"
      username: "sa"
      password: ""
      rls: false
    privileged:
      maximumPoolSize: "$DB_SUPER_POOL_SIZE:3"
      driverClassName: "org.h2.Driver"
      jdbcUrl: "jdbc:h2:tcp://localhost/~/oneteam/h2database/otai-dev-database;MODE=PostgreSQL;DATABASE_TO_UPPER=false"
      username: "sa"
      password: ""
      poolName: "PrivilegedPool"
      rls: false
```

Change the h2 dependency in the [../../build.gradle.kts](../../build.gradle.kts) file from testImplementation to
implementation:

```kotlin
    implementation(libs.h2)
``` 

Grab the h2 jar from your gradle cache
(eg ~
/.gradle/caches/modules-2/files-2.1/com.h2database/h2/2.3.232/4fcc05d966ccdb2812ae8b9a718f69226c0cf4e2/h2-2.3.232.jar)

```shell
mkdir -p ~/oneteam/h2database
cp ~/.gradle/caches/modules-2/files-2.1/com.h2database/h2/2.3.232/4fcc05d966ccdb2812ae8b9a718f69226c0cf4e2/h2-2.3.232.jar ~/oneteam/h2database/
```

Run the h2 database in server mode:

```bash
java -cp ~/oneteam/h2database/h2*.jar org.h2.tools.Server -ifNotExists -tcpAllowOthers
```

Access the h2 console at http://localhost:8082/
Now restart the server and the migrations will be applied to the h2 database.

Note: `DATABASE_TO_UPPER=false` is required to make the EXPOSED schema updates work correctly.

See https://www.h2database.com/html/features.html

## More Information:

* https://github.com/JetBrains/Exposed/tree/main/samples/exposed-migration
* https://documentation.red-gate.com/fd/migrations-184127470.html
* https://documentation.red-gate.com/fd/flyway-cli-and-api-183306238.html
* https://www.red-gate.com/blog/organising-your-migrations
