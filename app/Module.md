# Module - App

This is the runnable application for OneTeam AI. It is a KTOR server that provides a REST API for the OneTeam AI
services.
We currently bundle everything together into the one application, but in the future we may split this into separate
services.
This builds a fat jar using the KTOR plugin.

Tests in this module are for testing the KTOR server and the REST API. They are not for testing the database
integration.
As such, it uses an in-memory H2 database with minimal configuration to support starting the KTOR server.

For testing database integration, see the `shared` module. eg `shared/src/postgresTest`.




