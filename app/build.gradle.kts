plugins {
    id("project.conventions")
    alias(libs.plugins.ktor)
}

group = "services.oneteam.ai.app"
version = "0.0.1"

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    test {
        resources {
            srcDir("scripts/db") // so that h2 can find the db scripts and set up the users for migrations to succeed
        }
    }
}

ktor {
    fatJar {
        archiveFileName.set("oneteam-oa.jar")
    }
    docker {
        jib {
            from {
                image = "eclipse-temurin:21"
            }
            container {
                mainClass = "io.ktor.server.netty.EngineMain"
            }
        }
        jreVersion.set(JavaVersion.VERSION_21)
        localImageName.set("oneteam:oneteam-oa")

        imageTag.set(providers.environmentVariable("VERSION"))
        externalRegistry.set(
            io.ktor.plugin.features.DockerImageRegistry.externalRegistry(
                hostname = providers.environmentVariable("ACR_HOST"),
                project = providers.environmentVariable("ACR_PROJECT"),
                username = providers.environmentVariable("ACR_USERNAME"),
                password = providers.environmentVariable("ACR_PASSWORD")
            )
        )
    }
}

dependencies {
    implementation(project(":flows"))
    implementation(project(":shared"))
    implementation(project(":automergeRepo"))

    //ktor version is coming from plugin which includes the bom
    implementation("io.ktor:ktor-server-core-jvm")
    implementation("io.ktor:ktor-server-content-negotiation-jvm")
    implementation("io.ktor:ktor-serialization-kotlinx-json-jvm")
    implementation("io.ktor:ktor-server-netty-jvm")
    implementation("io.ktor:ktor-server-config-yaml")
    implementation("io.ktor:ktor-server-resources")
    implementation("io.ktor:ktor-server-status-pages")
    implementation("io.ktor:ktor-server-auth")
    implementation("io.ktor:ktor-server-auth-jwt")
    implementation("io.ktor:ktor-server-sessions")
    implementation("io.ktor:ktor-server-cors")
    implementation("io.ktor:ktor-server-request-validation")
    implementation("io.ktor:ktor-server-websockets")
    implementation("io.ktor:ktor-serialization-kotlinx-json")

    implementation("com.networknt:json-schema-validator")

    implementation(libs.dataframe)
    implementation(libs.json.schema.validator)
    implementation(libs.json.path)

    implementation(libs.opentelemetry.ktor)

    implementation(libs.postgresql)
    implementation(libs.logback.classic)
    implementation(libs.kotlinx.datetime)
    implementation(libs.bundles.exposed)
    implementation(libs.hikari)
    implementation(libs.bundles.flyway) // https://documentation.red-gate.com/fd/migrations-184127470.html
    implementation(libs.automerge)
    implementation(libs.nanoid)

    implementation(platform(libs.koin.bom))
    implementation("io.insert-koin:koin-core")
    implementation("io.insert-koin:koin-ktor")

    testImplementation("io.ktor:ktor-server-test-host-jvm")
    testImplementation(kotlin("test"))
    testImplementation(libs.bundles.kotest)
    testImplementation("io.insert-koin:koin-test-junit5")
    testImplementation(libs.h2)
    testImplementation(libs.mockito)
    testImplementation("io.ktor:ktor-client-mock")
}
