package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata

internal object Sort: ComputeFunction {
    override val key = "sort"
    override val syntax = Syntax("SORT(sortArray1, sortType1, [sortArray2, sortType2...], [returnArray])", listOf(
        Argument("sortArray", "The primary array to sort", "array"),
        Argument("sortType", "The order to sort the primary array in", "string"),
        Argument("returnArray", "The array to return, sorted based on the criteria (optional if only one sort array is used)", "array")
    ))
    override val description = "Sort an array based on multiple criteria"
    override val notes = "If only one array is provided for sorting, the returnArray parameter becomes optional and defaults to the input array"
    override val category = ""
    override val icon = Icon("function")
    override val functionName = "SORT"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = Jsonata.JFunction({ _, args ->
        if (args == null || args.size < 2) throw IllegalArgumentException("Insufficient arguments")

        val sortArrays = mutableListOf<List<*>>()
        val sortTypes = mutableListOf<String>()
        var returnArray: List<*>? = null

        val hasReturnArray = args.size % 2 == 1
        val lastSortArgIndex = if (hasReturnArray) args.size - 1 else args.size

        // Process sort arrays and sort types
        for (i in 0 until lastSortArgIndex step 2) {
            val sortArray = args[i] as? List<*>
            val sortType = args[i + 1] as? String
            if (sortArray == null || sortType == null || (sortType != "asc" && sortType != "desc")) {
                throw IllegalArgumentException("Invalid sorting criteria arguments")
            }
            sortArrays.add(sortArray)
            sortTypes.add(sortType)
        }

        // Determine the return array
        returnArray = if (hasReturnArray) {
            args.last() as? List<*> ?: throw IllegalArgumentException("returnArray argument is not an array")
        } else {
            // If only one sort array and no return array, use the sort array as return array
            if (sortArrays.size == 1) {
                sortArrays[0]
            } else {
                throw IllegalArgumentException("returnArray is required when using multiple sort arrays")
            }
        }

        if (sortArrays.isEmpty() || sortArrays.size != sortTypes.size) {
            throw IllegalArgumentException("Mismatch between sort arrays and sort types count")
        }

        val expectedLength = sortArrays[0].size
        if (sortArrays.any { it.size != expectedLength }) {
            throw IllegalArgumentException("All sort arrays must have the same length")
        }
        if (returnArray.size != expectedLength) {
            throw IllegalArgumentException("Length of returnArray does not match length of sortArray")
        }

        val sortedIndices = sortArrays[0].indices.sortedWith(Comparator { i1, i2 ->
            for (index in sortArrays.indices) {
                val sortArray = sortArrays[index]
                val sortType = sortTypes[index]

                val value1 = sortArray[i1]
                val value2 = sortArray[i2]

                val comparison = when {
                    value1 is Comparable<*> && value2 is Comparable<*> -> {
                        @Suppress("UNCHECKED_CAST")
                        (value1 as Comparable<Any>).compareTo(value2 as Comparable<Any>)
                    }
                    value1 == null && value2 != null -> -1
                    value1 != null && value2 == null -> 1
                    else -> value1?.toString().orEmpty().compareTo(value2?.toString().orEmpty())
                }
                if (comparison != 0) {
                    return@Comparator if (sortType == "asc") comparison else -comparison
                }
            }
            0
        })

        sortedIndices.map {
            returnArray[it]
        }
    }, "<x+:a<x>>")
}