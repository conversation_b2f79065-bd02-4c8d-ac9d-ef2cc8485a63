package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.datetime.*
import kotlinx.serialization.Serializable

@Serializable
internal object AddYears : ComputeFunction {
    override val key = "addYears"
    override val syntax = Syntax(
        "ADDYEARS(date, years)", listOf(
            Argument("date", "The date to add years to", "string"),
            Argument("years", "The number of years to add", "number")
        )
    )
    override val description = "Add years to a date"
    override val notes = "Add years to a date"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "ADDYEARS"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val date = args[0].toString()
        val years = args[1].toString().toInt()

        val localDate = LocalDate.parse(date)
        val result = localDate.plus( DatePeriod(years = years) ).toString()

        return@JFunction result
    }, "<sx:s>")
}
