package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import com.dashjoin.jsonata.Jsonata.JFunctionCallable
import kotlinx.serialization.Serializable

@Serializable
internal object Filter : ComputeFunction {
    override val key = "filter"
    override val syntax = Syntax(
        "FILTER(range, criteria)", listOf(
            Argument("range", "The column or table to filter", "array"),
            Argument(
                "criteria",
                "The jsonata expression which evaluates to a boolean array, based on which the range is filtered",
                "expression"
            )
        )
    )
    override val description = "Filters data based on specific criteria"
    override val notes = "Supports various operators like AND, OR, IS_EMPTY or simply an array of booleans"
    override val category = "lookup"
    override val icon = Icon("filter")
    override val functionName = "FILTER"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction(JFunctionCallable { _, args ->
        val range = args[0] as? List<*>
            ?: throw IllegalArgumentException("Expected 'range' to be a List, but got ${args[0]?.javaClass?.name}")
        var criteriaBooleanArray = args[1] as? List<*>
            ?: throw IllegalArgumentException("Expected 'criteria' to be a List, but got ${args[1]?.javaClass?.name}")

        require(!(criteriaBooleanArray.any { it !is Boolean })) { "All elements in 'criteria' must be Booleans" }
        criteriaBooleanArray = criteriaBooleanArray.filterIsInstance<Boolean>()

        // Filter the range based on the criteria
        val filteredResult = range.mapIndexedNotNullTo(mutableListOf<Any?>()) { index, rangeItem ->
            if (index < criteriaBooleanArray.size && criteriaBooleanArray[index]) rangeItem else null
        }

        // Handle extra true values in criteria
        if (criteriaBooleanArray.size > range.size) {
            filteredResult.addAll(
                criteriaBooleanArray.subList(range.size, criteriaBooleanArray.size)
                    .filter { it } // Keep only true values
                    .map { null } // Append null for extra true values
            )
        }

        return@JFunctionCallable filteredResult
    }, "<aa:a>")
}