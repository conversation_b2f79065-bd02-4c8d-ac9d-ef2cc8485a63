package services.oneteam.ai.flow.execution

import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import services.oneteam.ai.flow.execution.FlowExecution.SchemaVersion
import services.oneteam.ai.flow.expression.conditional.Condition
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.InstantSerializer
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.EventKey
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.workspace.*
import java.time.Instant


typealias NextStepId = FlowExecution.Step.Id

val currentSchemaVersion = SchemaVersion("2025-03")

@Serializable
sealed class FlowExecution {


    @Serializable
    @JvmInline
    value class SchemaVersion(val value: String)

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    @JvmInline
    value class DocumentId(val value: String)

    @Serializable
    var steps: Map<Step.Id, Step>? = null

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Description(val value: String)


    @OptIn(ExperimentalSerializationApi::class)
    @Serializable
    data class Trigger(
        override val id: Id,
        val name: String,
        @EncodeDefault val variant: Step.Variant = Step.Variant.TRIGGER,
        val properties: Step.Properties,
        val next: NextStepId? = null,
        val metadata: EntityMetadata? = null,
        var condition: Condition? = null,
        var variableMappings: List<FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping>? = null
    ) : HasId<Trigger.Id> {
        @Serializable
        @JvmInline
        value class Id(val value: String)
    }

    enum class Result {
        @SerialName("PENDING")
        PENDING,

        @SerialName("SUCCESS")
        SUCCESS,

        @SerialName("FAILED")
        FAILED,

        @SerialName("CANCELLED")
        CANCELLED
    }

    enum class Status {
        @SerialName("PENDING")
        PENDING,

        @SerialName("RUNNING")
        RUNNING,

        @SerialName("COMPLETED")
        COMPLETED,

        @SerialName("CANCELLING")
        CANCELLING
    }

    enum class CancelTriggerType {
        @SerialName("MANUAL")
        MANUAL,

        @SerialName("TIME_LIMIT")
        TIME_LIMIT
    }

    @Serializable
    data class Step(
        override val id: Id,
        val variant: Variant,
        val name: String? = null,
        val properties: Properties,
        val next: NextStepId? = null,
        val subFlows: MutableMap<String, ForJson> = mutableMapOf(),
    ) : HasId<Step.Id> {
        @Serializable
        @JvmInline
        value class Id(val value: String)

        override fun toString(): String {
            return Json.encodeToString(this)
        }

        @Serializable
        enum class Variant {
            @SerialName("action")
            ACTION,

            @SerialName("setVariables")
            SET_VARIABLES,

            @SerialName("condition")
            CONDITION,

            @SerialName("iterator")
            ITERATOR,

            @SerialName("trigger")
            TRIGGER,

            @SerialName("flow")
            FLOW;
        }

        @Serializable
        data class Properties(
            // Trigger, Action, Iterator
            val typePrimaryIdentifier: String?,

            // In configuration, inputs will start off as a map of string->string, but we want to resolve variables in the Flow Execution document to their actual values so here it is a map of string->JsonElement
            //        "inputs": {
            //            "MyFirstVariable": "literal string",
            //            "MySecondVariable": "{{event.id}}"
            //        },
            // Trigger, Action, Iterator, Flow in flow
            val inputs: MutableMap<String, JsonElement> = mutableMapOf(), // inputs will start off as a map of string->string, but we want to resolve variables in the Flow Execution document to their actual values

            // Condition
            val branches: MutableList<ConditionBranch>? = null,

            // Set Variables
            val variables: MutableList<VariableInstance>? = null,

            // Iterator & Flow in flow
            val configuration: FlowConfiguration.ForJson? = null,

            // Action
            // used to store the resolved api call
            var apiCall: JsonObject? = null,

            ) {

            fun setInputs(map: MutableMap<String, JsonElement>) {
                inputs.clear()
                inputs.putAll(map)
            }

            fun setBranches(branches: List<ConditionBranch>) {
                this.branches?.clear()
                this.branches?.addAll(branches)
            }

            @Serializable
            data class ConditionBranch(
                val name: String,
                val condition: Condition,
                val next: NextStepId? = null,
            )
        }
    }

    @Serializable
    @OptIn(ExperimentalSerializationApi::class)
    data class State(
        @Serializable var status: Status = Status.PENDING,

        var result: Result = Result.PENDING,

        @Serializable var metadata: Metadata = Metadata(),

        var message: String? = null,

        @Serializable @EncodeDefault var steps: OrderedMap<Step.Id, Step>? = OrderedMap(listOf())
    ) {

        @Serializable
        data class Step(
            override val id: Id, // TODO this should refer to the real (one and only) Step.Id
            val metadata: Metadata = Metadata(),
            var status: Status = Status.PENDING,
            var result: Result = Result.PENDING,
            var message: MutableList<String> = mutableListOf(),
            var thisStep: MutableMap<String, JsonElement> = mutableMapOf(),
            val subFlows: MutableMap<String, ForJson> = mutableMapOf(),
        ) : HasId<Step.Id> {
            @Serializable
            @JvmInline
            value class Id(val value: String)

            fun generateMetrics(): StepMetrics {
                return StepMetrics(
                    stepId = id,
                    variant = FlowExecution.Step.Variant.ACTION,
                    duration = metadata.duration(),
                    message = emptyList()
                )
            }

            enum class Result {
                @SerialName("PENDING")
                PENDING,

                @SerialName("SUCCESS")
                SUCCESS,

                @SerialName("FAILED")
                FAILED,

                @SerialName("ABORTED")
                ABORTED,

                @SerialName("COMPLETED")
                COMPLETED,
            }

            enum class Status {
                @SerialName("PENDING")
                PENDING,

                @SerialName("RUNNING")
                RUNNING,

                @SerialName("COMPLETED")
                COMPLETED,
            }
        }
    }

    @Serializable
    data class Properties(
        val event: TriggerEvent,
        val variables: List<FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping>,
        val settings: Settings,
        var cancellation: Cancellation? = null,
        var eventGroupId: String? = null
    ) {
        @Serializable
        data class Settings(
            val timeoutMins: Long
        )

        @Serializable
        data class TriggerEvent(
            val key: EventKey, val eventProperties: Event.EventProperties
        )

        @Serializable
        data class Cancellation(
            val trigger: CancelTriggerType,
            val userId: Long? = null,
            val properties: Properties? = null,
            @Serializable(with = InstantSerializer::class) val triggeredAt: Instant? = null
        ) {
            @Serializable
            data class Properties(val reason: String, val timeoutMins: Long)
        }
    }

    @Serializable
    data class ForApi(
        val id: Id,
        val workspaceId: Workspace.Id,
        val workspaceVersionId: WorkspaceVersion.Id,
//        val properties: Step.Properties,
        val status: FlowExecution.Status,
        val result: FlowExecution.Result,
        val documentId: FlowExecution.DocumentId,
        val configurationId: String,
        val flowConfigurationName: String,
        val isCanceled: Boolean = false,
        @Serializable(with = InstantSerializer::class) val startedAt: Instant? = null,
        @Serializable(with = InstantSerializer::class) val finishedAt: Instant? = null,
        @Serializable(with = InstantSerializer::class) val createdAt: Instant? = null,
    ) : FlowExecution()

    @Serializable
    @OptIn(ExperimentalSerializationApi::class)
    class ForJson(
        var context: FlowContext,
        @EncodeDefault var state: State = State(),
        val start: Step.Id? = null,
        @EncodeDefault val steps: MutableMap<Step.Id, Step> = mutableMapOf(),
        val startingVariables: List<FlowConfiguration.Content>? = null,
        val endingVariables: List<FlowConfiguration.Content>? = null,
        val properties: FlowConfiguration.Properties? = null,
        val name: Name = Name(""),
        val description: Description? = null,
        val trigger: Trigger? = null,
        @EncodeDefault val schemaVersion: SchemaVersion = currentSchemaVersion,
    ) : CollaborationDocument {
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault
        override val type = CollaborationDocumentType.FLOW_EXECUTION

        override fun descriptor(): String {
            return Id.toString()
        }

        override fun toString(): String {
            return Json { prettyPrint = true }.encodeToString(this)
        }


        fun generateMetrics(): FlowExecutionMetrics {
            val executionSteps: List<State.Step> = state.steps!!.toList()
            return FlowExecutionMetrics(
                flowConfigurationName = this.context.global.flowConfigurationName,
                workspaceId = this.context.global.workspaceId,
                duration = state.metadata.finishedAt!!.toEpochMilli() - state.metadata.startedAt!!.toEpochMilli(),
                stepsCount = steps.size,
                stepsCountByType = executionSteps.groupBy { it: State.Step ->
                    steps[Step.Id(it.id.value)]?.variant?.name ?: "unknown"
                }.mapValues { (_, values) -> values.size },
                variablesCount = context.variables.size,
                result = state.result,
                steps = state.steps!!.toList().map { step -> step.generateMetrics() })
        }
    }


    @Serializable
    data class StepMetrics(
        val stepId: State.Step.Id,
        val variant: Step.Variant,
        val name: String? = null,
        val duration: Long,
        val message: List<String> = emptyList()
    )

    @Serializable
    data class FlowExecutionMetrics(
        val flowConfigurationName: FlowConfiguration.Name,
        val workspaceId: Workspace.Id,
        val duration: Long,
        val stepsCount: Int,
        val stepsCountByType: Map<String, Int>,
        val variablesCount: Int,
        val result: Result,
        val steps: List<StepMetrics>
    ) {
        override fun toString(): String {
            return Json.encodeToString(this)
        }
    }
}


@Serializable
data class Metadata(
    @Serializable(with = InstantSerializer::class) var startedAt: Instant? = null,
    @Serializable(with = InstantSerializer::class) var finishedAt: Instant? = null,
) {
    fun duration(): Long {
        return if (finishedAt != null && startedAt != null) {
            finishedAt!!.toEpochMilli() - startedAt!!.toEpochMilli()
        } else {
            0L
        }
    }
}



