package services.oneteam.ai.flow.extensions

import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import services.oneteam.ai.flow.execution.mapBuilders.FormJsonMapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.FoundationJsonMapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.SeriesIntervalMapBuilder
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.VariableDefinition
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.flow.variables.VariableProperties

class TypeConverter(
    private val formBuilder: FormJsonMapBuilder,
    private val foundationBuilder: FoundationJsonMapBuilder,
    private val seriesIntervalBuilder: SeriesIntervalMapBuilder
) {
    fun getValueAsType(
        value: JsonElement, configuration: VariableDefinition.VariableConfiguration
    ): JsonElement = when {
        configuration.type == VariableDataType.JSON -> getValueAsJsonType(value, configuration)
        configuration.type.typeDef.startsWith("form") -> runBlocking { getValueAsFormType(value, configuration) }
        configuration.type.typeDef.startsWith("foundation") -> runBlocking {
            getValueAsFoundationType(
                value, configuration
            )
        }

        configuration.type.typeDef.startsWith("seriesInterval") -> runBlocking {
            getValueAsSeriesIntervalType(
                value, configuration
            )
        }

        configuration.type == VariableDataType.TEXT

            -> value

        configuration.type == VariableDataType.BOOLEAN -> if (value is JsonPrimitive && value.isString) JsonPrimitive(
            value.content.toBoolean()
        ) else value

        configuration.type == VariableDataType.NUMBER -> if (value is JsonPrimitive && value.isString) JsonPrimitive(
            value.content.toLong()
        ) else value

        else -> value
    }

    private fun getValueAsJsonType(
        value: JsonElement, configuration: VariableDefinition.VariableConfiguration
    ): JsonElement {
        val props = configuration.properties as? VariableProperties.JsonVariableProperties ?: return value
        //todo: is this needed?
//    if (props.isUnstructured) return value
        val items = props.items ?: emptyList()
        if (value !is JsonObject) {
            if (items.any { it.identifier == "id" }) return JsonObject(
                mapOf("id" to value)
            )
            return value
        }
        val map = mutableMapOf<String, JsonElement>()
        items.forEach { item ->
            val conf = item
            value[conf.identifier]?.let { subValue ->
                map[conf.identifier] = getValueAsType(subValue, conf)
            }
        }
        return JsonObject(map)
    }

    private suspend fun getValueAsSeriesIntervalType(
        value: JsonElement, configuration: VariableDefinition.VariableConfiguration
    ): JsonElement {
        return seriesIntervalBuilder.handle(
            VariableInstance.Variable(
                value = value,
                type = configuration.type,
                identifier = configuration.identifier,
                properties = configuration.properties
            )
        )

    }

    private suspend fun getValueAsFormType(
        value: JsonElement, configuration: VariableDefinition.VariableConfiguration
    ): JsonElement {
        return formBuilder.handle(
            VariableInstance.Variable(
                value = value,
                type = configuration.type,
                identifier = configuration.identifier,
                properties = configuration.properties
            )
        )
    }

    private suspend fun getValueAsFoundationType(
        value: JsonElement, configuration: VariableDefinition.VariableConfiguration
    ): JsonElement {
        return foundationBuilder.handle(
            VariableInstance.Variable(
                value = value,
                type = configuration.type,
                identifier = configuration.identifier,
                properties = configuration.properties
            )
        )
    }
}