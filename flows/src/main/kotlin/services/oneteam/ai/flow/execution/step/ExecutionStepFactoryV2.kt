package services.oneteam.ai.flow.execution.step

import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.execution.FlowRunner
import services.oneteam.ai.flow.execution.MapBuilder
import services.oneteam.ai.flow.execution.variables.DefaultVariableOperation
import services.oneteam.ai.flow.execution.variables.FileVariableOperation
import services.oneteam.ai.flow.execution.variables.ListVariableOperation
import services.oneteam.ai.flow.execution.variables.TableVariableOperation
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService

class ExecutionStepFactoryV2(
    private val flowStepTypeConfigurationService: FlowStepTypeConfigurationService,
    private val proxyService: ProxyService,
    private val internalProxyService: ProxyService,
    private val filePressService: FilePressService,
    private val mapBuilders: List<MapBuilder>,
    private val workspaceVersionService: WorkspaceVersionService,
    private val expressionEvaluator: JsonataExpressionEvaluator
) : ExecutionStepFactory {
    override suspend fun createStep(step: FlowExecution.Step, flowRunner: FlowRunner): ExecutionStep {
        val contextToJsonObjectBuilder = ContextToJsonObjectBuilder(mapBuilders, step.id.value)

        when (step.variant) {
            FlowExecution.Step.Variant.SET_VARIABLES -> return SetVariablesExecutionStepV2(
                step, contextToJsonObjectBuilder, listOf(
                    TableVariableOperation(false, expressionEvaluator),
                    ListVariableOperation(false, expressionEvaluator),
                    FileVariableOperation(false, expressionEvaluator),
                    DefaultVariableOperation(false, expressionEvaluator)
                ), expressionEvaluator
            )

            FlowExecution.Step.Variant.ITERATOR -> {
                val identifier = step.properties.typePrimaryIdentifier
                    ?: throw IllegalArgumentException("No identifier found for iterator step")
                val flowStepTypeConfiguration = flowStepTypeConfigurationService.getByPrimaryIdentifier(identifier)
                    ?: throw IllegalArgumentException("No configuration found for iterator step")

                return when (step.properties.typePrimaryIdentifier) {
                    "iteratorForEach" -> IteratorForEachStep(
                        step, flowRunner, contextToJsonObjectBuilder, flowStepTypeConfiguration
                    )

                    "iteratorFilter" -> IteratorFilterStep(
                        step, flowRunner, contextToJsonObjectBuilder, flowStepTypeConfiguration
                    )

                    "iteratorAggregate" -> IteratorAggregateStep(
                        step, flowRunner, contextToJsonObjectBuilder, flowStepTypeConfiguration
                    )

                    else -> throw IllegalArgumentException("Unknown iterator type $identifier")
                }
            }

            FlowExecution.Step.Variant.CONDITION -> {
                return ConditionExecutionStepV2(
                    step, contextToJsonObjectBuilder, expressionEvaluator,
                )
            }

            FlowExecution.Step.Variant.ACTION -> return ActionExecutionStepV2(
                step,
                flowStepTypeConfigurationService.getByPrimaryIdentifier(step.properties.typePrimaryIdentifier!!)
                    ?: throw IllegalArgumentException("No configuration found for action step"),
                contextToJsonObjectBuilder,
                proxyService,
                internalProxyService,
                filePressService,
                expressionEvaluator
            )

            FlowExecution.Step.Variant.FLOW -> return FlowInFlowExecutionStep(
                step, flowRunner, workspaceVersionService, contextToJsonObjectBuilder
            )

            else -> throw IllegalArgumentException("Unknown step type ${step.variant}")
        }
    }
}
