package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonPrimitive
import services.oneteam.ai.flow.execution.step.ContextToJsonObjectBuilder
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.conditional.Condition
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType

class TriggerTemplatePopulator(
    val flowContextLocalStep: FlowContextWithLocalStep,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
) {
    val jsonPlaceholderReplacer = JsonPlaceholderReplacer()
    val jsonataPlaceHolderReplacer = JsonataPlaceHolderReplacer(quoteStrategy = EscapeQuoteStrategy())

    /**
     * Populates the trigger with the resolved inputs and condition.
     *
     * @param trigger The trigger to populate.
     * @param triggerConfiguration The trigger event configuration.
     */
    suspend fun populateTrigger(
        trigger: FlowExecution.Trigger,
        triggerConfiguration: FlowStepType.Properties.Configuration
    ) {
        val cacheKey = "triggerFlowEventListener"

        val valueProvider = ContextPlaceholderValueProvider(contextToJsonObjectBuilder, flowContextLocalStep)
        val resolvedInputs = jsonPlaceholderReplacer.replacePlaceholders(
            trigger.properties.inputs,
            valueProvider
        )

        resolvedInputs.forEach { (k, v) ->
            val content = triggerConfiguration.findContentByIdentifier(k)
            val type = content?.properties?.properties?.type ?: content?.properties?.type ?: content?.type

            val result = if (type != "multiSelect") {
                if (v is JsonPrimitive) v.jsonPrimitive.content else v
            } else {
                val test = Json.parseToJsonElement(v.jsonPrimitive.content)
                JsonataExpressionEvaluator().evaluate(test.toString(), mapOf<Any, Any>())
            }
            val value = result as? JsonElement ?: JsonPrimitive(result.toString())
            flowContextLocalStep.setThisStep(k, value)
            trigger.properties.inputs[k] = v
        }

        // record the condition in the flow execution document
        val updatedValueProvider = ContextPlaceholderValueProvider(
            contextToJsonObjectBuilder.copy(cacheKey = "$cacheKey-2"),
            flowContextLocalStep
        )
        val eventKey = flowContextLocalStep.flowContext.event?.eventProperties?.key
        trigger.condition = Json.decodeFromString(
            Condition.serializer(),
            jsonataPlaceHolderReplacer.replacePlaceholders(
                Json.encodeToString(triggerConfiguration.subscribeTo!![eventKey]?.condition),
                updatedValueProvider
            )
        )

        // record variable mappings in the flow execution document
        trigger.variableMappings =
            jsonPlaceholderReplacer.replacePlaceholders(
                triggerConfiguration.subscribeTo!![eventKey]?.variableMappings,
                updatedValueProvider
            )
    }

}