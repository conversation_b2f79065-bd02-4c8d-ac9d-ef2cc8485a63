package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import kotlinx.serialization.Serializable

@Serializable
internal object ListAll : ComputeFunction {
    override val key = "listAll"
    override val syntax = Syntax("LISTALL(value1, value2...)", listOf(
        Argument("value", "The values to list", "varargs")
    ))
    override val description = "List all elements of an array"
    override val notes = ""
    override val category = ""
    override val icon = Icon("function")
    override val functionName = "LISTALL"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = Jsonata.JFunction({ _, args ->
        args!!.toList()
    }, "<x+:a<x>>")
}