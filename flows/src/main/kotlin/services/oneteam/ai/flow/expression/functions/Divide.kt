package services.oneteam.ai.flow.expression.functions

import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.expression.functions.Divide.DEFAULT_VALUE
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import java.math.BigDecimal

@Serializable
internal object Divide : ComputeFunction {

    val DEFAULT_VALUE: BigDecimal = BigDecimal.ONE

    override val key = "divide"
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "DIVIDE"

    override val syntax = Syntax(
        "$functionName(any, ... )", listOf(
            Argument("any", "Any (or Tensors) to compare", "any"),
            Argument("...", "Additional any (or Tensors) to compare", "any"),
        )
    )
    override val description = "Divide the values (or Tensors)"
    override val notes = """
        A Tensor can be a single value (scalar), an array of values (vector), or a matrix of values.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()

    override val examples = listOf(
        Example(
            "$$functionName(6, 2) = 3",
            "Divide values",
        ),
        Example(
            "$$functionName([12, 4]) = 3",
            "Divide values in a list",
        ),
        Example(
            "$$functionName([[10,8],[6,4]], 2) = [[5,4],[3,2]]",
            "Divide pairwise",
        )
    )

    val implementations: List<MatchableFunction> = listOf(
        FuzzyDivide,
        ErrorOnList(functionName),
        Pairwise(PairwiseOperation.DIVIDE, DEFAULT_VALUE),
    )

    @Transient
    override val evaluatorFunction = buildJFunctionForImplementations(
        implementations, "<x+:x>",
        functionName
    )

}

internal object FuzzyDivide : MatchableFunction {
    override fun match(args: List<*>): Boolean {
        return args.all { it !is List<*> }
    }

    override fun perform(args: List<*>): Number {

        if (args.isEmpty()) return BigDecimal.ZERO.convertToNiceBigDecimal()

        if (args.size == 1) return (args.first().toString().toBigDecimalOrNull()
            ?: BigDecimal.ZERO).convertToNiceBigDecimal()

        return args.map { it.toString().toBigDecimalOrNull() ?: DEFAULT_VALUE }
            .reduce { acc, bd -> acc.divide(bd) }
            .convertToNiceBigDecimal()

    }
}