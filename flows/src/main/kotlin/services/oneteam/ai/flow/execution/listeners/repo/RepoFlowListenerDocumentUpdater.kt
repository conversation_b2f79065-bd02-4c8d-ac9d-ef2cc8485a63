package services.oneteam.ai.flow.execution.listeners

import automergeRepo.src.main.kotlin.DocHandle
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*

class RepoFlowListenerDocumentUpdater(
    val documentId: FlowExecution.DocumentId,
    val handle: DocHandle<FlowExecution.ForJson>,
    val currentFlowGetter: (rootFlow: FlowExecution.ForJson) -> FlowExecution.ForJson
) : FlowRunListener, FlowStepListener, FlowContextListener {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun onFlowStarted(flowExecution: FlowExecution.ForJson) {
        handle.change { currentFlowGetter(it).state = flowExecution.state }
    }

    override suspend fun onFlowFinished(
        flowExecution: FlowExecution.ForJson, cancellationInformation: FlowExecution.Properties.Cancellation?
    ) {
        handle.change { currentFlowGetter(it).state = flowExecution.state }
        logger.info("View the execution document at http://localhost:8000/ai/api/debug/#automerge:{}", documentId.value)
    }

    override suspend fun onStepStarted(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {
        logger.debug("Step started: {}, state: {}", step.name, stepState)


        handle.change {
            val stepId = FlowExecution.State.Step.Id(step.id.value)
            val currentFlow = currentFlowGetter(it)

            currentFlow.state.steps!!.order.add(stepId)
            currentFlow.state.steps!!.entities[stepId] = stepState
        }

        onStepUpdated(step)
    }

    override suspend fun onStepFinished(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {
        logger.debug("Step finished: {}, state: {}", step.name, stepState)

        handle.change {
            val stepId = FlowExecution.State.Step.Id(step.id.value)
            currentFlowGetter(it).state.steps!!.entities[stepId] = stepState
        }

        onStepUpdated(step)
    }

    override suspend fun onStepUpdated(step: FlowExecution.Step) {
        handle.change { currentFlowGetter(it).steps[step.id] = step }
    }

    override suspend fun onVariableSet(value: VariableDefinition.Variable) {
        logger.debug("Context variable set: {}", value)

        handle.change { currentFlowGetter(it).context.variables[value.identifier] = value }
    }

}
