package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.variables.VariableOperation
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.Variable
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance

@Deprecated("Use SetVariablesExecutionStepV2 instead", ReplaceWith("SetVariablesExecutionStepV2"))
class SetVariablesExecutionStep(
    val step: FlowExecution.Step,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
    val variableOperations: List<VariableOperation> = emptyList(),
) : ExecutionStep {

    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val simplePlaceHolderReplacer = SimplePlaceHolderReplacer()
    val jsonataPlaceholderReplacer = JsonataPlaceHolderReplacer()
    val jsonPlaceholderReplacer = JsonPlaceholderReplacer()

    override suspend fun execute(context: FlowContextWithLocalStep): NextStepId? {

        // cacheKey ensures accesses to models and documents are fetched only once
        // this is acceptable since users cannot modify models or documents as part of setVariable
        val cacheKey = step.id.value

        step.properties.variables?.forEachIndexed { idx, variable ->
            val resolver = variableOperations.firstOrNull() { it.match(variable) }
            if (resolver != null) {
                val transformedVariable = populate(variable, context, "$cacheKey-$idx")
                step.properties.variables[idx] = transformedVariable // update FED with transformed variable
                val resolvedVariable = resolver.resolve(transformedVariable, context)
                logger.trace("Setting variable `{}` to `{}`", resolvedVariable.identifier, resolvedVariable.get())
                context.flowContext.set(resolvedVariable)
            } else {
                logger.error("No variable resolver found for variable: {}", variable)
            }
        }
        return step.next

    }


    suspend fun populate(variable: Variable, context: FlowContextWithLocalStep, cacheKey: String): VariableInstance {

        val valueProvider =
            ContextPlaceholderValueProvider(contextToJsonObjectBuilder.copy(cacheKey = cacheKey), context)

        val populatedVariable = VariableInstance.Variable(
            type = VariableDataType.fromString(
                simplePlaceHolderReplacer.replacePlaceholders(
                    variable.type.typeDef, valueProvider
                )
            ),
            identifier = simplePlaceHolderReplacer.replacePlaceholders(variable.identifier, valueProvider),
            properties = jsonPlaceholderReplacer.replacePlaceholders(variable.properties, valueProvider),
            value = JsonPrimitive(
                jsonataPlaceholderReplacer.replacePlaceholders(
                    (variable as VariableInstance).get().jsonPrimitive.content, valueProvider
                )
            )
        )


        logger.trace("Populated expression `{}` for `{}`", variable.get(), variable.identifier)
        return populatedVariable
    }
}