package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import com.dashjoin.jsonata.Jsonata.JFunction
import com.dashjoin.jsonata.Utils
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import java.math.BigDecimal
import java.math.MathContext

/**
 * Function to convert an object to its string representation.
 * This is a copy of the Jsonata `string` methods from com.dashjoin.jsonata.Functions.
 * Needed to comment out references to Parser.Symbol since that is private in the Jsonata library.
 *
 * The only difference atm is how BigDecimal is handled. Here we use the `convertToNiceBigDecimal` extension function to
 * ensure nice formatting of the numbers.
 */

internal object ToString : ComputeFunction {
    override val key = "toString"
    override val syntax = Syntax(
        "TOSTRING(object)", listOf(
            Argument("object", "Object to convert to string", "any")
        )
    )
    override val description = "Returns a string representation of the provided object"
    override val notes = ""
    override val category = "String Functions"
    override val icon = Icon("function")
    override val functionName = "TOSTRING"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        if (args.isEmpty()) {
            return@JFunction ""
        }

        if (args.size == 1 && args[0] is List<*>) {
            // If a single list is provided, stringify the list
            return@JFunction string(args[0], false)
        }

        return@JFunction string(args, false)

    }, "<x+:s>")


    /**
     * Stringify arguments
     * @param {Object} arg - Arguments
     * @param {boolean} [prettify] - Pretty print the result
     * @returns {String} String from arguments
     */
    fun string(arg: Any?, prettify: Boolean?): String? {
        var arg = arg
        if (arg is Utils.JList<*>) if (arg.outerWrapper) arg = arg.get(0)

        if (arg == null) return null

        // see https://docs.jsonata.org/string-functions#string: Strings are unchanged
        if (arg is String) return arg

        val sb = StringBuilder()
        string(sb, arg, prettify != null && prettify, "")
        return sb.toString()
    }

    /**
     * Internal recursive string function based on StringBuilder.
     * Avoids creation of intermediate String objects
     */
    fun string(b: StringBuilder, arg: Any?, prettify: Boolean, indent: String?) {
        // if (arg == null)
        //   return null;

        if (arg == null || arg === Jsonata.NULL_VALUE) {
            b.append("null")
            return
        }

        if (arg is JFunction) {
            return
        }

//        if (arg is Parser.Symbol) {
//            return
//        }

        if (arg is Double) {
            // TODO: this really should be in the jackson serializer
            val bd = BigDecimal(arg, MathContext(15))
            var res = bd.convertToNiceBigDecimal().toString()

            if (res.indexOf("E+") > 0) res = res.replace("E+", "e+")
            if (res.indexOf("E-") > 0) res = res.replace("E-", "e-")

            b.append(res)
            return
        }

        if (arg is Number || arg is Boolean) {
            b.append(arg)
            return
        }

        if (arg is String) {
            // quotes within strings must be escaped
            Utils.quote(arg, b)
            return
        }

        if (arg is MutableMap<*, *>) {
            b.append('{')
            if (prettify) b.append('\n')
            for (e in (arg as MutableMap<String?, Any?>).entries) {
                if (prettify) {
                    b.append(indent)
                    b.append("  ")
                }
                b.append('"')
                b.append(e.key)
                b.append('"')
                b.append(':')
                if (prettify) b.append(' ')
                val v = e.value
                if (v is String
//                    || v is Parser.Symbol
                    || v is JFunction
                ) {
                    b.append('"')
                    string(b, v, prettify, indent + "  ")
                    b.append('"')
                } else string(b, v, prettify, indent + "  ")
                b.append(',')
                if (prettify) b.append('\n')
            }
            if (!(arg as MutableMap<*, *>).isEmpty()) b.deleteCharAt(b.length - (if (prettify) 2 else 1))
            if (prettify) b.append(indent)
            b.append('}')
            return
        }

        if ((arg is MutableList<*>)) {
            if (arg.isEmpty()) {
                b.append("[]")
                return
            }
            b.append('[')
            if (prettify) b.append('\n')
            for (v in arg) {
                if (prettify) {
                    b.append(indent)
                    b.append("  ")
                }
                if (v is String
//                    || v is Parser.Symbol
                    || v is JFunction
                ) {
                    b.append('"')
                    string(b, v, prettify, indent + "  ")
                    b.append('"')
                } else string(b, v, prettify, indent + "  ")
                b.append(',')
                if (prettify) b.append('\n')
            }
            if (!arg.isEmpty()) b.deleteCharAt(b.length - (if (prettify) 2 else 1))
            if (prettify) b.append(indent)
            b.append(']')
            return
        }

        // Throw error for unknown types
        throw IllegalArgumentException("Only JSON types (values, Map, List) can be stringified. Unsupported type: " + arg.javaClass.getName())
    }

}