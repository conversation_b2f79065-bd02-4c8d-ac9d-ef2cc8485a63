package services.oneteam.ai.flow.execution.listeners

import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution

interface FlowListener

interface FlowRunListener : FlowListener {
    suspend fun onFlowStarted(
        flowExecution: FlowExecution.ForJson
    )
    suspend fun onFlowFinished(
        flowExecution: FlowExecution.ForJson,
        cancellationInformation: FlowExecution.Properties.Cancellation? = null,
    )
}

interface FlowStepListener : FlowListener {

    suspend fun onStepStarted(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    )

    suspend fun onStepFinished(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    )

    suspend fun onStepUpdated(step: FlowExecution.Step)


}
