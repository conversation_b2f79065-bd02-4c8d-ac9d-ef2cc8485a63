package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import kotlin.math.pow
import kotlin.math.round

@Serializable
internal object Round : ComputeFunction {
    override val key = "round"
    override val syntax = Syntax(
        "ROUND(number, precision)", listOf(
            Argument("number", "The number to round", "number"),
            Argument("precision", "The number of decimal places to round to", "number")
        )
    )
    override val description = "Round a number to a specified number of decimal places"
    override val notes = "one of the basic number functions, to round a number to a specified number of decimal places"
    override val category = "number"
    override val icon = Icon("function")
    override val functionName = "ROUND"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        //need it as a double
        val value = args[0].toString().toDouble()

        //get the precision, default to 0.0
        val precision = args.getOrNull(1)?.toString()?.toDouble() ?: 0.0

        //shift the decimal point
        val factor = 10.0.pow(precision)

        //round to nearest int for correct precision, then rescale
        //dividing by factor shifts the decimal point back to its original position
        round(value * factor) / factor
    }, "<xx?:n>")
}
