package services.oneteam.ai.flow.execution

import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.javatime.timestamp
import org.jetbrains.exposed.sql.json.jsonb
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.otSerializer

class FlowExecutionEntity(id: EntityID<Long>) : BaseLongEntity(id, FlowExecutions) {
    companion object : BaseLongEntityClass<FlowExecutionEntity>(FlowExecutions)

    var workspaceId by FlowExecutions.workspaceId;
    var workspaceVersionId by FlowExecutions.workspaceVersionId;
    var tenantId by FlowExecutions.tenantId;
    var properties by FlowExecutions.properties;
    var status by FlowExecutions.status;
    var result by FlowExecutions.result;
    var documentId by FlowExecutions.documentId;
    var flowConfigurationId by FlowExecutions.flowConfigurationId;
    var startedAt by FlowExecutions.startedAt;
    var finishedAt by FlowExecutions.finishedAt;
    var flowConfigurationName by FlowExecutions.flowConfigurationName;
    var isCanceled by FlowExecutions.isCanceled
}


object FlowExecutions : BaseLongIdTable("flow_executions") {
    val workspaceId = long("workspace_id").references(id)
    val workspaceVersionId = long("workspace_version_id").references(id)
    val tenantId = long("tenant_id").references(id)
    val properties = jsonb<FlowExecution.Properties>("properties", otSerializer)
    val status = enumerationByName("status", 50, FlowExecution.Status::class)
    val result = enumerationByName("result", 50, FlowExecution.Result::class)
    val documentId = text("document_id")
    val flowConfigurationId = text("flow_configuration_id")
    val startedAt = timestamp("started_at").nullable()
    val finishedAt = timestamp("finished_at").nullable()
    val flowConfigurationName = text("flow_configuration_name")
    val isCanceled = bool("is_canceled")
}
