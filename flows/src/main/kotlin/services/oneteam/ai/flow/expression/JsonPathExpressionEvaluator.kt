package services.oneteam.ai.flow.expression

import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.JsonPath
import com.jayway.jsonpath.Option
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class JsonPathExpressionEvaluator : ExpressionEvaluator<Any, Any> {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun evaluate(expression: String, context: Any): Any {
        // context is a json string
        // expression is a jsonpath expression
        // return the result of evaluating the expression on the context

        val config = Configuration.defaultConfiguration()
            .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL, Option.SUPPRESS_EXCEPTIONS)
        val ctx = JsonPath.using(config).parse(context)
        logger.trace("Evaluating expression `{}` with context {}", expression, context)
        val result: Any = ctx.read(expression) ?: ""
        logger.trace("Evaluated expression `{}` with result `{}` using context {} ", expression, result, context)
        return result
    }
}