package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.util.*

@Serializable
internal object Trim : ComputeFunction {
    override val key = "trim"
    override val syntax = Syntax(
        "TRIM(string)", listOf(
            Argument("string", "The string to trim", "string")
        )
    )
    override val description = "Trim whitespace from the beginning and end of a string"
    override val notes = "one of the basic string functions, to trim whitespace from the beginning and end of a string"
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "TRIM"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args.first().toString().trim()
    }, "<s:s>")
}
