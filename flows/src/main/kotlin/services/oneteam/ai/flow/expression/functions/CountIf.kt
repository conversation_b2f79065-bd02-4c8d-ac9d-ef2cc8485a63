package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object CountIf : ComputeFunction {
    override val key = "countIf"
    override val syntax = Syntax(
        "COUNTIF(array, value)", listOf(
            Argument("array", "The array of numbers to count against", "array"),
            Argument("value", "the value to predicate a count condition against", "object")
        )
    )
    override val description = "Count the number of cells that meet a criterion"
    override val notes = "one of the statistical functions, to count the number of cells that meet a criterion; for example, to count the number of times a particular city appears in a customer list."
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "COUNTIF"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val values = args!![0] as? List<*> ?: return@JFunction "0"
        val criteria = args[1]
        val count = values.count { meetsCriteria(it, criteria) }
        count.toString()
    }, "<xx:n>")
}
