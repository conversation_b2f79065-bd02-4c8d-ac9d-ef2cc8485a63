package services.oneteam.ai.flow.execution.step

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.variables.VariableOperation
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.RegExTokenizer
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.helpers.getContent

class SetVariablesExecutionStepV2(
    val step: FlowExecution.Step,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
    val variableOperations: List<VariableOperation> = emptyList(),
    val expressionEvaluator: JsonataExpressionEvaluator
) : ExecutionStep {

    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val simplePlaceHolderReplacer = SimplePlaceHolderReplacer()
    val jsonPlaceholderReplacer = JsonPlaceholderReplacer()
    val tokenizer = RegExTokenizer()

    override suspend fun execute(context: FlowContextWithLocalStep): NextStepId? {

        // cacheKey ensures accesses to models and documents are fetched only once
        // this is acceptable since users cannot modify models or documents as part of setVariable
        val cacheKey = step.id.value
        val stepJsonataResolver = StepJsonataResolver(context, contextToJsonObjectBuilder, expressionEvaluator)

        step.properties.variables?.forEachIndexed { idx, variable ->
            val resolver = variableOperations.firstOrNull() { it.match(variable) }
            if (resolver != null) {
                val transformedVariable = populate(variable, context, "$cacheKey-$idx")
                step.properties.variables[idx] = transformedVariable // update FED with transformed variable

                val expression = transformedVariable.getRevealed().getContent()
                val evaluatedValue = stepJsonataResolver.resolve(expression)

                logger.trace(
                    "Produced evaluated value `{}` for variable `{}`", evaluatedValue, transformedVariable.identifier
                )

                // set value
                val resolvedVariable = resolver.resolve(
                    VariableInstance.Variable(
                        value = TypeToJsonElementConverter.toJsonElement(evaluatedValue),
                        type = transformedVariable.type,
                        identifier = transformedVariable.identifier,
                        properties = transformedVariable.properties,
                    ), context
                )
                logger.trace("Setting variable `{}` to `{}`", resolvedVariable.identifier, resolvedVariable.get())
                context.flowContext.set(resolvedVariable)
            } else {
                logger.error("No variable resolver found for variable: {}", variable)
            }
        }
        return step.next

    }

    suspend fun populate(
        variable: VariableInstance, context: FlowContextWithLocalStep, cacheKey: String
    ): VariableInstance {

        val valueProvider =
            ContextPlaceholderValueProvider(contextToJsonObjectBuilder.copy(cacheKey = cacheKey), context)

        val populatedVariable = VariableInstance.Variable(
            value = variable.get(),
            type = VariableDataType.fromString(
                simplePlaceHolderReplacer.replacePlaceholders(
                    variable.type.typeDef, valueProvider
                )
            ),
            identifier = simplePlaceHolderReplacer.replacePlaceholders(variable.identifier, valueProvider),
            properties = jsonPlaceholderReplacer.replacePlaceholders(variable.properties, valueProvider),
        )
        logger.trace("Populated expression `{}` for `{}`", variable.get(), variable.identifier)
        return populatedVariable
    }
}