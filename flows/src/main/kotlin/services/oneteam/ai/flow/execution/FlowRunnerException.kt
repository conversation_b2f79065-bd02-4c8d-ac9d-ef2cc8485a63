package services.oneteam.ai.flow.execution

class FlowRunnerException : RuntimeException {
    constructor(message: String) : super(message)
    constructor(message: String, cause: Throwable) : super(message, cause)
    constructor(cause: Throwable) : super(cause)
    constructor() : super()
}

class FlowCancellationException: RuntimeException{
    constructor(message: String) : super(message)
    constructor(message: String, cause: Throwable) : super(message, cause)
}