package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.util.*

@Serializable
internal object Count : ComputeFunction {
    override val key = "count"
    override val syntax = Syntax(
        "COUNT(array)", listOf(
            Argument("array", "The array to count", "array")
        )
    )
    override val description = "Count the number of elements in an array"
    override val notes = "one of the basic array functions, to count the number of elements in an array"
    override val category = "array"
    override val icon = Icon("function")
    override val functionName = "COUNT"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args.first().let {
            when (it) {
                is List<*> -> it.size
                else -> 0
            }
        }
    }, "<a:n>")
}
