package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object DoesNotContain : ComputeFunction {
    override val key = "doesNotContain"
    override val syntax = Syntax(
        "DOES_NOT_CONTAIN(value, searchString)", listOf(
            Argument("value", "The string in which to search for the specified substring.", "string"),
            Argument("searchString", "The substring to search for within the value string.", "string")
        )
    )
    override val description = "Check if the given search string is not contained in the value string"
    override val notes = ""
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "DOES_NOT_CONTAIN"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args: MutableList<Any?>? ->
        !(args!![0] as String).contains(args[1] as String)
    }, "<ss:b>")
}
