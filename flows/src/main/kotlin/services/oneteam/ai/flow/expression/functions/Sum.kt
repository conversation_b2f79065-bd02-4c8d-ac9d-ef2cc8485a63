package services.oneteam.ai.flow.expression.functions

import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import services.oneteam.ai.shared.extensions.toBigDecimalList
import java.math.BigDecimal

/**
 * $SUM
 * https://help.hcl-software.com/dom_designer/12.0.0/basic/H_OPERATIONS_ON_LISTS.html
 *
 *
 * if many single numbers given, add them all together
 *
 * 1,2,3 => 6
 *
 * if a single array of numbers given, add them together to return a single number
 *
 * [1,2,3] => 6
 *
 * if multiple arrays of numbers given, zip-add them together to return an array of numbers
 *
 * [1,2,3],[1,2,3] => [2,4,6]
 *
 * if arrays of arrays of numbers given, zip and add together
 *
 * [[1,2,3],[1,2,3]], [[1,2,3],[1,2,3]] → [[2,4,6],[2,4,6]],[[2,4,6],[2,4,6]])
 *
 * If the dimensions of an input differs, the lower dimension applies to everything in the dimension above
 *
 * [1,2,3], 2 => [2,4,5]
 * [[1,2],[3,4]], 1
 * → [[1,2]+1, [3,4]+1]
 * → [[2,3], [4,5]
 * [[1,2],[3,4]], [1,2]
 * → [[1,2]+[1,2] , [3,4]+[1,2]]
 * → [[2,4], [4,6]]
 *
 * If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty)
 * [1,2,3], [1,2] => [2,4,3]
 *
 * final result is reflective of the highest dimension where things are applied left-to-right
 * [1,2,3], 4, [[5,6]]
 * → [1+4+[5,6], 2+4+[0,0], 3+4+[0,0]]
 * → [5+[5,6], [6+[0,0], 7+[0,0]]
 * → [[5+5, 5+6], [6+0,6+0], [7+0, 7+0]]
 * → [[10,11], [6,6], [7,7]]
 */
@Serializable
internal object Sum : ComputeFunction {

    val DEFAULT_VALUE: BigDecimal = BigDecimal.ZERO

    override val key = "sum"
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "SUM"

    override val syntax = Syntax(
        "$functionName(number, ... )", listOf(
            Argument("number", "Numbers (or Tensors) to sum", "number"),
            Argument("...", "Additional numbers (or Tensors) to sum", "number"),
        )
    )
    override val description = "Sums the Numbers (or Tensors) together"
    override val notes = """
        A Tensor can be a single number (scalar), an array of numbers (vector), or a matrix of numbers.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()
    override val examples = listOf(
        Example(
            "$$functionName(1,2,3) => 6",
            "Sums all the numbers together",
        ),
        Example(
            "$$functionName([1,2,3],[1,2,3]) => [2,4,6]",
            "Pairwise sums two lists of numbers together",
        ),
        Example(
            "$$functionName([1,2,3], 5) => [6,7,8]",
            "Lower dimension is uplifted (5 -> [5,5,5]) then pairwise added",
        ),
        Example(
            "$$functionName([1,2,3], [4,5]) => [5,7,3]",
            "Pairwise sums two lists of numbers together, shorter list is padded with 0",
        ),
        Example(
            "$$functionName([1, 2], [[3, 4, 5], [6, 7, 8]])",
            """
                Dimension uplift and padded then pairwise addition.
                [1,2] + [[3,4,5], [6,7,8]] => [[4,6,5], [7,9,8]]
            """.trimIndent(),
        ),
    )


    val implementations: List<MatchableFunction> = listOf(
        FuzzySum,
        FuzzyList(FuzzySum),
        Pairwise(PairwiseOperation.SUM, DEFAULT_VALUE),
    )

    @Transient
    override val evaluatorFunction = buildJFunctionForImplementations(
        implementations, "<x+:x>",
        functionName
    )

}

object FuzzySum : MatchableFunction {
    override fun match(args: List<*>): Boolean {
        return args.all { it !is List<*> }
    }

    override fun perform(args: List<*>): Number {
        var res = BigDecimal.ZERO
        args.toBigDecimalList().forEach { res = res.add(it) }
        return res.convertToNiceBigDecimal()
    }
}

