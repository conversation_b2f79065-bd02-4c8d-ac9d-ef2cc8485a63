package services.oneteam.ai.flow.execution.listeners

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.pubsub.*
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.manualKeys
import services.oneteam.ai.shared.domains.workspace.Workspace

class FlowListenerNotifier(val executionId: FlowExecution.Id, val executionDocumentId: FlowExecution.DocumentId) :
    FlowRunListener, FlowStepListener {

    val logger: Logger = LoggerFactory.getLogger(javaClass)
    var lastStartedStep: FlowExecution.Step? = null


    override suspend fun onFlowFinished(
        flowExecution: FlowExecution.ForJson, cancellationInformation: FlowExecution.Properties.Cancellation?
    ) {

        val workspaceId = flowExecution.context.event?.workspaceId
        val eventProperties = flowExecution.context.event?.eventProperties

        val userId = when (eventProperties) {
            is Event.EventProperties.StartFlowManuallyFromFormProperties -> eventProperties.userId
            is Event.EventProperties.StartFlowManuallyFromFoundationProperties -> eventProperties.userId
            else -> null
        }
        val buttonLabel = when (eventProperties) {
            is Event.EventProperties.StartFlowManuallyFromFormProperties -> eventProperties.buttonLabel
            is Event.EventProperties.StartFlowManuallyFromFoundationProperties -> eventProperties.buttonLabel
            else -> return
        }

        if (userId == null) {
            return
        }
        if (workspaceId == null) {
            return
        }

        //only sending if the flow was started manually
        //https://oneteam-services.atlassian.net/browse/OA-2005
        if (manualKeys.contains(flowExecution.context.event?.eventProperties?.key)) {

            when (flowExecution.state.result) {
                FlowExecution.Result.PENDING -> {
                    throw IllegalStateException("Flow finished in ${FlowExecution.Result.PENDING.name} state")
                }

                FlowExecution.Result.CANCELLED -> { //no requirements for cancelled notifs rn
                }

                FlowExecution.Result.SUCCESS -> {
                    successMessage(userId, workspaceId, executionId)
                }

                FlowExecution.Result.FAILED -> {
                    failMessage(
                        userId,
                        workspaceId,
                        executionDocumentId,
                        buttonLabel ?: "Flow",
                        lastStartedStep?.name ?: "unnamed step",
                        flowExecution.state.message ?: "unknown error"
                    )

                }
            }
        }
    }

    override suspend fun onFlowStarted(flowExecution: FlowExecution.ForJson) {

    }

    override suspend fun onStepStarted(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {
        lastStartedStep = step
    }

    override suspend fun onStepFinished(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {

    }

    override suspend fun onStepUpdated(step: FlowExecution.Step) {

    }
}

private fun flowStartedMessage(
    userId: Long,
    workspaceId: Workspace.Id,
    executionId: FlowExecution.Id,
) {
    PubSubService.sendToUserForWorkspace(
        userId, workspaceId, WebSocketMessage(
            MessageType.Notification, NotificationMessage(
                heading = Message(key = "ui.notifications.notification"),
                description = Message(key = "ui.flow.queuedSuccessfully"),
                toastNotificationVariant = ToastNotificationVariant.INFO,
                action = Action.ViewInFlowRunner(executionId),
            )
        )
    )
}

private fun successMessage(userId: Long, workspaceId: Workspace.Id, executionId: FlowExecution.Id) {
    PubSubService.sendToUserForWorkspace(
        userId, workspaceId, WebSocketMessage(
            MessageType.Notification, NotificationMessage(
                heading = Message(key = "ui.notifications.success"),
                description = Message(key = "ui.flow.result.SUCCESS"),
                toastNotificationVariant = ToastNotificationVariant.SUCCESS,
                action = Action.ViewInFlowRunner(executionId),
            )
        )
    )
}

private fun failMessage(
    userId: Long,
    workspaceId: Workspace.Id,
    documentId: FlowExecution.DocumentId,
    buttonLabel: String,
    lastStartedStep: String,
    errorMessage: String
) {
    PubSubService.sendToUserForWorkspace(
        userId, workspaceId, WebSocketMessage(
            MessageType.Notification, NotificationMessage(
                heading = Message(key = "ui.notifications.warning"),
                description = Message(
                    key = "ui.flow.failedInQueue", context = mapOf(
                        "buttonLabel" to (buttonLabel), "failedAt" to (lastStartedStep), "error" to (errorMessage)
                    )
                ),
                toastNotificationVariant = ToastNotificationVariant.DANGER,
                action = Action.ViewExecutionDocument(documentId),
            )
        )
    )
}