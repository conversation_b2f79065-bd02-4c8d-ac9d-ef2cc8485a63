package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Date : ComputeFunction {
    override val key = "date"
    override val syntax = Syntax(
        "DATE(year, month, day)", listOf(
            Argument("year", "The year", "number"),
            Argument("month", "The month", "number"),
            Argument("day", "The day", "number")
        )
    )
    override val description = "Create a date"
    override val notes = "one of the basic date functions, to create a date"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "DATE"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val year = args[0] as Int
        val month = args[1] as Int
        val day = args[2] as Int
        java.time.LocalDate.of(year, month, day).toString()
    }, "<nnn:s>")
}
