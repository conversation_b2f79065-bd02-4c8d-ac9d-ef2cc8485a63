package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Or : ComputeFunction {
    override val key = "or"
    override val syntax = Syntax(
        "OR(condition1, condition2, ...)", listOf(
            Argument("condition1", "The first condition to evaluate", "boolean"),
            Argument("condition2", "The second condition to evaluate", "boolean"),
            Argument("...", "Additional conditions to evaluate", "boolean")
        )
)
    override val description = "Logical OR"
    override val notes = "Returns true if any of the conditions are true"
    override val category = "logical"
    override val icon = Icon("function")
    override val functionName = "OR"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args!!.any { parseBooleanArg(it) }
    }, "<x+:b>")
}
