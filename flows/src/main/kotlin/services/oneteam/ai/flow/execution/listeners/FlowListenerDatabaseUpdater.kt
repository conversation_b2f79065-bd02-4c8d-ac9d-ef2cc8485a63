package services.oneteam.ai.flow.execution.listeners

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.execution.FlowExecutionRepository

class FlowListenerDatabaseUpdater(
    private val flowExecutionRepository: FlowExecutionRepository,
    private val flowExecutionId: FlowExecution.Id
) : FlowRunListener {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun onFlowStarted(flowExecution: FlowExecution.ForJson) {
        flowExecutionRepository.update(flowExecutionId.value) {
            it.status = flowExecution.state.status
            it.result = flowExecution.state.result
            it.startedAt = flowExecution.state.metadata.startedAt
        }
    }

    override suspend fun onFlowFinished(
        flowExecution: FlowExecution.ForJson,
        cancellationInformation: FlowExecution.Properties.Cancellation?
    ) {
        flowExecutionRepository.update(flowExecutionId.value) {
            it.status = flowExecution.state.status
            it.properties = it.properties.copy(
                cancellation = cancellationInformation
            )
            it.result = flowExecution.state.result
            it.finishedAt = flowExecution.state.metadata.finishedAt
        }
    }
}