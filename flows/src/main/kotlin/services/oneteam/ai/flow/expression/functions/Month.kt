package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Month : ComputeFunction {
    override val key = "month"
    override val syntax = Syntax(
        "MONTH(date)", listOf(
            Argument("date", "The date to get the month from", "date")
        )
    )
    override val description = "Get the month of the year"
    override val notes = "one of the basic date functions, to get the month of the year"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "MONTH"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        java.time.LocalDate.parse(args.first().toString()).monthValue
    }, "<s:n>")
}
