package services.oneteam.ai.flow.execution

import services.oneteam.ai.shared.domains.tenant.Tenant

enum class FEDRepositoryType {
    AUTOMERGE,
    BLOB
}

class DynamicFEDRepository(
    val defaultRepository: FEDRepositoryType,
    val automergeFEDRepository: AutomergeFEDRepository,
    val blobStorageFEDRepository: BlobStorageFEDRepository
) : FEDRepository {

    private val repositories = mapOf(
        FEDRepositoryType.AUTOMERGE to automergeFEDRepository,
        FEDRepositoryType.BLOB to blobStorageFEDRepository
    )

    override suspend fun create(tenant: Tenant, flowExecution: FlowExecution.ForJson): FlowExecution.DocumentId {
        return repositories[defaultRepository]?.create(tenant, flowExecution)
            ?: throw IllegalArgumentException("No repository found for type: $defaultRepository")
    }

    override suspend fun get(documentId: FlowExecution.DocumentId): FlowExecution.ForJson {
        return if (blobStorageFEDRepository.match(documentId)) {
            blobStorageFEDRepository.get(documentId)
        } else {
            automergeFEDRepository.get(documentId)
        }
    }

    suspend fun getViewURL(documentId: String): String {
        return if (blobStorageFEDRepository.match(FlowExecution.DocumentId(documentId))) {
            (blobStorageFEDRepository.getFedUrl(FlowExecution.DocumentId(documentId)))
        } else {
            automergeFEDRepository.getFedUrl(FlowExecution.DocumentId(documentId))
        }
    }
}