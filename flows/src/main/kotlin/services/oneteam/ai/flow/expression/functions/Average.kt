package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal

@Serializable
internal object Average : ComputeFunction {
    override val key = "average"
    override val syntax = Syntax(
        "AVERAGE(array)", listOf(
            Argument("array", "The array of numbers to average", "array")
        )
    )
    override val description = "Calculate the average of an array of numbers"
    override val notes = ""
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "AVERAGE"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val values = args!![0] as List<*>
        val mappedValues = values.map { it.toString().toDouble() }
        mappedValues.average().toBigDecimal().convertToNiceBigDecimal()
    }, "<x:n>")
}
