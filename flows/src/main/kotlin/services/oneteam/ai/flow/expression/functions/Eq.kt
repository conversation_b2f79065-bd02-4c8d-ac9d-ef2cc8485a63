package services.oneteam.ai.flow.expression.functions

import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation

/**

$EQ(any, any)

Checks if a value is equal to another value

$EQ(1, 1) = true

$EQ(1, 2) = false

$EQ([10, 10], [20, 10]) = [false, true]

$EQ([10, 0], 10) = [true, false]

$EQ([10, 0], [10]) = [true, true]

NB: this is due to [10] → [10, nil] → [10. 0]

 */
@Serializable
internal object Eq : ComputeFunction {

    const val DEFAULT_VALUE: String = ""

    override val key = "equalsTo"
    override val category = "logical"
    override val icon = Icon("function")
    override val functionName = "EQ"

    override val syntax = Syntax(
        "$functionName(any, ... )", listOf(
            Argument("any", "Any (or Tensors) to compare", "any"),
            Argument("...", "Additional any (or Tensors) to compare", "any"),
        )
    )
    override val description = "Compare the values (or Tensors) together"
    override val notes = """
        A Tensor can be a single value (scalar), an array of values (vector), or a matrix of values.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()
    override val examples = listOf(
        Example(
            "$$functionName(1, 1) = true",
            "Compare values",
        ),
        Example(
            "$$functionName(1, 2) = false",
            "Compare values",
        ),
        Example(
            "$$functionName([10, 10], [20, 10]) = [false, true]",
            "Compare lists of values",
        ),
        Example(
            "$$functionName([10, 0], 10) = [true, false]",
            """
                Dimension uplift then pairwise comparison.
                [10, 0], [10] => [10, 0] , [10, 10]
            """.trimIndent(),
        ),
        Example(
            "$$functionName([10, 0], [10]) = [true, true]",
            """
                Dimension padded then pairwise comparison.
                [10, 0], [10] => [10, 0] , [10, 0]
            """.trimIndent(),
        ),
        Example(
            "$$functionName([“aaa”, “bbb”], [“00”, “bbb”]) = [false, true]",
            """
                String compare
            """.trimIndent(),
        ),
    )

    val implementations: List<MatchableFunction> = listOf(
        FuzzyEq,
        FuzzyList(FuzzyEq),
        Pairwise(PairwiseOperation.EQ, DEFAULT_VALUE),
    )

    @Transient
    override val evaluatorFunction = buildJFunctionForImplementations(
        implementations, "<x+:x>",
        functionName
    )

}

object FuzzyEq : MatchableFunction {
    override fun match(args: List<*>): Boolean {
        return args.all { it !is List<*> }
    }

    override fun perform(args: List<*>): Boolean {
        if (args.isEmpty()) return false
        return args.all { it == args.first() }
    }
}