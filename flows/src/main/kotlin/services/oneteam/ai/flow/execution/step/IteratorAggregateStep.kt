package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.jsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration

class IteratorAggregateStep(
    val step: FlowExecution.Step,
    val flowRunner: FlowRunner,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
    val stepTypeConfiguration: FlowStepTypeConfiguration
) : ExecutionStep {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun populate(context: FlowContextWithLocalStep) {
        logger.trace("Populating iterator step")
        processInputs(context, contextToJsonObjectBuilder, step, stepTypeConfiguration)
    }

    override suspend fun execute(mainContext: FlowContextWithLocalStep): NextStepId? {

        val start = step.properties.configuration?.start ?: return null
        val steps = step.properties.configuration.steps ?: return null

        // get the iterable list from the context
        val list = (mainContext.thisStep["list"] as? JsonArray)
            ?: throw FlowRunnerException("Expected a JsonArray for 'list' but got ${mainContext.thisStep["list"]?.javaClass?.canonicalName}")

        logger.trace("Iterating over {}", list)

        // set up output variable
        val outputVariableName = mainContext.thisStep["resultVariableName"]?.jsonPrimitive?.content
            ?: throw FlowRunnerException("Expected a `resultVariableName` to be defined")
        val outputVariableType = mainContext.thisStep["resultVariableType"]?.jsonPrimitive?.content
            ?: throw FlowRunnerException("Expected a `resultVariableType` to be defined")

        mainContext.flowContext.set(
            VariableDefinition.Variable(
                TypeToJsonElementConverter.defaultByType(outputVariableType),
                outputVariableType,
                outputVariableName,
                null
            )
        )

        for ((index, item) in list.withIndex()) {
            val iterationIndex = index + 1 // 1-based index

            logger.trace("Iterating over item index {} / item {}", iterationIndex, item)
            val path = "steps[${step.id.value}].subFlows[${step.id.value}_$iterationIndex]"
            logger.trace("Using path {}", path)

            val iterationContext = mainContext.deepCopy()

            // variables for this loop - using dots in these names will break jsonPath
            setUpIterationVariables(step, iterationIndex, iterationContext, item)

            val stepExecutionDocument = FlowExecution.ForJson(
                context = iterationContext.flowContext,
                state = FlowExecution.State(),
                start = FlowExecution.Step.Id(start.value),
                steps = steps.entries.associate { (id, step) ->
                    FlowExecution.Step.Id(id.value) to step.toExecution()
                }.toMutableMap(),
            )

            step.subFlows.put("${step.id.value}_$iterationIndex", stepExecutionDocument)

            logger.trace("Starting iterator flow runner with stepExecutionDocument {}", stepExecutionDocument)

            try {
                val nestedFlowRunner =
                    flowRunner.spawnNestedFlowRunner(iterationContext.flowContext, stepExecutionDocument, path)
                flowRunner.updateSubFlowListenerFactory(path)
                nestedFlowRunner.start()

                if (stepExecutionDocument.state.result == FlowExecution.Result.FAILED) {
                    throw FlowRunnerException("Iterator flow runner failed")
                }
            } finally {
                logger.trace("Finished iterator flow runner with stepExecutionDocument {}", stepExecutionDocument)
            }

            syncValuesToMainContext(
                iterationContext = iterationContext,
                mainContext = mainContext
            )
        }

        return step.next
    }
}