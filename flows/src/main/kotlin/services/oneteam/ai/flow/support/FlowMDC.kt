package services.oneteam.ai.flow.support

import org.slf4j.MDC
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.shared.domains.tenant.Tenant

private const val FLOW_EXECUTION_ID = "flowExecutionId"
private const val FLOW_CONFIGURATION_ID = "flowConfigurationId"
private const val TENANT = "tenant"

/**
 * Utility object to manage MDC (Mapped Diagnostic Context) for flow execution.
 *
 * This object provides a method to set the flow execution ID in the MDC context,
 * which can be useful for logging and tracing purposes.
 */
object FlowMDC {
    fun withFlowExecutionId(flowExecutionId: FlowExecution.Id) {
        MDC.put(FLOW_EXECUTION_ID, flowExecutionId.value.toString()) // Replace with actual flow execution ID logic
    }

    fun withFlowConfigurationId(flowConfigurationId: String) {
        MDC.put(FLOW_CONFIGURATION_ID, flowConfigurationId)
    }

    fun withTenant(tenant: Tenant) {
        MDC.put(TENANT, tenant.name)
    }

    fun clear() {
        MDC.remove(FLOW_CONFIGURATION_ID)
        MDC.remove(FLOW_EXECUTION_ID)
        MDC.remove(TENANT)
    }

}