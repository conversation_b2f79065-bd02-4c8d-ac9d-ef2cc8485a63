package services.oneteam.ai.flow.support

import org.slf4j.MDC

/**
 * Utility object to manage MDC (Mapped Diagnostic Context) for flow execution.
 *
 * This object provides a method to set the flow execution ID in the MDC context,
 * which can be useful for logging and tracing purposes.
 */
object FlowMDC {
    fun withFlowExecutionId(flowExecutionId: Long) {
        MDC.put("flowExecutionId", flowExecutionId.toString()) // Replace with actual flow execution ID logic
    }
}