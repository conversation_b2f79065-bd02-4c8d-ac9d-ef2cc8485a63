package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object And : ComputeFunction {
    override val key = "and"
    override val syntax = Syntax(
        "AND(condition1, condition2, ...)", listOf(
            Argument("condition1", "The first condition to evaluate", "boolean"),
            Argument("condition2", "The second condition to evaluate", "boolean"),
            Argument("...", "Additional conditions to evaluate", "boolean")
        )
    )
    override val description = "Logical AND"
    override val notes = "Returns true if all of the conditions are true"
    override val category = "logical"
    override val icon = Icon("function")
    override val functionName = "AND"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args!!.all { parseBooleanArg(it) }
    }, "<x+:b>")
}
