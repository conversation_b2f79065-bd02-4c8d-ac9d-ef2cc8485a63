package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Find : ComputeFunction {
    override val key = "find"
    override val syntax = Syntax(
        "FIND(find_text, within_text, [start_num])", listOf(
            Argument("find_text", "The text you want to find", "string"),
            Argument("within_text", "The text containing the text you want to find", "string"),
            Argument("start_num", "Specifies the character at which to start the search", "number")
        )
    )
    override val description =
        "Locates one text string within a second text string, returning the starting position"
    override val notes = "Counts each character as 1 regardless of byte size."
    override val category = "text"
    override val icon = Icon("function")
    override val functionName = "FIND"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val findText = args!![0].toString()
        val withinText = args[1].toString()
        val startNum = if (args.size > 2 && args[2] != null) {
            when (args[2]) {
                is Number -> args[2] as Int
                else -> args[2].toString().toIntOrNull() ?: 1
            }
        } else 1
        val index = withinText.indexOf(findText, startNum - 1)
        if (index >= 0) index + 1 else throw IllegalArgumentException("FIND: substring not found")
    }, "<ssn?:n>")
}
