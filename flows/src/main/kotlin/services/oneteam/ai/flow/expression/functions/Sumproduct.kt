package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Sumproduct : ComputeFunction {
    override val key = "sumproduct"
    override val syntax = Syntax("SUMPRODUCT(array1, array2)", listOf(
        Argument("array1", "The first array", "array"),
        Argument("array2", "The second array", "array")
    ))
    override val description = "Returns the sum of the products of corresponding numbers in two arrays"
    override val notes = "e.g. SUMPRODUCT([1, 2, 3], [4, 5, 6]) returns 32"
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "SUMPRODUCT"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val lhs = (args!![0] as List<*>).map { it.toString().toDouble() }
        val rhs = (args[1] as List<*>).map { it.toString().toDouble() }
        lhs.zip(rhs).sumOf { it.first * it.second }.toBigDecimal().toPlainString()
    }, "<xx:n>")
}
