package services.oneteam.ai.flow.execution.variables

import services.oneteam.ai.flow.execution.VariableDefinition

/**
 * A filter that allows only variables that are included in the provided set of json paths.
 * Only the first part of the path is used for filtering by variable identifier.
 *
 * Given a set of json paths like:
 *  - "a.b.c"
 *  - "d.e"
 *  - "f[1].g"
 *
 *  The filter will only allow variables with identifiers "a" and "d" and "f".
 */
class PathBasedVariableFilter(include: Set<String>) : VariableFilter {

    val includeVariableIdentifiers = include.map { it.split(".").first().split("[").first() }

    override fun filter(variable: VariableDefinition): Boolean {
        return includeVariableIdentifiers.contains(variable.identifier)
    }

}

interface VariableFilter {
    fun filter(variable: VariableDefinition): <PERSON><PERSON><PERSON>
}