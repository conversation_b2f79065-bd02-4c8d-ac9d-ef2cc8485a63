package services.oneteam.ai.flow.execution.step

import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.variables.PathBasedVariableFilter
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.RegExTokenizer
import services.oneteam.ai.shared.extensions.toSimpleValue

class StepJsonataResolver(
    private val context: FlowContextWithLocalStep,
    private val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
    private val expressionEvaluator: JsonataExpressionEvaluator
) {
    val tokenizer = RegExTokenizer()

    suspend fun resolve(expression: String): Any {
        // find placeholders
        val placeholders = tokenizer.tokenize(expression)
        // build filter
        val filter = PathBasedVariableFilter(placeholders.toSet())
        // build context for jsonata evaluation
        // can we do anything to optimise how many times we build the context?
        val contextJsonObject = contextToJsonObjectBuilder.build(context, filter).toSimpleValue()
        // evaluate jsonata expression
        val evaluatedValue = expressionEvaluator.evaluate(
            tokenizer.removePrefixAndSuffix(expression), contextJsonObject ?: emptyMap<Any, Any>()
        )
        return evaluatedValue
    }
}