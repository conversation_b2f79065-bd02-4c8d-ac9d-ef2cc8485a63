package services.oneteam.ai.flow.pubsub

import com.azure.messaging.webpubsub.WebPubSubServiceAsyncClient
import com.azure.messaging.webpubsub.WebPubSubServiceClientBuilder
import com.azure.messaging.webpubsub.models.GetClientAccessTokenOptions
import com.azure.messaging.webpubsub.models.WebPubSubClientAccessToken
import com.azure.messaging.webpubsub.models.WebPubSubContentType
import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonDecoder
import kotlinx.serialization.json.JsonEncoder
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.shared.domains.workspace.Workspace
import java.time.Duration
import kotlin.properties.Delegates

@Serializable
enum class MessageType {
    FlowRunnerUpdate, Notification
}

@Serializable
enum class ToastNotificationVariant {
    INFO, SUCCESS, WARNING, DANGER, NOTIFICATION, LOADING
}

@Serializable
enum class ActionType {
    VIEW_IN_FLOW_RUNNER, VIEW_EXECUTION_DOCUMENT
}

@Serializable(ActionSerializer::class)
sealed class Action {
    abstract val actionType: ActionType

    @Serializable
    data class ViewInFlowRunner(
        val flowExecutionId: FlowExecution.Id,
        @SerialName("actionType") override val actionType: ActionType = ActionType.VIEW_IN_FLOW_RUNNER,
    ) : Action()

    @Serializable
    data class ViewExecutionDocument(
        val executionDocumentId: FlowExecution.DocumentId,
        @SerialName("actionType") override val actionType: ActionType = ActionType.VIEW_EXECUTION_DOCUMENT,
    ) : Action()
}

@Serializable
data class Message(
    val key: String, var context: Map<String, String>? = null
)

@Serializable
data class NotificationMessage(
    val heading: Message,
    val description: Message,
    val toastNotificationVariant: ToastNotificationVariant,
    val action: Action? = null,
)

@Serializable
data class WebSocketMessage(
    val type: MessageType, val notificationMessage: NotificationMessage? = null
)

/**
 * Service to interact with Azure Web PubSub service
 * @property client WebPubSubServiceAsyncClient
 * @property tokenExpirySeconds Expiry of the token before which it should be ‘exchanged’ for a real websocket connection
 * @property logger Logger
 */
object PubSubService {
    private const val CLIENT_NOT_INITIALIZED_ERROR_MESSAGE = "WebPubSubServiceAsyncClient is not initialized"

    private lateinit var client: WebPubSubServiceAsyncClient
    private var tokenExpirySeconds by Delegates.notNull<Long>()
    private val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun initialize(connectionString: String, hubName: String, tokenExpirySeconds: Long) {
        if (connectionString.isEmpty() || hubName.isEmpty()) {
            logger.error("Connection string and hub name must be provided")
            return
        }
        client = WebPubSubServiceClientBuilder().connectionString(connectionString).hub(hubName).buildAsyncClient()
        <EMAIL> = tokenExpirySeconds
    }

    fun generateToken(userId: Long, tenantId: Long, workspaceId: Workspace.Id): WebPubSubClientAccessToken? {
        if (!this::client.isInitialized) {
            logger.error(CLIENT_NOT_INITIALIZED_ERROR_MESSAGE)
            return null
        }

        val options: GetClientAccessTokenOptions =
            GetClientAccessTokenOptions().setUserId(userId.toString()).addGroup(getTenantGroupName(tenantId))
                .addGroup(getWorkspaceGroupName(workspaceId)).addGroup(getUserWorkspaceGroupName(userId, workspaceId))
                .setExpiresAfter(Duration.ofSeconds(tokenExpirySeconds))
        return client.getClientAccessToken(options).doOnNext { token ->
            logger.trace("Generated token: {}", token)
        }.doOnError { error -> logger.warn("Error generating token: $error") }
            .doOnSuccess { logger.trace("Token generation process completed") }.block()
    }

    /**
     * Sends a message to all connected clients. This method is used to broadcast messages to all users regardless of their tenant.
     * We'd very rarely use this method, as most messages are tenant-specific, workspace-specific, or user-specific
     * @param webSocketMessage The message to send
     */
    fun sendToAll(webSocketMessage: WebSocketMessage) {
        if (!this::client.isInitialized) {
            logger.error(CLIENT_NOT_INITIALIZED_ERROR_MESSAGE)
            return
        }
        client.sendToAll(
            Json.Default.encodeToString(webSocketMessage), WebPubSubContentType.APPLICATION_JSON
        ).subscribe(
            { logger.trace("Message sent successfully") },
            { error -> logger.warn("Error sending message: $error") })
    }

    /**
     * Sends a message to a specific user. This method is used to send messages to a user regardless of their workspace.
     * In most cases, you would want to use the `sendToUserForWorkspace` method instead.
     * @param userId The ID of the user to send the message to
     * @param webSocketMessage The message to send
     */
    fun sendToUser(userId: Long, webSocketMessage: WebSocketMessage) {
        if (!this::client.isInitialized) {
            logger.error(CLIENT_NOT_INITIALIZED_ERROR_MESSAGE)
            return
        }
        client.sendToUser(
            userId.toString(), Json.Default.encodeToString(webSocketMessage), WebPubSubContentType.APPLICATION_JSON
        ).subscribe(
            { logger.trace("Message sent to user") },
            { error -> logger.warn("Error sending message to user: $error") })
    }

    /**
     * Sends a message to a specific user for a specific workspace.
     * You'd often use this method since most notifications for a user are workspace-specific.
     * @param userId The ID of the user to send the message to
     * @param workspaceId The ID of the workspace
     * @param webSocketMessage The message to send
     */
    fun sendToUserForWorkspace(userId: Long, workspaceId: Workspace.Id, webSocketMessage: WebSocketMessage) {
        if (!this::client.isInitialized) {
            logger.error(CLIENT_NOT_INITIALIZED_ERROR_MESSAGE)
            return
        }
        client.sendToGroup(
            getUserWorkspaceGroupName(userId, workspaceId),
            Json.Default.encodeToString(webSocketMessage),
            WebPubSubContentType.APPLICATION_JSON,
        ).subscribe(
            { logger.trace("Message sent to user for workspace") },
            { error -> logger.warn("Error sending message to user for workspace: $error") })
    }

    /**
     * Sends a message to a specific tenant. This method is used to send messages to all users in a tenant.
     * @param tenantId The ID of the tenant to send the message to
     * @param webSocketMessage The message to send
     */
    fun sendToTenant(tenantId: Long, webSocketMessage: WebSocketMessage) {
        if (!this::client.isInitialized) {
            logger.error(CLIENT_NOT_INITIALIZED_ERROR_MESSAGE)
            return
        }
        client.sendToGroup(
            getTenantGroupName(tenantId),
            Json.Default.encodeToString(webSocketMessage),
            WebPubSubContentType.APPLICATION_JSON
        ).subscribe(
            { logger.trace("Message sent to tenant group") },
            { error -> logger.warn("Error sending message to tenant group: $error") })
    }

    /**
     * Sends a message to a specific workspace - all connected users will receive the message
     * @param workspaceId The ID of the workspace
     * @param webSocketMessage The message to send
     */
    fun sendToWorkspace(workspaceId: Workspace.Id, webSocketMessage: WebSocketMessage) {
        if (!this::client.isInitialized) {
            logger.error(CLIENT_NOT_INITIALIZED_ERROR_MESSAGE)
            return
        }
        client.sendToGroup(
            getWorkspaceGroupName(workspaceId),
            Json.Default.encodeToString(webSocketMessage),
            WebPubSubContentType.APPLICATION_JSON
        ).subscribe(
            { logger.trace("Message sent to workspace group") },
            { error -> logger.warn("Error sending message to workspace group: $error") })
    }

    private fun getWorkspaceGroupName(workspaceId: Workspace.Id): String {
        return "workspace-${workspaceId.value}"
    }

    private fun getTenantGroupName(tenantId: Long): String {
        return "tenant-$tenantId"
    }

    private fun getUserWorkspaceGroupName(userId: Long, workspaceId: Workspace.Id): String {
        return "user-$userId-workspace-${workspaceId.value}"
    }
}

class ActionSerializer : KSerializer<Action> {
    override val descriptor: SerialDescriptor = JsonObject.serializer().descriptor

    override fun serialize(encoder: Encoder, value: Action) {
        require(encoder is JsonEncoder) { IllegalArgumentException("This serializer only supports JSON encoding") }

        val jsonObject = when (value) {
            is Action.ViewInFlowRunner -> buildJsonObject {
                put("actionType", JsonPrimitive(value.actionType.name))
                put("flowExecutionId", JsonPrimitive(value.flowExecutionId.toString()))
            }

            is Action.ViewExecutionDocument -> buildJsonObject {
                put("actionType", JsonPrimitive(value.actionType.name))
                put("executionDocumentId", JsonPrimitive(value.executionDocumentId.value))
            }
        }
        encoder.encodeJsonElement(jsonObject)
    }

    override fun deserialize(decoder: Decoder): Action {
        require(decoder is JsonDecoder) { IllegalArgumentException("This serializer only supports JSON decoding") }

        val jsonObject = decoder.decodeJsonElement().jsonObject
        val actionType = jsonObject["actionType"]?.jsonPrimitive?.contentOrNull
            ?: throw SerializationException("Missing 'actionType' field")
        return when (ActionType.valueOf(actionType)) {
            ActionType.VIEW_IN_FLOW_RUNNER -> decoder.json.decodeFromJsonElement(
                Action.ViewInFlowRunner.serializer(), jsonObject
            )

            ActionType.VIEW_EXECUTION_DOCUMENT -> decoder.json.decodeFromJsonElement(
                Action.ViewExecutionDocument.serializer(), jsonObject
            )
        }
    }
}