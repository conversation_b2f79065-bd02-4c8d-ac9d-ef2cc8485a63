package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.Json
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * This class is a convenience wrapper around the SimplePlaceHolderReplacer to replace placeholders in JSON objects.
 * It simply encodes the object to a JSON string, replaces the placeholders using SimplePlaceHolderReplacer,
 * and then decodes it back to the original type.
 */
class JsonPlaceholderReplacer {

    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val replacer = SimplePlaceHolderReplacer(NoValueStrategy.BLANK)

    suspend inline fun <reified T> replacePlaceholders(obj: T, valueProvider: PlaceholderValueProvider): T {

        // serialize
        val template = Json.encodeToString(obj)
        // replace
        val populatedTemplate = replacer.replacePlaceholders(template, valueProvider).let {
            logger.trace("Populated template {}", it)
            it
        }
        // deserialize
        return Json.decodeFromString<T>(populatedTemplate)
    }

}