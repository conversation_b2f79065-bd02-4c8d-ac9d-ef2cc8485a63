package services.oneteam.ai.flow.support.pairwise

import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import java.math.BigDecimal

data class PairwiseValue(val value: Any, val dimension: Int) {

    fun upliftToLength(length: Int): PairwiseValue {
        return PairwiseValue(
            List(length) { value }, dimension + 1
        )
    }

    fun valueAtDimension(dim: Int): Any {
        var data = value
        for (i in 1 until dim) {
            if (data is List<*>) {
                data = data[0]!!
            } else {
                return data
            }
        }
        return data
    }

    // only checks the first value of the dimension - expects them all to be the same size
    fun lengthAtDimension(dim: Int): Int {
        // dimension 0 is scalar value, length is 1
        if (dim == 0) {
            return 1
        }

        require(value is List<*>)

        var data = value

        for (i in 0 until dimension - dim) {
            if (data is List<*>) {
                data = data[0]!!
            } else {
                return 1
            }
        }
        return (data as List<*>).size

    }

    companion object {
        fun of(value: Any): PairwiseValue {
            if (value is List<*>) {
                return PairwiseValue(value, dimension(value))
            }
            return PairwiseValue(value, 0)
        }

        // find number of nested lists
        fun dimension(value: Any): Int {
            return dimension(value, 0)
        }

        private fun dimension(value: Any, depth: Int): Int {
            if (value !is List<*> || value.isEmpty()) {
                return depth
            }
            return if (value[0] !is List<*>) {
                depth + 1
            } else dimension(value[0] as List<*>, depth + 1)
        }

        private fun validate(value: Any) {
            // if the value is a list, the elements must either all be lists or all be not lists
            if (value is List<*>) {
                if (value.isEmpty()) return
                val firstElementIsList = value[0] is List<*>
                for (element in value) {
                    if ((element is List<*>) != firstElementIsList) {
                        throw IllegalArgumentException("All elements must be of the same type (all lists or all non-lists)")
                    }
                }
            }
        }

        private fun transformToNumber(value: Any): Any {
            // if the value is a list, transform each element to a BigDecimal, default to BigDecimal.Zero if null or not a number
            return when (value) {
                is List<*> -> value.map { convertToBigDecimal(it) }
                else -> convertToBigDecimal(value)
            }
        }

        private fun convertToBigDecimal(it: Any?): BigDecimal {
            return (it?.toString()?.toBigDecimalOrNull() ?: BigDecimal.ZERO).convertToNiceBigDecimal()
        }
    }
}