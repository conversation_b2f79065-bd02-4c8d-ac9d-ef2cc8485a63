package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.util.*

@Serializable
internal object Lower : ComputeFunction {
    override val key = "lower"
    override val syntax = Syntax(
        "LOWER(string)", listOf(
            Argument("string", "The string to convert to lowercase", "string")
        )
    )
    override val description = "Convert a string to lowercase"
    override val notes = "one of the basic string functions, to convert a string to lowercase"
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "LOWER"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args.first().toString().lowercase(Locale.getDefault())
    }, "<s:s>")
}
