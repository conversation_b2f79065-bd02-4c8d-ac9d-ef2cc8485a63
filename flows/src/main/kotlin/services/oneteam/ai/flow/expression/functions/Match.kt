package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Match : ComputeFunction {
    override val key = "match"
    override val syntax = Syntax("MATCH(value, array)", listOf(
        Argument("value", "The value to look up in the array", "any"),
        Argument("array", "The array to search for the value", "array"),
        Argument("number", "the match type", "number")
    ))

    override val description = ""
    override val notes = ""
    override val category = "lookup"
    override val icon = Icon("function")
    override val functionName = "MATCH"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val value = args[0].toString()
        val inArray = args[1] as List<*>
        val matchType = args[2].toString().toInt()

        val array = inArray.map { it.toString() }

        val result = when (matchType) {
            0 -> array.indexOf(value)
            1 -> array.indexOfFirst { (it as Comparable<Any>) <= value as Comparable<Any> }
            -1 -> array.indexOfLast { (it as Comparable<Any>) >= value as Comparable<Any> }
            else -> throw IllegalArgumentException("Invalid match type")
        }
        // +1 for 1 based index
        result + 1

    }, "<xxx:x>")
}
