package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Mid : ComputeFunction {
    override val key = "mid"
    override val syntax = Syntax("MID(text, start, numChars)", listOf(
        Argument("text", "The text to extract from", "string"),
        Argument("start", "The position to start extracting from", "number"),
        Argument("numChars", "The number of characters to extract", "number")
    ))
    override val description = "Extracts a specified number of characters from a text string, starting at a specified position"
    override val notes = "e.g. MID('Hello, world!', 7, 5) returns 'world'"
    override val category = "text"
    override val icon = Icon("function")
    override val functionName = "MID"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val text = args!![0] as String
        val start = args[1] as Int
        val numChars = args[2] as Int
        text.drop(start - 1).take(numChars)
    }, "<snn:s>")
}
