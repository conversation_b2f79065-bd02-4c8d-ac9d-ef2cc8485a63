package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.jsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance


/**
 * A step that iterates over a list and executes a sub-flow for each item in the list.
 *
 * A for-each configuration could look like:
 *
 * ```json
 * {
 *       "id": "Jdp6LxciQWosjRBKT41pW",
 *       "metadata": {
 *         "createdAt": "2025-03-14T00:40:50.530Z",
 *         "updatedAt": "2025-03-14T00:40:50.530Z"
 *       },
 *       "name": "For each",
 *       "next": "",
 *       "properties": {
 *         "configuration": {
 *           "start": "oxvBCoWoUGfdgWMcrNaF4",
 *           "steps": {
 *             "oxvBCoWoUGfdgWMcrNaF4": {
 *               "id": "oxvBCoWoUGfdgWMcrNaF4",
 *               "metadata": {
 *                 "createdAt": "2025-03-14T01:05:34.592Z",
 *                 "updatedAt": "2025-03-14T01:05:34.592Z"
 *               },
 *               "name": "Set variable(s)",
 *               "next": "",
 *               "properties": {
 *                 "variables": [
 *                   {
 *                     "identifier": "item_output",
 *                     "type": "text",
 *                     "value": "{{item.O1REtNdy50}}"
 *                   }
 *                 ]
 *               },
 *               "variant": "setVariables"
 *             }
 *           }
 *         },
 *         "inputs": {
 *           "isReturn": "true",
 *           "itemVariableName": "item",
 *           "list": "{{form.VEOTahOeLj.answer}}",
 *           "resultVariableName": "output",
 *           "transformedValueType": "json"
 *         },
 *         "typePrimaryIdentifier": "iteratorForEach"
 *       },
 *       "variant": "iterator"
 *     }
 *
 * ```
 *
 * Step type configuration looks something like this:
 * ```json
 *   {
 *     "primaryIdentifier": "iteratorForEach",
 *     "name": "For each",
 *     "description": "",
 *     "type": "iterator",
 *     "properties": {
 *       "icon": {
 *         "name": "repeat"
 *       },
 *       "isLocked": true,
 *       "isHidden": false,
 *       "configuration": {
 *         "content": [
 *           {
 *             "text": "List to iterate over",
 *             "type": "variable",
 *             "identifier": "list",
 *             "properties": {
 *               "type": "list",
 *               "required": true
 *             }
 *           },
 *           {
 *             "text": "List item variable name",
 *             "type": "text",
 *             "identifier": "itemVariableName",
 *             "properties": {
 *               "required": true,
 *               "defaultValue": "item_{{thisStep.id}}",
 *               "regex": "^[a-zA-Z0-9_]*$"
 *             }
 *           },
 *           {
 *             "text": "Output transformed value(s)",
 *             "type": "select",
 *             "identifier": "isReturn",
 *             "properties": {
 *               "required": true,
 *               "options": [
 *                 {
 *                   "value": "true",
 *                   "label": "Yes"
 *                 },
 *                 {
 *                   "value": "false",
 *                   "label": "No"
 *                 }
 *               ],
 *               "defaultValue": "false"
 *             }
 *           },
 *           {
 *             "text": "List item output type",
 *             "type": "variable",
 *             "identifier": "transformedValueType",
 *             "properties": {
 *               "required": true,
 *               "type": "select",
 *               "properties": {
 *                 "dynamicOptions": {
 *                   "tag": "questionTypes",
 *                   "body": {
 *                     "skip": "select,multiSelect,files"
 *                   }
 *                 }
 *               },
 *               "defaultValue": "json"
 *             }
 *           },
 *           {
 *             "text": "Final output variable name",
 *             "type": "text",
 *             "identifier": "resultVariableName",
 *             "properties": {
 *               "required": false,
 *               "defaultValue": "output__step_{{thisStep.id}}",
 *               "regex": "^[a-zA-Z0-9_]*$"
 *             }
 *           }
 *         ],
 *         "variableMappings": [
 *           {
 *             "type": "table",
 *             "identifier": "{{thisStep.resultVariableName}}",
 *             "value": "{{thisStep.result}}"
 *           }
 *         ]
 *       }
 *     }
 *   }
 *   ```
 */
class IteratorForEachStep(
    val step: FlowExecution.Step,
    val flowRunner: FlowRunner,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
    val stepTypeConfiguration: FlowStepTypeConfiguration
) : ExecutionStep {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun populate(context: FlowContextWithLocalStep) {
        logger.trace("Populating iterator step")
        processInputs(context, contextToJsonObjectBuilder, step, stepTypeConfiguration)
    }

    override suspend fun execute(mainContext: FlowContextWithLocalStep): NextStepId? {

        val start = step.properties.configuration?.start ?: return null
        val steps = step.properties.configuration.steps ?: return null

        // get the iterable list from the context
        val list = (mainContext.thisStep["list"] as? JsonArray)
            ?: throw FlowRunnerException("Expected a JsonArray for 'list' but got ${mainContext.thisStep["list"]?.javaClass?.canonicalName}")

        logger.trace("Iterating over {}", list)

        // set up output variable
        val isReturn = mainContext.thisStep["isReturn"]?.jsonPrimitive?.content!! == "true"
        val outputVariableName = mainContext.thisStep["resultVariableName"]?.jsonPrimitive?.content
            ?: throw FlowRunnerException("Expected a `resultVariableName` to be defined")
        val outputVariableType = mainContext.thisStep["transformedValueType"]?.jsonPrimitive?.content
            ?: throw FlowRunnerException("Expected a `transformedValueType` to be defined")
        val itemVariableName = step.properties.inputs["itemVariableName"]?.jsonPrimitive?.content
            ?: throw FlowRunnerException("Expected a `itemVariableName` to be defined")

        val itemMappedOutputName = "${itemVariableName}_output"

        val iteratorOutput = mutableListOf<JsonElement>()

        for ((index, item) in (list).withIndex()) {
            val iterationIndex = index + 1 // 1-based index

            logger.trace("Iterating over item index {} / item {}", iterationIndex, item)
            val path = "steps[${step.id.value}].subFlows[${step.id.value}_$iterationIndex]"
            logger.trace("Using path {}", path)

            val iterationContext = mainContext.deepCopy()

            // variables for this loop - using dots in these names will break jsonPath
            setUpIterationVariables(step, iterationIndex, iterationContext, item)

            // upsert these values into global and then pass into
            val stepExecutionDocument = FlowExecution.ForJson(
                context = iterationContext.flowContext,
                state = FlowExecution.State(),
                start = FlowExecution.Step.Id(start.value),
                steps = steps.entries.associate { (id, step) ->
                    FlowExecution.Step.Id(id.value) to step.toExecution()
                }.toMutableMap(),
            )

            step.subFlows.put("${step.id.value}_$iterationIndex", stepExecutionDocument)

            logger.trace("Starting iterator flow runner with stepExecutionDocument {}", stepExecutionDocument)

            try {
                var nestedFlowRunner =
                    flowRunner.spawnNestedFlowRunner(iterationContext.flowContext, stepExecutionDocument, path)
                flowRunner.updateSubFlowListenerFactory(path)
                nestedFlowRunner.start()

                if (stepExecutionDocument.state.result == FlowExecution.Result.FAILED) {
                    throw FlowRunnerException("Iterator flow runner failed")
                }
            } finally {
                logger.trace("Finished iterator flow runner with stepExecutionDocument {}", stepExecutionDocument)
            }
            // add the output to the iteratorOutput list
            if (isReturn) {
                iteratorOutput.add(iterationContext.flowContext.variables[itemMappedOutputName]?.get() ?: JsonNull)
            }

            syncValuesToMainContext(
                iterationContext = iterationContext, mainContext = mainContext
            )
        }

        if (isReturn) {
            mainContext.flowContext.set(
                VariableInstance.Variable(
                    value = JsonArray(iteratorOutput),
                    type = VariableDataType.fromString(outputVariableType),
                    identifier = outputVariableName,
                    properties = null
                )
            )
        }

        return step.next
    }
}