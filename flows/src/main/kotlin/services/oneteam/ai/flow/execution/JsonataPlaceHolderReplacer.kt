package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.Json
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.expression.JsonPathFromJsonObjectExpressionEvaluator
import services.oneteam.ai.flow.expression.RegExTokenizer

/**
 * A class that replaces jsonPath placeholders in a string with values from a JsonObject,
 * in a way that is compatible with Jsonata.
 *
 * Note, this will only work when given a template string which represents ONLY the jsonata expression.
 * It will not work when given a template string which is part of a larger json object.
 *
 * eg if we have configuration like this:
 * ```
 *   {
 *     "identifier": "AssignmentPolicy",
 *     "type": "text",
 *     "value": "$INDEX({{form.9qAohrlqQ2.columns.0vKOR_mTMA.answer}},{{Assignee_index}})"
 *   }
 * ```
 * we should replace the placeholders one field at a time, e.g. value by itself.
 * ```
 *   $INDEX({{form.9qAohrlqQ2.columns.0vKOR_mTMA.answer}},{{Assignee_index}})
 * ```
 * to get
 * ```
 *  $INDEX([{"id":"0vKOR_mTMA","answer":"Bob"}],1)
 * ```
 * The values inserted need to be json encoded, so that the result is valid json.
 */
class JsonataPlaceHolderReplacer(
    val noValueStrategy: NoValueStrategy = NoValueStrategy.BLANK,
    val quoteStrategy: QuoteStrategy = IdentityStrategy(),
) {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    /**
     * A tokenizer that identifies quoted placeholders in the format "{{key}}".
     * It uses a regular expression to match the pattern.
     */
    val tokenizer = RegExTokenizer("{{", "}}")

    /**
     * Replaces placeholders in the input string with corresponding values from the map.
     *
     * @param input The input string containing placeholders in the format {{key}}.
     * @param valueProvider A provider for a map where keys are placeholder names and values are the corresponding replacement values.
     * @return The input string with placeholders replaced by their corresponding values.
     */
    suspend fun replacePlaceholders(input: String, valueProvider: PlaceholderValueProvider): String {
        // find tokens to replace
        val tokens = tokenizer.tokenize(input)
        val values = valueProvider.valuesForPlaceholders(tokens.toSet())
        val expressionEvaluator = JsonPathFromJsonObjectExpressionEvaluator(values)

        // replace each token
        var result = input
        for (token in tokens) {

            // derive value to use for the token
            var value = expressionEvaluator.evaluate(token)
            if (value == null) {
                logger.warn("No value found for token {} using blank string", token)
                value = noValueStrategy.producer()
            }
            // use json representation of the value to maintain compatibility with json
            val valueAsString = quoteStrategy.handleQuote(Json.encodeToString(value))

            result = tokenizer.replace(result, token, valueAsString)
        }
        return result
    }

}

interface QuoteStrategy {
    fun handleQuote(value: String): String
}

class IdentityStrategy : QuoteStrategy {
    override fun handleQuote(value: String): String {
        return value
    }
}

class EscapeQuoteStrategy : QuoteStrategy {
    override fun handleQuote(value: String): String {
        return value.replace("\"", "\\\"")
    }
}