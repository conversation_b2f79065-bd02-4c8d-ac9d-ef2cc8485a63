package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.datetime.*
import kotlinx.serialization.Serializable

@Serializable
internal object AddMonths : ComputeFunction {
    override val key = "addMonths"
    override val syntax = Syntax(
        "ADDMONTHS(date, months)", listOf(
            Argument("date", "The date to add months to", "string"),
            Argument("months", "The number of months to add", "number")
        )
    )
    override val description = "Add months to a date"
    override val notes = "Add months to a date"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "ADDMONTHS"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val date = args[0].toString()
        val months = args[1].toString().toInt()

        val localDate = LocalDate.parse(date)
        val result = localDate.plus( DatePeriod(months = months) ).toString()

        return@JFunction result
    }, "<sx:s>")
}
