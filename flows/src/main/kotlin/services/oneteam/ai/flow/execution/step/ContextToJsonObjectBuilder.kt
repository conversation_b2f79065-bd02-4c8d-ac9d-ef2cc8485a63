package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.Transient
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.buildJsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.MapBuilder
import services.oneteam.ai.flow.execution.variables.VariableFilter

/**
 * Utility object to convert FlowContextWithLocalStep to JsonObject, using correct types.
 */
data class ContextToJsonObjectBuilder(private val mapBuilders: List<MapBuilder>, val cacheKey: String) {

    @Transient
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    suspend fun build(context: FlowContextWithLocalStep, filter: VariableFilter): JsonObject {
        return buildJsonObject {
            put("event", context.flowContext.event?.toMap() ?: JsonNull)
            put("global", context.flowContext.global.toMap())
            put("thisStep", JsonObject(context.thisStep))

            context.flowContext.variables
                // only include variables that are in the include set
                .filter { filter.filter(it.value) }
                // build map from variable
                .forEach { (key, variable) ->
                    val mapBuilder = mapBuilders.firstOrNull { it.match(variable) }
                    if (mapBuilder != null) {
                        put(key, mapBuilder.handle(variable, cacheKey))
                    } else {
                        logger.warn("Could not find map builder for variable: {}", variable)
                    }
                }
        }.let {
            logger.trace("Context to JsonObject: {}", it)
            it
        }
    }

}
