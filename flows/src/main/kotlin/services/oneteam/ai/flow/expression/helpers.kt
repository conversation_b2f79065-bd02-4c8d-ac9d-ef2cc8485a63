package services.oneteam.ai.flow.expression

fun checkIfEmpty(value: Any?): Boolean {
    return when (value) {
        null -> true // if value == undefined
        is Boolean -> false
        is String -> value.isEmpty()
        is Number -> checkIfEmpty(value.toString())
        is List<*> -> value.all { element -> checkIfEmpty(element) }
        is Map<*, *> -> value.isEmpty()
        else -> true // if value == null and any other types
    }
}