package services.oneteam.ai.flow.execution.listeners

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.execution.VariableDefinition
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.domains.workspace.document.upsertAtPosition

class FlowListenerDocumentUpdater(
    private val documentService: IDocumentService,
    val documentId: FlowExecution.DocumentId,
    val path: String? = "",
    val skipStepUpdates: Boolean,
    val skipVariableUpdates: Boolean,
) : FlowRunListener, FlowStepListener, FlowContextListener {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    var documentStepsLocation = "steps"
    var documentStateStepOrderLocation = "state.steps.order"
    var documentStateStepEntitiesLocation = "state.steps.entities"
    var contextVariablesLocation = "context.variables"

    override suspend fun onFlowStarted(flowExecution: FlowExecution.ForJson) {

        documentService.upsertAtPosition(
            documentId.value,
            null,
            "${path}state",
            flowExecution.state,
        )
    }

    override suspend fun onFlowFinished(
        flowExecution: FlowExecution.ForJson,
        cancellationInformation: FlowExecution.Properties.Cancellation?
    ) {
        if (skipStepUpdates || skipVariableUpdates) {
            // update entire document with the final state
            documentService.update(
                documentId.value,
                null,
                flowExecution,
                "",
                FlowExecution.ForJson::class
            )

        } else {
            // update just the state
            documentService.upsertAtPosition(
                documentId.value,
                null,
                "${path}state",
                flowExecution.state,
            )
        }

        logger.info(
            "View the execution document at http://localhost:8000/ai/api/debug/#automerge:{}",
            documentId.value
        )
    }

    override suspend fun onStepStarted(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {

        // don't update the document, it'll be updated onStepFinish
        logger.trace("Step started: {}, state: {}", step.name, stepState)

    }

    override suspend fun onStepUpdated(step: FlowExecution.Step) {
        // don't update the document, it'll be updated onStepFinish
    }

    override suspend fun onStepFinished(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {
        // update the document with the final state

        logger.trace("Step finished: {}, state: {}", step.name, stepState)

        if (!skipStepUpdates) {
            // add to order array
            documentService.upsertAtPosition(
                documentId.value,
                null,
                "${path}$documentStateStepOrderLocation[]",
                step.id.value
            )

            // add step state
            documentService.upsertAtPosition(
                documentId.value,
                null,
                "${path}$documentStateStepEntitiesLocation[${step.id.value}]",
                stepState
            )

            // update step configuration
            documentService.upsertAtPosition(
                documentId.value,
                null,
                "${path}$documentStepsLocation[${step.id.value}]",
                step
            )
        }
    }

    override suspend fun onVariableSet(value: VariableDefinition.Variable) {
        logger.trace("Context variable set: {}", value)
        if (!skipVariableUpdates) {
            documentService.upsertAtPosition(
                documentId.value,
                null,
                "${path}$contextVariablesLocation[${value.identifier}]",
                value
            )
        }
    }
}
