package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Year : ComputeFunction {
    override val key = "year"
    override val syntax = Syntax(
        "YEAR(date)", listOf(
            Argument("date", "The date to get the year from", "date")
        )
    )
    override val description = "Get the year"
    override val notes = "one of the basic date functions, to get the year"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "YEAR"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        java.time.LocalDate.parse(args.first().toString()).year
    }, "<s:n>")
}
