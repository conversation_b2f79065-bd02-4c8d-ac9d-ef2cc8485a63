package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.text.SimpleDateFormat
import java.util.*

@Serializable
internal object NetworkDays : ComputeFunction {
    override val key = "networkdays"
    override val syntax = Syntax(
        "NETWORKDAYS(startDate, endDate, holidays)", listOf(
            Argument("startDate", "The start date", "string"),
            Argument("endDate", "The end date", "string"),
            Argument("holidays", "The list of holidays", "array")
        )
    )
    override val description = "Calculate the number of working days between two dates"
    override val notes = "Calculate the number of working days between two dates, excluding weekends and holidays"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "NETWORKDAYS"
    val dateFormat = SimpleDateFormat("yyyy-MM-dd")
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->

        val startDate = args[0] as String
        val endDate = args[1] as String
        val holidays = args[2]?.let { it as List<*> }?.map { it as String } ?: emptyList()
        val start = dateFormat.parse(startDate)
        val end = dateFormat.parse(endDate)
        val days = (end.time - start.time) / (1000 * 60 * 60 * 24)
        val weeks = days / 7
        val startDay = start.day
        val endDay = end.day
        val weekDays = weeks * 5
        var daysWithoutWeekends = days - weeks * 2
        if (startDay == 0 || startDay == 6) {
            daysWithoutWeekends -= 1
        }
        if (endDay == 0 || endDay == 6) {
            daysWithoutWeekends -= 1
        }
        val holidaysSet = holidays.toSet()
        val holidaysCount = holidaysSet.count { holiday ->
            val holidayDate = dateFormat.parse(holiday)
            holidayDate.time >= start.time && holidayDate.time <= end.time
        }
        (weekDays + daysWithoutWeekends - holidaysCount).toInt()
    }, "<ssx?:n>")
}
