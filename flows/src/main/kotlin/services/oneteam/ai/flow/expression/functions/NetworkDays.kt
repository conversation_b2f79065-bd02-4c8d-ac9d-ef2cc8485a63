package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.ChronoUnit

@Serializable
internal object NetworkDays : ComputeFunction {
    override val key = "networkdays"
    override val syntax = Syntax(
        "NETWORKDAYS(startDate, endDate, holidays)", listOf(
            Argument("startDate", "The start date", "string"),
            Argument("endDate", "The end date", "string"),
            Argument("holidays", "The list of holidays", "array")
        )
    )
    override val description = "Calculate the number of working days between two dates"
    override val notes = "Calculate the number of working days between two dates, excluding weekends and holidays"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "NETWORKDAYS"
    override val examples = emptyList<Example>()

    /**
     * Calculates the number of working days between two dates,
     * excluding weekends and specified holidays.
     * if start is after end, it returns negative value.
     */
    override val evaluatorFunction = JFunction({ _, args ->
        val inputStartDate = args[0] as String
        val inputEndDate = args[1] as String
        val inputHolidays = args[2]?.let { it as List<*> }?.map { it as String } ?: emptyList()

        val start = LocalDate.parse(inputStartDate)
        val end = LocalDate.parse(inputEndDate)
        val holidays = inputHolidays.map { LocalDate.parse(it) }.toSet()

        calculate(start, end, holidays)
    }, "<ssx?:n>")

    fun calculate(start: LocalDate, end: LocalDate, holidays: Set<LocalDate> = emptySet()): Int {
        val isSwapped = end.isBefore(start)
        val (fromDate, toDate) = if (isSwapped) Pair(end, start) else Pair(start, end)

        var allDays = ChronoUnit.DAYS.between(fromDate, toDate.plusDays(1))

        var totalDays = (0 until allDays).count { dayOffset ->
            val date = fromDate.plusDays(dayOffset)
            !isWeekend(date) && date !in holidays
        }

        return if (isSwapped) -totalDays else totalDays
    }

    private fun isWeekend(date: LocalDate): Boolean {
        return date.dayOfWeek in setOf(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)
    }
}
