package services.oneteam.ai.flow.execution.payload.filter

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.decodeFromJsonElement
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.workspace.BaseSection
import services.oneteam.ai.shared.domains.workspace.CommonQuestionProperties

object JsonSchemaQuestionFilter {

    private val logger: Logger = LoggerFactory.getLogger(javaClass)

    /**
     * Recursively filters a JSON payload based on a given schema.
     * Ensuring that only the keys defined in the schema are retained.
     * Currently, we support filtering JSON objects, arrays and primitives.
     *
     * @param payload The JSON payload to filter.
     * @param schema The schema defining the structure of the payload.
     * @return A filtered JSON element that only contains keys defined in the schema.
     */
    fun filter(payload: JsonElement, schema: BaseSection.BaseQuestion): JsonElement {
        val questionsByIdentifier: Map<String, BaseSection.BaseQuestion> = schema.toIdentifierMap()
        if (questionsByIdentifier.isEmpty()) {
            logger.warn("Schema has no questions defined, returning original payload.")
            return payload // No filtering needed, return original payload // including nested json objects / arrays with no schema
        }

        return when (payload) {
            is JsonPrimitive, JsonNull -> {
                // If the payload is a primitive or null, we return it as is.
                payload
            }

            is JsonObject -> {
                val filtered = payload.filterKeys { it in questionsByIdentifier }
                    .mapValues { (key, value) -> filter(value, questionsByIdentifier.getValue(key)) }
                JsonObject(filtered)
            }

            is JsonArray -> {
                val nextSchema = (schema.properties as? CommonQuestionProperties.ListQuestionProperties)
                    ?.items?.firstOrNull() ?: schema
                JsonArray(payload.map { value -> filter(value, nextSchema) })
            }

        }
    }

    /**
     * Filters the incoming payload based on the expected schema.
     * Returns null if the schema is invalid or if the payload cannot be filtered.
     * This is used to ensure that the payload conforms to the expected structure
     * and types defined in the schema.
     * We use this to filter response payload in Send API Request Action Step
     * We also use this to filter incoming payload in Receive Webhook Trigger Step
     *
     * Before:
     * {
     *   "number_1": 456,
     *   "nested": {
     *     "test": false,
     *     "shouldBeFiltered": "abc"
     *   },
     *   "shouldBeFiltered": "abc"
     * }
     *
     * After:
     * {
     *   "number_1": 456,
     *   "nested": {
     *     "test": false
     *   }
     * }
     *
     *
     * Schema:
     * {
     *   "type": "json",
     *   "properties": {
     *     "items": [
     *       {
     *         "type": "number",
     *         "identifier": "number_1",
     *         "text": "number_1",
     *         "properties": {
     *           "required": false
     *         },
     *         "id": "number_1"
     *       },
     *       {
     *         "type": "json",
     *         "properties": {
     *           "items": [
     *             {
     *               "type": "boolean",
     *               "identifier": "test",
     *               "text": "test",
     *               "properties": {
     *                 "required": false
     *               },
     *               "id": "test"
     *             }
     *           ]
     *         },
     *         "identifier": "nested",
     *         "id": "nested",
     *         "text": "nested"
     *       }
     *     ]
     *   },
     *   "identifier": "a8M9ApCgoQybDKmrZqaFz",
     *   "id": "a8M9ApCgoQybDKmrZqaFz",
     *   "text": "JSON"
     * }
     *
     *
     * @param expectedSchema The expected schema in JSON format.
     * @param incomingPayload The incoming payload to be filtered.
     * @return The filtered payload if it matches the schema, or null if the schema is invalid.
     */
    fun tryFilterPayload(
        expectedSchema: JsonElement,
        incomingPayload: JsonElement
    ): JsonElement? {

        if (expectedSchema == JsonNull || expectedSchema == JsonObject(emptyMap()) || expectedSchema == JsonArray(
                emptyList()
            )
        ) {
            return null
        }
        val schema = try {
            Json.Default.decodeFromJsonElement<BaseSection.BaseQuestion?>(expectedSchema)
        } catch (_: Exception) {
            logger.error(
                "Failed to decode expected schema: {}. Skipping payload filtering.",
                expectedSchema
            )
            // todo: how to propagate error to FED but not break the flow? Should we hard fail
            null
        } ?: return null
        return filter(incomingPayload, schema)
    }
}