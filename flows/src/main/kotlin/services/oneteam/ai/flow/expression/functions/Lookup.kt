package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Functions.isNumeric
import com.dashjoin.jsonata.Jsonata.JFunction
import com.dashjoin.jsonata.Jsonata.JFunctionCallable
import kotlinx.serialization.Serializable

@Serializable
internal object Lookup : ComputeFunction {
    override val key = "lookup"
    override val syntax = Syntax(
        "LOOKUP(lookupValue, lookupArray, [returnArray])", listOf(
            Argument("lookupValue", "The value to look up", "any"),
            Argument("lookupArray", "The array to search", "array"),
            Argument("returnArray", "The array to return the value from", "array")
        )
    )
    override val description = "Performs an approximate lookup in a sorted array"
    override val notes = "Assumes numeric values and ascending order in lookupArray"
    override val category = "lookup"
    override val icon = Icon("function")
    override val functionName = "LOOKUP"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction(JFunctionCallable { _, args ->
        val lookupValue = args!![0]
        val lookupArray = args[1] as List<*>
        val returnArray = if (args.size > 2 && args[2] != null) args[2] as List<*> else lookupArray
        if (lookupArray.isEmpty()) throw IllegalArgumentException(
            "LOOKUP: lookupArray is empty"
        )
        if (isNumeric(lookupValue.toString())) {
            val lookupNum = lookupValue.toString().toDouble()
            var resultIndex = -1
            for (i in lookupArray.indices) {
                val elem = lookupArray[i]
                if (elem !is Number) throw IllegalArgumentException("LOOKUP: lookupArray elements must be numeric")
                if (elem.toDouble() > lookupNum) break
                resultIndex = i
            }
            if (resultIndex == -1) throw IllegalArgumentException("LOOKUP: no element less than or equal to lookupValue")
            returnArray[resultIndex]
        } else {
            val index = lookupArray.indexOf(lookupValue)
            if (index == -1) "#N/A" else returnArray[index]
        }
    }, "<xxx?:x>")
}

