package services.oneteam.ai.flow.execution.payload.filter

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.helpers.getContent

class ProxyResponseFilter(
) {
    fun filterProxyResponseByStatusCode(
        configResponseArr: JsonArray?,
        response: ProxyService.ProxyEndpointResponse
    ): ProxyService.ProxyEndpointResponse {
        if (response.response == null) return response

        val responseJsonObject = parseResponseToJsonObject(response.response) ?: return response
        val payload = responseJsonObject["payload"] ?: return response
        val actualStatusCode = responseJsonObject["status"]?.getContent() ?: return response

        val matchingConfig = ConfigMatcher.findMatchingConfig(configResponseArr, actualStatusCode) ?: return response
        val expectedPayload = matchingConfig["expectedPayload"]?.jsonObject
        val expectedSchema = expectedPayload?.get("schema") ?: return response

        val filteredPayload = JsonSchemaQuestionFilter.tryFilterPayload(expectedSchema, payload) ?: return response
        return constructNewResponse(response, responseJsonObject, filteredPayload)
    }

    private fun parseResponseToJsonObject(response: String?): JsonObject? {
        // response is expected to be a JSON string
        return response?.let { Json.Default.parseToJsonElement(it).jsonObject }
    }

    private fun constructNewResponse(
        response: ProxyService.ProxyEndpointResponse,
        responseJsonObject: JsonObject,
        newPayload: JsonElement
    ): ProxyService.ProxyEndpointResponse {
        val updatedResponse = JsonObject(
            responseJsonObject.toMutableMap().apply { this["payload"] = newPayload }
        )
        return response.copy(response = updatedResponse.toString())
    }
}