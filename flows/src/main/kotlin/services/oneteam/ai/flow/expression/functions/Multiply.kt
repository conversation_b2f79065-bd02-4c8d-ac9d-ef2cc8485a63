package services.oneteam.ai.flow.expression.functions

import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.expression.functions.Sum.DEFAULT_VALUE
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import services.oneteam.ai.shared.extensions.toBigDecimalList
import java.math.BigDecimal

@Serializable
internal object Multiply : ComputeFunction {
    override val key = "multiply"
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "MULTIPLY"

    override val syntax = Syntax(
        "${functionName}(any, ... )", listOf(
            Argument("any", "Any (or Tensors) to compare", "any"),
            Argument("...", "Additional any (or Tensors) to compare", "any"),
        )
    )
    override val description = "Multiply two numbers"
    override val notes = """
        A Tensor can be a single value (scalar), an array of values (vector), or a matrix of values.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()

    override val examples = listOf(
        Example(
            "$${functionName}(6, 2) = 12",
            "Multiply values",
        ),
        Example(
            "$${functionName}([6, 2]) = 12",
            "Multiply values in a list",
        ),
        Example(
            "$${functionName}([1,2,3], [1,2]) = [1, 4, 0]",
            "Multiply pairwise",
        )
    )

    val implementations: List<MatchableFunction> = listOf(
        FuzzyMultiply,
        FuzzyList(FuzzyMultiply),
        Pairwise(PairwiseOperation.MULTIPLY, DEFAULT_VALUE),
    )

    @Transient
    override val evaluatorFunction = buildJFunctionForImplementations(
        implementations, "<x+:x>",
        functionName
    )
}

object FuzzyMultiply : MatchableFunction {
    override fun match(args: List<*>): Boolean {
        return args.all { it !is List<*> }
    }

    override fun perform(args: List<*>): Number {
        var res = BigDecimal.ONE
        args.toBigDecimalList().forEach { res = res.multiply(it) }
        return res.convertToNiceBigDecimal()
    }
}