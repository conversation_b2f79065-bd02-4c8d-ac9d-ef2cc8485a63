package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import java.math.BigDecimal

@Serializable
internal object Multiply : ComputeFunction {
    override val key = "multiply"
    override val syntax = Syntax(
        "MULTIPLY(number1, number2)", listOf(
            Argument("number1", "The first number to multiply", "number"),
            Argument("number2", "The second number to multiply", "number")
        )
    )
    override val description = "Multiply two numbers"
    override val notes = "one of the basic arithmetic functions, to multiply two numbers"
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "MULTIPLY"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        if (args[0] is List<*>) {

            //example: MULTIPLY([1, 2, 3], [4, 5, 6])
            //will end up doing 1*4, 2*5, 3*6 and return [4, 10, 18]

            val list1 = args[0] as List<*>
            val list2 = args[1] as List<*>

            //if one of the input arrays is longer than the shorter array; the shorter one will be padded with 0s
            val longerList = if (list1.size > list2.size) list1 else list2
            val outputList = mutableListOf<BigDecimal>()

            for (i in longerList.indices) {
                val num1 = list1.getOrNull(i)?.toString()?.toBigDecimalOrNull() ?: BigDecimal.ZERO
                val num2 = list2.getOrNull(i)?.toString()?.toBigDecimalOrNull() ?: BigDecimal.ZERO
                outputList.add((num1 * num2).convertToNiceBigDecimal())
            }

            outputList

        } else {
            //if the inputs are two nums
            val number1 = args!![0].toString().toBigDecimal()
            val number2 = args[1].toString().toBigDecimal()
            (number1 * number2).convertToNiceBigDecimal()
        }
    }, "<xx:n>")
}
