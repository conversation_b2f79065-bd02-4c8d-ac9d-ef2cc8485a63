package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata

internal object TextJoin: ComputeFunction {
    override val key = "textJoin"
    override val syntax = Syntax("TEXTJOIN(delimiter, ignore_empty, text1, [text2], ...)", listOf(
        Argument("delimiter", "The delimiter to use between text values", "string"),
        Argument("ignore_empty", "Required: Whether to ignore empty elements. TRUE/true/Yes/1 to ignore, FALSE/false/No/0 to include", "boolean"),
        Argument("text1", "First text item or list to join", "any"),
        Argument("text2", "Additional text items or lists to join", "any")
    ))
    override val description = "Combines text from multiple strings or lists with a delimiter"
    override val notes = "Joins a series of strings/lists with a specified delimiter, with an option to ignore empty items"
    override val category = "String Functions"
    override val icon = Icon("function")
    override val functionName = "TEXTJOIN"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = Jsonata.JFunction({ _, args ->
        if (args == null || args.size < 3) {
            throw IllegalArgumentException("TEXTJOIN requires at least 3 arguments")
        }

        val delimiter = args[0]?.toString() ?: ""
        val ignoreEmpty = parseBooleanArg(args[1])

        // Check if we're dealing with multiple lists for pairwise joining
        val lists = args.drop(2).filter { it is List<*> || it is Array<*> }
        val isListJoiningMode = lists.size >= 2 // Activate list joining mode when we have at least 2 lists

        if (isListJoiningMode) {
            // Convert all arguments to lists for pairwise joining
            val processedLists = args.drop(2).map {
                when (it) {
                    is List<*> -> it.map { item -> item?.toString() ?: "" }
                    is Array<*> -> it.map { item -> item?.toString() ?: "" }
                    else -> listOf(it?.toString() ?: "")
                }
            }

            // Find the maximum size of lists
            val maxSize = processedLists.maxOfOrNull { it.size } ?: 0

            // Join corresponding elements
            val result = mutableListOf<String>()
            for (i in 0 until maxSize) {
                val elements = mutableListOf<String>()
                for (list in processedLists) {
                    // Add the element if it exists, otherwise add empty string
                    val element = if (i < list.size) list[i] else ""
                    elements.add(element)
                }

                // If ignoreEmpty is true, filter out empty elements before joining
                val finalElements = if (ignoreEmpty) {
                    elements.filter { it.isNotEmpty() }
                } else {
                    elements
                }

                result.add(finalElements.joinToString(delimiter))
            }

            return@JFunction result
        } else {
            // for non-list arguments or a single list
            val textItems = mutableListOf<String>()

            // Process all remaining arguments (text items)
            for (i in 2 until args.size) {
                processItem(args[i], textItems, ignoreEmpty)
            }

            return@JFunction textItems.joinToString(delimiter)
        }
    }, "<x+:s>")

    /**
     * Recursively processes items, handling nested lists/arrays.
     * Adds extracted text values to the textItems list.
     */
    private fun processItem(item: Any?, textItems: MutableList<String>, ignoreEmpty: Boolean) {
        val iterable = when (item) {
            is List<*> -> item
            is Array<*> -> item.asList()
            else -> listOf(item)
        }

        for (element in iterable) {
            val textValue = element?.toString() ?: ""
            if (!ignoreEmpty || textValue.isNotEmpty()) {
                textItems.add(textValue)
            }
        }
    }
}