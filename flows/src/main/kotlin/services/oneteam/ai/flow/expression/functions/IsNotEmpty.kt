package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object IsNotEmpty : ComputeFunction {
    override val key = "isNotEmpty"
    override val syntax = Syntax(
        "IS_NOT_EMPTY(value)", listOf(
            Argument("value", "The value to check if not empty", "any")
        )
    )
    override val description = arrayOf(
        "Checks if the given value is not empty"
    ).joinToString("\n")
    override val notes = arrayOf(
        "A value is not empty if:",
        "- It is a non-empty string, number, or boolean.",
        "- It is an object with at least one key or an array with at least one non-empty element.",
        "- It is a deeply nested structure with at least one non-empty element or key."
    ).joinToString("\n")
    override val category = ""
    override val icon = Icon("function")
    override val functionName = "IS_NOT_EMPTY"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args: MutableList<Any?>? ->
        checkIfNotEmpty(args!!.first())
    }, "<x?:b>")

    private fun checkIfNotEmpty(value: Any?): Boolean {
        return when (value) {
            null -> false // if value == undefined
            is Boolean -> true
            is String -> value.isNotEmpty()
            is Number -> checkIfNotEmpty(value.toString())
            is List<*> -> value.any { element -> checkIfNotEmpty(element) }
            is Map<*, *> -> value.isNotEmpty()
            else -> false // if value == null and any other types
        }
    }
}
