package services.oneteam.ai.flow.execution.variables

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.FileOperation
import services.oneteam.ai.shared.domains.flow.variables.Variable
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.flow.variables.VariableProperties
import services.oneteam.ai.shared.helpers.getContent

/**
 * Resolver for file variables.
 *
 * When we have a file variable, we must supply properties with a file operation (SET_FILES, ADD_FILES, REMOVE_FILE).
 */
class FileVariableOperation(
    private val useJsonata: Boolean,
    private val jsonataExpressionEvaluator: JsonataExpressionEvaluator
) : VariableOperation {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override fun match(variable: Variable): Boolean {
        return variable.type == VariableDataType.FILES
    }

    override suspend fun resolve(
        variable: VariableInstance, context: FlowContextWithLocalStep
    ): VariableInstance {

        val fileProperties = variable.properties as VariableProperties.FileVariableProperties

        logger.trace("Executing file operation `{}`", fileProperties.fileOperation)

        // TODO remove useJsonata when we move to using ExecutionStepFactoryV2
        val resultValue = if (useJsonata) jsonataExpressionEvaluator.evaluate(
            variable.get().getContent(), mapOf<Any, Any>()
        ) else variable.get()

        val variableValue = when (fileProperties.fileOperation) {
            FileOperation.SET_FILES -> handleAddFiles(resultValue)
            FileOperation.ADD_FILES -> handleAddFiles(variable, context, resultValue)
            FileOperation.REMOVE_FILES -> handleRemoveFile(variable, context, resultValue)
        }

        logger.trace("Result of file operation is `{}`", Json.encodeToString(variableValue))

        return VariableInstance.Variable(
            value = variableValue,
            type = variable.type,
            identifier = variable.identifier,
            properties = variable.properties
        )
    }

    private fun handleAddFiles(value: Any): JsonElement {
        val valueAsJsonElement = TypeToJsonElementConverter.toJsonElement(value)

        if (valueAsJsonElement is JsonArray) {
            return valueAsJsonElement
        }
        return FlowContext.toJsonElement(listOf(value), VariableDataType.FILES)
    }

    @OptIn(ExperimentalSerializationApi::class)
    private fun handleAddFiles(
        variable: Variable, context: FlowContextWithLocalStep, value: Any
    ): JsonElement {
        val valueAsJsonElement = TypeToJsonElementConverter.toJsonElement(value)
        val existingFilesListValue = context.flowContext.variables[variable.identifier]?.get() ?: buildJsonArray {}

        // if the item added is a single item, we add it, if it's a list we need to flatten+add
        // we can only add to end of list
        val newFilesValue = buildJsonArray {
            addAll(existingFilesListValue.jsonArray)
            if (valueAsJsonElement is JsonArray) {
                addAll(valueAsJsonElement)
            } else {
                add(valueAsJsonElement)
            }
        }

        return newFilesValue
    }

    private fun handleRemoveFile(
        variable: Variable, context: FlowContextWithLocalStep, value: Any
    ): JsonElement {
        val existingFilesListValue = context.flowContext.variables[variable.identifier]?.get() ?: buildJsonArray {}
        val filesListProperties = variable.properties as VariableProperties.FileVariableProperties

        val itemIndex = filesListProperties.itemIndex.toString().toIntOrNull()

        require(itemIndex != null && itemIndex <= existingFilesListValue.jsonArray.size) { "Invalid itemIndex" }

        val newListValue = JsonArray(
            existingFilesListValue.jsonArray.filterIndexed { index, _ ->
                (index + 1) != itemIndex
            })

        return newListValue
    }
}