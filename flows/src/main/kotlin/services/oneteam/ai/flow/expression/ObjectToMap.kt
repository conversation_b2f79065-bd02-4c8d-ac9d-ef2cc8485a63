package services.oneteam.ai.flow.expression

import com.google.gson.GsonBuilder
import com.google.gson.ToNumberPolicy


object ObjectToMap {
    //    https://stackoverflow.com/a/70229243
    val gson = GsonBuilder()
        // prevent integers from being converted to doubles
        .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
        .create()

    fun convert(any: Any): Map<String, Any?> {
        return convertUsingGson(any)
    }

    fun convertToList(any: List<*>): List<Any?> {
        val json = gson.toJson(any)
        return gson.fromJson(json, List::class.java) as List<Any?>
    }

    fun convertUsingGson(any: Any): Map<String, Any?> {
        val json = gson.toJson(any)
        return gson.fromJson(json, Map::class.java) as Map<String, Any?>
    }
}