package services.oneteam.ai.flow.execution.listeners

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.BlobStorageFEDRepository
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.shared.domains.flow.variables.Variable

class FlowListenerBlobDocumentUpdater(
    val fedRepository: BlobStorageFEDRepository,
    val documentId: FlowExecution.DocumentId,
    val skipStepUpdates: <PERSON>olean,
    val skipVariableUpdates: <PERSON><PERSON><PERSON>,
    val skipFlowUpdates: <PERSON>ole<PERSON>,
) : FlowRunListener, FlowStepListener, FlowContextListener {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun onFlowStarted(flowExecution: FlowExecution.ForJson) {
        if (skipFlowUpdates) {
            return; }

        fedRepository.update(documentId, flowExecution)
    }

    override suspend fun onFlowFinished(
        flowExecution: FlowExecution.ForJson, cancellationInformation: FlowExecution.Properties.Cancellation?
    ) {
        if (skipFlowUpdates) {
            return; }
        fedRepository.update(documentId, flowExecution)

        logger.debug("Flow finished: View the FED at Azure blob storage id {}", fedRepository.getFedUrl(documentId))
    }

    override suspend fun onStepStarted(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {

        // don't update the document, it'll be updated onStepFinish
        logger.trace("Step started: {}", step.name)

    }

    override suspend fun onStepUpdated(step: FlowExecution.Step) {
        // don't update the document, it'll be updated onStepFinish
    }

    override suspend fun onStepFinished(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {
        // update the document with the final state

        logger.debug("Step finished: {}", step.name)

        if (!skipStepUpdates) {
            // add to order array
            fedRepository.update(documentId, flowExecution)
        }
    }

    override suspend fun onVariableSet(value: Variable) {
        // can't do anything here, we don't have the full document
    }
}
