package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.support.pairwise.Pairwise
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.flow.support.pairwise.PairwiseValue
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import services.oneteam.ai.shared.extensions.toBigDecimalList
import java.math.BigDecimal

@Serializable
internal object Min : ComputeFunction {
    override val key = "min"
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "MIN"

    override val syntax = Syntax(
        "MIN(number, ... )", listOf(
            Argument("number", "Numbers (or Tensors) to find the minimum value", "number"),
            Argument("...", "Additional numbers (or Tensors) to find the minimum value", "number")
        )
    )
    override val description = "Find the minimum value among the Numbers (or Tensors)"
    override val notes = """
        A Tensor can be a single number (scalar), an array of numbers (vector), or a matrix of numbers.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()
    override val examples = listOf(
        Example(
            "$$functionName(1,2,3) => 1",
            "Returns the minimum of the numbers"
        ),
        Example(
            "$$functionName([1,2,6],[4,5,3]) => [1,2,3]",
            "Pairwise minimum of two lists of numbers"
        ),
        Example(
            "$$functionName([1,2,3], 5) => [1,2,3]",
            "Lower dimension is uplifted (5 -> [5,5,5]) then find pairwise minimum",
        ),
        Example(
            "$$functionName([1,2,3], [4,5]) => [1, 2, 0]",
            "Finds pairwise minimum of two lists of numbers, shorter list is padded with 0",
        ),
        Example(
            "$$functionName([4, 5], [[1, 2, 3], [5, 4, 3]]) => [[1,2,0], [4,4,0]]",
            """
                Dimension uplift and padded then find pairwise minimum.
                => [[4,5,0],[4,5,0]] , [[1,2,3], [5,4,3]] 
                => [[1,2,0], [4,4,0]]
            """.trimIndent(),
        ),
    )


    val implementations: List<MinFunction> = listOf(
        FuzzyMin(),
        FuzzyListMin(),
        MinPairwise()
    )

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->

        return@JFunction implementations.first { it.match(args) }.let {
            return@JFunction (it.min(args))
        }.getOrElse { throw IllegalArgumentException("Invalid arguments for min function") }

    }, "<x+:x>")

}

interface MinFunction {
    fun match(args: List<*>): Boolean
    fun min(args: List<*>): Any
}

class FuzzyListMin : MinFunction {
    override fun match(args: List<*>): Boolean {
        return args.size == 1 && args[0] is List<*>
    }

    override fun min(args: List<*>): Number {
        //if the input is an array
        return (args[0] as List<*>).toBigDecimalList().minOfOrNull { it }?.convertToNiceBigDecimal() ?: BigDecimal.ZERO
    }
}

class FuzzyMin : MinFunction {
    override fun match(args: List<*>): Boolean {
        return args.all { it !is List<*> }
    }

    override fun min(args: List<*>): Number {
        //if the input is an array
        return args.toBigDecimalList().minOfOrNull { it }?.convertToNiceBigDecimal() ?: BigDecimal.ZERO
    }
}

/**
 * if multiple arrays of numbers given, assume we are doing pairwise minimum
 */
class MinPairwise : MinFunction {

    // if all args are arrays, zip them together and find the minimum
    override fun match(args: List<*>): Boolean {
        return true // act as a catch-all
    }

    override fun min(args: List<*>): Any {
        return Pairwise(PairwiseOperation.MIN).pairwise(args.map { PairwiseValue.of(it ?: 0) }).value
    }

}