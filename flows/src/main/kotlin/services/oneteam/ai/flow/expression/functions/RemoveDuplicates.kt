package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata

internal object RemoveDuplicates : ComputeFunction {
    override val key = "removeDuplicates"
    override val syntax = Syntax("REMOVEDUPLICATES(array, [array2...], [returnArray])", listOf(
        Argument("array", "The primary array to remove duplicates from", "array"),
        Argument("array2", "Additional arrays to consider for duplicates (optional)", "array"),
        Argument("returnArray", "The array to return corresponding values from (optional, defaults to the primary array if omitted)", "array")
    ))
    override val description = "Remove duplicate values from multiple arrays and return corresponding values from a return array"
    override val notes = "If only one array is provided, it will be used as both the input and return array"
    override val category = "Array Functions"
    override val icon = Icon("function")
    override val functionName = "REMOVEDUPLICATES"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = Jsonata.JFunction({ _, args ->
        if (args == null || args.isEmpty()) throw IllegalArgumentException("Insufficient arguments")

        // If only one array is provided, use it as both input and return array
        val (inputArrays, returnArray) = if (args.size == 1) {
            val array = args[0] as? List<*> ?: throw IllegalArgumentException("Input is not an array")
            Pair(listOf(array), array)
        } else {
            val returnArray = args.last() as? List<*> ?: throw IllegalArgumentException("returnArray argument is not an array")
            val inputArrays = args.dropLast(1).map { it as? List<*> ?: throw IllegalArgumentException("One of the input arrays is not an array") }
            Pair(inputArrays, returnArray)
        }

        // Validate that all arrays have the same length
        inputArrays.forEach { inputArray ->
            if (inputArray.size != returnArray.size) {
                throw IllegalArgumentException("All input arrays and the return array must have the same length")
            }
        }

        val seen = mutableSetOf<Any?>()
        val resultMap = linkedMapOf<Any?, Any?>()
        for (inputArray in inputArrays) {
            for (i in inputArray.indices) {
                val value = inputArray[i]
                if (value == null || value in seen) {
                    continue
                }
                seen.add(value)
                resultMap.putIfAbsent(returnArray[i], returnArray[i])
            }
        }

        resultMap.values.toList()
    }, "<x+:a<x>>")
}