package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

sealed interface ComputeFunction {
    val key: String
    val syntax: Syntax
    val description: String
    val notes: String
    val examples: List<Example>
    val category: String
    val icon: Icon
    val functionName: String
    val evaluatorFunction: JFunction

    @Serializable
    data class ForJson(
        val key: String,
        val syntax: Syntax,
        val description: String,
        val notes: String,
        val category: String,
        val icon: Icon,
        val functionName: String,
        val examples: List<Example> = emptyList()
    )

    companion object {
        fun convertForJson(computeFunction: ComputeFunction): ForJson {
            return computeFunction.run {
                ForJson(
                    key,
                    syntax,
                    description,
                    notes,
                    category,
                    icon,
                    functionName,
                    examples
                )
            }
        }
    }
}

@Serializable
data class Syntax(
    val signature: String,
    val arguments: List<Argument>
)

@Serializable
data class Argument(
    val name: String,
    val description: String,
    val type: String
)

@Serializable
data class Example(
    val codeSample: String,
    val description: String
)

@Serializable
data class Icon(
    val name: String,
    val fillStyle: FillStyle? = null
) {
    @Serializable
    enum class FillStyle {
        @SerialName("filled")
        FILLED,

        @SerialName("outline")
        OUTLINE
    }
}

abstract class ComputeFunctions {
    companion object {
        val definitions = ComputeFunction::class.sealedSubclasses.mapNotNull { it.objectInstance }
            .associateBy({ it.functionName }, { it })
    }
}
