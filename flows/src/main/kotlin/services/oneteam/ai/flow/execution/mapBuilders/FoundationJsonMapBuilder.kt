package services.oneteam.ai.flow.execution.mapBuilders

import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import services.oneteam.ai.flow.execution.MapBuilder
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace

/**
 * See liveFoundationDataSample.ts for the structure of the foundation JSON
 * See populateRealValuesUsingFlowContext.test.ts
 */
/**
 * See liveFoundationDataSample.ts for the structure of the foundation JSON
 * See populateRealValuesUsingFlowContext.test.ts
 */
class FoundationJsonMapBuilder(
    private val foundationService: FoundationService,
    private val workspace: Workspace.ForJson,
) : MapBuilder {

    override suspend fun match(variable: Variable): Boolean {
        return variable.type.startsWith("foundation")
    }

    var latestCacheKey: String? = null
    var cache: MutableMap<String, List<Any>> = mutableMapOf()
    suspend fun getOrCache(key: String?, foundationId: Long): List<Any> {
        if (latestCacheKey == null || latestCacheKey != key) {
            cache.clear()
        }
        latestCacheKey = key

        val paramKey = "$key-${foundationId}"
        if (cache.containsKey(paramKey)) {
            return cache[paramKey]!!
        }
        cache.containsKey(key)

        val foundation = foundationService.get(Foundation.Id(foundationId))
        val foundationConfiguration = workspace.foundations.entities[foundation.foundationConfigurationId]!!
        val items = listOf(foundation, foundationConfiguration)
        cache[paramKey] = items
        return items
    }

    override suspend fun handle(variable: Variable, cacheKey: String?): JsonElement {
        try {
            val foundationId = variable.value.toString().trim('"').toLong()
            val cached = getOrCache(cacheKey, foundationId)
            val foundation = cached[0] as Foundation.ForApi
            val foundationConfiguration = cached[1] as FoundationConfiguration.ForApi
            return JsonObject(foundation.toMap(foundationConfiguration))
        } catch (_: Exception) {
            return JsonNull
        }
    }
}