package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import com.dashjoin.jsonata.Jsonata.JFunctionCallable
import kotlinx.serialization.Serializable

@Serializable
internal object Xlookup : ComputeFunction {
    override val key = "xlookup"
    override val syntax = Syntax("XLOOKUP(lookupValue, lookupArray, returnArray)", listOf(
        Argument("lookupValue", "The value to look up in the lookupArray", "any"),
        Argument("lookupArray", "The array to search for the lookupValue", "array"),
        Argument("returnArray", "The array to return the value from", "array")
    ))
    override val description = "Look up a value in an array and return a value from another array"
    override val notes = ""
    override val category = "lookup"
    override val icon = Icon("function")
    override val functionName = "XLOOKUP"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction(JFunctionCallable { _, args ->
        val lookupValue = args!![0] as Any
        val lookupArray = args[1] as List<*>
        val returnArray = args[2] as List<*>

        val lookupValueIndex = lookupArray.indexOf(lookupValue)
        if (lookupValueIndex == -1) {
            return@JFunctionCallable "#N/A"
        }
        returnArray[lookupValueIndex]
    }, "<xxx:x>")
}
