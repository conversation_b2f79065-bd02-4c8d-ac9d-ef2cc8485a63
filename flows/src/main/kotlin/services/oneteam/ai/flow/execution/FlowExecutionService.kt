package services.oneteam.ai.flow.execution

import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.encodeToJsonElement
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.json.contains
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowExecution.CancelTriggerType
import services.oneteam.ai.flow.execution.FlowExecution.Properties.TriggerEvent
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.flow.execution.listeners.DefaultListenerFactory
import services.oneteam.ai.flow.execution.mapBuilders.FormJsonMapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.FoundationJsonMapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.SeriesIntervalMapBuilder
import services.oneteam.ai.flow.execution.step.ContextToJsonObjectBuilder
import services.oneteam.ai.flow.execution.step.ExecutionStepFactoryV1
import services.oneteam.ai.flow.execution.step.ExecutionStepFactoryV2
import services.oneteam.ai.flow.expression.conditional.Condition
import services.oneteam.ai.flow.pubsub.*
import services.oneteam.ai.flow.support.FlowMDC
import services.oneteam.ai.shared.domains.NotFoundException
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.EventKey
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfigurationStatusType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVariableConfiguration
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariableService
import services.oneteam.ai.shared.helpers.getContent
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.withTenantTransactionScope
import java.time.Instant
import kotlin.coroutines.coroutineContext

class FlowExecutionService constructor(
    private val flowExecutionRepository: FlowExecutionRepository,
    private val workspaceVariableService: WorkspaceVariableService,
    private val documentService: IDocumentService,
    private val proxyService: ProxyService,
    private val internalProxyService: ProxyService,
    private val filePressService: FilePressService,
    private val workspaceVersionService: WorkspaceVersionService,
    private val formService: FormService,
    private val foundationService: FoundationService,
    private val flowStepTypeConfigurationService: FlowStepTypeConfigurationService,
    private val flowDispatcher: CustomDispatcher,
    private val useExecutionStepFactoryV1: Boolean? = true,
    private val includeLogging: Boolean = true,
    private val skipStepUpdates: Boolean,
    private val skipVariableUpdates: Boolean,
    private val skipSubFlowFlowUpdates: Boolean,
    private val fedRepository: DynamicFEDRepository
) {
    private val logger: Logger = LoggerFactory.getLogger(javaClass)

    suspend fun search(
        pageRequest: PageRequest,
        searchTerm: String?,
        workspaceId: Workspace.Id,
        status: FlowExecution.Status?,
        result: FlowExecution.Result?
    ): Page<FlowExecution.ForApi> = withTenantTransactionScope {
        val searchResult = flowExecutionRepository.search(
            pageRequest, searchTerm, workspaceId.value, status, result
        )
        return@withTenantTransactionScope Page(
            searchResult.page, searchResult.total, searchResult.items.map { it.toDTO() })
    }

    suspend fun get(flowExecutionId: FlowExecution.Id): FlowExecution.ForApi? = withTenantTransactionScope {
        return@withTenantTransactionScope flowExecutionRepository.get { FlowExecutions.id eq flowExecutionId.value }
            ?.toDTO()
    }

    internal suspend fun findSameFlowExecution(
        workspaceId: Workspace.Id, flowConfigurationId: FlowConfiguration.Id, event: Event.ForApi
    ): FlowExecution.ForApi? = withTenantTransactionScope {
        val triggerEvent = TriggerEvent(event.eventProperties.key, event.eventProperties)

        val eventGroupIdMatches = FlowExecutions.properties.contains((buildJsonObject {
            put("eventGroupId", Json.encodeToJsonElement(event.eventGroupId))
        }).toString())
        val eventPropertiesMatches = FlowExecutions.properties.contains((buildJsonObject {
            put("event", buildJsonObject {
                put("eventProperties", Json.encodeToJsonElement(triggerEvent.eventProperties))
            })
        }).toString())

        val flowExecution = flowExecutionRepository.get {
            (FlowExecutions.workspaceId eq workspaceId.value).and(FlowExecutions.flowConfigurationId eq flowConfigurationId.value!!)
                .and(FlowExecutions.status inList listOf(FlowExecution.Status.PENDING, FlowExecution.Status.RUNNING))
                .and(eventGroupIdMatches.or(eventPropertiesMatches))
        }

        return@withTenantTransactionScope flowExecution?.toDTO()
    }

    suspend fun runFlowByExecutionId(
        flowExecutionId: FlowExecution.Id, workspaceId: Workspace.Id
    ): FlowExecution.ForApi? = withTenantTransactionScope {

        val flowExecution = flowExecutionRepository.get {
            (FlowExecutions.status eq FlowExecution.Status.PENDING).and(FlowExecutions.workspaceId eq workspaceId.value)
                .and(FlowExecutions.id eq flowExecutionId.value)
        }

        if (flowExecution == null) {
            logger.debug("Flow execution with ID {} not found or not pending", flowExecutionId)
            return@withTenantTransactionScope null
        }

        return@withTenantTransactionScope runFlowByExecutionEntity(flowExecution, workspaceId)
    }

    suspend fun runOldestFlow(workspaceId: Workspace.Id): FlowExecution.ForApi? = withTenantTransactionScope {

        val flowExecution = flowExecutionRepository.get {
            (FlowExecutions.status eq FlowExecution.Status.PENDING).and(FlowExecutions.workspaceId eq workspaceId.value)
        }

        if (flowExecution == null) return@withTenantTransactionScope null

        return@withTenantTransactionScope runFlowByExecutionEntity(flowExecution, workspaceId)
    }

    private suspend fun runFlowByExecutionEntity(
        flowExecution: FlowExecutionEntity, workspaceId: Workspace.Id
    ): FlowExecution.ForApi? {
        val flowExecutionId = FlowExecution.Id(flowExecution.id.value)
        val flowExecutionDocument = fedRepository.get(FlowExecution.DocumentId(flowExecution.documentId))

        logger.info("Running flow by execution entity with ID: {}", flowExecutionId)

        val workspace =
            workspaceVersionService.findVersion(workspaceId, WorkspaceVersion.Id(flowExecution.workspaceVersionId))

        val listenerFactory = DefaultListenerFactory(
            documentService,
            fedRepository.blobStorageFEDRepository,
            FlowExecution.DocumentId(flowExecution.documentId),
            flowExecutionId,
            flowExecutionRepository,
            "",
            includeLogging,
            skipStepUpdates,
            skipVariableUpdates,
            skipSubFlowFlowUpdates,
            fedRepository.defaultRepository == FEDRepositoryType.AUTOMERGE
        )


        flowExecutionDocument.context.listeners.addAll(listenerFactory.createContextListeners())

        val stepListeners = listenerFactory.createStepListeners()

        val flowRunner = FlowRunner(
            flowExecutionId,
            flowExecutionDocument.context,
            flowExecutionDocument,
            listenerFactory,
            listenerFactory.createFlowListenersForMainFlow(),
            stepListeners,
            this,
            flowExecution.properties.settings.timeoutMins,
            if (useExecutionStepFactoryV1 == true) ExecutionStepFactoryV1(
                flowStepTypeConfigurationService, proxyService, internalProxyService, filePressService, listOf(
                    FoundationJsonMapBuilder(foundationService, workspace.configuration), FormJsonMapBuilder(
                        formService, foundationService, documentService, null, workspace.configuration
                    ), SeriesIntervalMapBuilder(
                        workspace.configuration
                    ), DefaultMapBuilder() // must come last
                ), workspaceVersionService
            ) else ExecutionStepFactoryV2(
                flowStepTypeConfigurationService, proxyService, internalProxyService, filePressService, listOf(
                    FoundationJsonMapBuilder(foundationService, workspace.configuration), FormJsonMapBuilder(
                        formService, foundationService, documentService, null, workspace.configuration
                    ), SeriesIntervalMapBuilder(
                        workspace.configuration
                    ), DefaultMapBuilder() // must come last
                ), workspaceVersionService
            )
        )

        // copy the context across from the original request
        // this will have to change later when using a scheduler
        val tenant = coroutineContext[RequestContext]!!.tenant
        val principal = coroutineContext[RequestContext]!!.principal

        try {
            FlowMDC.withFlowExecutionId(flowExecutionId)
            FlowMDC.withFlowConfigurationId(flowExecution.flowConfigurationId)
            FlowMDC.withTenant(tenant)

            flowDispatcher.run(MDCContext() + RequestContext(tenant, principal)) {
                // notify start so the UI can update
                PubSubService.sendToWorkspace(workspaceId, WebSocketMessage(MessageType.FlowRunnerUpdate))
                // run
                logger.debug("Flow execution started for ID: {}", flowExecutionId)
                try {
                    flowRunner.start()
                } finally {
                    logger.debug("Flow execution finished for ID: {}", flowExecutionId)
                    // notify end so the UI can update
                    PubSubService.sendToWorkspace(workspaceId, WebSocketMessage(MessageType.FlowRunnerUpdate))
                }
            }


            logger.info("Submitted flow execution entity with ID: {}", flowExecutionId)
        } finally {
            FlowMDC.clear()
        }
        return flowExecution.toDTO()
    }

    private fun checkAndSendNotificationToUser(
        createFlowExecutionRequestBody: CreateFlowExecutionRequestBody, message: WebSocketMessage
    ) {
        val eventProperties = createFlowExecutionRequestBody.event.eventProperties
        val userId = when (eventProperties) {
            is Event.EventProperties.StartFlowManuallyFromFormProperties -> eventProperties.userId
            is Event.EventProperties.StartFlowManuallyFromFoundationProperties -> eventProperties.userId
            else -> null
        }
        val buttonLabel = when (eventProperties) {
            is Event.EventProperties.StartFlowManuallyFromFormProperties -> eventProperties.buttonLabel
            is Event.EventProperties.StartFlowManuallyFromFoundationProperties -> eventProperties.buttonLabel
            else -> null
        }
        if (userId == null) {
            return
        }
        message.notificationMessage?.description?.context = mapOf(
            "buttonLabel" to (buttonLabel ?: "Flow")
        )
        val workspaceId = createFlowExecutionRequestBody.event.workspaceId

        if (createFlowExecutionRequestBody.event.shouldNotifyUser) {
            PubSubService.sendToUserForWorkspace(
                userId, workspaceId, message
            )
        }
    }

    suspend fun createIfNoDuplicates(
        workspaceId: Workspace.Id, createFlowExecutionRequestBody: CreateFlowExecutionRequestBody
    ): FlowExecution.ForApi? = withTenantTransactionScope {
        // Create a unique key for current flow execution
        val lockKey =
            "${workspaceId.value}:${createFlowExecutionRequestBody.flowConfigurationId.value}:${createFlowExecutionRequestBody.event.eventGroupId}".hashCode()

        // Acquire an advisory lock
        val connection = TransactionManager.current().connection
        val statement = connection.prepareStatement(
            "SELECT pg_advisory_xact_lock($lockKey)", false
        )
        statement.executeQuery()

        val existingFlowExecution = findSameFlowExecution(
            workspaceId, createFlowExecutionRequestBody.flowConfigurationId, createFlowExecutionRequestBody.event
        )

        if (existingFlowExecution != null) {
            checkAndSendNotificationToUser(
                createFlowExecutionRequestBody, WebSocketMessage(
                    MessageType.Notification, NotificationMessage(
                        heading = Message(key = "ui.notifications.warning"),
                        description = Message(key = "ui.flow.alreadyQueued"),
                        toastNotificationVariant = ToastNotificationVariant.WARNING,
                        action = Action.ViewInFlowRunner(existingFlowExecution.id),
                    )
                )
            )
            return@withTenantTransactionScope existingFlowExecution
        }

        // Create a new flow execution if no duplicates are found
        return@withTenantTransactionScope create(workspaceId, createFlowExecutionRequestBody)
    }

    /**
     * Creates a new FlowExecutionEntity as a row in flow_executions.
     * Creates a new document (@see FlowExecution.DocumentId property) for tracking the current status of the flow.
     * Inserts the configuration ID for this flow (@see FlowExecution.FlowConfigurationId) which acts as the template configuration for this flow to initialise
     *
     * @return FlowExecution.ForApi related to the flow that was selected to be run, flow is executed asynchronously. eg, the function may return prior to the completion of
     */
    suspend fun create(
        workspaceId: Workspace.Id,
        createFlowExecutionRequestBody: CreateFlowExecutionRequestBody,
    ): FlowExecution.ForApi {

        val workspaceVersionId = createFlowExecutionRequestBody.workspaceVersionId
        val workspaceVersion = workspaceVersionService.findVersion(workspaceId, workspaceVersionId)

        val flowConfiguration =
            workspaceVersion.configuration.findFlow(createFlowExecutionRequestBody.flowConfigurationId)

        val secureWorkspaceVariables =
            workspaceVariableService.getAllByVersionAndReveal(workspaceId, workspaceVersionId)

        //weave in the workspace variables with the secure variables (they start with null if they are secured)
        val workspaceVariables: MutableMap<VariableIdentifier, JsonElement> =
            workspaceVersion.configuration.variables.values.associate { variable ->
                val secureVar = secureWorkspaceVariables.firstOrNull { it.ref.ref == variable.securedRef }
                variable.name.value to Json.encodeToJsonElement(secureVar?.value?.revealedValue ?: variable.value)
            }.toMutableMap()

        // create the flowConfiguration context
        val flowContext = FlowContext(
            global = GlobalVariables(
                workspaceId,
                workspaceVersion.id,
                coroutineContext[RequestContext]!!.tenant.id,
                createFlowExecutionRequestBody.flowConfigurationId,
                workspaceVersion.configuration.findFlow(createFlowExecutionRequestBody.flowConfigurationId).name
            ),
            workspace = WorkspaceContext(workspaceId, workspaceVariables),
            variables = mutableMapOf(),
            event = createFlowExecutionRequestBody.event,
        )

        // add initial values to the context
        createFlowExecutionRequestBody.variables.forEach { value ->
            flowContext.set(
                Variable(
                    TypeToJsonElementConverter.toJsonElement(value.value), value.type, value.identifier, null
                ), // ignores properties???
            ) // note, this doesn't copy properties across, or handle lists/maps yet
        }

        val document = flowConfiguration.toExecution(
            flowContext, flowConfiguration.triggers?.get(createFlowExecutionRequestBody.trigger.id)
        )

        if (document.trigger != null) {

            val trigger = document.trigger
            val stepTypeConfiguration =
                flowStepTypeConfigurationService.getByPrimaryIdentifier(trigger.properties.typePrimaryIdentifier!!)

            val flowContextLocalStep = FlowContextWithLocalStep(flowContext = flowContext)

            val contextToJsonObjectBuilder = ContextToJsonObjectBuilder(emptyList(), trigger.id.value)

            TriggerTemplatePopulator(flowContextLocalStep, contextToJsonObjectBuilder).populateTrigger(
                trigger, stepTypeConfiguration?.properties?.configuration!!
            )

        }

        val newFlowExecution = withTenantTransactionScope { tenant ->
            val documentId = fedRepository.create(tenant, document)
            val createdFlowExecution = flowExecutionRepository.create(
                tenant, workspaceId, createFlowExecutionRequestBody, documentId, flowConfiguration.name
            ).toDTO()

            logger.info(
                "View the execution document at http://localhost:8000/ai/api/debug/#automerge:{}", documentId.value
            )

            return@withTenantTransactionScope createdFlowExecution
        }

        checkAndSendNotificationToUser(
            createFlowExecutionRequestBody, WebSocketMessage(
                MessageType.Notification, NotificationMessage(
                    heading = Message(key = "ui.notifications.notification"),
                    description = Message(key = "ui.flow.queuedSuccessfully"),
                    toastNotificationVariant = ToastNotificationVariant.INFO,
                    action = Action.ViewInFlowRunner(newFlowExecution.id),
                )
            )
        )

        return newFlowExecution
    }

    suspend fun getFlowButtons(
        configurationId: String, inputIdentifier: String, workspaceId: Workspace.Id, eventKey: EventKey
    ): List<FlowButton> {
        val buttonLabels = mutableSetOf<FlowButton>();
        val workspaceVersion = workspaceVersionService.findVersion(workspaceId)
        val flowConfigurations = workspaceVersion.configuration.flows.entities.values.toList()
            .filter { !it.start?.value.isNullOrEmpty() && it.status != FlowConfigurationStatusType.INACTIVE }
        val allTriggers = flowConfigurations.flatMap { it.triggers?.values ?: emptyList() }
        val typePrimaryIdentifier = allTriggers.mapNotNull { it.properties.typePrimaryIdentifier }
        val flowStepTypes =
            flowStepTypeConfigurationService.getAllByQuery(mapOf("primaryIdentifier" to typePrimaryIdentifier))
        val matchingFlowStepTypes = flowStepTypes.filter {
            val subscribedEvents: Set<EventKey>? = it.properties?.configuration?.subscribeTo?.keys
            subscribedEvents?.contains(eventKey) ?: false
        }
        val primaryIdentifiers = matchingFlowStepTypes.map { it.primaryIdentifier }
        allTriggers.forEach { trigger ->
            if (trigger.properties.typePrimaryIdentifier in primaryIdentifiers) {
                val inputs = trigger.properties.inputs
                val label = inputs["buttonLabel"]
                val inputId = inputs[inputIdentifier]
                if (label != null && label.getContent().isNotEmpty() && inputId?.getContent() == configurationId) {
                    buttonLabels.add(FlowButton(label.getContent()))
                }
            }
        }
        return buttonLabels.toList();
    }

    suspend fun cancelFlowExecution(flowExecutionId: FlowExecution.Id, userId: Long) = withTenantTransactionScope {
        val flowExecution = flowExecutionRepository.get {
            FlowExecutions.id eq flowExecutionId.value
        } ?: throw NotFoundException()
        if (flowExecution.status == FlowExecution.Status.PENDING) {
            flowExecutionRepository.update(flowExecutionId.value) {
                it.status = FlowExecution.Status.COMPLETED
                it.result = FlowExecution.Result.CANCELLED
                it.properties = it.properties.copy(
                    cancellation = FlowExecution.Properties.Cancellation(
                        trigger = CancelTriggerType.MANUAL, triggeredAt = Instant.now(), userId = userId
                    )
                )
            }
        } else if (flowExecution.status == FlowExecution.Status.RUNNING) {
            flowExecutionRepository.update(flowExecutionId.value) {
                it.isCanceled = true
                it.status = FlowExecution.Status.CANCELLING
                it.properties = it.properties.copy(
                    cancellation = FlowExecution.Properties.Cancellation(
                        trigger = CancelTriggerType.MANUAL, triggeredAt = Instant.now(), userId = userId
                    )
                )
            }
        } else {
            throw Exception("Invalid flow execution")
        }
    }

    suspend fun getViewURL(flowExecutionId: FlowExecution.Id): String = withTenantTransactionScope {
        val flowExecution = flowExecutionRepository.get {
            FlowExecutions.id eq flowExecutionId.value
        } ?: throw NotFoundException()

        return@withTenantTransactionScope fedRepository.getViewURL(flowExecution.documentId)
    }
}

fun FlowConfiguration.ForJson.toExecution(
    flowContext: FlowContext, trigger: FlowConfiguration.Trigger?
): FlowExecution.ForJson {
    return FlowExecution.ForJson(
        context = flowContext,
        state = FlowExecution.State(),
        start = if (this.start?.value != null) Step.Id(this.start!!.value) else null,
        steps = (this.steps?.entries?.associate { (id, step) ->
            FlowExecution.Step.Id(id.value) to step.toExecution()
        }?.toMutableMap() ?: mutableMapOf()),
        startingVariables = this.startingVariables,
        endingVariables = this.endingVariables,
        name = FlowExecution.Name(this.name.value),
        description = if (this.description?.value != null) FlowExecution.Description(this.description!!.value!!) else null,
        // trigger will be set by information from the event
        trigger = trigger?.toExecution()
    )
}

fun FlowConfiguration.Trigger.toExecution(): FlowExecution.Trigger {
    return FlowExecution.Trigger(
        id = FlowExecution.Trigger.Id(this.id.value),
        name = this.name,
        variant = Step.Variant.valueOf(this.variant.name),
        properties = this.properties.toExecution(),
        next = this.next?.value?.let { Step.Id(it) },
    )
}

fun FlowConfiguration.Step.toExecution(): FlowExecution.Step {
    return FlowExecution.Step(
        id = FlowExecution.Step.Id(this.id.value),
        variant = FlowExecution.Step.Variant.valueOf(this.variant.name),
        name = this.name,
        properties = this.properties.toExecution(),
        next = this.next?.value?.let { FlowExecution.Step.Id(it) },
    )
}

fun FlowConfiguration.Step.Properties.toExecution(): FlowExecution.Step.Properties {
    return FlowExecution.Step.Properties(
        typePrimaryIdentifier = this.typePrimaryIdentifier,
        // convert inputs map to mutable map of string to JsonElement
        inputs = this.inputs.mapValues { (_, value) ->
            value
        }.toMutableMap(),
        configuration = this.configuration,
        variables = this.variables?.map {
            VariableDefinition.Variable(
                value = Json.encodeToJsonElement(it.value),
                type = it.type,
                identifier = it.identifier,
                properties = it.properties?.let {
                    Json.decodeFromJsonElement(
                        VariableDefinition.VariableProperties.serializer(), it
                    )
                })
        }?.toMutableList(),
        branches = this.branches?.map {
            FlowExecution.Step.Properties.ConditionBranch(
                name = it.name,
                condition = Json.decodeFromJsonElement(Condition.serializer(), it.condition),
                next = it.next?.value?.let { FlowExecution.Step.Id(it) })
        }?.toMutableList(),
    )
}

fun FlowExecutionEntity.toDTO(): FlowExecution.ForApi {
    return FlowExecution.ForApi(
        id = FlowExecution.Id(this.id.value),
        workspaceId = Workspace.Id(this.workspaceId),
        status = FlowExecution.Status.valueOf(this.status.name),
        result = FlowExecution.Result.valueOf(this.result.name),
        documentId = FlowExecution.DocumentId(this.documentId),
        configurationId = this.flowConfigurationId,
        startedAt = this.startedAt,
        finishedAt = this.finishedAt,
        createdAt = this.createdAt,
        flowConfigurationName = this.flowConfigurationName,
        isCanceled = this.isCanceled
    )
}

//I would prefer to have this on the WorkspaceVariableConfiguration class, but the 'to' and 'from' types are in different modules.
//would cause a circular dependency if I put it there.
fun WorkspaceVariableConfiguration.toFlowVariable(): Variable {
    return Variable(
        value = Json.encodeToJsonElement(this.value),
        type = "workspaceVariable", // workspace variables are always text for now
        identifier = this.name.value,
        properties = null
    )
}

@Serializable
data class FlowButton(
    val label: String,
)

@Serializable
data class FlowResponse(
    val success: Boolean, val data: FlowExecution.ForApi?
)

@Serializable
data class CreateFlowExecutionRequestBody(
    val flowConfigurationId: FlowConfiguration.Id,
    val workspaceVersionId: WorkspaceVersion.Id,
    val event: Event.ForApi,
    val variables: List<VariableMapping>,
    val creationMode: CreationMode? = CreationMode.ALWAYS,
    val settings: FlowExecution.Properties.Settings,
    val trigger: TriggerProperties,
) {
    @Serializable
    enum class CreationMode {
        @SerialName("always")
        ALWAYS,

        @SerialName("avoidDuplication")
        AVOID_DUPLICATION
    }

    @Serializable
    data class TriggerProperties(
        val id: FlowConfiguration.Step.Id,
        val primaryIdentifier: String,
    )
}