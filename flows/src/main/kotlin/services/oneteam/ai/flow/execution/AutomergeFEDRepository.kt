package services.oneteam.ai.flow.execution

import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.domains.workspace.document.create
import services.oneteam.ai.shared.domains.workspace.document.show

class AutomergeFEDRepository(private val documentService: IDocumentService) : FEDRepository {

    override suspend fun create(tenant: Tenant, flowExecution: FlowExecution.ForJson): FlowExecution.DocumentId {
        return FlowExecution.DocumentId(documentService.create(tenant.id, flowExecution))
    }

    override suspend fun get(documentId: FlowExecution.DocumentId): FlowExecution.ForJson {
        return documentService.show<FlowExecution.ForJson>(documentId.value, null, true)
    }

    suspend fun getFedUrl(documentId: FlowExecution.DocumentId): String {
        if (documentService is ApiDocumentService) {
            return documentService.getViewUrl(documentId.value)
        } else {
            throw IllegalArgumentException("Document service does not support FED URLs")
        }
    }
}

