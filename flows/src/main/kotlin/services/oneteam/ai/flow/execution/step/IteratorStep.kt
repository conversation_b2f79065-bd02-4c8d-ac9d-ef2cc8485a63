package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.encodeToJsonElement
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.shared.helpers.getContent

internal suspend fun setUpIterationVariables(
    step: FlowExecution.Step,
    index: Int,
    ctx: FlowContextWithLocalStep,
    item: JsonElement
) {

    val itemVariableName = step.properties.inputs["itemVariableName"]!!.getContent()

    ctx.flowContext.variables[itemVariableName] = Variable(item, "json", itemVariableName, null)
    ctx.flowContext.variables["${itemVariableName}_index"] =
        Variable(Json.encodeToJsonElement(index.toString()), "number", "${itemVariableName}_index", null)

    when (step.properties.typePrimaryIdentifier) {
        "iteratorAggregate" -> {
            return
        }

        else -> {
            ctx.flowContext.variables["${itemVariableName}_output"] = when (step.properties.typePrimaryIdentifier) {
                "iteratorForEach" -> Variable(
                    JsonNull,
                    step.properties.inputs["transformedValueType"]!!.getContent(),
                    "${itemVariableName}_output",
                    null
                )

                "iteratorFilter" -> Variable(JsonNull, "boolean", "${itemVariableName}_output", null)
                else -> throw IllegalArgumentException("Unknown iterator type ${step.properties.typePrimaryIdentifier}")
            }

        }
    }
}


/**
 * If any variables that exist in the main context have been updated by the iteration, then update them in the main context.
 */
internal suspend fun syncValuesToMainContext(
    iterationContext: FlowContextWithLocalStep,
    mainContext: FlowContextWithLocalStep
) {
    for ((variableIdentifier, variable) in iterationContext.flowContext.variables) {
        if (mainContext.containsVariable(variableIdentifier)) {
            val existingVariable = mainContext.getVariable(variableIdentifier)
            if (existingVariable!!.value != variable.value) {
                mainContext.flowContext.set(existingVariable.copy(value = variable.value))
            }
        }
    }
}