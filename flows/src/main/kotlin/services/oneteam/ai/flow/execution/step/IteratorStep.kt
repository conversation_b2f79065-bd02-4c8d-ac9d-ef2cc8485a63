package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.encodeToJsonElement
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.helpers.getContent

internal suspend fun setUpIterationVariables(
    step: FlowExecution.Step, index: Int, ctx: FlowContextWithLocalStep, item: JsonElement
) {

    val itemVariableName = step.properties.inputs["itemVariableName"]!!.getContent()

    // Find the one with the name of itemVariableName
    val itemStartingVariable =
        step.properties.configuration?.startingVariables?.find { it.identifier == itemVariableName }
    val itemType = itemStartingVariable?.type ?: VariableDataType.JSON

    ctx.flowContext.variables[itemVariableName] = VariableInstance.Variable(item, itemType, itemVariableName, null)
    ctx.flowContext.variables["${itemVariableName}_index"] = VariableInstance.Variable(
        Json.encodeToJsonElement(index.toString()), VariableDataType.NUMBER, "${itemVariableName}_index", null
    )

    when (step.properties.typePrimaryIdentifier) {
        "iteratorAggregate" -> {
            return
        }

        else -> {
            ctx.flowContext.variables["${itemVariableName}_output"] = when (step.properties.typePrimaryIdentifier) {
                "iteratorForEach" -> VariableInstance.Variable(
                    value = JsonNull,
                    type = VariableDataType.fromString(step.properties.inputs["transformedValueType"]!!.getContent()),
                    identifier = "${itemVariableName}_output",
                    properties = null
                )

                "iteratorFilter" -> VariableInstance.Variable(
                    value = JsonNull,
                    type = VariableDataType.fromString("boolean"),
                    identifier = "${itemVariableName}_output",
                    properties = null
                )

                else -> throw IllegalArgumentException("Unknown iterator type ${step.properties.typePrimaryIdentifier}")
            }

        }
    }
}


/**
 * If any variables that exist in the main context have been updated by the iteration, then update them in the main context.
 */
internal suspend fun syncValuesToMainContext(
    iterationContext: FlowContextWithLocalStep, mainContext: FlowContextWithLocalStep
) {
    for ((variableIdentifier, variable) in iterationContext.flowContext.variables) {
        if (mainContext.containsVariable(variableIdentifier)) {
            val existingVariable = mainContext.getVariable(variableIdentifier)
            if (existingVariable!!.get() != variable.get()) {
                mainContext.flowContext.set(
                    VariableInstance.Variable(
                        value = variable.get(),
                        type = existingVariable.type,
                        identifier = existingVariable.identifier,
                        properties = null
                    )
                )
            }
        }
    }
}