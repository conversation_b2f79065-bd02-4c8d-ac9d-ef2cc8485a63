package services.oneteam.ai.flow.execution.variables

import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.jsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.flow.variables.Variable
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance

/**
 * Default variable resolver that does not perform any operation.
 *
 * This resolver is used when no specific resolver matches the variable type.
 * It simply returns the variable as is without any modifications.
 */
class DefaultVariableOperation(
    private val useJsonata: Boolean,
    private val jsonataExpressionEvaluator: JsonataExpressionEvaluator
) : VariableOperation {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override fun match(variable: Variable): Boolean {
        return true
    }

    override suspend fun resolve(variable: VariableInstance, context: FlowContextWithLocalStep): VariableInstance {
        // TODO remove useJsonata when we move to using ExecutionStepFactoryV2
        if (useJsonata) {
            val result =
                jsonataExpressionEvaluator.evaluate(variable.get().jsonPrimitive.content, mapOf<Any, Any>())
            logger.trace("Result of expression evaluation is `{}`", result)
            val value = result as? JsonElement ?: FlowContext.toJsonElement(result, variable.type)
            return VariableInstance.Variable(
                value = value, type = variable.type, identifier = variable.identifier, properties = variable.properties
            )
        }
        return variable
    }
}