package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import kotlin.math.pow

@Serializable
internal object RoundUp : ComputeFunction {
    override val key = "roundup"
    override val syntax = Syntax(
        "ROUNDUP(number, precision)", listOf(
            Argument("number", "The number to round", "number"),
            Argument("precision", "The number of decimal places to round to", "number")
        )
    )
    override val description = "Round a number up to a specified number of decimal places"
    override val notes =
        "one of the basic number functions, to round a number up to a specified number of decimal places"
    override val category = "number"
    override val icon = Icon("function")
    override val functionName = "ROUNDUP"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val value = args[0].toString().toDouble()

        val precision = if (args[1] != null) args[1].toString().toDouble() else 0.0
        val factor = 10.0.pow(precision)
        val result = (value * factor).toLong() / factor

        if (result < value) { result + 1 / factor }
        else { result }
    }, "<xx?:n>")
}
