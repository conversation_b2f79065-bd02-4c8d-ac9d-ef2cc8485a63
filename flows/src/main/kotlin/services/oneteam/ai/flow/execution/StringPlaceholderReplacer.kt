package services.oneteam.ai.flow.execution

import org.slf4j.Logger
import org.slf4j.LoggerFactory

class StringPlaceholderReplacer {

    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val replacer = SimplePlaceHolderReplacer(NoValueStrategy.BLANK)

    suspend fun replacePlaceholders(template: String, valueProvider: ContextPlaceholderValueProvider): String {

        val populatedTemplate = replacer.replacePlaceholders(template, valueProvider)
        logger.trace("Populated template {}", populatedTemplate)

        return populatedTemplate
    }
}