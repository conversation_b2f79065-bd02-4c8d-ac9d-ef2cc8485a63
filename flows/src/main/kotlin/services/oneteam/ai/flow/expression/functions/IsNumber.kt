package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object IsNumber : ComputeFunction {
    override val key = "isNumber"
    override val syntax = Syntax(
        "ISNUMBER(value)", listOf(
            Argument("value", "The value to check", "any")
        )
    )
    override val description = "Check if a value is a number"
    override val notes = "Check if a value is a number"
    override val category = "type"
    override val icon = Icon("function")
    override val functionName = "ISNUMBER"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args.first().let {
            when (it) {
                is Double -> true
                is Int -> true
                is Long -> true
                is Float -> true
                is Short -> true
                is Byte -> true
                else -> false
            }
        }
    }, "<x:b>")
}
