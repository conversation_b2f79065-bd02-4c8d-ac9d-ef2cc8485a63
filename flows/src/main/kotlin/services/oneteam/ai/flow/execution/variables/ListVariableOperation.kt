package services.oneteam.ai.flow.execution.variables

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.flow.variables.ListOperation
import services.oneteam.ai.shared.domains.flow.variables.Variable
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.flow.variables.VariableProperties
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.helpers.getContent

/**
 * Resolver for list variables.
 *
 * When we have a list variable, we must supply properties with a list operation (SET_LIST, SET_ITEM, REMOVE_ITEM).
 */
class ListVariableOperation(val useJsonata: Boolean) : VariableOperation {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override fun match(variable: Variable): Boolean {
        return variable.type == VariableDataType.LIST
    }

    override suspend fun resolve(
        variable: VariableInstance, context: FlowContextWithLocalStep
    ): VariableInstance {

        val listProperties = variable.properties as VariableProperties.ListVariableProperties

        logger.trace("Executing list operation `{}`", listProperties.listOperation)

        // TODO remove useJsonata when we move to using ExecutionStepFactoryV2
        val resultValue = if (useJsonata) JsonataExpressionEvaluator().evaluate(
            variable.get().getContent(), mapOf<Any, Any>()
        ) else variable.get()

        val variableValue = when (listProperties.listOperation) {
            ListOperation.SET_LIST -> handleSetList(resultValue)
            ListOperation.SET_ITEM -> handleSetItem(variable, context, resultValue)
            ListOperation.REMOVE_ITEM -> handleRemoveItem(variable, context, resultValue)
        }

        logger.trace("Result of operation is `{}`", Json.encodeToString(variableValue))

        return VariableInstance.Variable(
            value = variableValue,
            type = variable.type,
            identifier = variable.identifier,
            properties = variable.properties
        )
    }

    private fun handleSetList(value: Any): JsonElement {
        if (value !is JsonElement) {
            return FlowContext.toJsonElement(value, VariableDataType.LIST)
        } else if (value is JsonNull || (value is JsonPrimitive && value.isString && value.content.isEmpty())) {
            return JsonArray(emptyList())
        }

        return value
    }

    @OptIn(ExperimentalSerializationApi::class)
    private fun handleSetItem(
        variable: Variable, context: FlowContextWithLocalStep, value: Any
    ): JsonElement {
        val valueAsJsonElement = TypeToJsonElementConverter.toJsonElement(value)
        val existingListValue = context.flowContext.variables[variable.identifier]?.get() ?: buildJsonArray {}
        val listProperties = variable.properties as VariableProperties.ListVariableProperties
        val itemIndex = listProperties.itemIndex.toString().toIntOrNull()

        val newListValue = if (itemIndex != null && itemIndex > 0) {
            val mutableList = existingListValue.jsonArray.toMutableList()

            // Ensure the list has enough size by filling with JsonNull
            while (mutableList.size < itemIndex) {
                mutableList.add(JsonNull)
            }
            mutableList[itemIndex - 1] = valueAsJsonElement
            JsonArray(mutableList)
        } else {
            buildJsonArray {
                addAll(existingListValue.jsonArray)
                add(valueAsJsonElement)
            }
        }

        return newListValue
    }

    private fun handleRemoveItem(
        variable: Variable, context: FlowContextWithLocalStep, value: Any
    ): JsonElement {
        val existingListValue = context.flowContext.variables[variable.identifier]?.get() ?: buildJsonArray {}
        val listProperties = variable.properties as VariableProperties.ListVariableProperties

        val itemIndex = listProperties.itemIndex.toString().toIntOrNull()

        require(itemIndex != null && itemIndex <= existingListValue.jsonArray.size) { "Invalid itemIndex" }

        val newListValue = JsonArray(
            existingListValue.jsonArray.filterIndexed { index, _ ->
                (index + 1) != itemIndex
            })

        return newListValue
    }
}