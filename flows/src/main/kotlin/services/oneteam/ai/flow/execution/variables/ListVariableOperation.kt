package services.oneteam.ai.flow.execution.variables

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.VariableDefinition.*
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.helpers.getContent

/**
 * Resolver for list variables.
 *
 * When we have a list variable, we must supply properties with a list operation (SET_LIST, SET_ITEM, REMOVE_ITEM).
 */
class ListVariableOperation(val useJsonata: Boolean) : VariableOperation {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override fun match(variable: Variable): Boolean {
        return variable.type == "list"
    }

    override suspend fun resolve(
        variable: Variable, context: FlowContextWithLocalStep
    ): Variable {

        val listProperties = variable.properties as ListVariableProperties

        logger.trace("Executing list operation `{}`", listProperties.listOperation)

        // TODO remove useJsonata when we move to using ExecutionStepFactoryV2
        val resultValue = if (useJsonata) JsonataExpressionEvaluator().evaluate(
            variable.value.getContent(),
            mapOf<Any, Any>()
        ) else variable.value

        val variableValue = when (listProperties.listOperation) {
            ListOperation.SET_LIST -> handleSetList(resultValue)
            ListOperation.SET_ITEM -> handleSetItem(variable, context, resultValue)
            ListOperation.REMOVE_ITEM -> handleRemoveItem(variable, context, resultValue)
        }

        logger.trace("Result of operation is `{}`", Json.encodeToString(variableValue))

        return variable.copy(value = variableValue)
    }

    private fun handleSetList(value: Any): JsonElement {
        if (value is JsonElement) {
            return value
        }
        return FlowContext.toJsonElement(value, "list")
    }

    @OptIn(ExperimentalSerializationApi::class)
    private fun handleSetItem(
        variable: Variable, context: FlowContextWithLocalStep, value: Any
    ): JsonElement {
        val valueAsJsonElement = TypeToJsonElementConverter.toJsonElement(value)
        val existingListValue = context.flowContext.variables[variable.identifier]?.value ?: buildJsonArray {}
        val listProperties = variable.properties as ListVariableProperties
        val itemIndex = listProperties.itemIndex.toString().toIntOrNull()

        val newListValue = if (itemIndex != null && itemIndex > 0) {
            val mutableList = existingListValue.jsonArray.toMutableList()

            // Ensure the list has enough size by filling with JsonNull
            while (mutableList.size < itemIndex) {
                mutableList.add(JsonNull)
            }
            mutableList[itemIndex - 1] = valueAsJsonElement
            JsonArray(mutableList)
        } else {
            buildJsonArray {
                addAll(existingListValue.jsonArray)
                add(valueAsJsonElement)
            }
        }

        return newListValue
    }

    private fun handleRemoveItem(
        variable: Variable, context: FlowContextWithLocalStep, value: Any
    ): JsonElement {
        val existingListValue = context.flowContext.variables[variable.identifier]?.value ?: buildJsonArray {}
        val listProperties = variable.properties as ListVariableProperties

        val itemIndex = listProperties.itemIndex.toString().toIntOrNull()

        require(itemIndex != null && itemIndex <= existingListValue.jsonArray.size) { "Invalid itemIndex" }

        val newListValue = JsonArray(
            existingListValue.jsonArray.filterIndexed { index, _ ->
                (index + 1) != itemIndex
            }
        )

        return newListValue
    }
}