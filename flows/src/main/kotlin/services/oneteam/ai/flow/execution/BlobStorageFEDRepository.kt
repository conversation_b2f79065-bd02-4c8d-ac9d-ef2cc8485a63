package services.oneteam.ai.flow.execution

import io.ktor.http.*
import kotlinx.serialization.json.Json
import services.oneteam.ai.shared.domains.collection.form.BlobService
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.helpers.CustomNanoId

class BlobStorageFEDRepository(
    private val blobService: BlobService
) : FEDRepository {

    private val prefix: String = "BLOB:"
    private val suffix: String = ".json"

    override suspend fun create(tenant: Tenant, flowExecution: FlowExecution.ForJson): FlowExecution.DocumentId {
        val name = "${flowExecution.context.global.tenantId}" +
                "/${flowExecution.context.global.workspaceId.value}" +
                "/flows" +
                "/${flowExecution.context.global.flowConfigurationId.value}" +
                "/$prefix${CustomNanoId.generate()}$suffix"
        return blobService.uploadFromStream(name, flowExecution.toString().byteInputStream()).let {
            FlowExecution.DocumentId(name)
        }
    }

    override suspend fun get(documentId: FlowExecution.DocumentId): FlowExecution.ForJson {
        return blobService.downloadFileBytes(documentId.value).let {
            Json.Default.decodeFromString(it.decodeToString())
        }
    }

    fun update(documentId: FlowExecution.DocumentId, flowExecution: FlowExecution.ForJson) {
        if (!match(documentId)) {
            throw IllegalArgumentException("Document ID does not match BlobStorageFEDRepository prefix")
        }
        blobService.uploadFromStreamWithMimeType(
            documentId.value,
            flowExecution.toString().byteInputStream(),
            ContentType.Application.Json.contentType
        )
    }

    fun match(documentId: FlowExecution.DocumentId): Boolean {
        return documentId.value.contains(prefix)
    }

    fun getFedUrl(documentId: FlowExecution.DocumentId): String {
        if (!match(documentId)) {
            throw IllegalArgumentException("Document ID does not match BlobStorageFEDRepository prefix")
        }
        return blobService.generateSasToken(documentId.value, false)
    }
}