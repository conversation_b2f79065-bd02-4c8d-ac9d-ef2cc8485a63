package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.util.*

@Serializable
internal object Substitute : ComputeFunction {
    override val key = "substitute"
    override val syntax = Syntax(
        "SUBSTITUTE(string, search, replace, [occurrence])", listOf(
            Argument("string", "The string to search", "string"),
            Argument("search", "The string to search for", "string"),
            Argument("replace", "The string to replace with", "string"),
            Argument("occurrence", "Optional, limit of replacements", "number")
        )
    )
    override val description = "Replace all occurrences of a string within another string"
    override val notes = "If occurrence is provided, only that many occurrences are replaced."
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "SUBSTITUTE"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val text = args!![0] as String
        val oldText = args[1] as String
        val newText = args[2] as String
        val occurrence = if (args.size > 3 && args[3] != null) args[3] as Int else null

        if (occurrence == null) {
            text.replace(oldText, newText)
        } else {
            var count = 0
            Regex(Regex.escape(oldText)).replace(text) { matchResult ->
                count++
                if (count <= occurrence) newText else matchResult.value
            }
        }
    }, "<sss?:s>")
}


