package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.time.LocalDate
import java.time.Period
import java.time.temporal.ChronoUnit

@Serializable
internal object DateDif : ComputeFunction {
    override val key = "datedif"
    override val syntax = Syntax(
        "DATEDIF(start_date, end_date, unit)", listOf(
            Argument("start_date", "The start date as a string in ISO format (yyyy-MM-dd)", "string"),
            Argument("end_date", "The end date as a string in ISO format (yyyy-MM-dd)", "string"),
            Argument("unit", "The unit of difference: Y, M, D, MD, YM, YD", "string")
        )
    )
    override val description = "Calculate the difference between two dates based on a specified unit"
    override val notes = "The DATEDIF function is useful in formulas where you need to calculate an age."
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "DATEDIF"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        if (args == null || args.size < 3) return@JFunction ""
        val startDateStr = args[0]?.toString() ?: return@JFunction ""
        val endDateStr = args[1]?.toString() ?: return@JFunction ""
        val unit = args[2]?.toString()?.trim()?.uppercase() ?: return@JFunction ""

        val start = try { LocalDate.parse(startDateStr) } catch (e: Exception) { return@JFunction "" }
        val end = try { LocalDate.parse(endDateStr) } catch (e: Exception) { return@JFunction "" }
        if (end.isBefore(start)) return@JFunction ""

        val result = when(unit) {
            "Y" -> Period.between(start, end).years
            "M" -> {
                val totalMonths = (end.year - start.year) * 12 + end.monthValue - start.monthValue -
                        if (end.dayOfMonth < start.dayOfMonth) 1 else 0
                totalMonths
            }
            "D" -> ChronoUnit.DAYS.between(start, end).toInt()
            "MD" -> {
                val d1 = start.dayOfMonth
                val d2 = end.dayOfMonth
                if (d2 >= d1) d2 - d1 else {
                    val prevMonth = end.minusMonths(1)
                    d2 + prevMonth.lengthOfMonth() - d1
                }
            }
            "YM" -> {
                var m = end.monthValue - start.monthValue
                if (end.dayOfMonth < start.dayOfMonth) m--
                if (m < 0) m += 12
                m
            }
            "YD" -> {
                var startAdj = start.withYear(end.year)
                if (end.isBefore(startAdj)) startAdj = startAdj.minusYears(1)
                ChronoUnit.DAYS.between(startAdj, end).toInt()
            }
            else -> 0
        }
        result.toString()
    }, "<xxx:n>")
}
