package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object SumIf : ComputeFunction {
    override val key = "sumif"
    override val syntax = Syntax(
        "SUMIF(range, criteria, [sum_range])", listOf(
            Argument("range", "The range to evaluate", "array"),
            Argument("criteria", "The condition to filter the range", "any"),
            Argument("sum_range", "Optional range to sum, if omitted, range is used", "array")
        )
    )
    override val description = "Sum the elements in a range that meet a condition"
    override val notes = "Mimics Excel's SUMIF function"
    override val category = "array"
    override val icon = Icon("function")
    override val functionName = "SUMIF"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val range = args[0] as List<*>
        val criteria = args[1]
        val sumRange = if (args.size > 2 && args[2] is List<*>) args[2] as List<*> else range

        val res = range.indices.sumOf { i ->
            if (meetsCriteria(range[i], criteria)) {
                sumRange.getOrNull(i)?.toString()?.toDoubleOrNull() ?: 0.0
            } else 0.0
        }

        return@JFunction res.toString()
    }, "<x+:n>")
}

fun meetsCriteria(value: Any?, criteria: Any?): Boolean {
    if (value == null) return false
    if (criteria is Number) {
        val num = value.toString().toDoubleOrNull() ?: return false
        return num == criteria.toDouble()
    }
    if (criteria is String) {
        val crit = criteria.trim()
        val regex = Regex("^(>=|<=|<>|>|<|=)?\\s*(-?\\d+(\\.\\d+)?)$")
        val match = regex.find(crit)
        if (match != null) {
            val op = match.groupValues[1]
            val operand = match.groupValues[2].toDouble()
            val num = value.toString().toDoubleOrNull() ?: return false
            return when (op) {
                ">" -> num > operand
                ">=" -> num >= operand
                "<" -> num < operand
                "<=" -> num <= operand
                "<>" -> num != operand
                else -> num == operand
            }
        }
        return value.toString().equals(crit, ignoreCase = true)
    }
    return value == criteria
}
