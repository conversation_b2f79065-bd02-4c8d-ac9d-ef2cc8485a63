package services.oneteam.ai.flow.expression.conditional

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
enum class Operator {
    @SerialName("=")
    EQUAL,

    @SerialName("isEmpty")
    IS_EMPTY,

    @SerialName("isNotEmpty")
    IS_NOT_EMPTY,

    @SerialName("contains")
    CONTAINS,

    @SerialName("doesNotContain")
    DOES_NOT_CONTAIN,

    @SerialName(">")
    GREATER_THAN,

    @SerialName("<")
    LESS_THAN,

    @SerialName("!=")
    NOT_EQUAL,

    @SerialName(">=")
    GREATER_THAN_OR_EQUAL,

    @SerialName("<=")
    LESS_THAN_OR_EQUAL
}

@Serializable
enum class LogicalOperator {
    @SerialName("AND")
    AND,

    @SerialName("OR")
    OR
}

fun mapOperator(operator: String): Enum<*> {
    LogicalOperator.entries.forEach {
        val serialName = it::class.java.getField(it.name).getAnnotation(SerialName::class.java)?.value
        if (serialName == operator) {
            return it
        }
    }

    Operator.entries.forEach {
        val serialName = it::class.java.getField(it.name).getAnnotation(SerialName::class.java)?.value
        if (serialName == operator) {
            return it
        }
    }

    throw IllegalArgumentException("Invalid operator: $operator")
}