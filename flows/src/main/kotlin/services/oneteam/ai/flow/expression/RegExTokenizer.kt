package services.oneteam.ai.flow.expression

/**
 * Note, does not handle nested tokens.
 */
class RegExTokenizer(val prefix: String = "{{", val suffix: String = "}}") : Tokenizer {
    // can we optimise this to something like `${Regex.escape(prefix)}([a-zA-Z0-9]+?)${Regex.escape(suffix)}`
    private val regex = Regex("(?=${Regex.escape(prefix)})|(?<=${Regex.escape(suffix)})")

    /**
     * Extracts the tokens from the given expression.
     *
     * When the expression is
     *  "This is a {{test}} string with {{multiple}} delimiters.",
     * the tokens will be
     *  ["test", "multiple"].
     */
    override fun tokenize(expression: String): List<String> {
        val tokens = regex.split(expression)
        return tokens.filter { it.startsWith(prefix) && it.endsWith(suffix) }
            .map { it.removePrefix(prefix).removeSuffix(suffix) }
    }

    override fun replace(expression: String, variable: String, value: String): String {
        return expression.replace("$prefix${variable}$suffix", value)
    }

    override fun hasTokens(string: String): Boolean {
        return string.contains(prefix) || string.contains(suffix)
    }

    fun removePrefixAndSuffix(string: String): String {
        return string.replace(prefix, "").replace(suffix, "")
    }
}