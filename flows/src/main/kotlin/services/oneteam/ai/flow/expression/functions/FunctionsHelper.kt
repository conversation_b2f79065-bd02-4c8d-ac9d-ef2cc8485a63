package services.oneteam.ai.flow.expression.functions

/**
 * Helper function to parse and validate boolean arguments
 * @param argName The name of the argument (for error messages)
 * @param value The value to parse
 * @return Boolean value (true or false)
 * @throws IllegalArgumentException if value is not "true" or "false"
 */
fun parseBooleanArg(value: Any?, default: Boolean? = null): Boolean {
    val strValue = value.toString().lowercase()
    return when (strValue) {
        "true", "1", "yes" -> true
        "false", "0", "no" -> false
        "null" -> default ?: throw IllegalArgumentException("Argument cannot be null")
        else -> throw IllegalArgumentException("Argument not allowed for this boolean value")
    }
}