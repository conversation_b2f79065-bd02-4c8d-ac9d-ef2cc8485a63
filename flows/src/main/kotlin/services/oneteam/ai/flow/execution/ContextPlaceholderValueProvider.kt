package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.JsonObject
import services.oneteam.ai.flow.execution.step.ContextToJsonObjectBuilder
import services.oneteam.ai.flow.execution.variables.PathBasedVariableFilter

class ContextPlaceholderValueProvider(
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
    val flowContextLocalStep: FlowContextWithLocalStep
) : PlaceholderValueProvider {

    override suspend fun valuesForPlaceholders(placeholders: Set<String>): JsonObject {
        return contextToJsonObjectBuilder.build(flowContextLocalStep, PathBasedVariableFilter(placeholders))
    }
}

interface PlaceholderValueProvider {
    suspend fun valuesForPlaceholders(placeholders: Set<String>): JsonObject
}