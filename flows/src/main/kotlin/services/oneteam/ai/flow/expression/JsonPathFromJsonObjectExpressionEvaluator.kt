package services.oneteam.ai.flow.expression

import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.DocumentContext
import com.jayway.jsonpath.JsonPath
import com.jayway.jsonpath.Option
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * This class is used to evaluate JSONPath expressions against a given JSON object.
 */
class JsonPathFromJsonObjectExpressionEvaluator(val context: JsonObject) {

    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val config: Configuration =
        Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL, Option.SUPPRESS_EXCEPTIONS)

    // only build the context once for all evaluations
    val ctx: DocumentContext = JsonPath.using(config).parse(context)

    fun evaluate(expression: String): JsonElement? {

        logger.trace("Evaluating expression `{}` with context {}", expression, context)
        val result: Any? = ctx.read(expression)
        logger.trace("Evaluated expression `{}` with result `{}` using context {} ", expression, result, context)

        /*
        Since the context is a JsonObject, we know that lists and maps will contain JsonElements.
         */

        // when we use a jsonpath like `$.cars.*.model` we get a list of strings - this needs to be converted to a JsonArray
        if (result is List<*>) {
            @Suppress("UNCHECKED_CAST")
            return JsonArray(result as List<JsonElement>)
        }
        // to be safe, we check if the result is a Map and convert it to a JsonObject
        if (result is Map<*, *>) {
            @Suppress("UNCHECKED_CAST")
            return JsonObject(result as Map<String, JsonElement>)
        }
        // if the result is a single value, we return it as is
        return result as JsonElement?

    }

}