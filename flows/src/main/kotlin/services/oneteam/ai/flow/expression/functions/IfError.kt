package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import com.dashjoin.jsonata.Jsonata.JFunctionCallable
import kotlinx.serialization.Serializable

@Serializable
internal object Iferror : ComputeFunction {
    override val key = "iferror"
    override val syntax = Syntax("IFERROR(value, value_if_error)", listOf(
        Argument("value", "The value to check", "any"),
        Argument("value_if_error", "The value to return if value is an error", "any")
    ))
    override val description = "Returns value_if_error if value is an error, otherwise returns value"
    override val notes = ""
    override val category = "logical"
    override val icon = Icon("function")
    override val functionName = "IFERROR"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction(JFunctionCallable { _, args ->
        val value = args!![0]
        val valueIfError = args[1]
        if (value is String && value.startsWith("#")) valueIfError else value
    }, "<xx:x>")
}
