package services.oneteam.ai.flow.expression.functions

import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import java.math.BigDecimal

@Serializable
internal object Gte : ComputeFunction {

    val DEFAULT_VALUE: BigDecimal = BigDecimal.ZERO

    override val key = "greaterThanOrEqualsTo"
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "GTE"

    override val syntax = Syntax(
        "$functionName(any, ... )", listOf(
            Argument("any", "Any (or Tensors) to compare", "any"),
            Argument("...", "Additional any (or Tensors) to compare", "any"),
        )
    )
    override val description = "Compare the values (or Tensors) together"
    override val notes = """
        A Tensor can be a single value (scalar), an array of values (vector), or a matrix of values.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()
    override val examples = listOf(
        Example(
            "$$functionName(1, 1) = true",
            "Compare values",
        ),
        Example(
            "$$functionName(3, 2) = true",
            "Compare values",
        ),
        Example(
            "$$functionName([30, 10], [20, 10]) = [true, true]",
            "Compare lists of values",
        ),
        Example(
            "$$functionName([10, 20], 10) = [true, true]",
            """
                Dimension uplift then pairwise comparison.
                [10, 20], [10] => [10, 20] , [10, 10]
            """.trimIndent(),
        ),
        Example(
            "$$functionName([10, -5], [10]) = [true, false]",
            """
                Dimension padded then pairwise comparison.
                [10, -5], [10] => [10, -5] , [10, 0]
            """.trimIndent(),
        ),
        Example(
            "$$functionName(\"b\",\"a\") = true",
            """
                String compare
            """.trimIndent(),
        ),
    )

    val implementations: List<MatchableFunction> = listOf(
        ErrorOnList(functionName),
        Pairwise(PairwiseOperation.GTE, DEFAULT_VALUE)
    )

    @Transient
    override val evaluatorFunction = buildJFunctionForImplementations(implementations, "<x+:x>", functionName)

}
