package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.support.pairwise.Pairwise
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.flow.support.pairwise.PairwiseValue

@Serializable
internal object Lt : ComputeFunction {
    override val key = "lessThan"
    override val syntax = Syntax(
        "LT(any, ... )", listOf(
            Argument("any", "Any (or Tensors) to compare", "any"),
            Argument("...", "Additional any (or Tensors) to compare", "any"),
        )
    )
    override val description = "Compare the values (or Tensors) together"
    override val notes = """
        A Tensor can be a single value (scalar), an array of values (vector), or a matrix of values.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()
    override val examples = listOf(
        Example(
            "\$LT(1, 1) = false",
            "Compare values",
        ),
        Example(
            "\$LT(1, 2) = true",
            "Compare values",
        ),
        Example(
            "\$LT([10, 10], [20, 10]) = [true, false]",
            "Compare lists of values",
        ),
        Example(
            "\$LT([10, 0], 10) = [false, true]",
            """
                Dimension uplift then pairwise comparison.
                [10, 0], [10] => [10, 0] , [10, 10]
            """.trimIndent(),
        ),
        Example(
            "\$LT([10, 5], [10]) = [false, false]",
            """
                Dimension padded then pairwise comparison.
                [10, 5], [10] => [10, 5] , [10, 0]
            """.trimIndent(),
        ),
        Example(
            "\$LT(\"a\",\"b\") = true",
            """
                String compare
            """.trimIndent(),
        ),
    )
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "LT"

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        return@JFunction Pairwise(PairwiseOperation.LT).pairwise(args.map { PairwiseValue.of(it ?: 0) }).value
    }, "<x+:x>")

}