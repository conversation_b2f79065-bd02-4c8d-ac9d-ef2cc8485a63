package services.oneteam.ai.flow.expression.conditional

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.descriptors.element
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.*
import services.oneteam.ai.shared.helpers.RegexDefinitions

/**
 * The lhs and rhs values will be wrapped in single quotes if needed.
 */
@Serializable(with = ExpressionSerializer::class)
data class Expression(var expression: String) : Evaluable {
    override fun toExpression(): String {
        return expression
    }
}

fun interface Evaluable {
    // NB: we could invert this to a visitor pattern to avoid the need for toJsonata and
    // class dedicated to transforming the Condition
    fun toExpression(): String
}

class ExpressionSerializer : KSerializer<Expression> {
    override val descriptor = buildClassSerialDescriptor("Expression") {
        element<String>("expression")
    }

    override fun serialize(encoder: Encoder, value: Expression) {
        require(encoder is <PERSON><PERSON><PERSON><PERSON><PERSON>)
        val jsonPrimitive = when {
            value.expression.matches(RegexDefinitions.long) -> JsonPrimitive(value.expression.toLong())
            value.expression.matches(RegexDefinitions.double) -> JsonPrimitive(value.expression.toDouble())
            value.expression.equals("true", ignoreCase = true) || value.expression.equals(
                "false",
                ignoreCase = true
            ) -> JsonPrimitive(value.expression.toBoolean())

            value.expression.equals("null", ignoreCase = true) -> JsonNull
            else -> JsonPrimitive(value.expression)
        }
        encoder.encodeJsonElement(jsonPrimitive)
    }

    override fun deserialize(decoder: Decoder): Expression {
        require(decoder is JsonDecoder)
        val jsonObject = decoder.decodeJsonElement().jsonPrimitive.content
        return Expression(jsonObject)
    }
}