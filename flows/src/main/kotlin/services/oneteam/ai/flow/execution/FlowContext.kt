package services.oneteam.ai.flow.execution

import kotlinx.serialization.*
import kotlinx.serialization.json.*
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.flow.execution.listeners.FlowContextListener
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion

@Serializable
data class FlowContextWithLocalStep(
    val stepId: Step.Id? = null,
    val flowContext: FlowContext,
    val thisStep: MutableMap<String, JsonElement> = mutableMapOf(),
) {

    fun setThisStep(key: String, value: JsonElement) {
        thisStep[key] = value
    }

    fun deepCopy(): FlowContextWithLocalStep {
        return FlowContextWithLocalStep(
            stepId = stepId,
            flowContext = flowContext.copy(
                global = flowContext.global.copy(),
                variables = flowContext.variables.toMutableMap(),
                event = flowContext.event?.copy(),
            ),
            thisStep = thisStep.toMutableMap()
        )
    }

    fun getThisStep(key: String): JsonElement? {
        return thisStep[key]
    }

    fun getVariable(identifier: VariableIdentifier): Variable? {
        return flowContext.variables[identifier]
    }

    fun containsVariable(identifier: VariableIdentifier): Boolean {
        return flowContext.variables.containsKey(identifier)
    }

    override fun toString(): String {
        return Json.encodeToString(this)
    }
}


@Serializable
data class FlowContext(
    val global: GlobalVariables,
    val variables: MutableMap<VariableIdentifier, Variable> = mutableMapOf(),
    val event: Event.ForApi?,
) {
    @Transient
    var listeners = mutableListOf<FlowContextListener>()

    suspend fun set(variable: Variable) {
        variables[variable.identifier] = variable
        listeners.forEach { it.onVariableSet(variable) }
    }

    override fun toString(): String {
        return Json.encodeToString(this)
    }

    companion object {


        // todo use enumeration for type
        @OptIn(ExperimentalSerializationApi::class)
        fun toJsonElement(value: Any?, type: String): JsonElement {
            if (value == null) {
                return JsonNull
            }
            if (value is String && value.isBlank()) {
                return JsonPrimitive(null)
            }
            if (value is JsonElement) {
                return value
            }

            // todo can this all be removed?
            when (type) {
                "text" -> return JsonPrimitive(value.toString())
                "date" -> return JsonPrimitive(value.toString())
                "number" -> {
                    return try {
                        JsonPrimitive(
                            value.toString().toBigDecimal()
                        ) // how to distinguish between int/long and double? - we need properties or more types?
                    } catch (e: NumberFormatException) {
                        return JsonPrimitive(0)
                    }
                }

                "boolean" -> return JsonPrimitive(value.toString().toBoolean())
                "list" -> {
                    return JsonArray((value as List<*>).map { TypeToJsonElementConverter.toJsonElement(it) }.toList())
                }

                "table", "json" -> return TypeToJsonElementConverter.toJsonElement(value)
                "files" -> return TypeToJsonElementConverter.toJsonElement(value)
            }
            throw IllegalArgumentException("Unsupported type `$type`")
        }

    }
}


@Serializable
data class GlobalVariables(
    val workspaceId: Workspace.Id,
    val workspaceVersionId: WorkspaceVersion.Id,
    val tenantId: Long,
    val flowConfigurationId: FlowConfiguration.Id,
    val flowConfigurationName: FlowConfiguration.Name,
    val flowDepth: Int = 1,
) {
    fun toMap(): JsonElement {
        // is this good enough? or does it need customisation?
        return Json.encodeToJsonElement(this)
    }
}

// TODO can we use an enum for type?
const val FILES_VARIABLE_TYPE = "files"

typealias VariableType = String;
typealias VariableIdentifier = String

// AJT: noting down my observations with this current structure. We'll need to fix this at some point.
// This 'VariableDefinition' section has been overloaded and needs to be split up.
// It's got some strange combination of the concepts of 'SetVariable' operation and the Context Variable muddled together
// I also don't know why we have these within the 'scope of a VariableDefinition instead of flattened
// Ideal is to have a VariableDefinition, VariableDeclaration (or VariableAssignment) for our context
//   we need to handle nested properties cos we want something similar to 'types',
//   and then for our SetVariable, we have a VariableConfiguration, SetVariableOperation (which would leverage VariableDefinition / Declaration)
@Serializable
sealed interface VariableDefinition {
    val type: VariableType
    val identifier: VariableIdentifier
    val properties: VariableProperties?

    // this one is really used as part of SetVariable
    @Serializable
    data class VariableConfiguration(
        val id: String? = null,
        override val type: VariableType,
        override val identifier: VariableIdentifier = "",
        override val properties: VariableProperties? = null,
    ) : VariableDefinition

    @Serializable
    data class Variable(
        val value: JsonElement,
        override val type: VariableType,
        override val identifier: VariableIdentifier,
        override val properties: VariableProperties? = null,
    ) : VariableDefinition {

        /**
         * Convert the variable to the proper type. We store numbers as strings, but when we want to use it as a number (e.g. in a calculation), we need to convert it to a number so it won't get quoted.
         */
        fun toProperType(): JsonElement {
            return TypeToJsonElementConverter.convert(type, value)
        }

        companion object {
            fun of(
                value: JsonElement,
                type: VariableType,
                identifier: String,
                properties: VariableProperties? = null
            ): Variable {
                return Variable(value, type, identifier, properties)
            }

            fun of(
                value: Any,
                type: VariableType,
                identifier: String,
                properties: VariableProperties? = null
            ): Variable {
                return Variable(TypeToJsonElementConverter.toJsonElement(value), type, identifier, properties)
            }
        }
    }

    @Serializable(VariablePropertiesSerializer::class)
    interface VariableProperties

    // AJT: no idea why this needs to be self-referential. if defining a nested structure the properties would be on each item
    @Serializable
    data class JsonVariableProperties(
        val isUnstructured: Boolean = false,
        val items: List<VariableConfiguration>? = null,
        val properties: JsonVariableProperties? = null
    ) : VariableProperties

    @Serializable
    data class OtherVariableProperties(
        val required: Boolean? = false,
    ) : VariableProperties

    @Serializable
    data class TableVariableProperties(
        val operation: TableOperation,
        val rowIdentifier: String? = null,
        val rowIndex: JsonElement? = null,
        val columnIdentifier: String? = null,
        val columns: List<VariableConfiguration>? = emptyList(),
    ) : VariableProperties

    @Serializable
    enum class TableOperation {
        @SerialName("setTable")
        SET_TABLE,

        @SerialName("setRow")
        SET_ROW,

        @SerialName("setColumn")
        SET_COLUMN,

        @SerialName("setCell")
        SET_CELL,
    }

    @Serializable
    data class ListVariableProperties(
        val listOperation: ListOperation,
        val itemIndex: JsonElement? = null
    ) : VariableProperties

    @Serializable
    enum class ListOperation {
        @SerialName("setList")
        SET_LIST,

        @SerialName("setItem")
        SET_ITEM,

        @SerialName("removeItem")
        REMOVE_ITEM,
    }

    @Serializable
    data class FileVariableProperties(
        val fileOperation: FileOperation,
        val itemIndex: JsonElement? = null
    ) : VariableProperties

    @Serializable
    enum class FileOperation {
        @SerialName("setFiles")
        SET_FILES,

        @SerialName("addFiles")
        ADD_FILES,

        @SerialName("removeFiles")
        REMOVE_FILES,
    }
}

object VariablePropertiesSerializer :
    JsonContentPolymorphicSerializer<VariableDefinition.VariableProperties>(VariableDefinition.VariableProperties::class) {
    override fun selectDeserializer(element: JsonElement): DeserializationStrategy<VariableDefinition.VariableProperties> {
        require(element is JsonObject) { "Unsupported type" }

        return when {
            element.containsKey("items") -> VariableDefinition.JsonVariableProperties.serializer()
            element.containsKey("operation") -> VariableDefinition.TableVariableProperties.serializer()
            element.containsKey("listOperation") -> VariableDefinition.ListVariableProperties.serializer()
            element.containsKey("fileOperation") -> VariableDefinition.FileVariableProperties.serializer()
            else -> VariableDefinition.OtherVariableProperties.serializer()
        }
    }
}