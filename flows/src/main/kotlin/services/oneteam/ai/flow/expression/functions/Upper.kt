package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.util.*

@Serializable
internal object Upper : ComputeFunction {
    override val key = "upper"
    override val syntax = Syntax(
        "UPPER(string)", listOf(
            Argument("string", "The string to convert to uppercase", "string")
        )
    )
    override val description = "Convert a string to uppercase"
    override val notes = "one of the basic string functions, to convert a string to uppercase"
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "UPPER"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        //might need to check users locale
        args[0].toString().uppercase(Locale.getDefault())
    }, "<s:s>")
}
