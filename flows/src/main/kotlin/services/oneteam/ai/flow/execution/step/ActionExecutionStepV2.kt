package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.flow.execution.payload.filter.ProxyResponseFilter
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.includeInternalServiceAccount

/**
 * This class is responsible for executing an action step in the flow execution. Actions are calls to APIs. The response
 * will be put into the flow context and can be used in the next steps. Variable mappings allow for extracting values from
 * the response and putting them into the flow context variables.
 *
 * Sub-steps:
 *
 * 1a. inputs - replace placeholders and add to the context in `thisStep`.
 * 1b. apiCall - replace placeholders in the step type configuration template
 * 2.  execute the api call
 * 3.  process the response - add response to the context in `thisStep`
 * 4.  process variable mappings - add to the context as variables
 *
 * An action step configuration looks like this:
 *
 * ```json
 *   {
 *     "id": "1cwpKek19oNQTJSRCiTvl",
 *     "name": "Select form",
 *     "next": "gbRQdKuuGBgMU896mni-l",
 *     "properties": {
 *       "inputs": {
 *         // never jsonata expressions
 *         "continueFlowIfNotFound": "true",
 *         "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4",
 *         "formVariableName": "CPForm",
 *         "foundationId": "{{foundation.id}}"
 *       },
 *       "typePrimaryIdentifier": "selectForm"
 *     },
 *     "variant": "action"
 *   }
 *   ```
 *
 * Step type configuration comes from the database, matching the `typePrimaryIdentifier` in the step configuration:
 * ```json
 *   {
 *     "primaryIdentifier": "selectForm",
 *     "name": "Select form",
 *     "description": "",
 *     "type": "action",
 *     "properties": {
 *       "icon": {
 *         "name": "search"
 *       },
 *       "isLocked": true,
 *       "isHidden": false,
 *       "configuration": {
 *         "content": [
 *           {
 *             "text": "Foundation ID",
 *             "type": "variable",
 *             "identifier": "foundationId",
 *             "properties": {
 *               "type": "text",
 *               "required": true
 *             }
 *           },
 *           {
 *             "text": "Form configuration ID",
 *             "type": "variable",
 *             "identifier": "formConfigurationId",
 *             "properties": {
 *               "type": "select",
 *               "required": true,
 *               "properties": {
 *                 "dynamicOptions": {
 *                   "tag": "formConfigurationId"
 *                 }
 *               }
 *             }
 *           },
 *           {
 *             "text": "Series interval ID",
 *             "type": "variable",
 *             "identifier": "intervalId",
 *             "properties": {
 *               "type": "select",
 *               "required": false,
 *               "defaultValue": "",
 *               "properties": {
 *                 "dynamicOptions": {
 *                   "tag": "intervalConfigurationId",
 *                   "body": {
 *                     "formConfigurationId": "{{thisStep.formConfigurationId}}"
 *                   }
 *                 }
 *               }
 *             }
 *           },
 *           {
 *             "text": "Form variable name",
 *             "type": "variable",
 *             "identifier": "formVariableName",
 *             "properties": {
 *               "type": "text",
 *               "required": false,
 *               "properties": {
 *                 "regex": "^[a-zA-Z0-9_]*$",
 *                 "defaultValue": "form__step_{{thisStep.id}}"
 *               }
 *             }
 *           },
 *           {
 *             "text": "Continue flow if form is not found",
 *             "type": "select",
 *             "identifier": "continueFlowIfNotFound",
 *             "properties": {
 *               "required": true,
 *               "options": [
 *                 {
 *                   "value": "true",
 *                   "label": "Yes"
 *                 },
 *                 {
 *                   "value": "false",
 *                   "label": "No"
 *                 }
 *               ],
 *               "defaultValue": "true"
 *             }
 *           }
 *         ],
 *         "apiCall": {
 *           "url": "/ai/api/forms/obtain",
 *           "internal": true,
 *           "method": "POST",
 *           "body": {
 *             "workspaceId": "{{global.workspaceId}}",
 *             "foundationId": "{{thisStep.foundationId}}",
 *             "formConfigurationId": "{{thisStep.formConfigurationId}}",
 *             "intervalId": "{{thisStep.intervalId}}",
 *             "allowNull": "{{thisStep.continueFlowIfNotFound}}"
 *           },
 *           "response": {
 *             "type": "json",
 *             "properties": {
 *               "items": [
 *                 {
 *                   "type": "form.minimal",
 *                   "identifier": "form"
 *                 }
 *               ]
 *             }
 *           }
 *         },
 *         "variableMappings": [
 *           {
 *             "type": "form.{{thisStep.formConfigurationId}}",
 *             "identifier": "{{thisStep.formVariableName}}",
 *             "value": "{{thisStep.response.form.id}}"
 *           }
 *         ]
 *       }
 *     }
 *   }
 * ```
 */
class ActionExecutionStepV2(
    val step: FlowExecution.Step,
    val stepTypeConfiguration: FlowStepTypeConfiguration,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
    val proxyService: ProxyService,
    val internalProxyService: ProxyService,
    val filePressService: FilePressService
) : ExecutionStep {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun populate(context: FlowContextWithLocalStep) {
        logger.trace("Populating action step")
        processInputs(context)
        processApiCall(context)
    }

    private suspend fun processApiCall(context: FlowContextWithLocalStep) {

        val valueProvider = ContextPlaceholderValueProvider(contextToJsonObjectBuilder, context)

        // Get the original API call
        val originalApiCall = stepTypeConfiguration.properties!!.configuration!!.apiCall!!
        val apiCallJson = Json.encodeToJsonElement(originalApiCall)
        val bodyProperties = apiCallJson.jsonObject["bodyProperties"]?.jsonObject
        val result = processJsonElement(null, apiCallJson, valueProvider, bodyProperties, context) as JsonObject

        // Store the result in the flow execution document
        logger.trace("Resolved api call to {}", result)
        step.properties.apiCall = result
    }

    /**
     * Recursively processes a JsonElement, replacing placeholders and evaluating JSONata expressions.
     */
    private suspend fun processJsonElement(
        key: String? = null,
        element: JsonElement,
        valueProvider: ContextPlaceholderValueProvider,
        bodyProperties: JsonObject?,
        context: FlowContextWithLocalStep
    ): JsonElement {
        return when (element) {
            is JsonObject -> {
                val processedMap = element.entries.associate { (key, value) ->
                    key to processJsonElement(key, value, valueProvider, bodyProperties, context)
                }
                JsonObject(processedMap)
            }

            is JsonArray -> {
                val processedList = element.map {
                    processJsonElement(
                        element = it, valueProvider = valueProvider, bodyProperties = bodyProperties, context = context
                    )
                }
                JsonArray(processedList)
            }

            is JsonPrimitive -> {
                // Check if this is a JSONata expression
                val isExpression =
                    key != null && bodyProperties != null && bodyProperties.containsKey(key) && bodyProperties[key]?.jsonObject?.get(
                        "isExpression"
                    )?.jsonPrimitive?.boolean == true

                if (isExpression) {
                    try {
//                        val replacedValueStr = JsonataPlaceHolderReplacer().replacePlaceholders(element.content, valueProvider)
                        val expression = element.content
                        val stepJsonataResolver = StepJsonataResolver(context, contextToJsonObjectBuilder)
                        val result = stepJsonataResolver.resolve(expression)
                        // Evaluate the JSONata expression
//                        val result = JsonataExpressionEvaluator().evaluate(replacedValueStr, emptyMap<String, String>())
                        TypeToJsonElementConverter.toJsonElement(result)
                    } catch (e: Exception) {
                        logger.error("Failed to evaluate JSONata expression: $element", e)
                        Json.parseToJsonElement(
                            StringPlaceholderReplacer().replacePlaceholders(
                                Json.encodeToString(
                                    element
                                ), valueProvider
                            )
                        )
                    }
                } else {
                    Json.parseToJsonElement(
                        StringPlaceholderReplacer().replacePlaceholders(
                            Json.encodeToString(element), valueProvider
                        )
                    )
                }
            }
        }
    }

    private suspend fun processInputs(context: FlowContextWithLocalStep) {
        processInputs(context, contextToJsonObjectBuilder, step, stepTypeConfiguration)
    }

    /**
     * If a property is marked as nullable, and the value is an empty string, set it to null.
     */
    fun getBody(context: JsonObject?, properties: JsonObject?): JsonObject {
        context?.let {
            val mutableContext = context.toMutableMap()
            properties?.forEach { (key, value) ->
                value.jsonObject["nullable"]?.jsonPrimitive?.boolean?.takeIf { it }?.let {
                    mutableContext[key]?.takeIf { it is JsonPrimitive && it.jsonPrimitive.isString && it.jsonPrimitive.content == "" }
                        ?.let {
                            mutableContext[key] = JsonNull
                        }
                }
            }
            return JsonObject(mutableContext)
        }
        return JsonObject(emptyMap())
    }

    override suspend fun execute(context: FlowContextWithLocalStep): NextStepId? {

        val apiCall = stepTypeConfiguration.properties?.configuration?.apiCall
            ?: throw IllegalArgumentException("No apiCall found for action step in ${stepTypeConfiguration.primaryIdentifier}")

        // call api
        val response = executeApiCall(if (apiCall.internal) internalProxyService else proxyService)

        // handle response
        if (response.status != ProxyService.ProxyEndpointResponseStatus.SUCCESS) {
            if (!apiCall.internal) {
                setResponse(response, context, step, stepTypeConfiguration)
            }
            logger.warn("Proxy Service call did not succeed")
            throw FlowRunnerException("Proxy Service call did not succeed: Error - ${response.error}, - Response ${response.response}")
        }
        setResponse(response, context, step, stepTypeConfiguration)
        return step.next
    }

    private suspend fun setResponse(
        response: ProxyService.ProxyEndpointResponse,
        context: FlowContextWithLocalStep,
        step: FlowExecution.Step,
        stepTypeConfiguration: FlowStepTypeConfiguration
    ) {
        val responseAsJsonElement = Json.parseToJsonElement(response.response!!)
        context.setThisStep("response", responseAsJsonElement)

        // Store the response in the flow execution document
        step.properties.apiCall = JsonObject(step.properties.apiCall!! + ("response" to responseAsJsonElement))

        // Process variable mappings
        processVariableMappings(stepTypeConfiguration.properties!!.configuration, context)
    }

    private suspend fun processVariableMappings(
        configuration: FlowStepType.Properties.Configuration?, context: FlowContextWithLocalStep
    ) {

        val valueProvider = ContextPlaceholderValueProvider(
            contextToJsonObjectBuilder.copy(cacheKey = contextToJsonObjectBuilder.cacheKey + ".after"), context
        )

        // evaluate variable mappings after we have the response from the api call
        val resolvedVariableMappingsString = StringPlaceholderReplacer().replacePlaceholders(
            Json.encodeToString(configuration!!.variableMappings), valueProvider
        )
        val variables: List<Variable> = Json.decodeFromString(resolvedVariableMappingsString)

        // add variables to context
        variables.forEach { context.flowContext.set(it) }
    }

    private suspend fun executeApiCall(proxy: ProxyService): ProxyService.ProxyEndpointResponse {
        val resolvedApiCall = step.properties.apiCall!!
        val isInternal = resolvedApiCall["internal"]?.jsonPrimitive?.boolean == true

        val isMultipart = resolvedApiCall["headers"]?.jsonObject?.any {
            it.key.equals("Content-Type", true) && it.value.jsonPrimitive.content.contains("multipart/form-data", true)
        } == true

        val requestBody = if (isMultipart) {
            multipartCall(resolvedApiCall)
        } else {
            jsonCall(isInternal, resolvedApiCall)
        }

        logger.trace("Request to proxy service is {}", requestBody)
        // TODO: make proxy timeout env variable
        val timeoutMillis = 60_000L
        val response = proxy.call(requestBody, !isInternal, timeoutMillis = timeoutMillis)
        logger.trace("Response from proxy service is {}", response)
        return ProxyResponseFilter().filterProxyResponseByStatusCode(
            step.properties.inputs["response"] as? JsonArray?,
            response
        )
    }

    private suspend fun jsonCall(isInternal: Boolean, apiInfoObject: JsonObject): ProxyService.ProxyEndpointBody {
        return if (isInternal) {
            ProxyService.ProxyEndpointBody(
                url = ProxyService.buildInternalTenantUrl(apiInfoObject.getValue("url").jsonPrimitive.content),
                method = apiInfoObject.getValue("method").jsonPrimitive.content,
                body = getBody(apiInfoObject["body"]?.jsonObject, apiInfoObject["bodyProperties"]?.jsonObject),
                queryParams = apiInfoObject["queryParams"]?.jsonObject?.map { it.key to it.value.jsonPrimitive.content }
                    ?.toMap(),
            ).includeInternalServiceAccount()
        } else {
            val bodyJsonObject = if (apiInfoObject["body"] is JsonObject) apiInfoObject["body"] else null
            ProxyService.ProxyEndpointBody(
                url = apiInfoObject.getValue("url").jsonPrimitive.content,
                method = apiInfoObject.getValue("method").jsonPrimitive.content,
                headers = apiInfoObject["headers"]?.jsonObject?.map { it.key to it.value.jsonPrimitive.content }
                    ?.toMap(),
                body = getBody(bodyJsonObject?.jsonObject, apiInfoObject["bodyProperties"]?.jsonObject),
                queryParams = apiInfoObject["queryParams"]?.jsonObject?.map { it.key to it.value.jsonPrimitive.content }
                    ?.toMap(),
            )
        }
    }

    private fun multipartCall(apiInfoObject: JsonObject): ProxyService.ProxyEndpointBody {

        val body =
            apiInfoObject["body"]?.jsonObject ?: throw IllegalArgumentException("No body found for multipart API call")

        val fileInfos: MutableList<ProxyService.FileInfo> = mutableListOf()

        body["files"]?.jsonObject?.forEach { file ->

            val path = file.value.jsonObject["path"]?.jsonPrimitive?.content
                ?: throw IllegalArgumentException("No file path found for multipart API call")

            val fileName = file.value.jsonObject["name"]?.jsonPrimitive?.content
                ?: throw IllegalArgumentException("No file name found for multipart API call")

            fileInfos += ProxyService.FileInfo(
                fileBytes = filePressService.getFileBytes(path),
                fileName = fileName,
                fileKey = file.key
            )
        }

        return ProxyService.ProxyEndpointBody(
            url = apiInfoObject.getValue("url").jsonPrimitive.content,
            method = apiInfoObject.getValue("method").jsonPrimitive.content,
            headers = apiInfoObject["headers"]?.jsonObject?.map { it.key to it.value.jsonPrimitive.content }?.toMap(),
            fileInfos = fileInfos
        )
    }
}