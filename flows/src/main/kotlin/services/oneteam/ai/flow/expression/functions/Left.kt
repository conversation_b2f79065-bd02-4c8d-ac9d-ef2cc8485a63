package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Left : ComputeFunction {
    override val key = "left"
    override val syntax = Syntax("LEFT(text, numChars)", listOf(
        Argument("text", "The text to extract from", "string"),
        Argument("numChars", "The number of characters to extract", "number")
    ))
    override val description = "Extracts a specified number of characters from the left side of a text string"
    override val notes = "e.g. LEFT('Hello, world!', 5) returns 'Hello'"
    override val category = "text"
    override val icon = Icon("function")
    override val functionName = "LEFT"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val text = (args!![0] as String).trim()
        val numChars = args[1] as Int
        text.take(numChars)
    }, "<sn:s>")
}

