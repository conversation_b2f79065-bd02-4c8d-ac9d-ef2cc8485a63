package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.datetime.*
import kotlinx.serialization.Serializable

@Serializable
internal object AddDays : ComputeFunction {
    override val key = "addDays"
    override val syntax = Syntax(
        "ADDDAYS(date, days)", listOf(
            Argument("date", "The date to add days to", "string"),
            Argument("days", "The number of days to add", "number")
        )
    )
    override val description = "Add days to a date"
    override val notes = "Add days to a date"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "ADDDAYS"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val date = args[0].toString()
        val days = args[1].toString().toInt()

        val localDate = LocalDate.parse(date)
        val result = localDate.plus( DatePeriod(days = days) ).toString()

        return@JFunction result
    }, "<sx:s>")
}
