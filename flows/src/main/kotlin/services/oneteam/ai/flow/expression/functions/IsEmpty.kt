package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object IsEmpty : ComputeFunction {
    override val key = "isEmpty"
    override val syntax = Syntax(
        "IS_EMPTY(value)", listOf(
            Argument("value", "The value to check if empty", "any")
        )
    )
    override val description = arrayOf(
        "Checks if the given value is empty"
    ).joinToString("\n")
    override val notes = arrayOf(
        "A value is empty if:",
        "- It is \"null\", \"undefined\", or an empty string.",
        "- It is an object with no keys or an array where all elements are empty.",
        "- It is a deeply nested structure where all elements or keys are empty."
    ).joinToString("\n")
    override val category = ""
    override val icon = Icon("function")
    override val functionName = "IS_EMPTY"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args: MutableList<Any?>? ->
        checkIfEmpty(args!!.first())
    }, "<x?:b>")

    private fun checkIfEmpty(value: Any?): Boolean {
        return when (value) {
            null -> true // if value == undefined
            is Boolean -> false
            is String -> value.isEmpty()
            is Number -> checkIfEmpty(value.toString())
            is List<*> -> value.all { element -> checkIfEmpty(element) }
            is Map<*, *> -> value.isEmpty()
            else -> true // if value == null and any other types
        }
    }
}
