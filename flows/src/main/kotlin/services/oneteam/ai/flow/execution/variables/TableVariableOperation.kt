package services.oneteam.ai.flow.execution.variables

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.VariableDefinition.*
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.helpers.getContent

/**
 * Resolver for table variables.
 *
 * When we have a table variable, we must supply properties with a table operation (SET_TABLE, SET_ROW, SET_COLUMN, SET_CELL).
 * This is so we can manipulate an existing table variable in the flow context. We won't always be replacing the whole table,
 * for example we may have an iterator which can build up a table variable one row at a time.
 */
class TableVariableOperation(val useJsonata: Boolean) : VariableOperation {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override fun match(variable: Variable): Boolean {
        return variable.type == "table"
    }

    override suspend fun resolve(
        variable: Variable, context: FlowContextWithLocalStep
    ): Variable {

        val tableProperties = variable.properties as TableVariableProperties

        logger.trace("Executing table operation `{}`", tableProperties.operation)

        // TODO remove useJsonata when we move to using ExecutionStepFactoryV2
        val resultValue = if (useJsonata) JsonataExpressionEvaluator().evaluate(
            variable.value.getContent(),
            mapOf<Any, Any>()
        ) else variable.value

        val variableValue = when (tableProperties.operation) {
            TableOperation.SET_TABLE -> handleSetTable(resultValue)
            TableOperation.SET_ROW -> handleSetRow(variable, context, resultValue)
            TableOperation.SET_COLUMN -> handleSetColumn(variable, context, resultValue)
            TableOperation.SET_CELL -> handleSetCell(variable, context, resultValue)
        }

        logger.trace("Result of expression evaluation is `{}`", Json.encodeToString(variableValue))
        return variable.copy(value = variableValue)
    }

    private fun handleSetTable(value: Any): JsonElement {
        if (value is JsonElement) {
            return value
        }

        val transformedValue = buildJsonArray {
            value as List<*>
            value.forEachIndexed { _, row ->
                val rowAsJsonElement = row?.let { TypeToJsonElementConverter.toJsonElement(it) }
                val rowAsJsonObject = rowAsJsonElement?.jsonObject?.toMutableMap()
                // if table variable is from a table, remove _rowIndex as its extraneous data
                // we keep _rowId as it is used to identify rows
                rowAsJsonObject?.remove("_rowIndex")
                rowAsJsonObject?.let { JsonObject(it) }?.let { add(it) }
            }
        }

        return FlowContext.toJsonElement(transformedValue, "table")
    }

    @OptIn(ExperimentalSerializationApi::class)
    private fun handleSetRow(
        variable: Variable, context: FlowContextWithLocalStep, value: Any
    ): JsonElement {
        // if table variable is from a table, remove _rowIndex as its extraneous data
        // we keep _rowId as it is used to identify rows
        val valueAsJsonObject = TypeToJsonElementConverter.toJsonElement(value).jsonObject.toMutableMap()
        valueAsJsonObject.remove("_rowIndex")
        val valueAsJsonElement = JsonObject(valueAsJsonObject)

        val existingTableValue = context.flowContext.variables[variable.identifier]?.value ?: buildJsonArray {}
        val tableProperties = variable.properties as TableVariableProperties

        if (tableProperties.rowIdentifier?.isNotBlank() == true) {
            val newTableValue = JsonArray(
                existingTableValue.jsonArray.map { row ->
                    if (row.jsonObject["rowId"]?.jsonPrimitive?.content == tableProperties.rowIdentifier) valueAsJsonElement
                    else row
                })
            return newTableValue
        }

        if (tableProperties.rowIndex != null) {
            val newTableValue = JsonArray(
                existingTableValue.jsonArray.mapIndexed { index, row ->
                    val rowIndex = index + 1
                    if (rowIndex == tableProperties.rowIndex.jsonPrimitive.int) valueAsJsonElement else row
                })
            return newTableValue
        }

        // Check if the last row is a placeholder
        if (existingTableValue.jsonArray.isNotEmpty() && existingTableValue.jsonArray.last().jsonObject.isEmpty()) {
            val newTableValue = JsonArray(existingTableValue.jsonArray.dropLast(1) + valueAsJsonElement)
            return newTableValue
        }

        val newTableValue = buildJsonArray {
            addAll(existingTableValue.jsonArray)
            add(valueAsJsonElement)
        }
        return newTableValue
    }

    private fun handleSetColumn(
        variable: Variable, context: FlowContextWithLocalStep, value: Any
    ): JsonElement {
        val valueAsJsonElement = TypeToJsonElementConverter.toJsonElement(value)
        val existingTableValue = context.flowContext.variables[variable.identifier]?.value ?: buildJsonArray { }
        val tableProperties = variable.properties as TableVariableProperties

        require(tableProperties.columnIdentifier?.isNotBlank() == true) { "Column identifier must be provided for setting a column" }

        val existingRows = existingTableValue.jsonArray
        val newValues = valueAsJsonElement.jsonArray
        val maxRows = maxOf(existingRows.size, newValues.size)

        val newTable = (0 until maxRows).map { i ->
            if (i < existingRows.size) {
                if (i < newValues.size) {
                    existingRows[i].jsonObject.replaceValue(
                        tableProperties.columnIdentifier, newValues[i]
                    )
                } else if (existingRows[i].jsonObject.containsKey(tableProperties.columnIdentifier)) {
                    // if new value is not provided for the row, clear the answer
                    existingRows[i].jsonObject.replaceValue(tableProperties.columnIdentifier, JsonNull)
                } else {
                    existingRows[i]
                }
            } else {
                buildJsonObject { put(tableProperties.columnIdentifier, newValues[i]) }
            }
        }
        return JsonArray(newTable)
    }


    private fun handleSetCell(
        variable: Variable, context: FlowContextWithLocalStep, value: Any
    ): JsonElement {
        val valueAsJsonElement = TypeToJsonElementConverter.toJsonElement(value)
        val existingTableValue = context.flowContext.variables[variable.identifier]?.value ?: buildJsonArray {}
        val tableProperties = variable.properties as TableVariableProperties

        require(tableProperties.columnIdentifier?.isNotBlank() == true) { "Column identifier must be provided for setting a cell" }
        require(tableProperties.rowIdentifier?.isNotBlank() == true || tableProperties.rowIndex != null) { "Row identifier or index must be provided for setting a cell" }

        if (tableProperties.rowIdentifier?.isNotBlank() == true) {
            val newTableValue = JsonArray(
                existingTableValue.jsonArray.map { row ->
                    val rowId = row.jsonObject["rowId"]?.jsonPrimitive?.content
                    if (rowId == tableProperties.rowIdentifier) {
                        row.jsonObject.replaceValue(tableProperties.columnIdentifier, valueAsJsonElement)
                    } else {
                        row
                    }
                })
            return newTableValue
        }

        val newTableValue = JsonArray(
            existingTableValue.jsonArray.mapIndexed { index, row ->
                val rowIndex = index + 1
                if (rowIndex == tableProperties.rowIndex?.jsonPrimitive?.int) {
                    row.jsonObject.replaceValue(tableProperties.columnIdentifier, valueAsJsonElement)
                } else {
                    row
                }
            })
        return newTableValue
    }
}

fun JsonObject.replaceValue(key: String, value: JsonElement): JsonObject {
    return JsonObject(this.toMutableMap().apply { put(key, value) })
}
