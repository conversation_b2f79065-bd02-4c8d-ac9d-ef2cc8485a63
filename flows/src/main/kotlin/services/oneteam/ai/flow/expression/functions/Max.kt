package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.support.pairwise.Pairwise
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.flow.support.pairwise.PairwiseValue
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import services.oneteam.ai.shared.extensions.toBigDecimalList
import java.math.BigDecimal

@Serializable
internal object Max : ComputeFunction {
    override val key = "max"
    override val category = "array"
    override val icon = Icon("function")
    override val functionName = "MAX"

    override val syntax = Syntax(
        "MAX(number, ... )", listOf(
            Argument("number", "Numbers (or Tensors) to find the maximum value", "number"),
            Argument("...", "Additional numbers (or Tensors) to find the maximum value", "number")
        )
    )
    override val description = "Find the maximum value in an array"
    override val notes = """
        A Tensor can be a single number (scalar), an array of numbers (vector), or a matrix of numbers.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()
    override val examples = listOf(
        Example(
            "$${functionName}(1,2,3) => 1",
            "Returns the maximum of the numbers"
        ),
        Example(
            "$${functionName}([1,2,6],[4,5,3]) => [1,2,3]",
            "Pairwise maximum of two lists of numbers"
        ),
        Example(
            "$${functionName}([1,2,3], 5) => [1,2,3]",
            "Lower dimension is uplifted (5 -> [5,5,5]) then find pairwise maximum",
        ),
        Example(
            "$${functionName}([1,2,3], [4,5]) => [1, 2, 0]",
            "Finds pairwise maximum of two lists of numbers, shorter list is padded with 0",
        ),
        Example(
            "$${functionName}([4, 5], [[1, 2, 3], [5, 4, 3]]) => [[1,2,0], [4,4,0]]",
            """
                Dimension uplift and padded then find pairwise maximum.
                => [[4,5,0],[4,5,0]] , [[1,2,3], [5,4,3]] 
                => [[1,2,0], [4,4,0]]
            """.trimIndent(),
        ),
    )

    val implementations: List<MaxFunction> = listOf(
        FuzzyMax(),
        FuzzyListMax(),
        MaxPairwise()
    )

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->

        return@JFunction implementations.first { it.match(args) }.let {
            return@JFunction (it.max(args))
        }.getOrElse { throw IllegalArgumentException("Invalid arguments for max function") }

    }, "<x+:x>")
}


interface MaxFunction {
    fun match(args: List<*>): Boolean
    fun max(args: List<*>): Any
}

class FuzzyListMax : MaxFunction {
    override fun match(args: List<*>): Boolean {
        return args.size == 1 && args[0] is List<*>
    }

    override fun max(args: List<*>): Number {
        return (args[0] as List<*>).toBigDecimalList().maxOfOrNull { it }?.convertToNiceBigDecimal() ?: BigDecimal.ZERO
    }
}

class FuzzyMax : MaxFunction {
    override fun match(args: List<*>): Boolean {
        return args.all { it !is List<*> }
    }

    override fun max(args: List<*>): Number {
        return args.toBigDecimalList().maxOfOrNull { it }?.convertToNiceBigDecimal() ?: BigDecimal.ZERO
    }
}

/**
 * if multiple arrays of numbers given, assume we are doing pairwise maximum
 */
class MaxPairwise : MaxFunction {

    // if all args are arrays, zip them together and find the maximum
    override fun match(args: List<*>): Boolean {
        return true // act as a catch-all
    }

    override fun max(args: List<*>): Any {
        return Pairwise(PairwiseOperation.MAX).pairwise(args.map { PairwiseValue.of(it ?: 0) }).value
    }

}