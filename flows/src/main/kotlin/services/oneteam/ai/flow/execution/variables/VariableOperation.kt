package services.oneteam.ai.flow.execution.variables

import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.shared.domains.flow.variables.Variable
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance

interface VariableOperation {
    fun match(variable: Variable): Boolean
    suspend fun resolve(variable: VariableInstance, context: FlowContextWithLocalStep): VariableInstance
}