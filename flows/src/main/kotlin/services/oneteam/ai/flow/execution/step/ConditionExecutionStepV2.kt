package services.oneteam.ai.flow.execution.step

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.execution.NextStepId

class ConditionExecutionStepV2(
    val step: FlowExecution.Step,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder
) : ExecutionStep {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun populate(context: FlowContextWithLocalStep) {
        logger.trace("Populated branches {}", step)
    }

    override suspend fun execute(context: FlowContextWithLocalStep): NextStepId? {
        val branches = step.properties.branches ?: return null
        val jsonataResolver = StepJsonataResolver(context, contextToJsonObjectBuilder)

        for (branch in branches) {
            val expression = branch.condition.toExpression()
            logger.trace("Executing step with condition `{}`", expression)
            val evaluatedValue = jsonataResolver.resolve(expression)
            logger.trace("Evaluated condition `{}` to `{}`", expression, evaluatedValue)
            if (evaluatedValue == true) {
                logger.trace("Branching to {}", branch.next)
                return branch.next
            }
        }

        logger.trace("Branching to {}", step.next)
        return step.next
    }

}