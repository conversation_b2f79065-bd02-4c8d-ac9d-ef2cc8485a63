package services.oneteam.ai.flow.execution

import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.CoroutineContext

/**
 * A custom dispatcher that uses a thread pool to run coroutines and limits concurrency so that we can
 * control how much memory and CPU we use.
 *
 * @param name The name of the dispatcher - used in the thread name
 * @param numberOfThreads The number of threads in the thread pool
 * @param maxConcurrentCoroutines The maximum number of coroutines that can run at the same time
 */
class CustomDispatcher(val name: String, numberOfThreads: Int, maxConcurrentCoroutines: Int) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    private val threadCount = AtomicInteger(0)
    private val threadFactory = ThreadFactory { runnable ->
        Thread(runnable, "${name}-${threadCount.incrementAndGet()}")
    }
    private val customThreadPool: ExecutorService = Executors.newFixedThreadPool(numberOfThreads, threadFactory)
    private val customDispatcher = customThreadPool.asCoroutineDispatcher()
    private val semaphore = Semaphore(maxConcurrentCoroutines)

    // todo maybe allow this to be set by the user
    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        logger.error("Unhandled exception in CustomDispatcher", exception)
    }

    private val flowRunnerScope: CoroutineScope = CoroutineScope(customDispatcher + exceptionHandler)

    init {
        logger.info("Starting dispatcher '$name' with $numberOfThreads threads and max concurrency of $maxConcurrentCoroutines")
    }


    fun run(context: CoroutineContext, block: suspend () -> Unit): Job {
        return flowRunnerScope.launch {
            logger.info("Running block in '$name' dispatcher with context: $context")
            semaphore.withPermit {
                logger.info("Acquired permit in '$name' dispatcher")
                withContext(context) {
                    logger.info("Inside context Running block in '$name' dispatcher with context: $context")
                    try {
                        block()
                    } catch (e: Exception) {
                        logger.error("Error running block in '$name' dispatcher", e)
                    } finally {
                        logger.info("Releasing permit in '$name' dispatcher")
                    }
                }
            }
        }
    }

    fun shutdown() {
        logger.info("Shutting down $name dispatcher")
        customThreadPool.shutdown()
        logger.info("Shut down $name dispatcher complete")
    }

    fun isShutdown(): Boolean {
        return customThreadPool.isShutdown
    }
}