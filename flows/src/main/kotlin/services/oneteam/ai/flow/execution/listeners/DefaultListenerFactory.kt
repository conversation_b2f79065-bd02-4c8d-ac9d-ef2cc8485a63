package services.oneteam.ai.flow.execution.listeners

import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.execution.FlowExecutionRepository
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService

class DefaultListenerFactory(
    val documentService: IDocumentService,
    val documentId: FlowExecution.DocumentId,
    val flowExecutionId: FlowExecution.Id,
    val flowExecutionRepository: FlowExecutionRepository,
    val path: String = "",
    val includeLogging: Boolean,
    val skipStepUpdates: <PERSON>olean,
    val skipVariableUpdates: Boolean,
) : ListenerFactory {

    override fun createStepListeners(): List<FlowStepListener> {
        return buildList {
            add(FlowListenerDocumentUpdater(documentService, documentId, path, skipStepUpdates, skipVariableUpdates))
            if (includeLogging) {
                add(FlowListenerLogger())
            }
        }
    }

    override fun createContextListeners(): List<FlowContextListener> {
        return listOf(
            FlowListenerDocumentUpdater(documentService, documentId, path, skipStepUpdates, skipVariableUpdates)
        )
    }

    override fun createFlowListenersForMainFlow(): List<FlowRunListener> {
        return buildList {
            add(FlowListenerDatabaseUpdater(flowExecutionRepository, flowExecutionId))
            add(FlowListenerDocumentUpdater(documentService, documentId, "", skipStepUpdates, skipVariableUpdates))
            if (includeLogging) {
                add(FlowListenerLogger())
            }
            add(FlowListenerNotifier(flowExecutionId, documentId))
        }
    }

    override fun createFlowListenersForSubFlow(): List<FlowRunListener> {
        return buildList {
            add(FlowListenerDocumentUpdater(documentService, documentId, path, skipStepUpdates, skipVariableUpdates))
            if (includeLogging) {
                add(FlowListenerLogger())
            }
        }

    }

    override fun clone(
        path: String,
    ): ListenerFactory {
        return DefaultListenerFactory(
            documentService,
            documentId,
            flowExecutionId,
            flowExecutionRepository,
            "${this.path}${path}.",
            includeLogging,
            skipStepUpdates,
            skipVariableUpdates,
        )
    }
}