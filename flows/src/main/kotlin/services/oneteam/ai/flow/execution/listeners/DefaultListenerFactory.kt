package services.oneteam.ai.flow.execution.listeners

import services.oneteam.ai.flow.execution.BlobStorageFEDRepository
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.execution.FlowExecutionRepository
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService

class DefaultListenerFactory(
    val documentService: IDocumentService,
    val blobStorageFEDRepository: BlobStorageFEDRepository,
    val documentId: FlowExecution.DocumentId,
    val flowExecutionId: FlowExecution.Id,
    val flowExecutionRepository: FlowExecutionRepository,
    val path: String = "",
    val includeLogging: Boolean,
    val skipStepUpdates: Boolean,
    val skipVariableUpdates: Boolean,
    val skipSubFlowFlowUpdates: Boolean,
    val useAutomergeFED: Boolean
) : ListenerFactory {

    override fun createStepListeners(): List<FlowStepListener> {
        return buildList {
            add(
                FlowListenerAutomergeDocumentUpdater(
                    documentService,
                    documentId,
                    path,
                    skipStepUpdates,
                    skipVariableUpdates,
                    skipSubFlowFlowUpdates
                )
            )
            if (includeLogging) {
                add(FlowListenerLogger())
            }
        }
    }

    override fun createContextListeners(): List<FlowContextListener> {
        return listOf(
            FlowListenerAutomergeDocumentUpdater(
                documentService,
                documentId,
                path,
                skipStepUpdates,
                skipVariableUpdates,
                skipSubFlowFlowUpdates
            )
        )
    }

    override fun createFlowListenersForMainFlow(): List<FlowRunListener> {
        return buildList {
            add(FlowListenerDatabaseUpdater(flowExecutionRepository, flowExecutionId))
            if (useAutomergeFED) {
                add(
                    FlowListenerAutomergeDocumentUpdater(
                        documentService,
                        documentId,
                        skipStepUpdates = skipStepUpdates,
                        skipVariableUpdates = skipVariableUpdates,
                        skipFlowUpdates = false,
                    )
                )
            } else {
                add(
                    FlowListenerBlobDocumentUpdater(
                        blobStorageFEDRepository,
                        documentId,
                        skipStepUpdates = skipStepUpdates,
                        skipVariableUpdates = skipVariableUpdates,
                        skipFlowUpdates = false,
                    )
                )
            }
            if (includeLogging) {
                add(FlowListenerLogger())
            }
            add(FlowListenerNotifier(flowExecutionId, documentId))
        }
    }

    override fun createFlowListenersForSubFlow(): List<FlowRunListener> {
        return buildList {
            add(
                FlowListenerAutomergeDocumentUpdater(
                    documentService,
                    documentId,
                    path,
                    skipStepUpdates,
                    skipVariableUpdates,
                    skipSubFlowFlowUpdates
                )
            )
            if (includeLogging) {
                add(FlowListenerLogger())
            }
        }

    }

    override fun clone(
        path: String,
    ): ListenerFactory {
        return DefaultListenerFactory(
            documentService,
            blobStorageFEDRepository,
            documentId,
            flowExecutionId,
            flowExecutionRepository,
            "${this.path}${path}.",
            includeLogging,
            skipStepUpdates,
            skipVariableUpdates,
            skipSubFlowFlowUpdates,
            useAutomergeFED
        )
    }
}