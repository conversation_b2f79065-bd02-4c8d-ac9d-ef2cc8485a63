package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.expression.checkIfEmpty

@Serializable
internal object CountA : ComputeFunction {
    override val key = "counta"
    override val syntax = Syntax(
        "COUNTA(array)", listOf(
            Argument("array", "The array to count non-empty elements", "array")
        )
    )
    override val description = "Count the number of non-empty elements in an array"
    override val notes = "Counts all elements that are not null"
    override val category = "array"
    override val icon = Icon("function")
    override val functionName = "COUNTA"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args.first().let { it ->
            when (it) {
                is List<*> -> {
                    it.count { element ->
                        !checkIfEmpty(element)
                    }
                }

                else -> {
                    0
                }
            }
        }
    }, "<a:n>")
}
