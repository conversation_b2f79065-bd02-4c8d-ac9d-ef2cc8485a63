package services.oneteam.ai.flow.execution.payload.filter

import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import services.oneteam.ai.shared.helpers.getContent

object ConfigMatcher {
    fun findMatchingConfig(configResponseArr: JsonArray?, actualStatusCode: String): JsonObject? {
        return configResponseArr
            ?.asSequence()
            ?.filterIsInstance<JsonObject>()
            ?.firstOrNull { configItem ->
                configItem["httpCode"]?.getContent() == actualStatusCode
            }
    }
}