package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object If : ComputeFunction {
    override val key = "if"
    override val syntax = Syntax(
        "IF(condition, trueValue, falseValue)", listOf(
            Argument("condition", "The condition to evaluate", "boolean"),
            Argument("trueValue", "The value to return if the condition is true", "any"),
            Argument("falseValue", "The value to return if the condition is false", "any")
        )
    )
    override val description = "Conditional expression"
    override val notes = "one of the basic conditional functions, to evaluate a condition and return a value based on the result"
    override val category = "conditional"
    override val icon = Icon("function")
    override val functionName = "IF"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        if (parseBooleanArg(args[0])) {
            args[1]
        } else {
            args[2]
        }
    }, "<xxx?:x>")
}
