package services.oneteam.ai.flow.expression

import kotlinx.serialization.json.*

object ObjectToJsonElement {
    private fun mapToJsonElement(map: Map<String, Any?>): JsonElement {
        return JsonObject(map.mapValues { (_, value) -> anyToJsonElement(value) })
    }

    @Suppress("UNCHECKED_CAST")
    fun anyToJsonElement(value: Any?): JsonElement {
        return when (value) {
            is Map<*, *> -> mapToJsonElement(value as Map<String, Any?>)
            is List<*> -> JsonArray(value.map { anyToJsonElement(it) })
            is String -> JsonPrimitive(value)
            is Number -> JsonPrimitive(value)
            is Boolean -> JsonPrimitive(value)
            null -> JsonNull
            else -> throw IllegalArgumentException("Unsupported type: ${value.javaClass}")
        }
    }
}

