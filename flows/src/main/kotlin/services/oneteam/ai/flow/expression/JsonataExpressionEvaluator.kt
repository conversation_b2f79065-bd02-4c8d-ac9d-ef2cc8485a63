package services.oneteam.ai.flow.expression

import com.dashjoin.jsonata.Jsonata
import kotlinx.serialization.json.JsonNull
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.expression.functions.ComputeFunctions
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter.toJsonElement
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance.SecuredVariable.Companion.SECURED_PREFIX
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariable
import services.oneteam.ai.shared.otSerializer

typealias Expression = String;

class JsonataExpressionEvaluator(
    private val secureWorkspaceVariables: List<WorkspaceVariable.ForServiceFullyRevealed>? = null
) : ExpressionEvaluator<Any, Any> {
    val logger: Logger = LoggerFactory.getLogger(javaClass)


    override suspend fun evaluate(expression: Expression, context: Any): Any {

        //strip quotes from refs
        val expression = secureWorkspaceVariables?.let { expression.stripRefs(it) } ?: expression

        if (logger.isTraceEnabled) {
            logger.trace(
                "Jsonata Evaluating expression `{}` with context {}",
                expression,
                otSerializer.encodeToString(toJsonElement(context))
            )
        }

        if (expression.isBlank()) {
            logger.warn("Jsonata expression is blank")
            return ""
        }

        val jsonataExpression = Jsonata.jsonata(expression)
        // find all functions referenced (this is an optimisation - we could just register everything!)
        ComputeFunctions.definitions.entries.forEach {
            jsonataExpression.registerFunction(it.key, it.value.evaluatorFunction)
        }

        // we build and replace using this context as late in the evaluation process as possible to avoid leaking the values
        val secureVarRefContext =
            secureWorkspaceVariables?.let { buildRefContext(secureWorkspaceVariables) } ?: emptyMap()

        val baseContext = mapOf(
            "TRUE" to true, "Yes" to true, "FALSE" to false, "No" to false,
        )

        val jsonataExpressionContext = if (context is Map<*, *>) {
            context + baseContext as Map<String, Any> + secureVarRefContext
        } else {
            context
        }

        val result = jsonataExpression.evaluate(jsonataExpressionContext)

        logger.trace(
            "Jsonata Evaluated expression `{}`", result?.maskSensitiveValues(secureWorkspaceVariables)
        )

        return result ?: JsonNull
    }

    /**
     * builds a context for the jsonata expression evaluation.
     * this context is a map of variable references to their values.
     */
    private fun buildRefContext(
        workspaceVars: List<WorkspaceVariable.ForServiceFullyRevealed>
    ): Map<String, Any> {
        return workspaceVars.associate { SECURED_PREFIX + it.ref.ref to it.value.revealedValue }
    }

    /**
     * strips quotations ("") from secured refs found within the expression string
     * so that we can use them as jsonata variables.
     */
    private fun Expression.stripRefs(workspaceVars: List<WorkspaceVariable.ForServiceFullyRevealed>): Expression {
        return workspaceVars.fold(this) { expr, wsVar ->
            val ref = SECURED_PREFIX + wsVar.ref.ref
            expr.replace("\"${ref}\"", ref)
        }
    }

    /**
     * Masks sensitive values in the expression by replacing them with their references.
     */
    private fun Any.maskSensitiveValues(
        workspaceVars: List<WorkspaceVariable.ForServiceFullyRevealed>?
    ): String = toString().let { str ->
        workspaceVars?.fold(str) { expr, wsVar ->
            expr.replace(wsVar.value.revealedValue, SECURED_PREFIX + wsVar.ref.ref)
        } ?: str
    }
}

