package services.oneteam.ai.flow.expression

import com.dashjoin.jsonata.Jsonata
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonNull
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.expression.functions.ComputeFunctions
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter.toJsonElement

class JsonataExpressionEvaluator : ExpressionEvaluator<Any, Any> {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun evaluate(expression: String, context: Any): Any {

        if (logger.isTraceEnabled) {
            logger.trace(
                "Jsonata Evaluating expression `{}` with context {}",
                expression,
                Json.encodeToString(toJsonElement(context))
            )
        }
        if (expression.isBlank()) {
            logger.warn("Jsonata expression is blank")
            return ""
        }

        val jsonataExpression = Jsonata.jsonata(expression)
        // find all functions referenced (this is an optimisation - we could just register everything!)
        ComputeFunctions.definitions.entries.forEach {
            jsonataExpression.registerFunction(it.key, it.value.evaluatorFunction)
        }

        val baseContext = mapOf(
            "TRUE" to true,
            "Yes" to true,
            "FALSE" to false,
            "No" to false
        )

        val jsonataExpressionContext = if (context is Map<*, *>) {
            context + baseContext as Map<String, Any>
        } else {
            context
        }

        val result = jsonataExpression.evaluate(jsonataExpressionContext)
        logger.trace("Jsonata Evaluated expression `{}`", result)

        return result ?: JsonNull

    }

}