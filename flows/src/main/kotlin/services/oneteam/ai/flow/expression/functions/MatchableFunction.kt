package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import services.oneteam.ai.flow.support.pairwise.Pairwise
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.flow.support.pairwise.PairwiseValue

interface MatchableFunction {
    fun match(args: List<*>): Boolean
    fun perform(args: List<*>): Any
}

class FuzzyList(private val fuzzy: MatchableFunction) : MatchableFunction {
    override fun match(args: List<*>): Boolean {
        return args.size == 1 && args[0] is List<*> && (args[0] as List<*>).all { it !is List<*> }
    }

    override fun perform(args: List<*>): Any {
        return fuzzy.perform(args[0] as List<*>)
    }
}

class ErrorOnList(private val functionName: String) : MatchableFunction {
    override fun match(args: List<*>): Boolean {
        return args.size == 1 && args[0] is List<*>
    }

    override fun perform(args: List<*>): Any {
        throw InvalidParametersException(functionName)
    }
}

/**
 * if multiple arrays of numbers given, assume we are doing pairwise
 */
class Pairwise(val operation: PairwiseOperation, val defaultValue: Any) : MatchableFunction {

    override fun match(args: List<*>): Boolean {
        return true // act as a catch-all
    }

    override fun perform(args: List<*>): Any {
        return Pairwise(operation).pairwise(args.map {
            PairwiseValue.of(it ?: defaultValue)
        }).value
    }

}

fun buildJFunctionForImplementations(
    implementations: List<MatchableFunction>,
    signature: String,
    functionName: String
): JFunction {
    return JFunction({ _, args ->
        val implementation = implementations.firstOrNull { it.match(args) }
            ?: throw IllegalArgumentException("Invalid arguments for $functionName function")
        implementation.perform(args)

    }, signature)
}