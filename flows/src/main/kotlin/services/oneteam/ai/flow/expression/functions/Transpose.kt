package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import com.dashjoin.jsonata.Jsonata.JFunctionCallable
import kotlinx.serialization.Serializable

@Serializable
internal object Transpose : ComputeFunction {
    override val key = "transpose"
    override val syntax = Syntax("TRANSPOSE(array)", listOf(
        Argument("array", "The array to transpose", "array")
    ))
    override val description = "Transpose an array"
    override val notes = ""
    override val category = "array"
    override val icon = Icon("function")
    override val functionName = "TRANSPOSE"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction(JFunctionCallable { _, args ->
        // list of [a,b,c] -> [[a],[b],[c]]
        // list of [[a],[b],[c]] -> [a,b,c]
        // range of [[a,b,c],[d,e,f]] -> [[a,d],[b,e],[c,f]]
        // range of [[a,d],[b,e],[c,f]] -> [[a,b,c],[d,e,f]]
        val arrayOrRange = args!![0] as List<*>
        if (arrayOrRange.isEmpty()) {
            return@JFunctionCallable listOf<Any>()
        }

        if (arrayOrRange[0] is List<*>) {
            val range = arrayOrRange as List<List<*>>
            return@JFunctionCallable range[0].indices.map { i -> range.map { it[i] } }
        }

        // list
        arrayOrRange.map { listOf(it) }
    }, "<x:x>")
}
