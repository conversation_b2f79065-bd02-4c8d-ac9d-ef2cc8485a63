package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.shared.JsonValue.valueOrNull
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.collection.form.FormRepository
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService

/**
 * Each map builder supports converting a particular variable type to a json map.
 */
interface MapBuilder {
    suspend fun match(variable: Variable): Boolean
    suspend fun handle(variable: Variable, cacheKey: String? = null): JsonElement
}

class DefaultMapBuilder : MapBuilder {
    override suspend fun match(variable: Variable): Boolean {
        return true
    }

    override suspend fun handle(variable: Variable, cacheKey: String?): JsonElement {
        return variable.toProperType() // put the correctly typed value into the map
    }
}

/**
 * See liveFoundationDataSample.ts for the structure of the foundation JSON
 * See populateRealValuesUsingFlowContext.test.ts
 */
class FoundationJsonMapBuilder(
    private val foundationService: FoundationService,
    private val workspace: Workspace.ForJson,
) : MapBuilder {

    override suspend fun match(variable: Variable): Boolean {
        return variable.type.startsWith("foundation")
    }

    var latestCacheKey: String? = null
    var cache: MutableMap<String, List<Any>> = mutableMapOf()
    suspend fun getOrCache(key: String?, foundationId: Long): List<Any> {
        if (latestCacheKey == null || latestCacheKey != key) {
            cache.clear();
        }
        latestCacheKey = key

        val paramKey = "$key-${foundationId}"
        if (cache.containsKey(paramKey)) {
            return cache[paramKey]!!
        }
        cache.containsKey(key)

        val foundation = foundationService.get(Foundation.Id(foundationId))
        val foundationConfiguration = workspace.foundations.entities[foundation.foundationConfigurationId]!!
        val items = listOf(foundation, foundationConfiguration)
        cache[paramKey] = items
        return items
    }

    override suspend fun handle(variable: Variable, cacheKey: String?): JsonElement {
        try {
            val foundationId = variable.value.toString().trim('"').toLong()
            val cached = getOrCache(cacheKey, foundationId)
            val foundation = cached[0] as Foundation.ForApi
            val foundationConfiguration = cached[1] as FoundationConfiguration.ForApi
            return JsonObject(foundation.toMap(foundationConfiguration))
        } catch (e: Exception) {
            return JsonNull
        }
    }
}

/**
 * See liveFormDataSample.ts for the structure of the form JSON
 * See populateRealValuesUsingFlowContext.test.ts
 */
class FormJsonMapBuilder(
    private val formRepository: FormRepository,
    private val foundationService: FoundationService,
    private val documentService: IDocumentService,
    private val cookie: String? = null,
    private val workspace: Workspace.ForJson,
) : MapBuilder {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun match(variable: Variable): Boolean {
        return variable.type.startsWith("form")
    }

    var latestCacheKey: String? = null
    var cache: MutableMap<String, List<Any>> = mutableMapOf()

    suspend fun getOrCache(key: String?, formId: Long): List<Any> {
        if (latestCacheKey == null || latestCacheKey != key) {
            cache.clear();
        }
        latestCacheKey = key

        val paramKey = "$key-${formId}"
        if (cache.containsKey(paramKey)) {
            return cache[paramKey]!!
        }

        val form = formRepository.getById(formId)!!
        val formConfiguration = workspace.findForm(form.formConfigurationId)
        val foundation = foundationService.get(form.foundationId)
        val foundationConfiguration = workspace.foundations.entities[foundation.foundationConfigurationId]!!
        val answers =
            documentService.show<FormAnswer.ForJson>(form.documentId!!.value, cookie, FormAnswer.ForJson::class, true)

        val items = listOf(form, formConfiguration, foundation, foundationConfiguration, answers)
        cache[paramKey] = items
        return items
    }

    override suspend fun handle(variable: Variable, cacheKey: String?): JsonElement {
        try {
            val formId = variable.value.toString().trim('"').toLong()
            val cached = getOrCache(cacheKey, formId)
            val form = cached[0] as Form.ForApi
            val formConfiguration = cached[1] as FormConfiguration.ForJson
            val foundation = cached[2] as Foundation.ForApi
            val foundationConfiguration = cached[3] as FoundationConfiguration.ForApi
            val answers = cached[4] as FormAnswer.ForJson

            val inflatedAnswers = inflateAnswers(answers, formConfiguration)
            return buildMap(form, formConfiguration, foundation, foundationConfiguration, inflatedAnswers)
        } catch (e: Exception) {
            logger.error("Could not handle variable $variable", e)
            throw e
        }
    }

    private fun inflateAnswers(
        answers: FormAnswer.ForJson, formConfiguration: FormConfiguration.ForJson
    ): FormAnswer.ForJson {
        val inflatedAnswers = mutableMapOf<BaseSection.Id, FormAnswer<*>>()
        formConfiguration.listAllQuestions().forEach { question ->
            val answer = answers.getAnswer(question.id)
            if (answer != null) {
                inflatedAnswers[question.id] = answer
            } else {
                inflatedAnswers[question.id] = formAnswerFromQuestion(question)
            }
        }
        return FormAnswer.ForJson(answers.id, inflatedAnswers)
    }

    private fun formAnswerFromQuestion(question: BaseSection.BaseQuestion): FormAnswer<out Any> {
        // todo add this factory method to QuestionType enum?
        return when (question.type) {
            QuestionType.TEXT -> FormAnswer.TextAnswer(question.id, null)
            QuestionType.TABLE -> FormAnswer.TableAnswer(
                question.id,
                OrderedMap(listOf())
            )

            QuestionType.SELECT -> FormAnswer.SelectAnswer(question.id, null)
            QuestionType.MULTISELECT -> FormAnswer.MultiSelectAnswer(question.id, emptyList())
            QuestionType.BOOLEAN -> FormAnswer.BooleanAnswer(question.id, null)
            QuestionType.NUMBER -> FormAnswer.NumberAnswer(question.id, null)
            QuestionType.DATE -> FormAnswer.DateAnswer(question.id, null)
            QuestionType.FILES -> FormAnswer.FileAnswer(question.id, emptyList())
            QuestionType.JSON -> FormAnswer.JsonAnswer(question.id, emptyMap())
            QuestionType.LIST -> FormAnswer.ListAnswer(
                question.id,
                OrderedMap(listOf())
            )
        }
    }

    fun buildMap(
        form: Form.ForApi,
        formConfiguration: FormConfiguration.ForJson,
        foundation: Foundation.ForApi,
        foundationConfiguration: FoundationConfiguration.ForApi,
        answers: FormAnswer.ForJson
    ): JsonObject {
        val series = if (form.seriesId != null) {
            JsonObject(mapOf("series" to workspace.findSeries(form.seriesId!!).toMap()))
        } else {
            JsonObject(mapOf())
        }

        var map: JsonObject = form.toMap(foundationConfiguration)
        map = JsonObject(map.plus(mapOf("foundation" to foundation.toMap(foundationConfiguration))))

        val formConf = JsonObject(formConfiguration.toMap().plus(series))
        map = JsonObject(map.plus(mapOf("formConfiguration" to formConf)))

        val intervalId = form.intervalId
        val seriesId = formConfiguration.seriesId

        if (seriesId != null && !intervalId?.value.isNullOrEmpty()) {
            val seriesConfiguration = workspace.findSeries(SeriesConfiguration.Id(seriesId))
            val nextInterval = seriesConfiguration.findNextIntervalOrNull(intervalId)
            val next = if (nextInterval != null) {
                JsonObject(mapOf("next" to nextInterval.toMap()))
            } else {
                JsonObject(mapOf("next" to JsonObject(mapOf("id" to JsonNull, "name" to JsonNull))))
            }

            val intervalSeriesConfiguration = JsonObject(mapOf("seriesConfiguration" to workspace.findSeries(SeriesConfiguration.Id(seriesId)).toMap()))

            val previousInterval = seriesConfiguration.findPreviousIntervalOrNull(intervalId)
            val previous = if (previousInterval != null) {
                JsonObject(mapOf("previous" to previousInterval.toMap()))
            } else {
                JsonObject(mapOf("previous" to JsonObject(mapOf("id" to JsonNull, "name" to JsonNull))))
            }
            val seriesInterval = JsonObject(
                workspace.findInterval(SeriesConfiguration.Id(seriesId), intervalId).toMap().plus(intervalSeriesConfiguration).plus(next).plus(previous)
            )

            map = JsonObject(map.plus("seriesInterval" to seriesInterval))
        }

        map = JsonObject(map.plus(AnswersToMapBuilder(workspace).answersToMap(answers, formConfiguration)))

        return map
    }

    class AnswersToMapBuilder(val workspace: Workspace.ForJson) {

        fun answersToMap(
            answers: FormAnswer.ForJson,
            formConfiguration: FormConfiguration.ForJson,
        ): JsonObject {

            var map1 = JsonObject(mapOf())

            answers.answers.forEach { (questionId, answer) ->
                val question = formConfiguration.findQuestion(questionId)
                // todo make sure all of the answer types build the right json
                // table and json and file answers need to be handled differently - see liveFormDataSample.ts
                val answerMap = answer.toJsonElement(workspace)

                val commonFields = mapOf(
                    "id" to JsonPrimitive(question!!.id.value),
                    "type" to JsonPrimitive(question.type.name),
                    "text" to JsonPrimitive(question.text),
                    "identifier" to JsonPrimitive(question.identifier),
                    "description" to JsonPrimitive(question.description),
                )

                val answerObject = when (answer.type) {
                    QuestionType.TABLE -> answerMap
                    QuestionType.LIST -> {
                        val itemAnswer = extractListAnswer(answerMap, question as BaseSection.ListQuestion)
                        JsonObject(commonFields + ("answer" to itemAnswer))
                    }
                    QuestionType.JSON -> {
                        // For JSON questions, process nested questions recursively
                        if (question.type == QuestionType.JSON) {
                            val jsonQuestion = question as BaseSection.JsonQuestion
                            val processedAnswer = processJsonQuestion(jsonQuestion, answerMap.jsonObject)
                            JsonObject(commonFields + ("answer" to processedAnswer))
                        } else {
                            // Not a JSON question, use the original answer
                            JsonObject(commonFields + ("answer" to answerMap))
                        }

                    }

                    else -> {
                        JsonObject(commonFields + ("answer" to answerMap))
                    }
                }

                map1 = JsonObject(map1.plus(question.id.value to answerObject))
            }

            return map1
        }

        // Process a JSON question and its nested questions recursively
        private fun processJsonQuestion(jsonQuestion: BaseSection.JsonQuestion, answerMap: JsonObject): JsonObject {
            val processedAnswers = mutableMapOf<String, JsonElement>()

            // Process all nested questions
            jsonQuestion.properties.items?.forEach { nestedQuestion ->
                val nestedQuestionId = nestedQuestion.id.value
                val nestedAnswerMap = answerMap[nestedQuestionId]

                if (nestedAnswerMap != null) {
                    if (nestedQuestion.type == QuestionType.LIST) {
                        val itemAnswer = extractListAnswer(nestedAnswerMap, nestedQuestion as BaseSection.ListQuestion)

                        processedAnswers[nestedQuestionId] = itemAnswer
                    }
                    // If it's a nested JSON question, process it recursively
                    else if (nestedQuestion.type == QuestionType.JSON) {
                        val nestedJsonQuestion = nestedQuestion as BaseSection.JsonQuestion
                        val processedNestedJson = processJsonQuestion(nestedJsonQuestion, nestedAnswerMap.jsonObject)

                        processedAnswers[nestedQuestionId] = processedNestedJson
                    }
                }
            }

            val updatedAnswerMap = answerMap.toMutableMap()
            processedAnswers.forEach { (key, value) -> updatedAnswerMap[key] = value }

            return JsonObject(updatedAnswerMap)
        }

        // Extract answer from a LIST question
        private fun extractListAnswer(answerMap: JsonElement, listQuestion: BaseSection.ListQuestion): JsonElement {
            val items = answerMap.jsonObject["items"]?.jsonObject
            val itemValue = listQuestion.properties.items?.get(0)?.id?.value.let {
                items?.get(it)
            } ?: JsonNull
            return itemValue.jsonObject["answer"] ?: JsonNull
        }
    }
}
