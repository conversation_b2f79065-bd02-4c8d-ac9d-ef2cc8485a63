package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.Json
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator

@Deprecated("Use ConditionExecutionStepV2 instead", ReplaceWith("ConditionExecutionStepV2"))
class ConditionExecutionStep(
    val step: FlowExecution.Step,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder
) : ExecutionStep {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val jsonataPlaceHolderReplacer = JsonataPlaceHolderReplacer(quoteStrategy = EscapeQuoteStrategy())

    override suspend fun populate(context: FlowContextWithLocalStep) {

        val valueProvider = ContextPlaceholderValueProvider(contextToJsonObjectBuilder, context)

        val template = Json.encodeToString(step.properties.branches)
        val populatedTemplate = jsonataPlaceHolderReplacer.replacePlaceholders(template, valueProvider)
        val populatedBranches =
            Json.decodeFromString<List<FlowExecution.Step.Properties.ConditionBranch>>(populatedTemplate)
        step.properties.setBranches(populatedBranches)

        logger.trace("Populated branches {}", step)
    }

    override suspend fun execute(context: FlowContextWithLocalStep): NextStepId? {
        val branches = step.properties.branches ?: return null

        for (branch in branches) {
            val expression = branch.condition.toExpression()
            logger.trace("Executing step with condition `{}`", expression)
            val result = JsonataExpressionEvaluator().evaluate(expression, mapOf<Any, Any>())
            logger.trace("Evaluated condition `{}` to `{}`", expression, result)
            if (result == true) {
                logger.trace("Branching to {}", branch.next)
                return branch.next
            }
        }

        logger.trace("Branching to {}", step.next)
        return step.next
    }

}