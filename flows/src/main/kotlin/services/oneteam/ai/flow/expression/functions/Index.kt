package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import com.dashjoin.jsonata.Jsonata.JFunctionCallable
import kotlinx.serialization.Serializable

@Serializable
internal object Index : ComputeFunction {
    override val key = "index"
    override val syntax = Syntax(
        "INDEX(array, row, column)", listOf(
            Argument("array", "The array to index", "array"),
            Argument("index", "The array index", "number"),
        )
    )
    override val description = "gets the element at the specified index"
    override val notes = "index is 1 based"
    override val category = "lookup"
    override val icon = Icon("function")
    override val functionName = "INDEX"
    override val examples = emptyList<Example>()
    //we're just working with 1d arrays for now

    @Transient
    override val evaluatorFunction = JFunction(JFunctionCallable { _, args ->
        val array = args[0] as List<*>
        //minus 1 because 1 based index
        val index = args[1].toString().toInt() - 1
        val res = array.getOrNull(index)
        if (res == null) {
            return@JFunctionCallable "#REF!"
        } else {
            return@JFunctionCallable res
        }
    }, "<xx:x>")
}
