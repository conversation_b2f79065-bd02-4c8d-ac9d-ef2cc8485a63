package services.oneteam.ai.flow.support.pairwise

import org.slf4j.Logger
import org.slf4j.LoggerFactory

class Pairwise(val operation: PairwiseOperation) {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun operation(inputs: Array<out Any?>): Any? {

        if (operation.test(inputs)) {
            return operation.function(inputs)
        }

        return when {

            inputs.all { it is List<*> } -> {
                val maxLength = inputs.filterIsInstance<List<*>>().maxOf { it.size }
                (0 until maxLength).map { index ->
                    recursivePerform(*inputs.map {
                        (it as List<*>).getOrNull(index) ?: List(it.size) { operation.defaultValue }
                    }
                        .toTypedArray())
                }
            }

            else -> throw IllegalArgumentException("Unsupported input types for pairwise ${operation.name}")
        }
    }

    fun perform(values: List<PairwiseValue>): PairwiseValue {
        require(values.isNotEmpty()) { "Values list cannot be empty" }

        // Start with the first value and add the rest pairwise
        val result = values.map { it.value }.reduce { acc: Any?, value: Any? ->
            recursivePerform(acc, value)
        }

        return PairwiseValue(result!!, values.first().dimension)
    }

    // Recursive function to add lists or numbers
    fun recursivePerform(vararg rawInputs: Any?): Any? {
        logger.trace("Adding inputs: ${rawInputs.joinToString(", ")}")
        val inputs = inflateArraysIfRequired(0, *rawInputs)
        logger.trace("Inflated inputs: ${inputs.joinToString(", ")}")
        return operation(inputs)
    }

    fun pairwise(values: List<PairwiseValue>): PairwiseValue {
        require(values.isNotEmpty()) { "Values list cannot be empty" }

        // uplift and add 2 at a time
        var result = values[0]

        for (i in 1 until values.size) {
            val value = values[i]
            val pairwiseValues = listOf(result, value)
            result = perform(upliftToMaxDimension(pairwiseValues))
        }
        return result

    }

    private fun upliftToMaxDimension(values: List<PairwiseValue>): List<PairwiseValue> {
        val maxDimension = values.maxOf { it.dimension }

        // get max length for each dimension so we can uplift all values to the same dimension with the required length
        val maxLengths = IntArray(maxDimension + 1) { 1 }

        for (value in values) {
            for (dim in 0 until value.dimension + 1) {
                maxLengths[dim] = maxOf(maxLengths[dim], value.lengthAtDimension(dim))
            }
        }

        logger.trace("Max length of each dimension {}", maxLengths.toList())
        logger.trace("Uplifting values {}", values)

        // uplift all values to the max dimension
        val upliftedValues = values.map {
            var upliftedValue = it
            while (upliftedValue.dimension < maxDimension) {
                val length = maxLengths[upliftedValue.dimension + 1]
                upliftedValue = upliftedValue.upliftToLength(length)
            }
            upliftedValue
        }
        logger.trace("uplifted values: {}", upliftedValues)

        return upliftedValues
    }

    // if inputs are all lists of numbers
    // and the lists are different lengths
    // we need to make them all the same length by adding the default value
    fun inflateArraysIfRequired(default: Any, vararg rawInputs: Any?): Array<out Any?> {
        if (rawInputs.size != 2) {
            return rawInputs
        }

        val input1 = rawInputs[0]
        val input2 = rawInputs[1]
        if (input1 is List<*> && input2 is List<*>) {
            if (input1.all { it is Number } && input2.all { it is Number }) {
                val maxLength = maxOf(input1.size, input2.size)
                return arrayOf(
                    input1 + List(maxLength - input1.size) { default },
                    input2 + List(maxLength - input2.size) { default }
                )
            }
        }
        return rawInputs
    }

}