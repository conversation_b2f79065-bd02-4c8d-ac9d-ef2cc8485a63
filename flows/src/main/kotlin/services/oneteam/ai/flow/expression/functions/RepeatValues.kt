package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata

internal object RepeatValues: ComputeFunction {
    override val key = "repeatValues"
    override val syntax = Syntax("REPEATVALUES(value, noOfTimes)", listOf(
        Argument("value", "The value to repeat", "string"),
        Argument("numberOfTimes", "The number of times to repeat the value", "number")
    ))
    override val description = "Repeat a value a specified number of times"
    override val notes = ""
    override val category = "String Functions"
    override val icon = Icon("function")
    override val functionName = "REPEATVALUES"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = Jsonata.JFunction({ _, args ->
        val value = args?.get(0) ?: throw IllegalArgumentException("value argument is missing")
        val numberOfTimes = args?.get(1)?.toString()?.toIntOrNull() ?: throw IllegalArgumentException("noOfTimes argument is missing or not a valid number")

        if (numberOfTimes < 0) {
            throw IllegalArgumentException("noOfTimes argument must be non-negative")
        }

        List(numberOfTimes) { value }
    }, "<xn:a<(x)>>")
}