package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object IsBlank : ComputeFunction {
    override val key = "isBlank"
    override val syntax = Syntax(
        "ISBLANK(value)", listOf(
            Argument("value", "The value to check", "any")
        )
    )
    override val description = "Check if a value is blank"
    override val notes = "Check if a value is blank"
    override val category = "type"
    override val icon = Icon("function")
    override val functionName = "ISBLANK"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args.first().let {
            when (it) {
                is String -> it.isBlank()
                else -> false
            }
        }
    }, "<x:b>")
}
