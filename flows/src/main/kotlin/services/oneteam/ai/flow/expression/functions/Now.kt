package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.util.*

@Serializable
internal object Now : ComputeFunction {
    override val key = "now"
    override val syntax = Syntax(
        "NOW()", listOf()
    )
    override val description = "Get the current date and time"
    override val notes = "one of the basic date functions, to get the current date and time"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "NOW"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, _ ->
        Date().time
    }, "<:n>")
}
