package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Right : ComputeFunction {
    override val key = "right"
    override val syntax = Syntax("RIGHT(text, numChars)", listOf(
        Argument("text", "The text to extract from", "string"),
        Argument("numChars", "The number of characters to extract", "number")
    ))
    override val description = "Extracts a specified number of characters from the right side of a text string"
    override val notes = "e.g. RIGHT('Hello, world!', 6) returns 'world!'"
    override val category = "text"
    override val icon = Icon("function")
    override val functionName = "RIGHT"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        val text = args!![0] as String
        val numChars = args[1] as Int
        text.takeLast(numChars)
    }, "<sn:s>")
}
