package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Concat : ComputeFunction {
    override val key = "concat"
    override val syntax = Syntax(
        "CONCAT(arg1, arg2, ...)", listOf(
            Argument("arg1", "The first value to concatenate", "any"),
            Argument("arg2", "The second value to concatenate", "any")
        )
    )
    override val description = "Concatenate values into a string"
    override val notes = "Converts all arguments to string and concatenates them"
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "CONCAT"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args.reduce { acc, arg -> acc.toString() + arg.toString() }
    }, "<x+:s>")
}

