package services.oneteam.ai.flow.expression.functions

import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation

@Serializable
internal object Concat : ComputeFunction {

    const val DEFAULT_VALUE = ""

    override val key = "concat"
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "CONCAT"
    override val description = "Concatenate values into a string"

    override val syntax = Syntax(
        "${functionName}(any, ... )", listOf(
            Argument("any", "Any (or Tensors) to compare", "any"),
            Argument("...", "Additional any (or Tensors) to compare", "any"),
        )
    )

    override val notes = """
        A Tensor can be a single value (scalar), an array of values (vector), or a matrix of values.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()

    override val examples = listOf(
        Example(
            "$${functionName}('Hello', 'World') = 'HelloWorld'",
            "Concatenate values",
        )
    )

    val implementations: List<MatchableFunction> = listOf(
        FuzzyConcat,
        FuzzyList(FuzzyConcat),
        Pairwise(PairwiseOperation.CONCATENATE, DEFAULT_VALUE)
    )

    @Transient
    override val evaluatorFunction = buildJFunctionForImplementations(implementations, "<x+:x>", functionName)

}

private object FuzzyConcat : MatchableFunction {
    override fun match(args: List<*>): Boolean {
        return args.all { it !is List<*> }
    }

    override fun perform(args: List<*>): Any {
        var res = ""
        args.forEach { res = "$res${it ?: Concat.DEFAULT_VALUE}" }
        return res
    }
}