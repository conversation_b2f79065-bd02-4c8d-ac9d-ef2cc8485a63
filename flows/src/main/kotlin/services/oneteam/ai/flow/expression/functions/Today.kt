package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Today : ComputeFunction {
    override val key = "today"
    override val syntax = Syntax(
        "TODAY()", emptyList()
    )
    override val description = "Get the current date"
    override val notes = "one of the basic date functions, to get the current date"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "TODAY"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, _ ->
        java.time.LocalDate.now().toString()
    }, "<:s>")
}
