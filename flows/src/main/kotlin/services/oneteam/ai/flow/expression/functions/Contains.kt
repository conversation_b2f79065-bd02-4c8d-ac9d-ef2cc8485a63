package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Contains : ComputeFunction {
    override val key = "contains"
    override val syntax = Syntax(
        "CONTAINS(value, searchString)", listOf(
            Argument("value", "The string in which to search for the specified substring.", "string"),
            Argument("searchString", "The substring to search for within the value string.", "string")
        )
    )
    override val description = "Check if the given search string is contained in the value string"
    override val notes = ""
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "CONTAINS"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args: MutableList<Any?>? ->
        (args!![0] as String).contains(args[1] as String)
    }, "<ss:b>")
}
