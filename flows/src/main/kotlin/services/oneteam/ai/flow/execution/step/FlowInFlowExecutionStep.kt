package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonNull
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import services.oneteam.ai.shared.helpers.getContent

class FlowInFlowExecutionStep(
    val step: FlowExecution.Step,
    val flowRunner: FlowRunner,
    val workspaceVersionService: WorkspaceVersionService,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
) : ExecutionStep {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun populate(context: FlowContextWithLocalStep) {
        val valueProvider = ContextPlaceholderValueProvider(contextToJsonObjectBuilder, context)

        logger.trace("Populating inputs {} for step {}", step.properties.inputs, step)
        step.properties.setInputs(JsonPlaceholderReplacer().replacePlaceholders(step.properties.inputs, valueProvider))
        logger.trace("Populated inputs {} for step {}", step.properties.inputs, step)

        // add the inputs to the context
        step.properties.inputs.forEach { (k, v) ->
            context.setThisStep(k, v)
        }

    }

    override suspend fun execute(context: FlowContextWithLocalStep): NextStepId? {
        val inputs = step.properties.inputs
        val flowConfigurationId = inputs["flowConfigurationId"]?.getContent()
            ?: throw IllegalArgumentException("No flow configuration ID found for flow in flow step")

        // Fetch flow config using workspace version id in globals of parent
        val globalVariables = context.flowContext.global
        val workspace =
            workspaceVersionService.findVersion(globalVariables.workspaceId, globalVariables.workspaceVersionId)
        val subFlowConfiguration = workspace.configuration.findFlow(FlowConfiguration.Id(flowConfigurationId))

        val path = "steps[${step.id.value}].subFlows[${step.id.value}]"
        logger.trace("Flow in flow using path {}", path)

        val flowDepth = globalVariables.flowDepth + 1
        if (flowDepth > 15) {
            throw FlowRunnerException("Max flow in flow depth of 15 exceeded")
        }

        // Set up the context variables
        val subFlowContext = FlowContextWithLocalStep(
            stepId = step.id,
            flowContext = FlowContext(
                global = GlobalVariables(
                    workspaceId = globalVariables.workspaceId,
                    workspaceVersionId = globalVariables.workspaceVersionId,
                    tenantId = globalVariables.tenantId,
                    flowConfigurationId = subFlowConfiguration.id,
                    flowConfigurationName = subFlowConfiguration.name,
                    flowDepth = flowDepth
                ),
                event = null
            ),
        )

        setupSubFlowStartingVariables(
            context,
            subFlowContext,
            subFlowConfiguration
        )

        // Flow Execution Document (FED)
        val stepExecutionDocument = subFlowConfiguration.toExecution(
            flowContext = subFlowContext.flowContext,
            trigger = null,
        )

        step.subFlows.put(step.id.value, stepExecutionDocument)

        logger.trace(
            "Starting flow in flow runner with stepExecutionDocument {}",
            Json.encodeToString(stepExecutionDocument)
        )

        try {
            flowRunner.spawnNestedFlowRunner(subFlowContext.flowContext, stepExecutionDocument, path).start()
            if (stepExecutionDocument.state.result == FlowExecution.Result.FAILED) {
                throw FlowRunnerException("Flow in flow run failed for parent flow ${globalVariables.flowConfigurationId.value} (${globalVariables.flowConfigurationName.value}) flowConfigurationId at step ${step.id.value} in sub-flow ${subFlowConfiguration.id} (${subFlowConfiguration.name.value})")
            }
            saveSubFlowEndingVariablesToFlowContext(context, subFlowContext, subFlowConfiguration)
        } finally {
            logger.trace(
                "Finished flow in flow runner with stepExecutionDocument {}",
                Json.encodeToString(stepExecutionDocument)
            )
        }

        return step.next
    }

    private suspend fun saveSubFlowEndingVariablesToFlowContext(
        context: FlowContextWithLocalStep,
        subFlowContext: FlowContextWithLocalStep,
        subFlowConfiguration: FlowConfiguration.ForJson,
    ) {
        subFlowConfiguration.endingVariables?.forEach { endingVariable ->
            val variableName = endingVariable.identifier
            val variableValue = subFlowContext.flowContext.variables[variableName]?.value ?: JsonNull
            context.flowContext.set(
                VariableDefinition.Variable(
                    variableValue,
                    endingVariable.type,
                    variableName
                )
            )
        }
    }

    /**
     * Set up the starting variables in the sub-flow
     *
     * We take the values from current step inputs "step.properties.inputs"
     * And populate them into the sub-flow context for the sub-flow starting variables
     */
    private fun setupSubFlowStartingVariables(
        context: FlowContextWithLocalStep,
        subFlowContext: FlowContextWithLocalStep,
        subFlowConfiguration: FlowConfiguration.ForJson
    ) {
        // Set up the context variables
        val startingVariableNamesMap = subFlowConfiguration.startingVariables?.associate {
            val inputValue = context.thisStep[it.identifier]
            if (inputValue?.getContent().isNullOrEmpty() && it.properties?.required == true) {
                throw IllegalArgumentException(
                    "Input for variable '${it.identifier}' is null and required",
                    IllegalArgumentException("Input for variable '${it.identifier}' is null and required")
                )
            }
            if (inputValue?.getContent().isNullOrEmpty()) {
                logger.warn("Input for variable '${it.identifier}' is null but not required", it.identifier)
            }
            it.identifier to VariableDefinition.Variable(
                inputValue ?: JsonNull,
                it.type,
                it.identifier,
                null
            )
        } ?: emptyMap()

        subFlowContext.flowContext.variables.putAll(
            startingVariableNamesMap // values from step inputs for subflow starting variables
        )
    }
}