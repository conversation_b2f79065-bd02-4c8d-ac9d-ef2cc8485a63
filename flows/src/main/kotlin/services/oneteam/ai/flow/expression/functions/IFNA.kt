package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import com.dashjoin.jsonata.Jsonata.JFunctionCallable
import kotlinx.serialization.Serializable

@Serializable
internal object Ifna : ComputeFunction {
    override val key = "ifna"
    override val syntax = Syntax("IFNA(value, value_if_na)", listOf(
        Argument("value", "The value to test", "any"),
        Argument("value_if_na", "The value to return if value is #N/A", "any")
    ))
    override val description = "Returns value_if_na if value is #N/A, otherwise returns value"
    override val notes = ""
    override val category = "logical"
    override val icon = Icon("function")
    override val functionName = "IFNA"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction(JFunctionCallable { _, args ->
        val value = args!![0]
        val valueIfNa = args[1]
        if (value is String && value == "#N/A") valueIfNa else value
    }, "<xx:x>")
}
