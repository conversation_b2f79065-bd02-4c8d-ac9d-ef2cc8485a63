package services.oneteam.ai.flow.execution.listeners

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.shared.domains.flow.variables.Variable

class FlowListenerLogger() : FlowRunListener, FlowStepListener, FlowContextListener {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun onFlowStarted(flowExecution: FlowExecution.ForJson) {
        logger.trace("Flow start configuration {}", flowExecution)
    }

    override suspend fun onFlowFinished(
        flowExecution: FlowExecution.ForJson, cancellationInformation: FlowExecution.Properties.Cancellation?
    ) {
        logger.trace("Flow completed {}", flowExecution)
    }

    override suspend fun onStepStarted(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {
        logger.trace("Step started: {}", step)
        logger.trace("Step context at start: {}", context)
    }

    override suspend fun onStepFinished(
        flowExecution: FlowExecution.ForJson,
        step: FlowExecution.Step,
        stepState: FlowExecution.State.Step,
        context: FlowContextWithLocalStep
    ) {
        logger.trace("Step finished: {}", step)
        logger.trace("Step context at finish: {}", context)
    }

    override suspend fun onStepUpdated(step: FlowExecution.Step) {
        logger.trace("Step updated: {}", step)
    }

    override suspend fun onVariableSet(value: Variable) {
        logger.trace("Variable set: {}", value)
    }
}