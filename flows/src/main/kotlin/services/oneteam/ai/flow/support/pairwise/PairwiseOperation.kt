package services.oneteam.ai.flow.support.pairwise

import services.oneteam.ai.shared.extensions.allNumbers
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import services.oneteam.ai.shared.extensions.toNumber

enum class PairwiseOperation(
    val defaultValue: Any,
    val test: (Array<out Any?>) -> <PERSON><PERSON><PERSON>,
    val function: (Array<out Any?>) -> Any?
) {

    ADD(
        0,
        allNumbers,
        { inputs: Array<out Any?> ->
            toNumber(inputs).reduce { acc, num -> if (acc != null && num != null) acc + num else null }
                ?.convertToNiceBigDecimal()
        }
    ),
    SUBTRACT(
        0,
        allNumbers,
        { inputs: Array<out Any?> ->
            toNumber(inputs).reduce { acc, num -> if (acc != null && num != null) acc - num else null }
                ?.convertToNiceBigDecimal()
        }
    ),
    MULTIPLY(
        1,
        allNumbers,
        { inputs: Array<out Any?> ->
            toNumber(inputs).reduce { acc, num -> if (acc != null && num != null) acc * num else null }
                ?.convertToNiceBigDecimal()
        }
    ),
    DIVIDE(
        1,
        allNumbers,
        { inputs: Array<out Any?> ->
            toNumber(inputs).reduce { acc, num -> if (acc != null && num != null && num.signum() != 0) acc / num else null }
                ?.convertToNiceBigDecimal()
        }
    ),
    MIN(
        0,
        allNumbers,
        { inputs: Array<out Any?> -> toNumber(inputs).filterNotNull().minOf { it }.convertToNiceBigDecimal() }
    ),
    MAX(
        0,
        allNumbers,
        { inputs: Array<out Any?> -> toNumber(inputs).filterNotNull().maxOf { it }.convertToNiceBigDecimal() }
    ),
    CONCATENATE(
        "",
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> -> inputs.joinToString("") }
    ),
    EQ(
        0,
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> -> inputs.reduce { acc, num -> acc == num } }
    ),
    GT(
        0,
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> ->
            inputs.reduce { acc, num ->
                if (acc is Comparable<*> && num is Comparable<*>) {
                    @Suppress("UNCHECKED_CAST")
                    return@reduce (acc as Comparable<Any>) > (num as Comparable<Any>)
                }
                return@reduce false
            }
        }
    ),
    GTE(
        0,
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> ->
            inputs.reduce { acc, num ->
                if (acc is Comparable<*> && num is Comparable<*>) {
                    @Suppress("UNCHECKED_CAST")
                    return@reduce (acc as Comparable<Any>) >= (num as Comparable<Any>)
                }
                return@reduce false
            }
        }
    ),
    LT(
        0,
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> ->
            inputs.reduce { acc, num ->
                if (acc is Comparable<*> && num is Comparable<*>) {
                    @Suppress("UNCHECKED_CAST")
                    return@reduce (acc as Comparable<Any>) < (num as Comparable<Any>)
                }
                return@reduce false
            }
        }
    ),
    LTE(
        0,
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> ->
            inputs.reduce { acc, num ->
                if (acc is Comparable<*> && num is Comparable<*>) {
                    @Suppress("UNCHECKED_CAST")
                    return@reduce (acc as Comparable<Any>) <= (num as Comparable<Any>)
                }
                return@reduce false
            }
        }
    ),
}

