package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable

@Serializable
internal object Day : ComputeFunction {
    override val key = "day"
    override val syntax = Syntax(
        "DAY(date)", listOf(
            Argument("date", "The date to get the day from", "date")
        )
    )
    override val description = "Get the day of the month"
    override val notes = "one of the basic date functions, to get the day of the month"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "DAY"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        java.time.LocalDate.parse(args.first().toString()).dayOfMonth
    }, "<s:n>")
}
