package services.oneteam.ai.flow.execution.listeners.repo

import automergeRepo.src.main.kotlin.DocHandle
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.listeners.*

class RepoListenerFactory(
    val documentId: FlowExecution.DocumentId,
    val flowExecutionId: FlowExecution.Id,
    val flowExecutionRepository: FlowExecutionRepository,
    val handle: DocHandle<FlowExecution.ForJson>,
    val currentFlowGetter: (rootFlow: FlowExecution.ForJson) -> FlowExecution.ForJson
) : ListenerFactory {
    override fun createStepListeners(): List<FlowStepListener> {
        return listOf(
            RepoFlowListenerDocumentUpdater(documentId, handle, currentFlowGetter),
            FlowListenerLogger()
        )
    }

    override fun createContextListeners(): List<FlowContextListener> {
        return listOf(
            RepoFlowListenerDocumentUpdater(documentId, handle, currentFlowGetter),
        )
    }

    override fun createFlowListenersForMainFlow(): List<FlowRunListener> {
        return listOf(
            FlowListenerDatabaseUpdater(flowExecutionRepository, flowExecutionId),
            RepoFlowListenerDocumentUpdater(documentId, handle, currentFlowGetter),
            FlowListenerLogger(),
            FlowListenerNotifier(flowExecutionId, documentId)
        )
    }

    override fun createFlowListenersForSubFlow(): List<FlowRunListener> {
        return listOf(
            RepoFlowListenerDocumentUpdater(documentId, handle, currentFlowGetter),
            FlowListenerLogger()
        )
    }

    override fun clone(path: String): ListenerFactory {
        return RepoListenerFactory(
            documentId,
            flowExecutionId,
            flowExecutionRepository,
            handle,
            currentFlowGetter
        )
    }
}