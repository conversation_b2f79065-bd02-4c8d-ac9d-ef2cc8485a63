package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata

internal object TextSplit: ComputeFunction {
    override val key = "textSplit"
    override val syntax = Syntax("TEXTSPLIT(text, columnDelimiter, [rowDelimiter], [ignoreEmpty], [matchMode], [padWith])", listOf(
        Argument("text", "The text you want to split", "string"),
        Argument("columnDelimiter", "The text that marks the point where to split the text across columns", "string"),
        Argument("rowDelimiter", "The text that marks the point where to split the text down rows", "string"),
        Argument("ignoreEmpty", "Optional: Specify TRUE/true/Yes/1 to ignore consecutive delimiters. Defaults to FALSE", "boolean"),
        Argument("matchMode", "Specify 1 to perform a case-insensitive match. Defaults to 0 for case-sensitive", "number"),
        Argument("padWith", "The value with which to pad the result. The default is null (#N/A)", "any")
    ))
    override val description = "Splits text strings using column and row delimiters"
    override val notes = "Returns a one or two-dimensional array of the split text values"
    override val category = ""
    override val icon = Icon("function")
    override val functionName = "TEXTSPLIT"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = Jsonata.JFunction({ _, args ->
        if (args == null || args.isEmpty()) {
            throw IllegalArgumentException("text argument is missing")
        }

        val text = args[0]?.toString() ?: throw IllegalArgumentException("text argument is missing")
        val columnDelimiter = args.getOrNull(1)?.toString()
            ?: throw IllegalArgumentException("columnDelimiter argument is missing")
        
        val rowDelimiter = if (args.size > 2 && args[2] != null && args[2].toString() != "null") args[2].toString() else null
        val ignoreEmpty = if (args.size > 3 && args[3] != null) parseBooleanArg(args[3], false) else false
        val matchMode = if (args.size > 4 && args[4] != null) args[4].toString().toIntOrNull() ?: 0 else 0
        val padWith = if (args.size > 5 && args[5] != null) args[5] else null

        // Handle case sensitivity based on matchMode
        val colDelimRegex = if (matchMode == 1) {
            Regex(Regex.escape(columnDelimiter), RegexOption.IGNORE_CASE)
        } else {
            Regex(Regex.escape(columnDelimiter))
        }

        // If no row delimiter, return simple 1D array
        if (rowDelimiter == null) {
            val splits = text.split(colDelimRegex)
            return@JFunction if (ignoreEmpty) splits.filter { it.isNotEmpty() } else splits
        }

        // Handle 2D array (with row delimiter)
        val rowDelimRegex = if (matchMode == 1) {
            Regex(Regex.escape(rowDelimiter), RegexOption.IGNORE_CASE)
        } else {
            Regex(Regex.escape(rowDelimiter))
        }

        val rows = text.split(rowDelimRegex)
        val filteredRows = if (ignoreEmpty) rows.filter { it.isNotEmpty() } else rows

        // Process each row
        val columnsPerRow = filteredRows.map { row ->
            val splits = row.split(colDelimRegex)
            if (ignoreEmpty) splits.filter { it.isNotEmpty() } else splits
        }

        // If padWith is specified, pad rows to equal length
        if (padWith != null) {
            val maxColumns = columnsPerRow.maxOfOrNull { it.size } ?: 0
            return@JFunction columnsPerRow.map { row ->
                val paddedRow = row.toMutableList()
                while (paddedRow.size < maxColumns) {
                    paddedRow.add(padWith.toString())
                }
                paddedRow
            }
        }

        return@JFunction columnsPerRow
    }, "<x+:a<x>>")
}