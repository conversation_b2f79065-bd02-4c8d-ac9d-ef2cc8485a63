package services.oneteam.ai.flow.expression.conditional

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.descriptors.element
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.*
import services.oneteam.ai.flow.expression.functions.Contains
import services.oneteam.ai.flow.expression.functions.DoesNotContain
import services.oneteam.ai.flow.expression.functions.IsEmpty
import services.oneteam.ai.flow.expression.functions.IsNotEmpty

/**
 * The class will parse the JSON condition structure into a string that can be used in a JSONata expression.
 *
 * Function call will be in the form of $condition(lhs, operator, rhs).
 * Condition will be wrapped in a function call to indicate that it is a condition.
 * The Condition will be wrapped in parentheses if it is part of an AND or OR condition.
 *
 * @param left: Evaluable - Condition or value on the left hand side of the condition
 * @param operator: Any - The operator or logical operator to use in the condition
 * @param right: List<Evaluable>? - Condition or value on the right hand side of the condition
 */
@Serializable(with = ConditionSerializer::class)
data class Condition(
    val left: Evaluable,
    val operator: Any,
    val right: List<Evaluable>? = null
) : Evaluable {
    override fun toExpression(): String {
        return when (operator) {
            is LogicalOperator -> when (operator) {
                LogicalOperator.AND -> "(${
                    (right?.map { it.toExpression() } ?: emptyList()).joinToString(
                        " and "
                    )
                })"

                LogicalOperator.OR -> "(${
                    (right?.map { it.toExpression() } ?: emptyList()).joinToString(
                        " or "
                    )
                })"
            }

            is Operator -> when (operator) {
                Operator.EQUAL -> "${left.toExpression()} = ${right?.get(0)?.toExpression()}"
                Operator.IS_EMPTY -> "$${IsEmpty.functionName}(${left.toExpression()})"
                Operator.IS_NOT_EMPTY -> "$${IsNotEmpty.functionName}(${left.toExpression()})"
                Operator.CONTAINS -> "$${Contains.functionName}(${left.toExpression()}, ${
                    right?.get(0)?.toExpression()
                })"

                Operator.DOES_NOT_CONTAIN -> "$${DoesNotContain.functionName}(${left.toExpression()}, ${
                    right?.get(0)?.toExpression()
                })"

                Operator.GREATER_THAN -> "${left.toExpression()} > ${right?.get(0)?.toExpression()}"
                Operator.LESS_THAN -> "${left.toExpression()} < ${right?.get(0)?.toExpression()}"
                Operator.NOT_EQUAL -> "${left.toExpression()} != ${right?.get(0)?.toExpression()}"
                Operator.GREATER_THAN_OR_EQUAL -> "${left.toExpression()} >= ${right?.get(0)?.toExpression()}"
                Operator.LESS_THAN_OR_EQUAL -> "${left.toExpression()} <= ${right?.get(0)?.toExpression()}"
            }

            else -> throw IllegalArgumentException("Invalid operator")
        }
    }

    override fun toString(): String {
        return toExpression()
    }
}


class ConditionSerializer : KSerializer<Condition> {
    override val descriptor = buildClassSerialDescriptor("Condition") {
        element<Evaluable>("left")
        element<String>("operator")
        element<List<Evaluable>?>("right")
    }

    override fun serialize(encoder: Encoder, value: Condition) {
        require(encoder is JsonEncoder) { IllegalArgumentException("This serializer only supports JSON encoding") }

        encoder.encodeJsonElement(
            when (value.operator) {
                is LogicalOperator -> buildJsonObject {
                    put(
                        value.operator.name,
                        buildJsonArray {
                            value.right!!.forEach {
                                add(
                                    encoder.json.encodeToJsonElement(
                                        EvaluableSerializer,
                                        it
                                    )
                                )
                            }
                        }
                    )
                }

                is Operator -> buildJsonObject {
                    put("lhs", encoder.json.encodeToJsonElement(EvaluableSerializer, value.left))
                    put("operator", encoder.json.encodeToJsonElement(Operator.serializer(), value.operator))
                    value.right?.get(0)?.let {
                        put("rhs", encoder.json.encodeToJsonElement(EvaluableSerializer, it))
                    }
                }

                else -> throw IllegalArgumentException("Invalid operator")
            }
        )
    }

    override fun deserialize(decoder: Decoder): Condition {
        require(decoder is JsonDecoder) { IllegalArgumentException("This serializer only supports JSON decoding") }

        val jsonElement = decoder.decodeJsonElement()
        val jsonObject = jsonElement.jsonObject

        when {
            jsonObject.containsKey("AND") -> {
                return Condition(
                    Expression(""),
                    LogicalOperator.AND,
                    jsonObject["AND"]!!.jsonArray.map {
                        decoder.json.decodeFromJsonElement(Condition.serializer(), it)
                    }
                )
            }

            jsonObject.containsKey("OR") -> {
                return Condition(
                    Expression(""),
                    LogicalOperator.OR,
                    jsonObject["OR"]!!.jsonArray.map {
                        decoder.json.decodeFromJsonElement(Condition.serializer(), it)
                    }
                )
            }

            jsonObject.containsKey("lhs") && jsonObject.containsKey("operator") && jsonObject.containsKey("rhs") -> {
                return Condition(
                    Expression(jsonObject["lhs"]!!.jsonPrimitive.content),
                    mapOperator(jsonObject["operator"]!!.jsonPrimitive.content),
                    listOf(Expression(jsonObject["rhs"]!!.jsonPrimitive.content))
                )
            }

            jsonObject.containsKey("lhs") && jsonObject.containsKey("operator") -> {
                return Condition(
                    Expression(jsonObject["lhs"]!!.jsonPrimitive.content),
                    mapOperator(jsonObject["operator"]!!.jsonPrimitive.content)
                )
            }

            else -> throw IllegalArgumentException("Invalid condition structure")
        }
    }
}

object EvaluableSerializer : JsonContentPolymorphicSerializer<Evaluable>(Evaluable::class) {
    override fun selectDeserializer(element: JsonElement): KSerializer<out Evaluable> {
        return when {
            element.jsonObject.keys.any { it in setOf("AND", "OR", "operator") } -> Condition.serializer()
            else -> Expression.serializer()
        }
    }
}
