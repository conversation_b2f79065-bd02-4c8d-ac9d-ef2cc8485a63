package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import java.util.*

@Serializable
internal object Proper : ComputeFunction {
    override val key = "proper"
    override val syntax = Syntax(
        "PROPER(value)", listOf(
            Argument("value", "The value to capitalize", "string")
        )
    )
    override val description = "Capitalize the first letter of each word in a string"
    override val notes = "Capitalize the first letter of each word in a string"
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "PROPER"
    override val examples = emptyList<Example>()
    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        args.first().let { it ->
            when (it) {
                is String -> it.split(" ").joinToString(" ") { it ->
                    it.replaceFirstChar {
                        if (it.isLowerCase()) it.titlecase(
                            Locale.getDefault()
                        ) else it.toString()
                    }
                }

                else -> ""
            }
        }
    }, "<s:s>")
}
