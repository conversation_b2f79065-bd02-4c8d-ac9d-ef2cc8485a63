{"primaryIdentifier": "selectForm", "name": "Select form", "description": "", "type": "action", "properties": {"icon": {"name": "search"}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Foundation ID", "type": "variable", "identifier": "foundationId", "properties": {"type": "text", "required": true}}, {"text": "Form configuration ID", "type": "variable", "identifier": "formConfigurationId", "properties": {"type": "select", "required": true, "properties": {"dynamicOptions": {"tag": "formConfigurationId"}}}}, {"text": "Series interval ID", "type": "variable", "identifier": "intervalId", "properties": {"type": "select", "required": false, "defaultValue": "", "properties": {"dynamicOptions": {"tag": "intervalConfigurationId", "body": {"formConfigurationId": "{{thisStep.formConfigurationId}}"}}}}}, {"text": "Form variable name", "type": "variable", "identifier": "formVariableName", "properties": {"type": "text", "required": false, "properties": {"regex": "^[a-zA-Z0-9_]*${'$'}", "defaultValue": "form__step_{{thisStep.id}}"}}}, {"text": "Continue flow if form is not found", "type": "select", "identifier": "continueFlowIfNotFound", "properties": {"required": true, "options": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}], "defaultValue": "true"}}], "apiCall": {"url": "/ai/api/forms/obtain", "internal": true, "method": "POST", "body": {"workspaceId": "{{global.workspaceId}}", "foundationId": "{{thisStep.foundationId}}", "formConfigurationId": "{{thisStep.formConfigurationId}}", "intervalId": "{{thisStep.intervalId}}", "allowNull": "{{thisStep.continueFlowIfNotFound}}"}, "response": {"type": "json", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}]}}}, "variableMappings": [{"type": "form.{{thisStep.formConfigurationId}}", "identifier": "{{thisStep.formVariableName}}", "value": "{{thisStep.response.form.id}}"}]}}}