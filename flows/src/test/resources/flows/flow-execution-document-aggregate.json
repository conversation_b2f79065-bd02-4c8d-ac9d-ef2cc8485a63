{"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "{{form.key}}", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form", "userId": null}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-aggregate", "flowConfigurationName": "iterator-aggregate", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "workspace": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "id": 130, "key": "WORKSPACE1", "name": "workspace1", "workspaceFoundationId": 1833, "variables": {}}, "variables": {"form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "total": {"identifier": "total", "type": "number", "value": "0"}}}, "description": "iterator-aggregate", "name": "iterator-aggregate", "start": "step1", "state": {"metadata": {"finishedAt": "2025-03-12T03:28:21.480752Z", "startedAt": "2025-03-12T03:28:20.135491Z"}, "result": "SUCCESS", "status": "COMPLETED", "steps": {"entities": {"step1": {"id": "step1", "metadata": {"finishedAt": "2025-03-12T03:28:21.456207Z", "startedAt": "2025-03-12T03:28:20.259149Z"}, "result": "SUCCESS", "status": "COMPLETED"}}, "order": ["step1"]}}, "steps": {"step1": {"id": "step1", "name": "iterator aggregate test", "properties": {"configuration": {"start": "subStep1", "startingVariables": [{"identifier": "iterator_step1_item", "properties": {"required": true}, "type": "json"}, {"identifier": "iterator_step1_index", "properties": {"required": true}, "type": "number"}, {"identifier": "iterator_step1_output", "properties": {"required": false}, "type": "{{resultVariableType}}"}], "steps": {"subStep1": {"id": "subStep1", "name": "iterator test", "properties": {"variables": [{"identifier": "iterator_step1_output", "type": "number", "value": "{{iterator_step1_output}} + {{iterator_step1_item.eALIKF2hFv}}"}]}, "variant": "setVariables"}}}, "inputs": {"inputVariableName": "list", "itemVariableName": "item_step1", "list": "{{form.VEOTahOeLj.answer}}", "resultVariableName": "aggregateTotal", "resultVariableType": "number"}, "typePrimaryIdentifier": "iteratorAggregate"}, "subFlows": {"step1_1": {"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "{{form.key}}", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form"}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-aggregate", "flowConfigurationName": "iterator-aggregate", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "workspace": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "id": 130, "key": "WORKSPACE1", "name": "workspace1", "workspaceFoundationId": 1833, "variables": {}}, "variables": {"aggregateTotal": {"identifier": "number", "type": "aggregateTotal", "value": null}, "form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "item_step1": {"identifier": "item_step1", "type": "list", "value": {"O1REtNdy50": {"answer": "aa", "id": "O1REtNdy50", "type": "text"}, "_rowId": "Q7JjeX6MIGdhGaqU9n1TH", "_rowIndex": 1, "eALIKF2hFv": {"answer": "11", "id": "eALIKF2hFv", "type": "number"}}}, "item_step1_1": {"identifier": "item_step1_1", "type": "number", "value": 1}, "iterator_step1_index": {"identifier": "iterator_step1_index", "type": "number", "value": 1}, "iterator_step1_item": {"identifier": "iterator_step1_item", "type": "jsonElement", "value": {"O1REtNdy50": {"answer": "aa", "id": "O1REtNdy50", "type": "text"}, "_rowId": "Q7JjeX6MIGdhGaqU9n1TH", "_rowIndex": 1, "eALIKF2hFv": {"answer": "11", "id": "eALIKF2hFv", "type": "number"}}}, "iterator_step1_output": {"identifier": "iterator_step1_output", "type": "json", "value": 0}, "total": {"identifier": "total", "type": "number", "value": "0"}}}, "start": "subStep1", "state": {"metadata": {"finishedAt": "2025-03-12T03:28:20.615386Z", "startedAt": "2025-03-12T03:28:20.359691Z"}, "result": "FAILED", "status": "COMPLETED", "steps": {"entities": {"subStep1": {"id": "subStep1", "metadata": {"finishedAt": "2025-03-12T03:28:20.594391Z", "startedAt": "2025-03-12T03:28:20.443092Z"}, "result": "FAILED", "status": "COMPLETED"}}, "order": ["subStep1"]}}, "steps": {"subStep1": {"id": "subStep1", "name": "iterator test", "properties": {"typePrimaryIdentifier": "", "variables": [{"identifier": "iterator_step1_output", "type": "number", "value": "{{iterator_step1_output}} + {{iterator_step1_item.eALIKF2hFv}}"}]}, "variant": "setVariables"}}, "type": "FLOW_EXECUTION"}, "step1_2": {"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "{{form.key}}", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form"}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-aggregate", "flowConfigurationName": "iterator-aggregate", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "workspace": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "id": 130, "key": "WORKSPACE1", "name": "workspace1", "workspaceFoundationId": 1833, "variables": {}}, "variables": {"aggregateTotal": {"identifier": "number", "type": "aggregateTotal", "value": null}, "form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "item_step1": {"identifier": "item_step1", "type": "list", "value": {"O1REtNdy50": {"answer": "bb", "id": "O1REtNdy50", "type": "text"}, "_rowId": "ryqry17ixv4vg5DtIQrp8", "_rowIndex": 2, "eALIKF2hFv": {"answer": "22", "id": "eALIKF2hFv", "type": "number"}}}, "item_step1_1": {"identifier": "item_step1_1", "type": "number", "value": 1}, "item_step1_2": {"identifier": "item_step1_2", "type": "number", "value": 2}, "iterator_step1_index": {"identifier": "iterator_step1_index", "type": "number", "value": 2}, "iterator_step1_item": {"identifier": "iterator_step1_item", "type": "jsonElement", "value": {"O1REtNdy50": {"answer": "bb", "id": "O1REtNdy50", "type": "text"}, "_rowId": "ryqry17ixv4vg5DtIQrp8", "_rowIndex": 2, "eALIKF2hFv": {"answer": "22", "id": "eALIKF2hFv", "type": "number"}}}, "iterator_step1_output": {"identifier": "iterator_step1_output", "type": "json", "value": 0}, "total": {"identifier": "total", "type": "number", "value": "0"}}}, "start": "subStep1", "state": {"metadata": {"finishedAt": "2025-03-12T03:28:20.897469Z", "startedAt": "2025-03-12T03:28:20.675165Z"}, "result": "FAILED", "status": "COMPLETED", "steps": {"entities": {"subStep1": {"id": "subStep1", "metadata": {"finishedAt": "2025-03-12T03:28:20.876674Z", "startedAt": "2025-03-12T03:28:20.767908Z"}, "result": "FAILED", "status": "COMPLETED"}}, "order": ["subStep1"]}}, "steps": {"subStep1": {"id": "subStep1", "name": "iterator test", "properties": {"typePrimaryIdentifier": "", "variables": [{"identifier": "iterator_step1_output", "type": "number", "value": "{{iterator_step1_output}} + {{iterator_step1_item.eALIKF2hFv}}"}]}, "variant": "setVariables"}}, "type": "FLOW_EXECUTION"}, "step1_3": {"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "{{form.key}}", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form"}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-aggregate", "flowConfigurationName": "iterator-aggregate", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "workspace": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "id": 130, "key": "WORKSPACE1", "name": "workspace1", "workspaceFoundationId": 1833, "variables": {}}, "variables": {"aggregateTotal": {"identifier": "number", "type": "aggregateTotal", "value": null}, "form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "item_step1": {"identifier": "item_step1", "type": "list", "value": {"O1REtNdy50": {"answer": "cc", "id": "O1REtNdy50", "type": "text"}, "_rowId": "JEU4NFPAQpCQFFJKFXaQ1", "_rowIndex": 3, "eALIKF2hFv": {"answer": "33", "id": "eALIKF2hFv", "type": "number"}}}, "item_step1_1": {"identifier": "item_step1_1", "type": "number", "value": 1}, "item_step1_2": {"identifier": "item_step1_2", "type": "number", "value": 2}, "item_step1_3": {"identifier": "item_step1_3", "type": "number", "value": 3}, "iterator_step1_index": {"identifier": "iterator_step1_index", "type": "number", "value": 3}, "iterator_step1_item": {"identifier": "iterator_step1_item", "type": "jsonElement", "value": {"O1REtNdy50": {"answer": "cc", "id": "O1REtNdy50", "type": "text"}, "_rowId": "JEU4NFPAQpCQFFJKFXaQ1", "_rowIndex": 3, "eALIKF2hFv": {"answer": "33", "id": "eALIKF2hFv", "type": "number"}}}, "iterator_step1_output": {"identifier": "iterator_step1_output", "type": "json", "value": 0}, "total": {"identifier": "total", "type": "number", "value": "0"}}}, "start": "subStep1", "state": {"metadata": {"finishedAt": "2025-03-12T03:28:21.376237Z", "startedAt": "2025-03-12T03:28:20.990307Z"}, "result": "FAILED", "status": "COMPLETED", "steps": {"entities": {"subStep1": {"id": "subStep1", "metadata": {"finishedAt": "2025-03-12T03:28:21.352008Z", "startedAt": "2025-03-12T03:28:21.184531Z"}, "result": "FAILED", "status": "COMPLETED"}}, "order": ["subStep1"]}}, "steps": {"subStep1": {"id": "subStep1", "name": "iterator test", "properties": {"typePrimaryIdentifier": "", "variables": [{"identifier": "iterator_step1_output", "type": "number", "value": "{{iterator_step1_output}} + {{iterator_step1_item.eALIKF2hFv}}"}]}, "variant": "setVariables"}}, "type": "FLOW_EXECUTION"}}, "variant": "iterator"}}, "trigger": {"id": "trigger2", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Run foundation flow", "foundationConfigurationId": "emp"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}, "type": "FLOW_EXECUTION"}