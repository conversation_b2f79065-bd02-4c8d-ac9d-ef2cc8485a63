{"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "unknown", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form", "userId": null}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-filter", "flowConfigurationName": "iterator-filter", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "workspace": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "id": 130, "key": "WORKSPACE1", "name": "workspace1", "workspaceFoundationId": 1833, "variables": {}}, "variables": {"form": {"identifier": "form", "type": "form.aqt", "value": "3936"}}}, "description": "iterator-filter", "name": "iterator-filter", "start": "step1", "state": {"metadata": {"finishedAt": "2025-03-12T03:25:38.962578Z", "startedAt": "2025-03-12T03:25:37.131563Z"}, "result": "SUCCESS", "status": "COMPLETED", "steps": {"entities": {"step1": {"id": "step1", "metadata": {"finishedAt": "2025-03-12T03:25:38.948024Z", "startedAt": "2025-03-12T03:25:37.493630Z"}, "result": "SUCCESS", "status": "COMPLETED"}}, "order": ["step1"]}}, "steps": {"step1": {"id": "step1", "name": "iterator filter test", "properties": {"configuration": {"start": "filterCondition", "startingVariables": [{"identifier": "iterator_step1_item", "properties": {"required": true}, "type": "json"}, {"identifier": "iterator_step1_index", "properties": {"required": true}, "type": "number"}, {"identifier": "iterator_step1_output", "properties": {"required": false}, "type": "list"}], "steps": {"filterCondition": {"id": "filterCondition", "name": "Iterator condition. Note that this is required for filters to predicate on.", "properties": {"branches": [{"condition": {"lhs": "{{iterator_step1_index}}", "operator": "<", "rhs": "2"}, "name": "test branch", "next": "filterSetVariables"}]}, "variant": "condition"}, "filterSetVariables": {"id": "filterSetVariables", "name": "test set", "properties": {"variables": [{"identifier": "iterator_step1_output", "type": "boolean", "value": true}]}, "variant": "setVariables"}}}, "inputs": {"inputVariableName": "list", "itemValueType": "list", "itemVariableName": "item_step1", "list": "{{form.VEOTahOeLj.answer}}", "resultVariableName": "filtered", "resultVariableType": "list"}, "typePrimaryIdentifier": "iteratorFilter"}, "subFlows": {"step1_1": {"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "unknown", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form"}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-filter", "flowConfigurationName": "iterator-filter", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "workspace": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "id": 130, "key": "WORKSPACE1", "name": "workspace1", "workspaceFoundationId": 1833, "variables": {}}, "variables": {"form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "item_step1": {"identifier": "item_step1", "type": "list", "value": {"O1REtNdy50": {"answer": "aa", "id": "O1REtNdy50", "type": "text"}, "_rowId": "Q7JjeX6MIGdhGaqU9n1TH", "_rowIndex": 1, "eALIKF2hFv": {"answer": "11", "id": "eALIKF2hFv", "type": "number"}}}, "item_step1_1": {"identifier": "item_step1_1", "type": "number", "value": 1}, "item_step1_output": {"identifier": "item_step1_output", "type": "todo", "value": null}, "iterator_step1_index": {"identifier": "iterator_step1_index", "type": "number", "value": 1}, "iterator_step1_item": {"identifier": "iterator_step1_item", "type": "jsonElement", "value": {"O1REtNdy50": {"answer": "aa", "id": "O1REtNdy50", "type": "text"}, "_rowId": "Q7JjeX6MIGdhGaqU9n1TH", "_rowIndex": 1, "eALIKF2hFv": {"answer": "11", "id": "eALIKF2hFv", "type": "number"}}}, "iterator_step1_output": {"identifier": "iterator_step1_output", "type": "json", "value": null}}}, "start": "filterCondition", "state": {"metadata": {"finishedAt": "2025-03-12T03:25:38.266517Z", "startedAt": "2025-03-12T03:25:37.737708Z"}, "result": "SUCCESS", "status": "COMPLETED", "steps": {"entities": {"filterCondition": {"id": "filterCondition", "metadata": {"finishedAt": "2025-03-12T03:25:38.049535Z", "startedAt": "2025-03-12T03:25:37.863770Z"}, "result": "SUCCESS", "status": "COMPLETED"}, "filterSetVariables": {"id": "filterSetVariables", "metadata": {"finishedAt": "2025-03-12T03:25:38.242788Z", "startedAt": "2025-03-12T03:25:38.119343Z"}, "result": "SUCCESS", "status": "COMPLETED"}}, "order": ["filterCondition", "filterSetVariables"]}}, "steps": {"filterCondition": {"id": "filterCondition", "name": "Iterator condition. Note that this is required for filters to predicate on.", "properties": {"branches": [{"condition": {"lhs": "{{iterator_step1_index}}", "operator": "<", "rhs": 2}, "name": "test branch", "next": "filterSetVariables"}], "typePrimaryIdentifier": ""}, "variant": "condition"}, "filterSetVariables": {"id": "filterSetVariables", "name": "test set", "properties": {"typePrimaryIdentifier": "", "variables": [{"identifier": "iterator_step1_output", "type": "boolean", "value": true}]}, "variant": "setVariables"}}, "type": "FLOW_EXECUTION"}, "step1_2": {"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "unknown", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form"}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-filter", "flowConfigurationName": "iterator-filter", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "workspace": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "id": 130, "key": "WORKSPACE1", "name": "workspace1", "workspaceFoundationId": 1833, "variables": {}}, "variables": {"form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "item_step1": {"identifier": "item_step1", "type": "list", "value": {"O1REtNdy50": {"answer": "bb", "id": "O1REtNdy50", "type": "text"}, "_rowId": "ryqry17ixv4vg5DtIQrp8", "_rowIndex": 2, "eALIKF2hFv": {"answer": "22", "id": "eALIKF2hFv", "type": "number"}}}, "item_step1_1": {"identifier": "item_step1_1", "type": "number", "value": 1}, "item_step1_2": {"identifier": "item_step1_2", "type": "number", "value": 2}, "item_step1_output": {"identifier": "item_step1_output", "type": "todo", "value": null}, "iterator_step1_index": {"identifier": "iterator_step1_index", "type": "number", "value": 2}, "iterator_step1_item": {"identifier": "iterator_step1_item", "type": "jsonElement", "value": {"O1REtNdy50": {"answer": "bb", "id": "O1REtNdy50", "type": "text"}, "_rowId": "ryqry17ixv4vg5DtIQrp8", "_rowIndex": 2, "eALIKF2hFv": {"answer": "22", "id": "eALIKF2hFv", "type": "number"}}}, "iterator_step1_output": {"identifier": "iterator_step1_output", "type": "json", "value": null}}}, "start": "filterCondition", "state": {"metadata": {"finishedAt": "2025-03-12T03:25:38.555973Z", "startedAt": "2025-03-12T03:25:38.352301Z"}, "result": "SUCCESS", "status": "COMPLETED", "steps": {"entities": {"filterCondition": {"id": "filterCondition", "metadata": {"finishedAt": "2025-03-12T03:25:38.545812Z", "startedAt": "2025-03-12T03:25:38.458845Z"}, "result": "SUCCESS", "status": "COMPLETED"}}, "order": ["filterCondition"]}}, "steps": {"filterCondition": {"id": "filterCondition", "name": "Iterator condition. Note that this is required for filters to predicate on.", "properties": {"branches": [{"condition": {"lhs": "{{iterator_step1_index}}", "operator": "<", "rhs": 2}, "name": "test branch", "next": "filterSetVariables"}], "typePrimaryIdentifier": ""}, "variant": "condition"}, "filterSetVariables": {"id": "filterSetVariables", "name": "test set", "properties": {"typePrimaryIdentifier": "", "variables": [{"identifier": "iterator_step1_output", "type": "boolean", "value": true}]}, "variant": "setVariables"}}, "type": "FLOW_EXECUTION"}, "step1_3": {"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "unknown", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form"}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-filter", "flowConfigurationName": "iterator-filter", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "workspace": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "id": 130, "key": "WORKSPACE1", "name": "workspace1", "workspaceFoundationId": 1833, "variables": {}}, "variables": {"form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "item_step1": {"identifier": "item_step1", "type": "list", "value": {"O1REtNdy50": {"answer": "cc", "id": "O1REtNdy50", "type": "text"}, "_rowId": "JEU4NFPAQpCQFFJKFXaQ1", "_rowIndex": 3, "eALIKF2hFv": {"answer": "33", "id": "eALIKF2hFv", "type": "number"}}}, "item_step1_1": {"identifier": "item_step1_1", "type": "number", "value": 1}, "item_step1_2": {"identifier": "item_step1_2", "type": "number", "value": 2}, "item_step1_3": {"identifier": "item_step1_3", "type": "number", "value": 3}, "item_step1_output": {"identifier": "item_step1_output", "type": "todo", "value": null}, "iterator_step1_index": {"identifier": "iterator_step1_index", "type": "number", "value": 3}, "iterator_step1_item": {"identifier": "iterator_step1_item", "type": "jsonElement", "value": {"O1REtNdy50": {"answer": "cc", "id": "O1REtNdy50", "type": "text"}, "_rowId": "JEU4NFPAQpCQFFJKFXaQ1", "_rowIndex": 3, "eALIKF2hFv": {"answer": "33", "id": "eALIKF2hFv", "type": "number"}}}, "iterator_step1_output": {"identifier": "iterator_step1_output", "type": "json", "value": null}}}, "start": "filterCondition", "state": {"metadata": {"finishedAt": "2025-03-12T03:25:38.877961Z", "startedAt": "2025-03-12T03:25:38.640397Z"}, "result": "SUCCESS", "status": "COMPLETED", "steps": {"entities": {"filterCondition": {"id": "filterCondition", "metadata": {"finishedAt": "2025-03-12T03:25:38.854989Z", "startedAt": "2025-03-12T03:25:38.750441Z"}, "result": "SUCCESS", "status": "COMPLETED"}}, "order": ["filterCondition"]}}, "steps": {"filterCondition": {"id": "filterCondition", "name": "Iterator condition. Note that this is required for filters to predicate on.", "properties": {"branches": [{"condition": {"lhs": "{{iterator_step1_index}}", "operator": "<", "rhs": 2}, "name": "test branch", "next": "filterSetVariables"}], "typePrimaryIdentifier": ""}, "variant": "condition"}, "filterSetVariables": {"id": "filterSetVariables", "name": "test set", "properties": {"typePrimaryIdentifier": "", "variables": [{"identifier": "iterator_step1_output", "type": "boolean", "value": true}]}, "variant": "setVariables"}}, "type": "FLOW_EXECUTION"}}, "variant": "iterator"}}, "trigger": {"id": "trigger2", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Run foundation flow", "foundationConfigurationId": "emp"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}, "type": "FLOW_EXECUTION"}