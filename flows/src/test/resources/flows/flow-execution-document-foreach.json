{"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "unknown", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form", "userId": null}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-foreach", "flowConfigurationName": "iterator-foreach", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "variables": {"form": {"identifier": "form", "type": "form.aqt", "value": "3936"}}}, "description": "iterator-foreach", "name": "iterator-foreach", "start": "step1", "state": {"metadata": {"finishedAt": "2025-03-12T03:13:13.970589Z", "startedAt": "2025-03-12T03:13:13.110545Z"}, "result": "SUCCESS", "status": "COMPLETED", "steps": {"entities": {"step1": {"id": "step1", "metadata": {"finishedAt": "2025-03-12T03:13:13.959405Z", "startedAt": "2025-03-12T03:13:13.281400Z"}, "result": "SUCCESS", "status": "COMPLETED"}}, "order": ["step1"]}}, "steps": {"step1": {"id": "step1", "name": "iterator forEach test", "properties": {"configuration": {"start": "subStep1", "startingVariables": [{"identifier": "iterator_step1_item", "properties": {"required": true}, "type": "json"}, {"identifier": "iterator_step1_index", "properties": {"required": true}, "type": "number"}, {"identifier": "iterator_step1_output", "properties": {"required": false}, "type": "number"}], "steps": {"subStep1": {"id": "subStep1", "name": "iterator test", "properties": {"variables": [{"identifier": "iterator_step1_output", "type": "text", "value": "{{iterator_step1_index}}"}]}, "variant": "setVariables"}}}, "inputs": {"inputVariableName": "list", "isReturn": "true", "itemVariableName": "item_step1", "list": "{{form.VEOTahOeLj.answer}}", "resultVariableName": "each", "resultVariableType": "list", "transformedValueType": "list"}, "typePrimaryIdentifier": "iteratorForEach"}, "subFlows": {"step1_1": {"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "unknown", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form"}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-foreach", "flowConfigurationName": "iterator-foreach", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "variables": {"form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "item_step1": {"identifier": "item_step1", "type": "list", "value": {"O1REtNdy50": {"answer": "aa", "id": "O1REtNdy50", "type": "text"}, "_rowId": "Q7JjeX6MIGdhGaqU9n1TH", "_rowIndex": 1, "eALIKF2hFv": {"answer": "11", "id": "eALIKF2hFv", "type": "number"}}}, "item_step1_1": {"identifier": "item_step1_1", "type": "number", "value": 1}, "item_step1_output": {"identifier": "item_step1_output", "type": "list", "value": null}, "iterator_step1_index": {"identifier": "iterator_step1_index", "type": "number", "value": 1}, "iterator_step1_item": {"identifier": "iterator_step1_item", "type": "jsonElement", "value": {"O1REtNdy50": {"answer": "aa", "id": "O1REtNdy50", "type": "text"}, "_rowId": "Q7JjeX6MIGdhGaqU9n1TH", "_rowIndex": 1, "eALIKF2hFv": {"answer": "11", "id": "eALIKF2hFv", "type": "number"}}}, "iterator_step1_output": {"identifier": "iterator_step1_output", "type": "json", "value": null}}}, "start": "subStep1", "state": {"metadata": {"finishedAt": "2025-03-12T03:13:13.604567Z", "startedAt": "2025-03-12T03:13:13.369659Z"}, "result": "SUCCESS", "status": "COMPLETED", "steps": {"entities": {"subStep1": {"id": "subStep1", "metadata": {"finishedAt": "2025-03-12T03:13:13.594949Z", "startedAt": "2025-03-12T03:13:13.466037Z"}, "result": "SUCCESS", "status": "COMPLETED"}}, "order": ["subStep1"]}}, "steps": {"subStep1": {"id": "subStep1", "name": "iterator test", "properties": {"typePrimaryIdentifier": "", "variables": [{"identifier": "iterator_step1_output", "type": "text", "value": "{{iterator_step1_index}}"}]}, "variant": "setVariables"}}, "type": "FLOW_EXECUTION"}, "step1_2": {"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "unknown", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form"}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-foreach", "flowConfigurationName": "iterator-foreach", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "variables": {"form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "item_step1": {"identifier": "item_step1", "type": "list", "value": {"O1REtNdy50": {"answer": "bb", "id": "O1REtNdy50", "type": "text"}, "_rowId": "ryqry17ixv4vg5DtIQrp8", "_rowIndex": 2, "eALIKF2hFv": {"answer": "22", "id": "eALIKF2hFv", "type": "number"}}}, "item_step1_1": {"identifier": "item_step1_1", "type": "number", "value": 1}, "item_step1_2": {"identifier": "item_step1_2", "type": "number", "value": 2}, "item_step1_output": {"identifier": "item_step1_output", "type": "list", "value": null}, "iterator_step1_index": {"identifier": "iterator_step1_index", "type": "number", "value": 2}, "iterator_step1_item": {"identifier": "iterator_step1_item", "type": "jsonElement", "value": {"O1REtNdy50": {"answer": "bb", "id": "O1REtNdy50", "type": "text"}, "_rowId": "ryqry17ixv4vg5DtIQrp8", "_rowIndex": 2, "eALIKF2hFv": {"answer": "22", "id": "eALIKF2hFv", "type": "number"}}}, "iterator_step1_output": {"identifier": "iterator_step1_output", "type": "json", "value": null}}}, "start": "subStep1", "state": {"metadata": {"finishedAt": "2025-03-12T03:13:13.806486Z", "startedAt": "2025-03-12T03:13:13.655577Z"}, "result": "SUCCESS", "status": "COMPLETED", "steps": {"entities": {"subStep1": {"id": "subStep1", "metadata": {"finishedAt": "2025-03-12T03:13:13.797956Z", "startedAt": "2025-03-12T03:13:13.715359Z"}, "result": "SUCCESS", "status": "COMPLETED"}}, "order": ["subStep1"]}}, "steps": {"subStep1": {"id": "subStep1", "name": "iterator test", "properties": {"typePrimaryIdentifier": "", "variables": [{"identifier": "iterator_step1_output", "type": "text", "value": "{{iterator_step1_index}}"}]}, "variant": "setVariables"}}, "type": "FLOW_EXECUTION"}, "step1_3": {"context": {"event": {"eventProperties": {"buttonLabel": "Run Flow", "form": {"documentId": "FLwErbawPCvVPWkumwE5sxTa7bS", "formConfiguration": {"id": "aqt", "key": "unknown", "name": "Name", "seriesId": null}, "foundationId": 1833, "id": 3936, "intervalId": null}, "key": "START_flow_manually_from_form"}, "id": "eventid", "tenantId": 1, "workspaceId": 130}, "global": {"flowConfigurationId": "iterator-foreach", "flowConfigurationName": "iterator-foreach", "tenantId": 1, "workspaceId": 130, "workspaceVersionId": 126}, "variables": {"form": {"identifier": "form", "type": "form.aqt", "value": "3936"}, "item_step1": {"identifier": "item_step1", "type": "list", "value": {"O1REtNdy50": {"answer": "cc", "id": "O1REtNdy50", "type": "text"}, "_rowId": "JEU4NFPAQpCQFFJKFXaQ1", "_rowIndex": 3, "eALIKF2hFv": {"answer": "33", "id": "eALIKF2hFv", "type": "number"}}}, "item_step1_1": {"identifier": "item_step1_1", "type": "number", "value": 1}, "item_step1_2": {"identifier": "item_step1_2", "type": "number", "value": 2}, "item_step1_3": {"identifier": "item_step1_3", "type": "number", "value": 3}, "item_step1_output": {"identifier": "item_step1_output", "type": "list", "value": null}, "iterator_step1_index": {"identifier": "iterator_step1_index", "type": "number", "value": 3}, "iterator_step1_item": {"identifier": "iterator_step1_item", "type": "jsonElement", "value": {"O1REtNdy50": {"answer": "cc", "id": "O1REtNdy50", "type": "text"}, "_rowId": "JEU4NFPAQpCQFFJKFXaQ1", "_rowIndex": 3, "eALIKF2hFv": {"answer": "33", "id": "eALIKF2hFv", "type": "number"}}}, "iterator_step1_output": {"identifier": "iterator_step1_output", "type": "json", "value": null}}}, "start": "subStep1", "state": {"metadata": {"finishedAt": "2025-03-12T03:13:13.930702Z", "startedAt": "2025-03-12T03:13:13.836190Z"}, "result": "SUCCESS", "status": "COMPLETED", "steps": {"entities": {"subStep1": {"id": "subStep1", "metadata": {"finishedAt": "2025-03-12T03:13:13.920876Z", "startedAt": "2025-03-12T03:13:13.871258Z"}, "result": "SUCCESS", "status": "COMPLETED"}}, "order": ["subStep1"]}}, "steps": {"subStep1": {"id": "subStep1", "name": "iterator test", "properties": {"typePrimaryIdentifier": "", "variables": [{"identifier": "iterator_step1_output", "type": "text", "value": "{{iterator_step1_index}}"}]}, "variant": "setVariables"}}, "type": "FLOW_EXECUTION"}}, "variant": "iterator"}}, "trigger": {"id": "trigger2", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Run foundation flow", "foundationConfigurationId": "emp"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}, "type": "FLOW_EXECUTION"}