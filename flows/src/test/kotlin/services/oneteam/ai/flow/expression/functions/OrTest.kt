package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class OrTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Or.functionName, Or.evaluatorFunction)
        jsonataExpression.registerFunction(Multiply.functionName, Multiply.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return true with function`() {
        val expression = "\$OR(\$MULTIPLY(2, 3) = 6.0, false)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe true
    }

    @Test
    fun `should return true when the first condition is true`() {
        val expression = "\$OR(true, false)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe true
    }

    @Test
    fun `should return true when the second condition is true`() {
        val expression = "\$OR(false, true)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe true
    }

    @Test
    fun `should return true when both conditions are true`() {
        val expression = "\$OR(true, true)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe true
    }

    @Test
    fun `should return false when both conditions are false`() {
        val expression = "\$OR(false, false)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe false
    }
}