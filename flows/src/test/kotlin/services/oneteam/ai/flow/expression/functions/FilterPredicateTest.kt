package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import kotlin.test.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class FilterPredicateTest {

    companion object {
        @JvmStatic
        fun testData() = listOf(
            "Psnak4wzY6",
            "Psnak4wzY-",
            "0snak4wzY6",
            "Psnak4wz Y6",
            "sna-k4wzY6",
            "Psnak4wzY0"
        ).map { columnQuestionId ->
            columnQuestionId to getTable(columnQuestionId)
        }

        private fun getTable(columnQuestionId: String): String {
            return """
            [
              {
                "bihoQERn53": "tofilter",
                "$columnQuestionId": 1,
                "_rowId": "06Mo6fCQ490_Y3HHVEWHG",
                "_rowIndex": 1
              },
              {
                "bihoQERn53": "good value",
                "$columnQuestionId": 4,
                "_rowId": "vo8h74_wwrGf2Fg7jmHvB",
                "_rowIndex": 2
              },
              {
                "bihoQERn53": "good value 2",
                "$columnQuestionId": 6,
                "_rowId": "IYC7PUjhfhUJ8kVesGXAb",
                "_rowIndex": 3
              }
            ][`$columnQuestionId` > 3]
            """.trimIndent()
        }
    }

    @ParameterizedTest
    @MethodSource("testData")
    fun `test filter predicate`(testData: Pair<String, String>) {
        val (columnQuestionId, table) = testData
        try {
            val result = Jsonata.jsonata(table).evaluate(null)
            val expected = listOf(
                mapOf(
                    "bihoQERn53" to "good value",
                    columnQuestionId to 4,
                    "_rowId" to "vo8h74_wwrGf2Fg7jmHvB",
                    "_rowIndex" to 2
                ),
                mapOf(
                    "bihoQERn53" to "good value 2",
                    columnQuestionId to 6,
                    "_rowId" to "IYC7PUjhfhUJ8kVesGXAb",
                    "_rowIndex" to 3
                )
            )
            assertEquals(expected, result)
        } catch (e: Exception) {
            throw AssertionError("An exception was thrown: ${e.message}", e)
        }
    }
}