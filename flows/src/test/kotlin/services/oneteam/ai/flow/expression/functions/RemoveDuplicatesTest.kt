package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class RemoveDuplicatesTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(RemoveDuplicates.functionName, RemoveDuplicates.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `processes arrays sequentially adding first occurrence of each value`() {
        val expression = "\$REMOVEDUPLICATES([\"apple\", \"orange\", \"banana\"], [\"kiwi\", \"melon\", \"peach\"], [\"A\", \"B\", \"C\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        // All values are unique, so all return array values should be included
        result.shouldContainExactly(listOf("A", "B", "C"))
    }

    @Test
    fun `skips values already seen in previous arrays`() {
        val expression = "\$REMOVEDUPLICATES([\"apple\", \"orange\", \"banana\"], [\"apple\", \"grape\", \"peach\"], [\"A\", \"B\", \"C\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("A", "B", "C"))
    }

    @Test
    fun `maintains original order from return array when deduplicating result`() {
        val expression = "\$REMOVEDUPLICATES([1, 2, 3], [4, 5, 6], [\"X\", \"Y\", \"Z\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("X", "Y", "Z"))
    }

    @Test
    fun `handles repeated values within a single array`() {
        val expression = "\$REMOVEDUPLICATES([\"dog\", \"cat\", \"dog\"], [\"mouse\", \"bird\", \"fish\"], [\"P\", \"Q\", \"R\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("P", "Q", "R"))
    }

    @Test
    fun `returns subset of return array for specific example`() {
        val expression = "\$REMOVEDUPLICATES([100, 200, 300, 100, 200], [400, 500, 600, 100, 200], [\"J\", \"K\", \"L\", \"M\", \"N\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        // Only J, K, L should be included as they correspond to the first occurrences of unique values
        result.shouldContainExactly(listOf("J", "K", "L"))
    }

    @Test
    fun `handles complex example with multiple arrays and repeated values`() {
        val expression = "\$REMOVEDUPLICATES([10, 20, 30, 10, 50], [10, 60, 20, 10, 80], [20, 80, 90, 10, 100], [\"red\", \"yellow\", \"green\", \"blue\", \"purple\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>

        result.shouldContainExactly(listOf("red", "yellow", "green", "purple"))
    }

    @Test
    fun `processes multiple input arrays correctly`() {
        val expression = "\$REMOVEDUPLICATES([10, 20, 30], [40, 50, 30], [60, 50, 70], [\"X\", \"Y\", \"Z\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("X", "Y", "Z"))
    }

    @Test
    fun `handles null values properly`() {
        val expression = "\$REMOVEDUPLICATES([1, null, 1], [4, null, null], [\"A\", \"B\", \"C\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        // null appears in both arrays and should be treated like any other value
        result.shouldContainExactly(listOf("A","B"))
    }

    @Test
    fun `validates all arrays have same length`() {
        val expression = "\$REMOVEDUPLICATES([1, 2, 3], [4, 5], [\"A\", \"B\", \"C\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(null)
        }.message shouldBe "All input arrays and the return array must have the same length"
    }


    @Test
    fun `handles object equality correctly`() {
        // Create two identical objects in different variables
        val obj1 = mapOf("id" to 1, "name" to "Alice")
        val obj2 = mapOf("id" to 1, "name" to "Alice")

        val data = mapOf(
            "inputArrays" to listOf(
                obj1,
                obj2
            ),
            "returnArray" to listOf("A", "B")
        )

        val expression = "\$REMOVEDUPLICATES(inputArrays, returnArray)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)

        val result =jsonataExpression.evaluate(data) as List<*>

        result.size shouldBe 1
        result.shouldContainExactly(listOf("A"))
    }

    @Test
    fun `handles two different object correctly`() {
        // Create two identical objects in different variables
        val obj1 = mapOf("id" to 1, "name" to "Alice")
        val obj2 = mapOf("id" to 2, "name" to "Bob")

        val data = mapOf(
            "inputArrays" to listOf(
                obj1,
                obj2
            ),
            "returnArray" to listOf("A", "B")
        )

        val expression = "\$REMOVEDUPLICATES(inputArrays, returnArray)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)

        val result =jsonataExpression.evaluate(data) as List<*>

        result.size shouldBe 2
        result.shouldContainExactly(listOf("A", "B"))
    }

    @Test
    fun `handles arrays with mixed types`() {
        val expression = "\$REMOVEDUPLICATES([1, \"1\", true], [\"2\", 2, false], [\"X\", \"Y\", \"Z\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>

        result.shouldContainExactly(listOf("X", "Y", "Z"))
    }

    @Test
    fun `can work when only one array is provided`() {
        val expression = "\$REMOVEDUPLICATES([1, 2, 3, 1])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>

        result.shouldContainExactly(listOf(1, 2, 3))
    }

    @Test
    fun `throws exception when returnArray is not an array`() {
        val expression = "\$REMOVEDUPLICATES([1, 2, 3], \"not an array\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(null)
        }
    }

    @Test
    fun `throws exception when input array is not an array`() {
        val expression = "\$REMOVEDUPLICATES(\"not an array\", [\"a\", \"b\", \"c\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(null)
        }
    }
}