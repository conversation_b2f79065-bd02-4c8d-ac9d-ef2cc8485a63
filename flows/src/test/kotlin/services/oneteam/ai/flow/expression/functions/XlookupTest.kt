package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class XlookupTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Xlookup.functionName, Xlookup.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `can find item in array of numbers`() {
        val expression = "\$XLOOKUP(2, [1, 2, 3], [123, 456, 789])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(456)
    }

    @Test
    fun `can find item in array of string`() {
        val expression = "\$XLOOKUP('b', ['a', 'b', 'c'], ['123', '456', '789'])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe("456")
    }

    @Test
    fun `can find item in array of array of numbers`() {
        val expression = "\$XLOOKUP([4,5,6], [[1, 2, 3], [4, 5, 6], [7, 8, 9]], [123, 456, 789])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(456)
    }

    @Test
    fun `returns #NA when it cannot find`() {
        val expression = "\$XLOOKUP('d', ['a', 'b', 'c'], ['123', '456', '789'])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe("#N/A")
    }
}