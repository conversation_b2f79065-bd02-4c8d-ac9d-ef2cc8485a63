package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonPrimitive
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import services.oneteam.ai.flow.execution.DefaultMapBuilder
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class StepJsonataResolverTest {
    data class Spec(val expression: String, val expected: Any?)

    fun specProvider() = listOf(
        Spec("{{testValue}}", "myValue"),
        Spec("3+{{testNumber}}", 5),
    )

    @ParameterizedTest
    @MethodSource("specProvider")
    fun test(spec: Spec) = runTest {
        // prepare
        val context = Fixtures.context()
        context.flowContext.set(
            VariableInstance.Variable(
                JsonPrimitive("myValue"), VariableDataType.TEXT, "testValue"
            )
        )
        context.flowContext.set(
            VariableInstance.Variable(
                JsonPrimitive(2), VariableDataType.NUMBER, "testNumber"
            )
        )

        val stepJsonataResolver = StepJsonataResolver(
            context = context,
            contextToJsonObjectBuilder = ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "cacheKey"),
            JsonataExpressionEvaluator()
        )

        val expression = spec.expression

        // perform
        val result = stepJsonataResolver.resolve(expression)

        // verify
        assertThat(result).isEqualTo(spec.expected)
    }
}