package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.DefaultMapBuilder
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.variables.DefaultVariableOperation
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.otSerializer
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SetVariablesExecutionStepTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun specProviderMany(): Stream<Spec> {
        return Stream.of(
            Spec(
                listOf(VariableInstance.Variable(JsonPrimitive("{{numberVar}}"), VariableDataType.NUMBER, "output")),
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive(Fixtures.Vars.numberVar.get().jsonPrimitive.int),
                        VariableDataType.NUMBER,
                        "output"
                    )
                )
            ), Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("{{numberVar}} * 2"), VariableDataType.NUMBER, "output"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive(Fixtures.Vars.numberVar.get().jsonPrimitive.int * 2),
                        VariableDataType.NUMBER,
                        "output"
                    )
                )
            ), Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("\$sum([{{numberVar}}, {{numberVar}}])"), VariableDataType.NUMBER, "output"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive(Fixtures.Vars.numberVar.get().jsonPrimitive.int + Fixtures.Vars.numberVar.get().jsonPrimitive.int),
                        VariableDataType.NUMBER,
                        "output"
                    )
                )
            ),
            // strings
            Spec(
                listOf(VariableInstance.Variable(JsonPrimitive("{{stringVar}}"), VariableDataType.TEXT, "output")),
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive(Fixtures.Vars.stringVar.get().jsonPrimitive.content),
                        VariableDataType.TEXT,
                        "output"
                    )
                )
            ), Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("\$join([{{stringVar}}, ' ', {{stringVar}}])"), VariableDataType.TEXT, "output"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("${Fixtures.Vars.stringVar.get().jsonPrimitive.content} ${Fixtures.Vars.stringVar.get().jsonPrimitive.content}"),
                        VariableDataType.TEXT,
                        "output"
                    )
                )
            ),
            // boolean
            Spec(
                listOf(VariableInstance.Variable(JsonPrimitive("{{booleanVar}}"), VariableDataType.BOOLEAN, "output")),
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive(Fixtures.Vars.booleanVar.get().jsonPrimitive.boolean),
                        VariableDataType.BOOLEAN,
                        "output"
                    )
                )
            ), Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("{{booleanVar}} = true"), VariableDataType.BOOLEAN, "output"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive(Fixtures.Vars.booleanVar.get().jsonPrimitive.boolean == true),
                        VariableDataType.BOOLEAN,
                        "output"
                    )
                )
            ), Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("{{booleanVar}} = false"), VariableDataType.BOOLEAN, "output"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive(Fixtures.Vars.booleanVar.get().jsonPrimitive.boolean == false),
                        VariableDataType.BOOLEAN,
                        "output"
                    )
                )
            ),
            // list
            Spec(
                listOf(VariableInstance.Variable(JsonPrimitive("{{listVar}}"), VariableDataType.LIST, "output")),
                listOf(VariableInstance.Variable(Fixtures.Vars.listVar.get(), VariableDataType.LIST, "output"))
            ), Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("\$count({{listVar}})"), VariableDataType.NUMBER, "output"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive((Fixtures.Vars.listVar.get() as JsonArray).size),
                        VariableDataType.NUMBER,
                        "output"
                    )
                )
            ), Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("\$count({{listVar}}) + 10"), VariableDataType.NUMBER, "output"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive((Fixtures.Vars.listVar.get() as JsonArray).size + 10),
                        VariableDataType.NUMBER,
                        "output"
                    )
                )
            ), Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("\$count({{listVar}}) + {{numberVar}}"), VariableDataType.NUMBER, "output"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive((Fixtures.Vars.listVar.get() as JsonArray).size + Fixtures.Vars.numberVar.get().jsonPrimitive.int),
                        VariableDataType.NUMBER,
                        "output"
                    )
                )
            )
        )
    }

    data class Spec(
        val rawVariables: List<VariableInstance>,
        val expectedVariables: List<VariableInstance>,
        val contextVariables: List<VariableInstance> = emptyList(),

        )

    @Test
    fun `should resolve type and identifier`() {
        `should resolve properly`(
            Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("{{stringVar}}"), VariableDataType.fromString("{{type}}"), "{{identifier}}"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive(Fixtures.Vars.stringVar.get().jsonPrimitive.content),
                        VariableDataType.fromString(Fixtures.Vars.type.get().jsonPrimitive.content),
                        Fixtures.Vars.identifier.get().jsonPrimitive.content,
                    )
                )
            ),
        )
    }

    @Test
    fun `should resolve type and identifier with prefix`() {
        `should resolve properly`(
            Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("{{stringVar}}"), VariableDataType.fromString("t{{ext}}"), "out.{{identifier}}"
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive(Fixtures.Vars.stringVar.get().jsonPrimitive.content),
                        VariableDataType.TEXT,
                        "out.${Fixtures.Vars.identifier.get().jsonPrimitive.content}",
                    )
                ), listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("ext"),
                        VariableDataType.TEXT,
                        "ext",
                    )
                )
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("specProviderMany")
    fun `should resolve properly`(spec: Spec) = runTest {
        val setVariablesStep = SetVariablesExecutionStep(
            Step(
                Step.Id("1"), Step.Variant.SET_VARIABLES, "name", Step.Properties(
                    typePrimaryIdentifier = null, variables = spec.rawVariables.toMutableList()
                )
            ),
            ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "1"),
            variableOperations = listOf(DefaultVariableOperation(true, JsonataExpressionEvaluator()))
        )

        val context = Fixtures.context()

        context.flowContext.set(Fixtures.Vars.numberVar)
        context.flowContext.set(Fixtures.Vars.stringVar)
        context.flowContext.set(Fixtures.Vars.booleanVar)
        context.flowContext.set(Fixtures.Vars.listVar)
        context.flowContext.set(Fixtures.Vars.objectVar)
        context.flowContext.set(Fixtures.Vars.type)
        context.flowContext.set(Fixtures.Vars.identifier)
        spec.contextVariables.forEach { context.flowContext.set(it) }

        setVariablesStep.execute(context)

        logger.debug("Flow context: {}", otSerializer.encodeToString(context))
        logger.debug("Actual. : {}", context.flowContext.variables["output"])
        logger.debug("Expected: {}", spec.expectedVariables[0])

        assertThat(context.flowContext.variables.values).containsAll(spec.expectedVariables)
    }

    @Test
    fun `should throw exception`() {
        `should resolve properly`(
            Spec(
                listOf(VariableInstance.Variable(JsonPrimitive("System.exit()"), VariableDataType.BOOLEAN, "output")),
                listOf(VariableInstance.Variable(JsonNull, VariableDataType.BOOLEAN, "output"))
            )
        )
    }


    @Test
    fun `should set variable`() = runTest {

        val setVariablesStep = SetVariablesExecutionStep(
            Step(
                Step.Id("1"), Step.Variant.SET_VARIABLES, "name", Step.Properties(
                    typePrimaryIdentifier = null, variables = mutableListOf(
                        VariableInstance.Variable(
                            JsonPrimitive("1 + 1"), VariableDataType.NUMBER, "var1"
                        ), VariableInstance.Variable(
                            JsonPrimitive("'string literal'"), VariableDataType.TEXT, "var2"
                        ), VariableInstance.Variable(
                            JsonPrimitive("{{var1}}"), VariableDataType.NUMBER, "var3"
                        ), VariableInstance.Variable(
                            // we won't support joining strings with expressions like `{{var2}} {{var2}}` because jsonata won't handle it
                            // for now they'll have to use a concatenation function or join
                            // when we replace the tokens we'll have to know to quote the strings for jsonata - based on either the JsonPrimitive.isString or the Variable.type
                            JsonPrimitive("\$join([{{var2}},'/',{{var2}}])"), // expect jsonata to receive quoted strings here
                            VariableDataType.TEXT, "var4"
                        )
                    )
                )
            ),
            ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "1"),
            variableOperations = listOf(DefaultVariableOperation(true, JsonataExpressionEvaluator()))
        )

        val context = FlowContextWithLocalStep(
            Step.Id("1"), FlowContext(
                global = Fixtures.global,
                workspace = Fixtures.workspaceContext,
                variables = mutableMapOf(),
                event = Fixtures.event,
            )
        )

        setVariablesStep.execute(context)

        logger.debug("Flow context: {}", otSerializer.encodeToString(context))

        // check that the FED is updated with the resolved values
        assertThat(setVariablesStep.step.properties.variables!![0].get()).isEqualTo(JsonPrimitive("1 + 1"))
        assertThat(setVariablesStep.step.properties.variables[1].get()).isEqualTo(JsonPrimitive("'string literal'"))
        assertThat(setVariablesStep.step.properties.variables[2].get()).isEqualTo(JsonPrimitive("2"))
        assertThat(setVariablesStep.step.properties.variables[3].get()).isEqualTo(JsonPrimitive("\$join([\"string literal\",'/',\"string literal\"])"))

        // check the context variables are correct
        assertThat(context.flowContext.variables["var1"]?.get()).isEqualTo(JsonPrimitive(2))
        assertThat(context.flowContext.variables["var2"]?.get()).isEqualTo(JsonPrimitive("string literal"))
        assertThat(context.flowContext.variables["var3"]?.get()).isEqualTo(JsonPrimitive(2))
        assertThat(context.flowContext.variables["var4"]?.get()).isEqualTo(JsonPrimitive("string literal/string literal"))

    }

    @Test
    fun `given step configuration when running step then should populate placeholders`() = runTest {
        val stepConfiguration = otSerializer.decodeFromString<FlowExecution.Step>(
            """
                {
                    "id": "LxUM7X-Hw1ND9oTMzc8TF",
                    "name": "Set variable(s)",
                    "next": "l4LcyIEnZaTTJM-KBPAbx",
                    "properties": {
                      "typePrimaryIdentifier": null,
                      "variables": [
                        {
                          "identifier": "CYPayroll",
                          "properties": {
                            "columnIdentifier": "{{someQuestionId}}",
                            "operation": "setCell",
                            "rowIndex": "{{PayrollItems_index}}"
                          },
                          "type": "table",
                          "value": "{{StandardMapping}}"
                        }
                      ]
                    },
                    "variant": "setVariables"
                  }
            """.trimIndent()
        )

        val executionStep = SetVariablesExecutionStep(
            stepConfiguration,
            ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "1"),
            variableOperations = listOf(DefaultVariableOperation(true, JsonataExpressionEvaluator()))
        )

        val context = Fixtures.context()
        context.flowContext.set(
            VariableInstance.Variable(JsonPrimitive("1"), VariableDataType.NUMBER, "PayrollItems_index")
        )
        context.flowContext.set(
            VariableInstance.Variable(JsonPrimitive("myStandardMapping"), VariableDataType.TEXT, "StandardMapping")
        )
        context.flowContext.set(
            VariableInstance.Variable(JsonPrimitive("testQuestionId"), VariableDataType.TEXT, "someQuestionId")
        )

        // Populated variable: Variable(value="\"myStandardMapping\"", type=table, identifier=CYPayroll, properties=TableVariableProperties(operation=SET_CELL, rowIdentifier=null, rowIndex=1, columnIdentifier=testQuestionId, columns=[]))
        stepConfiguration.properties.variables?.forEachIndexed { idx, variable ->
            val populatedVariable = executionStep.populate(variable, context, "$idx")
            logger.debug("Populated variable: {}", populatedVariable)
        }
    }

}