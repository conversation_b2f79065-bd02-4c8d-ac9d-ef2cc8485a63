package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration

object ActionFixtures {

    fun action(typePrimaryIdentifier: String, next: Step.Id): Step {
        return Step(
            Step.Id("id"),
            Step.Variant.ACTION,
            "name",
            Step.Properties(
                typePrimaryIdentifier = typePrimaryIdentifier,
                inputs = mutableMapOf(
                    "formId" to JsonPrim<PERSON>("{{formId}}"),
                    "foundationId" to JsonPrimitive("{{foundationId}}"),
                    "formVariableName" to JsonPrimitive("varName"),
                ),
            ),
            next,
        )
    }

    fun stepTypeConfiguration(typePrimaryIdentifier: String): FlowStepTypeConfiguration {
        return FlowStepTypeConfiguration(
            1, typePrimaryIdentifier, "action", "name", "description",
            FlowStepType.Properties(
                configuration = FlowStepType.Properties.Configuration(
                    content = listOf(
                        FlowStepType.Properties.Configuration.Content(
                            "Form ID",
                            "variable",
                            "formId",
                            FlowStepType.Properties.Configuration.Content.Properties(
                                required = JsonPrimitive(true),
                                type = "text",
                                properties = FlowStepType.Properties.Configuration.Content.Properties(
                                    regex = "^[a-zA-Z0-9_]*$",
                                    defaultValue = "{{thisStep.formId}}"
                                )
                            )
                        ),
                        FlowStepType.Properties.Configuration.Content(
                            "Foundation ID",
                            "variable",
                            "foundationId",
                            FlowStepType.Properties.Configuration.Content.Properties(
                                required = JsonPrimitive(true),
                                type = "text",
                                properties = FlowStepType.Properties.Configuration.Content.Properties(
                                    regex = "^[a-zA-Z0-9_]*$",
                                    defaultValue = "{{thisStep.formId}}"
                                )
                            )
                        ),
                        FlowStepType.Properties.Configuration.Content(
                            "Form Variable Name",
                            "variable",
                            "formVariableName",
                            FlowStepType.Properties.Configuration.Content.Properties(
                                required = JsonPrimitive(true),
                                type = "text",
                                properties = FlowStepType.Properties.Configuration.Content.Properties(
                                    regex = "^[a-zA-Z0-9_]*$",
                                    defaultValue = "name"
                                )
                            )
                        )
                    ),
                    apiCall = FlowStepType.Properties.Configuration.ApiCall(
                        "/test/{{thisStep.formId}}/test",
                        "POST",
                        JsonObject(mapOf(
                            "fId" to JsonPrimitive("{{thisStep.formId}}")
                        )),
                        response = FlowStepType.Properties.Configuration.ApiCall.Response(
                            type = "json",
                            properties = FlowStepType.Properties.Configuration.ApiCall.Response.Property(
                                listOf(
                                    FlowStepType.Properties.Configuration.ApiCall.Response.Item(
                                        "form.{{thisStep.formId}}",
                                        "form"
                                    ),
                                    FlowStepType.Properties.Configuration.ApiCall.Response.Item(
                                        "answer",
                                        "answer"
                                    ),
                                )
                            )
                        ),
                        internal = true
                    ),
                    variableMappings = listOf(
                        FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping(
                            "form.{{thisStep.foundationId}}",
                            "{{thisStep.formVariableName}}",
                            JsonPrimitive("{{thisStep.response.form.id}}")
                        )
                    )
                ),
            ),
            1
        )
    }
}