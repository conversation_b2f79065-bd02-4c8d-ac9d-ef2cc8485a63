package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SubtractTest {

    private val fn = Subtract

    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(fn.functionName, fn.evaluatorFunction)
        return jsonataExpression
    }

    data class Spec(
        val input: String,
        val expected: Any,
    )

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
//            Spec("$$functionName(1, 1)", BigDecimal(0)),
//            Spec("$$functionName(1, 1, 1)", BigDecimal(-1)),
//            Spec("$$functionName([1,2], 1)", listOf(BigDecimal(0), BigDecimal(1))),
//            Spec("$$functionName([1,2], [1,2])", listOf(BigDecimal(0), BigDecimal(0))),
            Spec("$$functionName([94.5], [null])", listOf(BigDecimal(94.5))),
//            Spec("$$functionName([94.5], [0])", listOf(BigDecimal(94.5))),
//            Spec("$$functionName(['94.5'], ['0'])", listOf(BigDecimal(94.5))),
//            Spec(
//                "$$functionName([[1,2],[3,4]], 1)", listOf(
//                    listOf(BigDecimal(0), BigDecimal(1)), listOf(
//                        BigDecimal(2),
//                        BigDecimal(3)
//                    )
//                )
//            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        val jsonataExpression = jsonataWithFunctionRegistered(spec.input)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(spec.expected)
    }

}