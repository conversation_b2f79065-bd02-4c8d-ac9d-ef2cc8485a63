package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.JsonElement
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.mapBuilders.FormJsonMapBuilder
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.otSerializer

class AnswersToMapBuilderTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun `should build typed map`() {

        val workspaceForJson = otSerializer.decodeFromString(
            Workspace.ForJson.serializer(),
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )

        val actual = FormJsonMapBuilder.AnswersToMapBuilder(workspaceForJson).answersToMap(
            FormAnswer.ForJson(
                Form.Id(1), mapOf(
                    BaseSection.Id("q1") to FormAnswer.NumberAnswer(BaseSection.Id("q1"), "15"),
                )
            ),
            FormConfiguration.ForJson(
                id = "1",
                key = "key1",
                foundationId = "foundationId1",
                name = "name1",
                description = "description1",
                seriesId = null,
                metadata = EntityMetadata.now(),
                level = 0,
                content = listOf(
                    BaseSection.NumberQuestion(
                        BaseSection.Id("q1"),
                        "Description q1",
                        "Question q1",
                        QuestionType.NUMBER,
                        "my q1",
                        CommonQuestionProperties.NumberQuestionProperties(
                            required = true
                        )
                    )
                ),
            ),
        )

        val expected = otSerializer.decodeFromString<JsonElement>(
            """
                {
                  "q1": {
                    "id": "q1",
                    "type": "number",
                    "text": "my q1",
                    "identifier": "Question q1",
                    "description": "Description q1",
                    "answer": 15.0
                  }
                }
            """.trimIndent()
        )

        assertThat(actual).isEqualTo(expected)
    }


    @Test
    fun `should build typed map for table`() {

        val workspaceForJson = otSerializer.decodeFromString(
            Workspace.ForJson.serializer(),
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )

        val formConfiguration = workspaceForJson.findForm(FormConfiguration.Id("aqt"))

        val actual = FormJsonMapBuilder.AnswersToMapBuilder(workspaceForJson).answersToMap(
            FormAnswer.ForJson(
                Form.Id(1), mapOf(
                    BaseSection.Id("VEOTahOeLj") to FormAnswer.TableAnswer(
                        BaseSection.Id("VEOTahOeLj"), OrderedMap(
                            listOf(
                                TableAnswerRow(
                                    RowId("row1"), mapOf(
                                        BaseSection.Id("O1REtNdy50") to FormAnswer.TextAnswer(
                                            BaseSection.Id("O1REtNdy50"), "test1"
                                        ),
                                        BaseSection.Id("eALIKF2hFv") to FormAnswer.NumberAnswer(
                                            BaseSection.Id("eALIKF2hFv"), "14"
                                        ),
                                    )
                                ), TableAnswerRow(
                                    RowId("row2"), mapOf(
                                        BaseSection.Id("O1REtNdy50") to FormAnswer.TextAnswer(
                                            BaseSection.Id("O1REtNdy50"), "test12"
                                        ),
                                        BaseSection.Id("eALIKF2hFv") to FormAnswer.NumberAnswer(
                                            BaseSection.Id("eALIKF2hFv"), "145"
                                        ),
                                    )
                                )
                            )
                        )
                    ),
                )
            ), formConfiguration
        )

        val expected = otSerializer.decodeFromString<JsonElement>(
            """
                {
                  "VEOTahOeLj": {
                    "id": "VEOTahOeLj",
                    "type": "table",
                    "text": "Table",
                    "answer": [
                      {
                        "O1REtNdy50": "test1",
                        "eALIKF2hFv": 14.0,
                        "_rowId": "row1",
                        "_rowIndex": 1
                      },
                      {
                        "O1REtNdy50": "test12",
                        "eALIKF2hFv": 145.0,
                        "_rowId": "row2",
                        "_rowIndex": 2
                      }
                    ],
                    "columns": {
                      "O1REtNdy50": {
                        "id": "O1REtNdy50",
                        "type": "text",
                        "text": "TableText1",
                        "answer": [
                          "test1",
                          "test12"
                        ]
                      },
                      "eALIKF2hFv": {
                        "id": "eALIKF2hFv",
                        "type": "number",
                        "text": "TableNumber1",
                        "answer": [
                          14.0,
                          145.0
                        ]
                      },
                      "ids": [
                        "O1REtNdy50",
                        "eALIKF2hFv"
                      ]
                    }
                  }
                }
            """.trimIndent()
        )

        logger.debug("Actual: {}", actual)

        assertThat(actual).isEqualTo(expected)
    }
}