package services.oneteam.ai.flow.execution

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.listeners.DefaultListenerFactory
import services.oneteam.ai.flow.execution.listeners.FlowListenerLogger
import services.oneteam.ai.flow.execution.mapBuilders.FormJsonMapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.FoundationJsonMapBuilder
import services.oneteam.ai.flow.execution.step.ExecutionStepFactoryV1
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.create
import services.oneteam.ai.shared.otSerializer
import kotlin.test.assertEquals


class FlowRunnerWithFormTest {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val documentService = MockDocumentService()

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            "string", null, null,
        ), Event.Id("1"), 1
    )

    @Test
    fun `test run flow`() = runTest {

        val workspace = otSerializer.decodeFromString<Workspace.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )

        val formAnswers = otSerializer.decodeFromString<FormAnswer.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-answers-aqt.json")!!.readText()
        )

        // create a flow configuration with a step that sets a variable by reading a value from the form
        val flowConfiguration = FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("flow-id"),
            name = FlowConfiguration.Name("flow-name"),
            description = FlowConfiguration.Description("flow-description"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("A"),
            steps = mutableMapOf(
                FlowConfiguration.Step.Id("A") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("A"),
                    name = "Set Variables A",
                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "type",
                        variables = listOf(
                            VariableInstance.Variable(
                                identifier = "bonus",
                                value = JsonPrimitive("{{form.np9GiUMruw.answer}}"),
                                type = VariableDataType.NUMBER,
                                properties = null
                            )
                        ),
                    ),
                    next = null
                )
            )
        )

        logger.debug("Using flow configuration {}", otSerializer.encodeToString(flowConfiguration))

        val global = FlowContext.GlobalVariables(
            Workspace.Id(1),
            WorkspaceVersion.Id(1),
            1,
            FlowConfiguration.Id("flow-id"),
            FlowConfiguration.Name("flow-name"),
        )
        val workspaceContext = FlowContext.WorkspaceContext(
            documentId = Workspace.DocumentId("1"),
            id = Workspace.Id(1),
            key = Workspace.Key("WORKSPACE1"),
            name = Workspace.Name("workspace1"),
            workspaceFoundationId = Foundation.Id(1),
            variables = emptyMap()
        )
        val context = FlowContext(
            global = global, workspace = workspaceContext, variables = mutableMapOf(
                "form" to VariableInstance.Variable(
                    JsonPrimitive(1), VariableDataType.fromString("form.aqt"), "form", null
                )
            ), event = event
        )

        val flow = flowConfiguration.toExecution(
            context, null
        )

        val documentId = FlowExecution.DocumentId(
            documentService.create(
                1, FlowExecution.ForJson(
                    context,
                    flow.state,
                    flow.start,
                    flow.steps,
                )
            )
        )

        val formAnswersId = documentService.create(1, formAnswers)

        val flowExecutionRepository = mock(FlowExecutionRepository::class.java)
        val flowExecutionService = mock(FlowExecutionService::class.java)
        val flowExecutionId = 1.toLong()
        val flowStepTypeConfigurationService = mock(FlowStepTypeConfigurationService::class.java)
        val formService = mock(FormService::class.java)
        val foundationService = mock(FoundationService::class.java)
        val blobStorageFEDRepository = mock(BlobStorageFEDRepository::class.java)

        `when`(foundationService.get(Foundation.Id(1))).thenReturn(
            Foundation.ForApi(
                Foundation.Id(1),
                Foundation.Name("name"),
                Foundation.Key("key"),
                FoundationConfiguration.Id("emp"),
                Workspace.Id(1),
                null,
                EntityMetadata.now()
            )
        )

        `when`(formService.get(Form.Id(1))).thenReturn(
            Form.ForApi(
                Form.Id(1),
                FormConfiguration.Id("aqt"),
                Foundation.Id(1),
                null,
                null,
                Form.DocumentId(formAnswersId),
                Form.AnnotationDocumentId("annotationDocumentId"),
                Workspace.Id(1),
                EntityMetadata.now(),
                Foundation.ForApi(
                    Foundation.Id(1),
                    Foundation.Name("name"),
                    Foundation.Key("key"),
                    FoundationConfiguration.Id("emp"),
                    Workspace.Id(1),
                    null,
                    EntityMetadata.now()
                )
            )
        )

        val filePressService = mock(FilePressService::class.java)
        val proxyService = mock(ProxyService::class.java)
        val internalProxyService = mock(ProxyService::class.java)
        val workspaceVersionService = mock(WorkspaceVersionService::class.java)
        val listenerFactory = DefaultListenerFactory(
            documentService,
            blobStorageFEDRepository,
            documentId = documentId,
            FlowExecution.Id(flowExecutionId),
            flowExecutionRepository,
            includeLogging = true,
            skipStepUpdates = false,
            skipVariableUpdates = false,
            skipSubFlowFlowUpdates = false,
            useAutomergeFED = false
        )
        val flowRunner = FlowRunner(
            FlowExecution.Id(flowExecutionId),
            context,
            flow,
            listenerFactory,
            listOf(FlowListenerLogger()),
            listOf(FlowListenerLogger()),
            flowExecutionService,
            timeoutMins = 2,
            ExecutionStepFactoryV1(
                flowStepTypeConfigurationService, proxyService, internalProxyService, filePressService, listOf(
                    FoundationJsonMapBuilder(foundationService, workspace), FormJsonMapBuilder(
                        formService, foundationService, documentService, null, workspace
                    ), DefaultMapBuilder() // must come last
                ), workspaceVersionService = workspaceVersionService, listOf()
            )
        )

        val flowExecution = flowRunner.start()

        logger.debug("Document state: {}", documentService)

        assertNotNull(flowExecution)

        logger.debug("context: {}", otSerializer.encodeToString(context))

        assertEquals(JsonPrimitive(1), context.variables["form"]?.get())
        assertEquals(JsonPrimitive(3), context.variables["bonus"]?.get())

    }
}

