package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class AdvancedExpressionTest {

    fun jsonataWithAllFunctionsRegistered(expression: String): Jsonata {
        val jsonata = Jsonata.jsonata(expression)
        ComputeFunctions.definitions.entries.forEach {
            jsonata.registerFunction(it.key, it.value.evaluatorFunction)
        }
        return jsonata
    }

    @Test
    fun `should return the first value when condition is true and second value is a nested function`() {
        val expression = "\$IF(\$MULTIPLY(2, 2) = 4.0, \$MULTIPLY(4, 4), 1.0)"
        val jsonata = jsonataWithAllFunctionsRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe BigDecimal("16")
    }

    @Test
    fun `should concatenate trimmed left and upper values`() {
        val expression = "\$CONCAT(\$LEFT('  Hello World  ', 5), \$UPPER(\$TRIM('  test ')))"
        val jsonata = jsonataWithAllFunctionsRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "HelloTEST"
    }

    @Test
    fun `should perform xlookup and multiply the result`() {
        val expression = "\$MULTIPLY(\$XLOOKUP('b', ['a', 'b', 'c'], [10, 20, 30]), 3)"
        val jsonata = jsonataWithAllFunctionsRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe BigDecimal(60)
    }

    @Test
    fun `should return proper date components concatenated`() {
        val expression =
            "\$CONCAT(\$YEAR(\$DATE(2021, 7, 20)), '-', \$MONTH(\$DATE(2021, 7, 20)), '-', \$DAY(\$DATE(2021, 7, 20)))"
        val jsonata = jsonataWithAllFunctionsRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "2021-7-20"
    }

    @Test
    fun `should evaluate nested logical functions correctly`() {
        val expression = "\$IF(\$AND(true, \$OR(false, true)), 'yes', 'no')"
        val jsonata = jsonataWithAllFunctionsRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "yes"
    }

    @Test
    fun `should substitute text and then calculate length`() {
        val expression = "\$LEN(\$SUBSTITUTE('Hello, World!', 'World', 'Jsonata'))"
        val jsonata = jsonataWithAllFunctionsRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 15
    }
}
