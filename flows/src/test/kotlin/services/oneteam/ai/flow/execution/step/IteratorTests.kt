package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.listeners.DefaultListenerFactory
import services.oneteam.ai.flow.execution.mapBuilders.FormJsonMapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.FoundationJsonMapBuilder
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.create
import java.math.BigDecimal
import java.time.Instant
import kotlin.test.assertEquals


class IteratorTest {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val documentService = MockDocumentService()

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            "string", null, null,
        ), Event.Id("1"), 1
    )

    @Disabled
    @Test
    fun `test run foreach`() = runTest {
        val flowConfiguration = FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("flow-id"),
            name = FlowConfiguration.Name("test flow"),
            description = FlowConfiguration.Description("flow-description"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("iteratorForEachStep"),
            steps = mutableMapOf(
                FlowConfiguration.Step.Id("iteratorForEachStep") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("iteratorForEachStep"),
                    name = "foreach iterator",
                    variant = FlowConfiguration.Step.Variant.ITERATOR,
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "iteratorForEach", configuration = FlowConfiguration.ForJson(
                            start = FlowConfiguration.Step.Id("subStep1"), steps = mutableMapOf(
                                FlowConfiguration.Step.Id("subStep1") to FlowConfiguration.Step(
                                    id = FlowConfiguration.Step.Id("subStep1"),
                                    name = "sub step 1",
                                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                                    next = FlowConfiguration.Step.Id("subStep2"),
                                    properties = FlowConfiguration.Step.Properties(
                                        typePrimaryIdentifier = "type", variables = listOf(
                                            VariableInstance.Variable(
                                                identifier = "foreach_test",
                                                value = JsonPrimitive("{{iterator_iteratorForEachStep.item}}"),
                                                type = VariableDataType.TEXT,
                                                properties = null
                                            ),
                                        )
                                    )
                                ), FlowConfiguration.Step.Id("subStep2") to FlowConfiguration.Step(
                                    id = FlowConfiguration.Step.Id("subStep2"),
                                    name = "sub step 2",
                                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                                    properties = FlowConfiguration.Step.Properties(
                                        typePrimaryIdentifier = "type", variables = listOf(
                                            VariableInstance.Variable(
                                                identifier = "foreach_test2",
                                                value = JsonPrimitive("{{iterator_iteratorForEachStep.item}}"),
                                                type = VariableDataType.TEXT,
                                                properties = null
                                            ),
                                        )
                                    )
                                )
                            )
                        )
                    ),
                ),
            )
        )

        logger.debug("Using flow configuration {}", Json.encodeToString(flowConfiguration))

        val context = FlowContext(
            FlowContext.GlobalVariables(
                Workspace.Id(1),
                WorkspaceVersion.Id(1),
                1,
                FlowConfiguration.Id("flow-id"),
                FlowConfiguration.Name("flow-name"),
            ), workspace = FlowContext.WorkspaceContext(
                documentId = Workspace.DocumentId("1"),
                id = Workspace.Id(1),
                key = Workspace.Key("WORKSPACE1"),
                name = Workspace.Name("workspace1"),
                workspaceFoundationId = Foundation.Id(1),
                variables = emptyMap()
            ), variables = mutableMapOf(), event = event
        )

        val flow = flowConfiguration.toExecution(
            context, null
        )


        val documentId = FlowExecution.DocumentId(
            documentService.create(
                1, FlowExecution.ForJson(
                    context,
                    flow.state,
                    flow.start,
                    flow.steps,
                )
            )
        )

        val formConfiguration = FormConfiguration.ForJson(
            id = "1", name = "Form", foundationId = "1", level = 1, metadata = EntityMetadata(
                createdAt = Instant.now(),
                updatedAt = Instant.now(),
            ), key = "key", content = listOf(
                BaseSection.TextQuestion(
                    id = BaseSection.Id("1"),
                    type = QuestionType.TEXT,
                    text = "How are you today?",
                    description = "description",
                    identifier = "identifier1",
                    properties = CommonQuestionProperties.TextQuestionProperties(
                        maxLength = 10, minLength = 1, required = true
                    )
                ), BaseSection.NumberQuestion(
                    id = BaseSection.Id("2"),
                    type = QuestionType.NUMBER,
                    text = "Bonus",
                    description = "description",
                    identifier = "Bonus",
                    properties = CommonQuestionProperties.NumberQuestionProperties(
                        decimalPlaces = 2,
                        min = BigDecimal(0),
                        max = BigDecimal(100000),
                        defaultValue = BigDecimal(0),
                        placeholder = "bonus",
                        type = CommonQuestionProperties.NumberType.NUMBER,
                        required = true
                    )
                )
            )
        )

        val workspace = Workspace.ForJson(
            Workspace.Id(1),
            Workspace.Name("1"),
            Workspace.Key("1"),
            Workspace.Description("1"),
            OrderedMap<FoundationConfiguration.Id, FoundationConfiguration.ForApi>(listOf()),
            mapOf<FormConfiguration.Id, FormConfiguration.ForJson>(FormConfiguration.Id(formConfiguration.id) to formConfiguration),
            OrderedMap<FlowConfiguration.Id, FlowConfiguration.ForJson>(listOf(flowConfiguration)),
            mapOf(),
            emptyMap(),
            emptyMap(),
            EntityMetadata.now()
        )
        val blobStorageFEDRepository = mock(BlobStorageFEDRepository::class.java)

        val filePressService = mock(FilePressService::class.java)
        val flowStepTypeConfigurationService = mock(FlowStepTypeConfigurationService::class.java)
        val flowExecutionRepository = mock(FlowExecutionRepository::class.java)
        val flowExecutionService = mock(FlowExecutionService::class.java)
        val formService = mock(FormService::class.java)
        val foundationService = mock(FoundationService::class.java)
        val proxyService = mock(ProxyService::class.java)
        val internalProxyService = mock(ProxyService::class.java)
        val workspaceVersionService = mock(WorkspaceVersionService::class.java)
        val flowExecutionId = 1.toLong()
        val listenerFactory = DefaultListenerFactory(
            documentService,
            blobStorageFEDRepository,
            documentId = documentId,
            FlowExecution.Id(flowExecutionId),
            flowExecutionRepository,
            includeLogging = true,
            skipStepUpdates = false,
            skipVariableUpdates = false,
            skipSubFlowFlowUpdates = false,
            useAutomergeFED = false
        )
        val flowRunner = FlowRunner(
            FlowExecution.Id(flowExecutionId),
            context,
            flow,
            listenerFactory,
            listenerFactory.createFlowListenersForMainFlow(),
            listenerFactory.createStepListeners(),
            flowExecutionService,
            timeoutMins = 2,
            ExecutionStepFactoryV1(
                flowStepTypeConfigurationService,
                proxyService,
                internalProxyService,
                filePressService = filePressService,
                listOf(
                    FoundationJsonMapBuilder(foundationService, workspace), FormJsonMapBuilder(
                        formService, foundationService, documentService, "cookie", workspace
                    )
                ),
                workspaceVersionService = workspaceVersionService, listOf()
            )
        )

        val flowExecution = flowRunner.start()

        logger.debug("Document state: {}", documentService)

        assertNotNull(flowExecution)

        assertEquals(JsonPrimitive("forEach"), context.variables["foreach_test"]?.get())
        assertEquals(JsonPrimitive("forEach"), context.variables["foreach_test2"]?.get())
    }


    @Disabled
    @Test
    fun `test run filter`() = runTest {
        val flowConfiguration = FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("flow-id"),
            name = FlowConfiguration.Name("test flow"),
            description = FlowConfiguration.Description("flow-description"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("iteratorFilterStep"),
            steps = mutableMapOf(
                FlowConfiguration.Step.Id("iteratorFilterStep") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("iteratorFilterStep"),
                    name = "iterator filter",
                    variant = FlowConfiguration.Step.Variant.ITERATOR,
                    properties = FlowConfiguration.Step.Properties(
                        inputs = mapOf("outputVariableName" to JsonPrimitive("outputVariableNameTest")),
                        typePrimaryIdentifier = "iteratorFilter",
                        configuration = FlowConfiguration.ForJson(
                            start = FlowConfiguration.Step.Id("subStep1"), steps = mutableMapOf(
                                FlowConfiguration.Step.Id("subStep1") to FlowConfiguration.Step(
                                    id = FlowConfiguration.Step.Id("subStep1"),
                                    name = "sub step 1",
                                    variant = FlowConfiguration.Step.Variant.CONDITION,
                                    properties = FlowConfiguration.Step.Properties(
                                        typePrimaryIdentifier = "type", branches = listOf(
                                            FlowConfiguration.Step.Properties.ConditionBranch(
                                                name = "branch1", condition = JsonObject(
                                                    mapOf(
                                                        "lhs" to JsonPrimitive("{{iterator_iteratorFilterStep.item}}"),
                                                        "operator" to JsonPrimitive("="),
                                                        "rhs" to JsonPrimitive("test")
                                                    )
                                                ), next = FlowConfiguration.Step.Id("filterSetVariables")
                                            )
                                        )

                                    )
                                ), FlowConfiguration.Step.Id("filterSetVariables") to FlowConfiguration.Step(
                                    id = FlowConfiguration.Step.Id("filterSetVariables"),
                                    name = "filter Set Variables",
                                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                                    properties = FlowConfiguration.Step.Properties(
                                        typePrimaryIdentifier = "type", variables = listOf(
                                            VariableInstance.Variable(
                                                identifier = "iterator_iteratorFilterStep.output",
                                                value = JsonPrimitive("true"),
                                                type = VariableDataType.TEXT,
                                                properties = null
                                            ),
                                        )
                                    )
                                )
                            )
                        )
                    )
                ),
            ),
        )

        logger.debug("Using flow configuration {}", Json.encodeToString(flowConfiguration))

        val context = FlowContext(
            FlowContext.GlobalVariables(
                Workspace.Id(1),
                WorkspaceVersion.Id(1),
                1,
                FlowConfiguration.Id("flow-id"),
                FlowConfiguration.Name("flow-name"),
            ), workspace = FlowContext.WorkspaceContext(
                documentId = Workspace.DocumentId("1"),
                id = Workspace.Id(1),
                key = Workspace.Key("WORKSPACE1"),
                name = Workspace.Name("workspace1"),
                workspaceFoundationId = Foundation.Id(1),
                variables = emptyMap()
            ), variables = mutableMapOf(), event = event
        )

        val flow = flowConfiguration.toExecution(
            context, null
        )


        val documentId = FlowExecution.DocumentId(
            documentService.create(
                1, FlowExecution.ForJson(
                    context,
                    flow.state,
                    flow.start,
                    flow.steps,
                )
            )
        )

        val formConfiguration = FormConfiguration.ForJson(
            id = "1", name = "Form", foundationId = "1", level = 1, metadata = EntityMetadata(
                createdAt = Instant.now(),
                updatedAt = Instant.now(),
            ), key = "key", content = listOf(
                BaseSection.TextQuestion(
                    id = BaseSection.Id("1"),
                    type = QuestionType.TEXT,
                    text = "How are you today?",
                    description = "description",
                    identifier = "identifier1",
                    properties = CommonQuestionProperties.TextQuestionProperties(
                        maxLength = 10, minLength = 1, required = true
                    )
                ), BaseSection.NumberQuestion(
                    id = BaseSection.Id("2"),
                    type = QuestionType.NUMBER,
                    text = "Bonus",
                    description = "description",
                    identifier = "Bonus",
                    properties = CommonQuestionProperties.NumberQuestionProperties(
                        decimalPlaces = 2,
                        min = BigDecimal(0),
                        max = BigDecimal(100000),
                        defaultValue = BigDecimal(0),
                        placeholder = "bonus",
                        type = CommonQuestionProperties.NumberType.NUMBER,
                        required = true
                    )
                )
            )
        )

        val workspace = Workspace.ForJson(
            Workspace.Id(1),
            Workspace.Name("1"),
            Workspace.Key("1"),
            Workspace.Description("1"),
            OrderedMap<FoundationConfiguration.Id, FoundationConfiguration.ForApi>(listOf()),
            mapOf<FormConfiguration.Id, FormConfiguration.ForJson>(FormConfiguration.Id(formConfiguration.id) to formConfiguration),
            OrderedMap<FlowConfiguration.Id, FlowConfiguration.ForJson>(listOf(flowConfiguration)),
            mapOf(),
            emptyMap(),
            emptyMap(),
            EntityMetadata.now()
        )
        val blobStorageFEDRepository = mock(BlobStorageFEDRepository::class.java)

        val filePressService = mock(FilePressService::class.java)
        val flowStepTypeConfigurationService = mock(FlowStepTypeConfigurationService::class.java)
        val flowExecutionRepository = mock(FlowExecutionRepository::class.java)
        val flowExecutionService = mock(FlowExecutionService::class.java)
        val formService = mock(FormService::class.java)
        val foundationService = mock(FoundationService::class.java)
        val proxyService = mock(ProxyService::class.java)
        val internalProxyService = mock(ProxyService::class.java)
        val workspaceVersionService = mock(WorkspaceVersionService::class.java)
        val flowExecutionId = 1.toLong()
        val listenerFactory = DefaultListenerFactory(
            documentService,
            blobStorageFEDRepository,
            documentId = documentId,
            FlowExecution.Id(flowExecutionId),
            flowExecutionRepository,
            includeLogging = true,
            skipStepUpdates = false,
            skipVariableUpdates = false,
            skipSubFlowFlowUpdates = false,
            useAutomergeFED = false
        )
        val flowRunner = FlowRunner(
            FlowExecution.Id(flowExecutionId),
            context,
            flow,
            listenerFactory,
            listenerFactory.createFlowListenersForMainFlow(),
            listenerFactory.createStepListeners(),
            flowExecutionService,
            timeoutMins = 2,

            ExecutionStepFactoryV1(
                flowStepTypeConfigurationService, proxyService, internalProxyService, filePressService, listOf(
                    FoundationJsonMapBuilder(foundationService, workspace), FormJsonMapBuilder(
                        formService, foundationService, documentService, "cookie", workspace
                    )
                ), workspaceVersionService = workspaceVersionService, listOf()
            )
        )

        val flowExecution = flowRunner.start()

        logger.debug("Document state: {}", documentService)

        assertNotNull(flowExecution)

        assertEquals(JsonPrimitive("test"), context.variables["outputVariableNameTest"]?.get())

    }

    @Disabled
    @Test
    fun `test run aggregate`() = runTest {
        val flowConfiguration = FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("flow-id"),
            name = FlowConfiguration.Name("test flow"),
            description = FlowConfiguration.Description("flow-description"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("iteratorAggregateStep"),
            steps = mutableMapOf(
                FlowConfiguration.Step.Id("iteratorAggregateStep") to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id("iteratorAggregateStep"),
                    name = "iterator aggregate",
                    variant = FlowConfiguration.Step.Variant.ITERATOR,
                    properties = FlowConfiguration.Step.Properties(
                        inputs = mapOf("outputVariableName" to JsonPrimitive("outputVariableNameTest")),
                        typePrimaryIdentifier = "iteratorAggregate",
                        configuration = FlowConfiguration.ForJson(
                            start = FlowConfiguration.Step.Id("setVariablesStep"), steps = mutableMapOf(
                                FlowConfiguration.Step.Id("setVariablesStep") to FlowConfiguration.Step(
                                    id = FlowConfiguration.Step.Id("setVariablesStep"),
                                    name = "set variables",
                                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                                    next = FlowConfiguration.Step.Id("setVariablesStep2"),
                                    properties = FlowConfiguration.Step.Properties(
                                        typePrimaryIdentifier = "type", variables = listOf(
                                            VariableInstance.Variable(
                                                identifier = "iterator_iteratorAggregateStep.output",
                                                value = JsonPrimitive("0"),
                                                type = VariableDataType.NUMBER,
                                                properties = null
                                            ),
                                        )
                                    )
                                ), FlowConfiguration.Step.Id("setVariablesStep2") to FlowConfiguration.Step(
                                    id = FlowConfiguration.Step.Id("setVariablesStep2"),
                                    name = "set variables",
                                    variant = FlowConfiguration.Step.Variant.SET_VARIABLES,
                                    properties = FlowConfiguration.Step.Properties(
                                        typePrimaryIdentifier = "type", variables = listOf(
                                            VariableInstance.Variable(
                                                identifier = "iterator_iteratorAggregateStep.output",
                                                value = JsonPrimitive("1"),
                                                type = VariableDataType.NUMBER,
                                                properties = null
                                            ),
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            ),
        )

        logger.debug("Using flow configuration {}", Json.encodeToString(flowConfiguration))

        val context = FlowContext(
            FlowContext.GlobalVariables(
                Workspace.Id(1),
                WorkspaceVersion.Id(1),
                1,
                FlowConfiguration.Id("flow-id"),
                FlowConfiguration.Name("flow-name"),
            ), workspace = FlowContext.WorkspaceContext(
                documentId = Workspace.DocumentId("1"),
                id = Workspace.Id(1),
                key = Workspace.Key("WORKSPACE1"),
                name = Workspace.Name("workspace1"),
                workspaceFoundationId = Foundation.Id(1),
                variables = emptyMap()
            ), variables = mutableMapOf(), event = event
        )

        val flow = flowConfiguration.toExecution(
            context, null
        )

        val documentId = FlowExecution.DocumentId(
            documentService.create(
                1, FlowExecution.ForJson(
                    context,
                    flow.state,
                    flow.start,
                    flow.steps,
                )
            )
        )

        val formConfiguration = FormConfiguration.ForJson(
            id = "1", name = "Form", foundationId = "1", level = 1, metadata = EntityMetadata(
                createdAt = Instant.now(),
                updatedAt = Instant.now(),
            ), key = "key", content = listOf(
                BaseSection.TextQuestion(
                    id = BaseSection.Id("1"),
                    type = QuestionType.TEXT,
                    text = "How are you today?",
                    description = "description",
                    identifier = "identifier1",
                    properties = CommonQuestionProperties.TextQuestionProperties(
                        maxLength = 10, minLength = 1, required = true
                    )
                ), BaseSection.NumberQuestion(
                    id = BaseSection.Id("2"),
                    type = QuestionType.NUMBER,
                    text = "Bonus",
                    description = "description",
                    identifier = "Bonus",
                    properties = CommonQuestionProperties.NumberQuestionProperties(
                        decimalPlaces = 2,
                        min = BigDecimal(0),
                        max = BigDecimal(100000),
                        defaultValue = BigDecimal(0),
                        placeholder = "bonus",
                        type = CommonQuestionProperties.NumberType.NUMBER,
                        required = true
                    )
                )
            )
        )

        val workspace = Workspace.ForJson(
            Workspace.Id(1),
            Workspace.Name("1"),
            Workspace.Key("1"),
            Workspace.Description("1"),
            OrderedMap<FoundationConfiguration.Id, FoundationConfiguration.ForApi>(listOf()),
            mapOf<FormConfiguration.Id, FormConfiguration.ForJson>(FormConfiguration.Id(formConfiguration.id) to formConfiguration),
            OrderedMap<FlowConfiguration.Id, FlowConfiguration.ForJson>(listOf(flowConfiguration)),
            mapOf(),
            emptyMap(),
            emptyMap(),
            EntityMetadata.now()
        )

        val filePressService = mock(FilePressService::class.java)
        val flowStepTypeConfigurationService = mock(FlowStepTypeConfigurationService::class.java)
        val flowExecutionRepository = mock(FlowExecutionRepository::class.java)
        val flowExecutionService = mock(FlowExecutionService::class.java)
        val formService = mock(FormService::class.java)
        val foundationService = mock(FoundationService::class.java)
        val proxyService = mock(ProxyService::class.java)
        val internalProxyService = mock(ProxyService::class.java)
        val workspaceVersionService = mock(WorkspaceVersionService::class.java)
        val flowExecutionId = 1.toLong()
        val blobStorageFEDRepository = mock(BlobStorageFEDRepository::class.java)

        val listenerFactory = DefaultListenerFactory(
            documentService,
            blobStorageFEDRepository,
            documentId = documentId,
            FlowExecution.Id(flowExecutionId),
            flowExecutionRepository,
            includeLogging = true,
            skipStepUpdates = false,
            skipVariableUpdates = false,
            skipSubFlowFlowUpdates = false,
            useAutomergeFED = false
        )
        val flowRunner = FlowRunner(
            FlowExecution.Id(flowExecutionId),
            context,
            flow,
            listenerFactory,
            listenerFactory.createFlowListenersForMainFlow(),
            listenerFactory.createStepListeners(),
            flowExecutionService,
            timeoutMins = 2,
            ExecutionStepFactoryV1(
                flowStepTypeConfigurationService, proxyService, internalProxyService, filePressService, listOf(
                    FoundationJsonMapBuilder(foundationService, workspace), FormJsonMapBuilder(
                        formService, foundationService, documentService, "cookie", workspace
                    )
                ), workspaceVersionService = workspaceVersionService, listOf()
            )
        )

        val flowExecution = flowRunner.start()

        logger.debug("Document state: {}", documentService)

        assertNotNull(flowExecution)

        assertEquals(JsonPrimitive("test"), context.variables["outputVariableNameTest"]?.get())

    }

}

