package services.oneteam.ai.flow.spike

import com.dashjoin.jsonata.Jsonata
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.helpers.getContent

class JsonElementSpike {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @OptIn(ExperimentalSerializationApi::class)
    @Test
    fun `should create element`() {
        val string = JsonPrimitive("string")
        val int = JsonPrimitive(1)
        val double = JsonPrimitive(1.8)
        val boolean = JsonPrimitive(true)
        val nullElement = JsonPrimitive(null)
        val jsonnull = JsonNull

        println(string)
        println(int)
        println(double)
        println(boolean)
        println(nullElement)
        println(jsonnull)
    }

    @Test
    fun `should create object`() {
        val obj = JsonObject(mapOf("key" to JsonPrimitive("value"), "number" to JsonPrimitive(1)))
        println(obj)
    }

    @Test
    fun `should create array`() {
        val array = JsonArray(listOf(JsonPrimitive("value"), JsonPrimitive(1)))
        println(array)
    }

    @Test
    fun `escaping quotes`() {
        val string = JsonPrimitive("Hello \"Bob\"!") // Hello "Bob"!
        logger.debug(string.jsonPrimitive.content) // Hello "Bob"!
        val serialized = Json.encodeToString(string)
        logger.debug(serialized) // "Hello \"Bob\"!"
        assertThat(serialized).isEqualTo("\"Hello \\\"Bob\\\"!\"")
    }

    @Test
    fun arrays() {
        val obj = JsonArray(
            listOf(
                JsonPrimitive("my value with comma, and \"quotes\""),
                JsonPrimitive("and square [brackets]")
            )
        )
        logger.debug("obj {}", obj) // obj ["my value with comma, and \"quotes\"",1]
        val result = Jsonata.jsonata(Json.encodeToString(obj)).evaluate(mapOf<Any, Any>())
        logger.debug("result {}", result) // result [my value with comma, and "quotes", and square [brackets]]
        val asJson = TypeToJsonElementConverter.toJsonElement(result)
        logger.debug(
            "asJson {}",
            Json.encodeToString(asJson)
        ) // asJson ["my value with comma, and \"quotes\"","and square [brackets]"]
    }

    @Test
    fun concat() {
        val v1 = JsonPrimitive("value1")
        val v2 = JsonPrimitive("\"")
        val v3 = JsonPrimitive("value3")
        val v4 = JsonPrimitive("\"")

        logger.debug(Json.encodeToString(JsonPrimitive("value1"))) // "value1"
        logger.debug(v1.getContent()) // value1
        logger.debug(JsonPrimitive("value1").jsonPrimitive.content) // value1

        // "YEAR(\"2025-01-01\")"
        logger.debug(
            Json.encodeToString(
                JsonPrimitive(
                    """
            YEAR("2025-01-01")
        """.trimIndent()
                ).jsonPrimitive.content
            )
        )

        // ["A","B","C"]
        logger.debug(Json.encodeToString(JsonArray(listOf(JsonPrimitive("A"), JsonPrimitive("B"), JsonPrimitive("C")))))

        val result =
            JsonPrimitive(v1.jsonPrimitive.content + v2.jsonPrimitive.content + v3.jsonPrimitive.content + v4.jsonPrimitive.content)
        logger.debug("result {}", result) // result value1"value3"
    }

    @Test
    fun concat2() {
        val result = JsonPrimitive("value1\"value3\"")
        logger.debug("result {}", result) // result value1"value3"
    }

    @Test
    fun `should add field to object`() {
        val obj = JsonObject(mapOf("key" to JsonPrimitive("value"), "number" to JsonPrimitive(1)))
        val obj2 = buildJsonObject {
            obj.forEach { (k, v) -> put(k, v) }
            put("key", JsonPrimitive("value2"))
        }
        logger.debug("obj2 {}", obj2) // obj2 {"key":"value2","number":1}

        assertThat(obj2).isEqualTo(JsonObject(mapOf("key" to JsonPrimitive("value2"), "number" to JsonPrimitive(1))))
    }
}