package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class CountATest {
    private fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(CountA.functionName, CountA.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `control test`() {
        val expression = "\$COUNTA([1, 'a', '3'])"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result.shouldBe(3)
    }

    @Test
    fun `counts numbers and strings`() {
        val expression = "\$COUNTA([1, 'a', ''])"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result.shouldBe(2)
    }

    @Test
    fun `returns zero for empty array`() {
        val expression = "\$COUNTA([])"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result.shouldBe(0)
    }
}
