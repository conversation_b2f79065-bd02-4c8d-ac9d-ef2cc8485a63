package services.oneteam.ai.flow.execution

import io.kotest.matchers.equals.shouldBeEqual
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableDefinition
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.flow.variables.VariableProperties
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.otSerializer

class FlowContextTest {

    val global = FlowContext.GlobalVariables(
        workspaceId = Workspace.Id(1),
        workspaceVersionId = WorkspaceVersion.Id(1),
        tenantId = 1,
        flowConfigurationId = FlowConfiguration.Id("1"),
        flowConfigurationName = FlowConfiguration.Name("flow")
    )


    val workspace = FlowContext.WorkspaceContext(
        documentId = Workspace.DocumentId("1"),
        id = Workspace.Id(1),
        key = Workspace.Key("WORKSPACE1"),
        name = Workspace.Name("workspace1"),
        workspaceFoundationId = Foundation.Id(1),
        variables = emptyMap()
    )


    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            buttonLabel = "string",
            form = null,
            userId = 1,
        ), Event.Id("1"), 1
    )

    //these should be mostly useless, but due to the possibility of changes during serialization, they need to be run
    //@formatter:off
    val globalAssertion: (FlowContext.GlobalVariables) -> Boolean = { it ->
        it.workspaceId == Workspace.Id(1) &&
        it.workspaceVersionId == WorkspaceVersion.Id(1) &&
        it.tenantId == 1L &&
        it.flowConfigurationId == FlowConfiguration.Id("1") &&
        it.flowConfigurationName == FlowConfiguration.Name("flow")
    }

    val workspaceAssertion: (FlowContext.WorkspaceContext) -> Boolean = { it ->
        it.documentId == Workspace.DocumentId("1") &&
        it.id == Workspace.Id(1) &&
        it.key == Workspace.Key("WORKSPACE1") &&
        it.name == Workspace.Name("workspace1") &&
        it.workspaceFoundationId == Foundation.Id(1) &&
        it.variables.isEmpty()
    }

    val eventAssertion: (Event.ForApi) -> Boolean = { it ->
        it.workspaceId == Workspace.Id(1) &&
        it.eventProperties is StartFlowManuallyFromFormProperties &&
        (it.eventProperties as StartFlowManuallyFromFormProperties).buttonLabel == "string" &&
        (it.eventProperties as StartFlowManuallyFromFormProperties).userId == 1L &&
        it.id == Event.Id("1") &&
        it.tenantId == 1L
    }
    //@formatter:on

    @Test
    fun `should serialize context`() {
        val context = FlowContext(
            global = global, variables = mutableMapOf(
                "key" to VariableInstance.Variable(
                    value = JsonPrimitive("value"), type = VariableDataType.TEXT, identifier = "key", properties = null
                )
            ), workspace = workspace, event = event
        )

        val serialized = otSerializer.encodeToString(context)

        //will throw exception if serialization fails, important for serialization tests
        val deserialized = otSerializer.decodeFromString<FlowContext>(serialized)

        assert(globalAssertion(deserialized.global))
        assert(workspaceAssertion(deserialized.workspace))
        assert(eventAssertion(deserialized.event!!))

        val variable = deserialized.variables.values.first()

        assert(variable.type == VariableDataType.TEXT)
        assert(variable.identifier == "key")
        assert(variable.get() == JsonPrimitive("value"))
    }

    @Test
    fun `should serialize context with list`() {
        val context = FlowContext(
            global = global, variables = mutableMapOf(
                "key" to VariableInstance.Variable(
                    value = JsonArray(listOf(JsonPrimitive("value"))),
                    type = VariableDataType.TEXT,
                    identifier = "key",
                    properties = null
                )
            ), workspace = workspace, event = event

        )

        val serialized = otSerializer.encodeToString(context)
        val deserialized = otSerializer.decodeFromString<FlowContext>(serialized)

        assert(globalAssertion(deserialized.global))
        assert(workspaceAssertion(deserialized.workspace))
        assert(eventAssertion(deserialized.event!!))

        val variable = deserialized.variables.values.first()

        assert(variable.type == VariableDataType.TEXT)
        assert(variable.identifier == "key")
        assert(variable.get() == JsonArray(listOf(JsonPrimitive("value"))))
    }

    @Test
    fun `should be able to set value`() = runTest {
        val context = FlowContext(
            global = global, variables = mutableMapOf(
                "1" to VariableInstance.Variable(
                    value = JsonPrimitive("z"), type = VariableDataType.TEXT, identifier = "key", properties = null
                ), "2" to VariableInstance.Variable(
                    value = JsonPrimitive(9), type = VariableDataType.NUMBER, identifier = "key", properties = null
                )

            ), workspace = workspace, event = event

        )

        context.set(
            VariableInstance.Variable(
                value = JsonPrimitive("a"), type = VariableDataType.TEXT, identifier = "1", properties = null
            )
        )
        context.set(
            VariableInstance.Variable(
                value = JsonPrimitive(2), type = VariableDataType.NUMBER, identifier = "2", properties = null
            )
        )
        assertEquals(context.variables["1"]?.get(), JsonPrimitive("a"))
        assertEquals(context.variables["2"]?.get(), JsonPrimitive(2))
    }

    @Test
    fun `should map to object values`() = runTest {
        val context = FlowContext(
            global = global, variables = mutableMapOf(
                "form" to VariableInstance.Variable(
                    value = JsonObject(mapOf("id" to JsonPrimitive(400))),
                    identifier = "form",
                    type = VariableDataType.JSON,
                )
            ), workspace = workspace, event = event
        )

        assertEquals(JsonObject(mapOf("id" to JsonPrimitive(400))), context.variables["form"]?.get())
    }

    @Test
    fun `should deserialize json variable declaration`() = runTest {
        val serialized = """
              {
                "type": "json",
                "identifier": "document",
                "value": {
                  "id": "y5WOPTzFLI",
                  "url": "myUrl",
                  "path": "/upload/1/29/tmp/rsvZJocVvk-a2nJ5zCDagR4jqH9A8yRp94hD/y5WOPTzFLI",
                  "name": "outputdoc.docx",
                  "extension": "docx"
                },
                "properties": {
                  "items": [
                    { "type": "text", "identifier": "url" },
                    { "type": "text", "identifier": "id" },
                    { "type": "text", "identifier": "name" },
                    { "type": "text", "identifier": "path" },
                    { "type": "text", "identifier": "extension" }
                  ]
                }
              }
            """.trimIndent()

        val vm: VariableInstance.Variable = otSerializer.decodeFromString(serialized)

        vm.type.typeDef shouldBeEqual VariableDataType.JSON.typeDef
        vm.identifier shouldBeEqual "document"
        vm.get() shouldBeEqual JsonObject(
            mapOf(
                "id" to JsonPrimitive("y5WOPTzFLI"),
                "url" to JsonPrimitive("myUrl"),
                "path" to JsonPrimitive("/upload/1/29/tmp/rsvZJocVvk-a2nJ5zCDagR4jqH9A8yRp94hD/y5WOPTzFLI"),
                "name" to JsonPrimitive("outputdoc.docx"),
                "extension" to JsonPrimitive("docx")
            )
        )
        vm.properties?.shouldBeEqual(
            VariableProperties.JsonVariableProperties(
                items = listOf(
                    VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "url"
                    ), VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "id"
                    ), VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "name"
                    ), VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "path"
                    ), VariableDefinition.VariableConfiguration(
                        type = VariableDataType.TEXT, identifier = "extension"
                    )
                )
            )
        )
    }

}
