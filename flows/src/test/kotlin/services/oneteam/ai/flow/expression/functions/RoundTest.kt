package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class RoundTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Round.functionName, Round.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns rounded number`() {
        val expression = "\$ROUND(1.234)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(1)
    }

    @Test
    fun `returns rounded number from decimal string`() {
        val expression = "\$ROUND(\"1.234\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(1)
    }

    @Test
    fun `returns rounded number from string`() {
        val expression = "\$ROUND(\"4\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(4)
    }

    @Test
    fun `returns rounded number with precision`() {
        val expression = "\$ROUND(1.234, 2)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(1.23)
    }

    @Test
    fun `returns rounded number with 0 precision`() {
        val expression = "\$ROUND(1.234, 0)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(1)
    }

    @Test
    fun `should round half‑up at 2 decimals`() {
        val expression = "\$ROUND(1153.8465, 2)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(1153.85)
    }
}
