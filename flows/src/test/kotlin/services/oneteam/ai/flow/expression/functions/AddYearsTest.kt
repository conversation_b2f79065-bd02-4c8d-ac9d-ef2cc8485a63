package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class AddYearsTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(AddYears.functionName, AddYears.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns date after adding years`() {
        val expression = "\$ADDYEARS(\"2021-01-01\", 1)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2022-01-01"
    }

    @Test
    fun `returns date after minus years`() {
        val expression = "\$ADDYEARS(\"2021-01-01\", -1)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2020-01-01"
    }

    @Test
    fun `returns date after adding heaps of years`() {
        val expression = "\$ADDYEARS(\"2021-01-01\", 10)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2031-01-01"
    }
}