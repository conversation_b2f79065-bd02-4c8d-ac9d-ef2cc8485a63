package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class UpperTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Upper.functionName, Upper.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns uppercase string`() {
        val expression = "\$UPPER('Hello')";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("HELLO")
    }

    @Test
    fun `returns uppercase string of empty string`() {
        val expression = "\$UPPER('')";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("")
    }
}