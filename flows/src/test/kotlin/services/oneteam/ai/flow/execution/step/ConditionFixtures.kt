package services.oneteam.ai.flow.execution.step

import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.expression.conditional.Condition
import services.oneteam.ai.flow.expression.conditional.Expression
import services.oneteam.ai.flow.expression.conditional.Operator

object ConditionFixtures {

    fun condition(branch: FlowExecution.Step.Properties.ConditionBranch, next: String?): FlowExecution.Step {
        return FlowExecution.Step(
            id = FlowExecution.Step.Id("step1"),
            variant = FlowExecution.Step.Variant.CONDITION,
            next = if (next != null) FlowExecution.Step.Id(next) else null,
            properties = FlowExecution.Step.Properties(
                typePrimaryIdentifier = "",
                branches = listOf(branch).toMutableList()
            )
        )
    }

    fun branch(
        left: String,
        operator: Operator,
        right: String?,
        next: String?
    ): FlowExecution.Step.Properties.ConditionBranch {
        return FlowExecution.Step.Properties.ConditionBranch(
            name = "If",
            condition = Condition(
                left = Expression(left),
                operator = operator,
                right = if (right != null) listOf(Expression(right)) else null
            ),
            next = if (next != null) FlowExecution.Step.Id(next) else null,
        )
    }
}