package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class ConcatTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Concat.functionName, Concat.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns concatenated string of two strings`() {
        val expression = "\$CONCAT('Hello', 'World')";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("HelloWorld")
    }

    @Test
    fun `returns concatenated string of two empty strings`() {
        val expression = "\$CONCAT('', '')";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("")
    }
}