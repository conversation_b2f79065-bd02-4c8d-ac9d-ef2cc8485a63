package services.oneteam.ai.flow.expression.functions

import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ConcatTest {

    private val fn = Concat

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName('Hello', 'World')", "HelloWorld"),
            Spec("$$functionName('', '')", ""),
            Spec("$$functionName(0, 1)", "01"),
            Spec("$$functionName(0, NULL,  1)", "01"), // NULL is treated as null
            Spec("$$functionName([])", ""),
            Spec("$$functionName([0, 1])", "01"),
            Spec("$$functionName([0, null, 1])", "0null1"), // null is treated as a string
            Spec(
                "$$functionName([['A', 'B'],['C','D']],[['E', 'F'],['G','H']])",
                listOf(listOf("AE", "BF"), listOf("CG", "DH"))
            ),
            Spec(
                "$$functionName([['A'],['B']],'C')",
                listOf(listOf("AC"), listOf("BC"))
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        functionTest(fn, spec)
    }

}