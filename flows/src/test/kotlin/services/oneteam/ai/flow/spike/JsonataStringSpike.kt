package services.oneteam.ai.flow.spike

import com.dashjoin.jsonata.Jsonata
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class JsonataStringSpike {
    @Test
    fun `should return string representation of list`() {
        val expression = "\$string([1, 2, 3])"
        val jsonata = Jsonata.jsonata(expression)
        val result = jsonata.evaluate(mapOf<Any, Any>())
        assertThat(result).isEqualTo("[1,2,3]")
    }
}