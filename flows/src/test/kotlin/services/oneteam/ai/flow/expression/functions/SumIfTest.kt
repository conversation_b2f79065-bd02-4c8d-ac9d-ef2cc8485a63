package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class SumIfTest {
    private fun jsonataWithFunction(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(SumIf.functionName, SumIf.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `numeric equality`() {
        val expression = "\$SUMIF([1, 2, 2], 2)"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "4.0"
    }

    @Test
    fun `string numeric equality`() {
        val expression = "\$SUMIF(['1', '2', '2'], '2')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "4.0"
    }

    @Test
    fun `using sum_range with relational operator`() {
        val expression = "\$SUMIF([1,2,3], '>1', [10,20,30])"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "50.0"
    }

    @Test
    fun `no matching elements returns zero`() {
        val expression = "\$SUMIF([1,2,3], '<0')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "0.0"
    }

    @Test
    fun `non-numeric range with separate numeric sum_range`() {
        val expression = "\$SUMIF(['A','B','A'], 'A', [100,200,300])"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "400.0"
    }

    @Test
    fun `range with null values`() {
        val expression = "\$SUMIF([null,2,3], 2)"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "2.0"
    }

    @Test
    fun `relational operator less than or equal`() {
        val expression = "\$SUMIF([1,2,3,4], '<=2')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "3.0"
    }

    @Test
    fun `explicit equals operator`() {
        val expression = "\$SUMIF([1,2,3], '=2')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "2.0"
    }

    @Test
    fun `negative numbers with relational operator`() {
        val expression = "\$SUMIF([-1,-2,0,1], '<0')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "-3.0"
    }

    @Test
    fun `sum if with string lookup`() {
        val expression = "\$SUMIF([\"Hello\", \"Hello\", \"goodbye\"], \"Hello\", [1, 2, 3])"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "3.0"
    }

    @Test
    fun `sumif with string values`() {
        val expression = "\$SUMIF([\"Hello\", \"Hello\", \"goodbye\"], \"Hello\", [\"1\", \"2\", \"3\"])"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "3.0"
    }
}
