package services.oneteam.ai.flow.expression.conditional

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.expression.functions.Contains
import services.oneteam.ai.flow.expression.functions.DoesNotContain
import services.oneteam.ai.flow.expression.functions.IsEmpty

class ConditionTest {

    @Test
    fun `test parser with simple condition`() {
        val condition = Condition(
            Expression("5000"),
            Operator.GREATER_THAN,
            listOf(Expression("0"))
        )
        assertEquals("5000 > 0", condition.toString())
    }

    @Test
    fun `test parser with simple condition with string`() {
        val condition = Condition(
            Expression("\"hello world\""),
            Operator.CONTAINS,
            listOf(Expression("\"hello\""))
        )
        assertEquals("$${Contains.functionName}(\"hello world\", \"hello\")", condition.toString())
    }

    @Test
    fun `test parser with simple condition with date`() {
        val condition = Condition(
            Expression("\"2025-01-01\""),
            Operator.GREATER_THAN,
            listOf(Expression("\"2020-01-01\""))
        )
        assertEquals("\"2025-01-01\" > \"2020-01-01\"", condition.toString())
    }

    @Test
    fun `test parser with simple condition with boolean`() {
        val condition = Condition(
            Expression("false"),
            Operator.EQUAL,
            listOf(Expression("false"))
        )
        assertEquals("false = false", condition.toString())
    }

    @Test
    fun `test parser with isEmpty`() {
        val condition = Condition(
            Expression("\"\""),
            Operator.IS_EMPTY
        )
        assertEquals("$${IsEmpty.functionName}(\"\")", condition.toString())
    }

    @Test
    fun `test parser with variable to be replaced`() {
        val condition = Condition(
            Expression("{{variable}}"),
            Operator.EQUAL,
            listOf(Expression("\"hello\""))
        )
        assertEquals("{{variable}} = \"hello\"", condition.toString())
    }


    @Test
    fun `test parser with multiple conditions`() {
        val condition = Condition(
            Expression(""),
            LogicalOperator.AND,
            listOf(
                Condition(
                    Expression("{{form.np9GiUMruw.answer}}"),
                    Operator.GREATER_THAN,
                    listOf(Expression("5")),
                ),
                Condition(
                    Expression("${'$'}count({{form.VEOTahOeLj.answer}})"),
                    Operator.GREATER_THAN,
                    listOf(Expression("2"))
                )
            )
        )
        assertEquals(
            "({{form.np9GiUMruw.answer}} > 5 and ${'$'}count({{form.VEOTahOeLj.answer}}) > 2)", condition.toString()
        )
    }

    @Test
    fun `test parser with complex condition`() {
        val condition = Condition(
            Expression("5000"),
            LogicalOperator.AND,
            listOf(
                Condition(
                    Expression("5000"),
                    Operator.GREATER_THAN,
                    listOf(Expression("0"))
                ),
                Condition(
                    Expression("\"\""),
                    Operator.IS_EMPTY
                ),
                Condition(
                    Expression(""),
                    LogicalOperator.OR,
                    listOf(
                        Condition(
                            Expression("\"hello world\""),
                            Operator.CONTAINS,
                            listOf(Expression("\"hello\""))
                        ),
                        Condition(
                            Expression("'2025-01-01'"),
                            Operator.GREATER_THAN,
                            listOf(Expression("'2020-01-01'"))
                        ),
                        Condition(
                            Expression(""),
                            LogicalOperator.AND,
                            listOf(
                                Condition(
                                    Expression("false"),
                                    Operator.EQUAL,
                                    listOf(Expression("false"))
                                ),
                                Condition(
                                    Expression("true"),
                                    Operator.NOT_EQUAL,
                                    listOf(Expression("false"))
                                )
                            )
                        )
                    )
                ),
                Condition(
                    Expression(""),
                    LogicalOperator.AND,
                    listOf(
                        Condition(
                            Expression("5000"),
                            Operator.EQUAL,
                            listOf(Expression("5000"))
                        ),
                        Condition(
                            Expression("\"hello world\""),
                            Operator.DOES_NOT_CONTAIN,
                            listOf(Expression("\"goodbye\""))
                        )
                    )
                )
            )
        )
        val expectedOutput =
            "(5000 > 0 and $${IsEmpty.functionName}(\"\") and ($${Contains.functionName}(\"hello world\", \"hello\") or '2025-01-01' > '2020-01-01' or (false = false and true != false)) and (5000 = 5000 and $${DoesNotContain.functionName}(\"hello world\", \"goodbye\")))"
        assertEquals(expectedOutput, condition.toString())
    }
}