package services.oneteam.ai.flow.spike

import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.JsonPath
import com.jayway.jsonpath.Option
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import org.junit.jupiter.api.Test

class JsonPathSpike {

    @Test
    fun `test1`() {
        val json = """
            {
                "name": "<PERSON>",
                "age": 30,
                "cars": [
                    {
                        "model": "Ford",
                        "mpg": 25.5
                    },
                    {
                        "model": "BMW",
                        "mpg": 30.0
                    }
                ]
            }
        """.trimIndent()

        val jsonObject = Json.decodeFromString<JsonElement>(json)
        val jsonPath = "$.cars.*.model"

        val config = Configuration.defaultConfiguration()
            .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL, Option.SUPPRESS_EXCEPTIONS)
        val ctx = JsonPath.using(config).parse(jsonObject)

        val result: JsonArray = JsonArray(ctx.read(jsonPath))
        println(result) // Ford
    }
}