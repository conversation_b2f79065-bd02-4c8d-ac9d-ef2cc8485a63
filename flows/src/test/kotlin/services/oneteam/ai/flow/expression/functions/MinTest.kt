package services.oneteam.ai.flow.expression.functions

import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MinTest {
    private val fn = Min

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName([])", BigDecimal(0), null, "Returns 0 for an empty array"),
            Spec("$$functionName(1)", BigDecimal(1), null, ""),
            Spec("$$functionName(2, 3)", BigDecimal(2), null, "Returns the minimum of two numbers"),
            Spec("$$functionName(3, 2, 'a')", BigDecimal(2), null, "Ignores non-numeric values"),
            Spec("$$functionName(2, \"1\")", BigDecimal(1), null, "Handles numbers as strings and returns the minimum"),
            Spec("$$functionName([1, 2, 3, 4, 5])", BigDecimal(1), null, "Returns the minimum value in an array"),
// TODO need to standardise on BigDecimals in another pr
//            Spec(
//                "$$functionName([[1, 2, 3]])",
//                listOf(listOf(BigDecimal(1), BigDecimal(2), BigDecimal(3))),
//                "Returns the minimum value in an array"
//            ),
            Spec(
                "$$functionName([1, 2, 'a', 4, 5])",
                BigDecimal(1), null,
                "Returns the minimum value in an array, ignoring non-numeric values"
            ),
            Spec(
                "$$functionName([\"1\", 2, \"3\", 4, 5])",
                BigDecimal(1), null,
                "Handles mixed types in an array and returns the minimum"
            ),
            Spec(
                "$$functionName([1, 2, 3], 4)",
                listOf(BigDecimal(1), BigDecimal(2), BigDecimal(3)), null,
                "Performs pairwise comparison with a larger number"
            ),
            Spec(
                "$$functionName([1, 2, 3], -4)",
                listOf(BigDecimal(-4), BigDecimal(-4), BigDecimal(-4)), null,
                "Performs pairwise comparison with a smaller number"
            ),
            Spec(
                "$$functionName([4, 5], [[1, 2, 3], [5, 4, 3]])",
                listOf(
                    listOf(BigDecimal(1), BigDecimal(2), BigDecimal(0)),
                    listOf(BigDecimal(4), BigDecimal(4), BigDecimal(0))
                ), null,
                "Handles nested arrays and returns the pairwise minimum"
            ),
            Spec("$$functionName(0.1, 0.2)", BigDecimal("0.1"), null, "Returns the minimum of two fractional numbers"),
            Spec(
                "$$functionName([[1,2,3]],[[4,-5,6]])",
                listOf(listOf(BigDecimal("1"), BigDecimal("-5"), BigDecimal("3"))), null,
                "Returns the minimum of two fractional numbers"
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        functionTest(fn, spec)
    }

}