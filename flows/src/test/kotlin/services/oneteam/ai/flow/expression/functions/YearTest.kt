package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.time.format.DateTimeParseException

class YearTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Year.functionName, Year.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return the year`() {
        val expression = "\$YEAR(\"2021-01-01\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 2021
    }

    @Test
    fun `should return the year for a date in a different timezone`() {
        val expression = "\$YEAR(\"2021-01-01\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 2021
    }

    @Test
    fun `should throw an error when the input is not a date`() {
        shouldThrow<DateTimeParseException> {
        val expression = "\$YEAR(\"hello world\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
            jsonata.evaluate(null)
        }
    }
}