package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class SumproductTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Sumproduct.functionName, Sumproduct.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns SumProduct of 2 arrays`() {
        val jsonata = jsonataWithFunctionRegistered("\$SUMPRODUCT([1, 2, 3], [4, 5, 6])")
        val result = jsonata.evaluate(null)
        result shouldBe "32.0"
    }

    @Test
    fun `returns result based on size of first array`() {
        val jsonata = jsonataWithFunctionRegistered("\$SUMPRODUCT([1, 2, 3], [4, 5])")
        val result = jsonata.evaluate(null)
        result shouldBe "14.0"
    }

    @Test
    fun `returns result from mixed array type`() {
        val jsonata = jsonataWithFunctionRegistered("\$SUMPRODUCT([1, \"2\", 3], [4, \"5\"])")
        val result = jsonata.evaluate(null)
        result shouldBe "14.0"
    }
}