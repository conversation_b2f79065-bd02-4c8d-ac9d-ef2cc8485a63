package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class AddDaysTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(AddDays.functionName, AddDays.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns date after adding days`() {
        val expression = "\$ADDDAYS(\"2021-01-01\", 1)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2021-01-02"
    }

    @Test
    fun `returns date after minus days`() {
        val expression = "\$ADDDAYS(\"2021-01-01\", -1)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2020-12-31"
    }

    @Test
    fun `returns date after adding heaps of days`() {
        val expression = "\$ADDDAYS(\"2021-01-01\", 650)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2022-10-13"
    }
}