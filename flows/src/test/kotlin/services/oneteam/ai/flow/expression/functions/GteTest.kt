package services.oneteam.ai.flow.expression.functions

import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GteTest {

    private val fn = Gte

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName('2025-12-01', '2025-12-01')", true),
            Spec("$$functionName('2025-12-02', '2025-12-01')", true),
            Spec("$$functionName('2025-12-01', '2025-12-02')", false),

            Spec("$$functionName(1, 1)", true),
            Spec("$$functionName(3, 2)", true),
            Spec(
                "$$functionName([])",
                null,
                exception = InvalidParametersException::class,
                "Single list is not valid"
            ),
            Spec("$$functionName([30, 10], [20, 10])", listOf(true, true)),
            Spec("$$functionName([10, 20], 10)", listOf(true, true)),
            Spec("$$functionName([10, -5], [10])", listOf(true, false)),
            Spec("$$functionName([-10, 5], [10])", listOf(false, true)),
            Spec("$$functionName('a','b')", false),
            Spec("$$functionName('b','b')", true),
            Spec("$$functionName('c','b')", true),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        functionTest(fn, spec)
    }

}