package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class DateTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Date.functionName, Date.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns date from string`() {
        val expression = "\$DATE(2021,1,1)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("2021-01-01")
    }
}