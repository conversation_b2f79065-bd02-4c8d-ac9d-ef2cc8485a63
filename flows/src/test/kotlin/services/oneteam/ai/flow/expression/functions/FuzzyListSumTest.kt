package services.oneteam.ai.flow.expression.functions

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class FuzzyListSumTest {

    private val fuzzyListSum = FuzzyListSum()

    @Test
    fun `match should return true for a single list of numbers`() {
        fuzzyListSum.match(listOf(listOf(1, 2, 3))) shouldBe true
    }

    @Test
    fun `match should return true for a single list of numeric strings`() {
        fuzzyListSum.match(listOf(listOf("1", "2.5", "3", "abc"))) shouldBe true
    }

    @Test
    fun `match should return false for a single list with non-numeric values`() {
        fuzzyListSum.match(listOf(listOf(2), listOf(3))) shouldBe false
    }

    @Test
    fun `match should return false for multiple arguments`() {
        fuzzyListSum.match(listOf(1, 2, 3)) shouldBe false
    }

    @Test
    fun `sum should return correct result for a list of numbers`() {
        fuzzyListSum.sum(listOf(listOf(1, 2, 3))) shouldBe BigDecimal(6)
    }

    @Test
    fun `sum should return correct result for a list of numeric strings`() {
        fuzzyListSum.sum(listOf(listOf("1", "2.5", "3"))) shouldBe BigDecimal(6.5)
    }

    @Test
    fun `sum should ignore non-numeric values`() {
        fuzzyListSum.sum(listOf(listOf(1, "abc", 3))) shouldBe BigDecimal(4)
    }
}