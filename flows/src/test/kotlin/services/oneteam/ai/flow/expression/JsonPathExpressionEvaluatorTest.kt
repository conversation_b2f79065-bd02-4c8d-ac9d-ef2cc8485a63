package services.oneteam.ai.flow.expression

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.buildJsonObject
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class JsonPathExpressionEvaluatorTest {
    @Test
    fun `should leave empty string if variable not found`() = runTest {
        val jsonPathExpressionEvaluator = JsonPathExpressionEvaluator()
        val expression = "form.123.answer"
        val result = jsonPathExpressionEvaluator.evaluate(expression, buildJsonObject {  })
        assertEquals("", result)
    }
}