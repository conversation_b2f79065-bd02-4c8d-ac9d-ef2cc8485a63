package services.oneteam.ai.flow.expression

import com.dashjoin.jsonata.JException
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class JsonataExpressionEvaluatorScenarioTest {

    @Test
    fun `should evaluate expression with context`() = runTest {
        val expression = "\$MULTIPLY(CYForm.w1FWFfddba.answer, CYForm.AjV4Ryb0x.answer)"
        val result = JsonataExpressionEvaluator().evaluate(
            expression, mapOf(
                "CYForm" to mapOf(
                    "w1FWFfddba" to mapOf("answer" to 2),
                    "AjV4Ryb0x" to mapOf("answer" to 3)
                )
            )
        )

        assertThat(result).isEqualTo(BigDecimal(6))
    }

    @Test
    fun `should fail to evaluate when variables start with numbers`() = runTest {
        assertThrows<JException> {
            val expression = "\$MULTIPLY(CYForm.w1FWFfddba.answer, CYForm.8AjV4Ryb0x.answer)"
            JsonataExpressionEvaluator().evaluate(
                expression, mapOf(
                    "CYForm" to mapOf(
                        "w1FWFfddba" to mapOf("answer" to 2),
                        "8AjV4Ryb0x" to mapOf("answer" to 3)
                    )
                )
            )
        }
    }
}