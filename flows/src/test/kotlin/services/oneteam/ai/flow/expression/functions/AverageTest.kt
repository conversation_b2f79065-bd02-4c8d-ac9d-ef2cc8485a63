package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class AverageTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Average.functionName, Average.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns average of integers`() {
        val expression = "\$AVERAGE([1, 2, 3, 4, 5])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(BigDecimal("3"))
    }

    @Test
    fun `returns average of strings`() {
        val expression = "\$AVERAGE([\"1\", \"2\", \"3\", \"4\", \"5\"])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(BigDecimal("3"))
    }

    @Test
    fun `returns average of numbers and strings`() {
        val expression = "\$AVERAGE([1, \"2\", 3, 4, \"5\"])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(BigDecimal("3"))
    }

    @Test
    fun `returns average of decimals`() {
        val expression = "\$AVERAGE([1.1, 2.2, 5.5, 4.4, 3.3])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(BigDecimal("3.3"))
    }

    @Test
    fun `fails when array contains strings`() {
        val expression = "\$AVERAGE([1, 2, 'a', 4, 5])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<NumberFormatException> {
            jsonataExpression.evaluate(mapOf<String, Any>())
        }
    }
}