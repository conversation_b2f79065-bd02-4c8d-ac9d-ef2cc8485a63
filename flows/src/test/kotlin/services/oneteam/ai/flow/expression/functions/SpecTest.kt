package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import kotlin.reflect.KClass

data class Spec(
    val input: String,
    val expected: Any?,
    val exception: KClass<out Throwable>? = null, // Exception expected, if any
    val description: String? = null
)

fun jsonataWithFunctionRegistered(fn: ComputeFunction, expression: String): Jsonata {
    val jsonataExpression = Jsonata.jsonata(expression)
    jsonataExpression.registerFunction(fn.functionName, fn.evaluatorFunction)
    return jsonataExpression
}

fun functionTest(fn: ComputeFunction, spec: Spec) {
    val jsonataExpression = jsonataWithFunctionRegistered(fn, spec.input)
    try {
        val result = jsonataExpression.evaluate(null)
        // expect a valid result
        result.shouldBe(spec.expected)
    } catch (e: Exception) {
        if (spec.exception != null) {
            // expect an exception
            e::class shouldBe spec.exception
        } else {
            throw e // unexpected exception
        }
    }
}
