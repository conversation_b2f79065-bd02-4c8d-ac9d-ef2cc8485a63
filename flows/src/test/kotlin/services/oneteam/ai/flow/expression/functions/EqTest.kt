package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EqTest {

    private val fn = Eq

    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(fn.functionName, fn.evaluatorFunction)
        return jsonataExpression
    }

    data class Spec(
        val input: String,
        val expected: Any,
    )

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName('2025-12-01', '2025-12-01')", true),
            Spec("$$functionName('2025-12-01', '2025-12-31')", false),
            Spec("$$functionName(1, 1)", true),
            Spec("$$functionName(1, 2)", false),
            Spec("$$functionName([10, 10], [20, 10])", listOf(false, true)),
            Spec("$$functionName([10, 0], 10)", listOf(true, false)),
            Spec("$$functionName([10, 0], [10])", listOf(true, true)),
            Spec("$$functionName([[10, 0],[5, 7]], [[10, 0],[5, 7]])", listOf(listOf(true, true), listOf(true, true))),
            Spec("$$functionName([[10, 0],[5, 7]], [[10],[5, 7]])", listOf(listOf(true, true), listOf(true, true))),
            Spec("$$functionName([[10, 0],[5, 7]], [[10],[5, 8]])", listOf(listOf(true, true), listOf(true, false))),
            Spec("$$functionName([[10, 0],[5, 7]], [[10],[5]])", listOf(listOf(true, true), listOf(true, false))),
            Spec("$$functionName([[10, 0],[5, 7]], 10)", listOf(listOf(true, false), listOf(false, false))),
            Spec("$$functionName([['a', 'b'],['c', 'd']], 'a')", listOf(listOf(true, false), listOf(false, false))),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        val jsonataExpression = jsonataWithFunctionRegistered(spec.input)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(spec.expected)
    }

}