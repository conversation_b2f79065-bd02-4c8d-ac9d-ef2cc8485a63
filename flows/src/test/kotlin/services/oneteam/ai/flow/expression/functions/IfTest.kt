package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class IfTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(If.functionName, If.evaluatorFunction)
        jsonataExpression.registerFunction(Multiply.functionName, Multiply.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return the first value when the condition is true`() {
        val expression = "\$IF(true, 1, 2)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `should return the second value when the condition is false`() {
        val expression = "\$IF(false, 1, 2)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 2
    }

    @Test
    fun `should return the first value when the condition is true and the second value is not provided`() {
        val expression = "\$IF(true, 1)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `should return null when the condition is false and the second value is not provided`() {
        val expression = "\$IF(false, 1)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe null
    }

    @Test
    fun `should return the first value when the condition is true and the second value is null`() {
        val expression = "\$IF(true, 1, null)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `should return null when the condition is false and the second value is null`() {
        val expression = "\$IF(false, 1, null)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe null
    }

    @Test
    fun `should return the first value when the condition is true and the second value is a function`() {
        val expression = "\$IF(\$MULTIPLY(2, 2) = 4.0, \$MULTIPLY(4, 4), 1.0)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe BigDecimal("16")
    }
}