package services.oneteam.ai.flow.expression

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class JsonPathFromJsonObjectExpressionEvaluatorTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun `test list from json object`() {
        val json = """
            {
                "name": "<PERSON>",
                "age": 30,
                "cars": [
                    {
                        "model": "Ford",
                        "mpg": 25.5
                    },
                    {
                        "model": "BMW",
                        "mpg": 30.0
                    }
                ]
            }
        """.trimIndent()

        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val jsonPath = "$.cars.*.model"

        val result = JsonPathFromJsonObjectExpressionEvaluator(jsonObject).evaluate(jsonPath)

        assertThat(result).isEqualTo(JsonArray(listOf(JsonPrimitive("Ford"), JsonPrimitive("BMW"))))
    }

    @Test
    fun `test object from json object`() {
        val json = """
            {
                "name": "John",
                "age": 30,
                "cars": [
                    {
                        "model": "Ford",
                        "mpg": 25.5
                    },
                    {
                        "model": "BMW",
                        "mpg": 30.0
                    }
                ]
            }
        """.trimIndent()

        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val jsonPath = "$.cars[1]"

        val result = JsonPathFromJsonObjectExpressionEvaluator(jsonObject).evaluate(jsonPath)

        assertThat(result).isEqualTo(JsonObject(mapOf("model" to JsonPrimitive("BMW"), "mpg" to JsonPrimitive(30.0))))
    }

    @Test
    fun `test array from json object`() {
        val json = """
            {
              "table": 
                [
                  { "_rowId": 213, "column1": { "answer": "12", "questionId": "2131" } },
                  { "_rowId": 214, "column1": { "answer": "13", "questionId": "2131" } }
                ]
            }
        """.trimIndent()

        val jsonObject = Json.decodeFromString<JsonObject>(json)
        val jsonPath = "table.*.column1.answer"

        val result = JsonPathFromJsonObjectExpressionEvaluator(jsonObject).evaluate(jsonPath)
        logger.trace("result: {}", result)

        assertThat(result).isEqualTo(
            JsonArray(
                listOf(
                    JsonPrimitive("12"),
                    JsonPrimitive("13")
                )
            )
        )
    }
}