package services.oneteam.ai.flow.support.pairwise

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class PairwiseValueTest {

    @Test
    fun `should create PairwiseValue with correct dimension`() {
        assertEquals(PairwiseValue(4, 0), PairwiseValue.of(4))
        assertEquals(PairwiseValue(listOf(1, 2, 3), 1), PairwiseValue.of(listOf(1, 2, 3)))
        assertEquals(PairwiseValue(listOf(listOf(4, 5)), 2), PairwiseValue.of(listOf(listOf(4, 5))))
    }

    @Test
    fun `should calculate length at dimension`() {
        assertEquals(1, PairwiseValue.of(4).lengthAtDimension(0))
        assertEquals(1, PairwiseValue.of(listOf(1, 2, 3)).lengthAtDimension(0))
        assertEquals(3, PairwiseValue.of(listOf(1, 2, 3)).lengthAtDimension(1))
        assertEquals(2, PairwiseValue.of(listOf(listOf(4, 5), listOf(4, 5), listOf(4, 5))).lengthAtDimension(1))
        assertEquals(3, PairwiseValue.of(listOf(listOf(4, 5), listOf(4, 5), listOf(4, 5))).lengthAtDimension(2))
    }

    @Test
    fun `should calculate dimension`() {
        assertThat(PairwiseValue.dimension(4)).isEqualTo(0)
        assertThat(PairwiseValue.dimension(listOf(4))).isEqualTo(1)
        assertThat(PairwiseValue.dimension(listOf(listOf(4, 5)))).isEqualTo(2)
        assertThat(PairwiseValue.dimension(listOf(listOf(4, 5), listOf(4, 5)))).isEqualTo(2)
    }

    @Test
    fun `should uplift to length`() {
        assertThat(PairwiseValue.of(4).upliftToLength(3)).isEqualTo(
            PairwiseValue.of(
                listOf(4, 4, 4)
            )
        )
        assertThat(PairwiseValue.of(listOf(1, 2, 3)).upliftToLength(2)).isEqualTo(
            PairwiseValue.of(listOf(listOf(1, 2, 3), listOf(1, 2, 3)))
        )

    }

    @Test
    fun `should find value at dimension`() {
        assertThat(PairwiseValue.of(4).valueAtDimension(0)).isEqualTo(4)
        assertThat(PairwiseValue.of(listOf(1)).valueAtDimension(1)).isEqualTo(listOf(1))
        assertThat(PairwiseValue.of(listOf(1, 2, 3)).valueAtDimension(1)).isEqualTo(listOf(1, 2, 3))
        assertThat(PairwiseValue.of(listOf(listOf(listOf(5, 6), listOf(0, 0)))).valueAtDimension(2)).isEqualTo(
            listOf(
                listOf(
                    5,
                    6
                ), listOf(0, 0)
            )
        )
    }

    @Test
    fun `should calculate length`() {
        assertThat(PairwiseValue.of(4).lengthAtDimension(0)).isEqualTo(1)
        assertThat(PairwiseValue.of(listOf(1, 2, 3)).lengthAtDimension(1)).isEqualTo(3)
        assertThat(PairwiseValue.of(listOf(listOf(5, 6))).lengthAtDimension(1)).isEqualTo(2)
        assertThat(PairwiseValue.of(listOf(listOf(5, 6), listOf(5, 6), listOf(5, 6))).lengthAtDimension(2)).isEqualTo(3)
    }
}