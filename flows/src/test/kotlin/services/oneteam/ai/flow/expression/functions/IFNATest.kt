package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class IfnaTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Ifna.functionName, Ifna.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns value_if_na when value is #NA`() {
        val expression = "\$IFNA('#N/A', 'default')"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("default")
    }

    @Test
    fun `returns original value when not #NA`() {
        val expression = "\$IFNA(100, 'default')"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(100)
    }
}
