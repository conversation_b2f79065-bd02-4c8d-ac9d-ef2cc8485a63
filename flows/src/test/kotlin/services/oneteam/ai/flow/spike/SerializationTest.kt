package services.oneteam.ai.flow.spike

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.ToNumberPolicy
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.jsonPrimitive
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import kotlin.test.assertEquals

class SerializationSpike {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun `should escape quotes`() {
        val jsonString = """
            {
                "key": "\"value\"",
                "key2": "value2",
                "key3": "value3"
            }
        """.trimIndent()

        logger.debug("jsonString {}", jsonString)

        val map = Json.decodeFromString<HashMap<String, JsonElement>>(jsonString)
        logger.debug("map {}", map)
        logger.debug("map[key] `{}`", map["key"]!!.jsonPrimitive.content)

        val str = Json.encodeToString(map)
        logger.debug("str {}", str)
    }

    @Test
    fun `should deserialize map`() {
        val jsonString = """
            { 
                "key1": "value1",
                "key2": 2,
                "key3": {
                    "key4": "value4"
                }
            }
        """.trimIndent()

        val map = Json.decodeFromString<HashMap<String, JsonElement>>(jsonString)

        logger.debug("map {}", map)

    }

    @Disabled("Fails with Serializer for class 'Any' is not found.")
    @Test
    fun `should serialize map`() {
        val map = mapOf(
            "key1" to "value1",
            "key2" to 2,
            "key3" to mapOf("key4" to "value4")
        )

//        Fails with
//        Serializer for class 'Any' is not found.
//        Please ensure that class is marked as '@Serializable' and that the serialization compiler plugin is applied.

        val jsonString = Json.encodeToString(map)

        logger.debug("jsonString {}", jsonString)

    }

    @Test
    fun `should serialize map with gson`() {
        val map = mapOf(
            "key1" to "value1",
            "key2" to 2,
            "key3" to mapOf("key4" to "value4")
        )

        val jsonString = Gson().toJson(map)

        logger.debug("jsonString {}", jsonString)

        assertEquals(
            """
                {"key1":"value1","key2":2,"key3":{"key4":"value4"}}
            """.trimIndent(),
            jsonString
        )
    }

    @Test
    fun `should deserialize map with gson`() {
        val jsonString = """
            { 
                "key1": "value1",
                "key2": 2,
                "key3": {
                    "key4": "value4"
                }
            }
        """.trimIndent()

        val map: Map<*, *> = Gson().fromJson(jsonString, HashMap::class.java)

        println(map)

        assertEquals(
            mapOf(
                "key1" to "value1",
                // integer comes thru as double (see next test)
                "key2" to 2.0,
                "key3" to mapOf("key4" to "value4")
            ), map
        )
    }

    @Test
    fun `should deserialize map with gson with integers`() {
        val jsonString = """
            { 
                "key1": "value1",
                "key2": 2,
                "key4": 2.2,
                "key3": {
                    "key4": "value4"
                }
            }
        """.trimIndent()

        val map: Map<*, *> = GsonBuilder()
            // https://stackoverflow.com/a/70229243
            //   prevent integers from being converted to doubles
            .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
            .create()
            .fromJson(jsonString, HashMap::class.java)

        logger.debug("map {}", map)


        assertEquals(
            mapOf(
                "key1" to "value1",
                "key2" to 2L,
                "key4" to 2.2,
                "key3" to mapOf("key4" to "value4")
            ), map
        )
    }
}