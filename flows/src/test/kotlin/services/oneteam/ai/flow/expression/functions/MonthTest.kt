package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.time.format.DateTimeParseException

class MonthTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Month.functionName, Month.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return the month of the year`() {
        val expression = "\$MONTH(\"2021-01-01\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `should return the month of the year for a date in a different timezone`() {
        val expression = "\$MONTH(\"2021-01-01\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `should throw an error when the input is not a date`() {
        shouldThrow<DateTimeParseException> {
        val expression = "\$MONTH(\"hello world\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
            jsonata.evaluate(null)
        }
    }
}