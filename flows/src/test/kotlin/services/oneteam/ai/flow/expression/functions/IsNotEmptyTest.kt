package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class IsNotEmptyTest {
    private fun assertIsNotEmpty(expression: String, expected: Boolean) {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(IsNotEmpty.functionName, IsNotEmpty.evaluatorFunction)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(expected)
    }

    @Test
    fun `returns false for null`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(null)", expected = false)
    }

    @Test
    fun `returns false for undefined`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(undefined)", expected = false)
    }

    @Test
    fun `returns false for empty string`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY('')", expected = false)
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(\"\")", expected = false)
    }

    @Test
    fun `returns true for non-empty string 'Hello'`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY('Hello')", expected = true)
    }

    @Test
    fun `returns true for non-empty string ' ' (space)`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(' ')", expected = true)
    }

    @Test
    fun `returns true for non-empty string '  '`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY('  ')", expected = true)
    }

    @Test
    fun `return true for number 123`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(123)", expected = true)
    }

    @Test
    fun `return true for number 0`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(0)", expected = true)
    }

    @Test
    fun `return true for date 2023-10-01`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY('2023-10-01')", expected = true)
    }

    @Test
    fun `return true for boolean true`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(true)", expected = true)
    }

    @Test
    fun `return true for boolean false`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(false)", expected = true)
    }

    @Test
    fun `return false for empty object {}`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY({})", expected = false)
    }

    @Test
    fun `return true for non-empty object`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY({'key': 'value'})", expected = true)
    }

    @Test
    fun `return false for empty array`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([])", expected = false)
    }

    @Test
    fun `return false for array with empty strings`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([\"\", \"\"])", expected = false)
    }

    @Test
    fun `return false for nested arrays with empty strings`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([[\"\", \"\"],[\"\", \"\"]])", expected = false)
    }


    @Test
    fun `return false for array with empty values`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([null, null])", expected = false)
    }

    @Test
    fun `return false for arrays in arrays with empty values`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([[\"\"], []])", expected = false)
    }

    @Test
    fun `return true for array with non-empty strings`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(['Hello', 'World', ''])", expected = true)
    }

    @Test
    fun `return true for array with non-empty values`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([1, 2, 3])", expected = true)
    }

    @Test
    fun `return true for array with mixed values`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([null, 1, true])", expected = true)
    }

    @Test
    fun `return true for floating values`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(1.5)", expected = true)
    }

    @Test
    fun `return false for deeply nested empty arrays`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([[[[]]]])", expected = false)
    }

    @Test
    fun `return false for nested empty arrays`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([[],[]])", expected = false)
    }

    @Test
    fun `return false for nested empty arrays and objects`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([{}, {}, {}, [{}, {}]])", expected = false)
    }

    @Test
    fun `return true for deeply nested arrays with values`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY([[[1]]])", expected = true)
    }

    @Test
    fun `return true for nested objects with values`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY({'key': {'nestedKey': 'value'}})", expected = true)
    }

    @Test
    fun `return true for nested empty objects`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY({'key': {}})", expected = true)
    }

    @Test
    fun `return true for string with special characters`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY('!@#$%^&*()')", expected = true)
    }

    @Test
    fun `return true for very large number`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY(999999999999999999)", expected = true)
    }

    @Test
    fun `return true for very long string`() {
        assertIsNotEmpty(expression = "\$IS_NOT_EMPTY('${"a".repeat(10000)}')", expected = true)
    }
}