package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class ProperTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Proper.functionName, Proper.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should capitalize the first letter of each word in a string`() {
        val expression = "\$PROPER(\"hello world\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello World"
    }

    @Test
    fun `should capitalize the first letter of each word in a string with multiple spaces`() {
        val expression = "\$PROPER(\"hello   world\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello   World"
    }

    @Test
    fun `should capitalize the first letter of each word in a string with multiple spaces and tabs`() {
        val expression = "\$PROPER(\"hello \t  world\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello \t  World"
    }

    @Test
    fun `should capitalize the first letter of each word in a string with multiple spaces and newlines`() {
        val expression = "\$PROPER(\"hello \n  world\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello \n  World"
    }

    @Test
    fun `should capitalize the first letter of each word in a string with multiple spaces, tabs, and newlines`() {
        val expression = "\$PROPER(\"hello \n\t  world\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello \n\t  World"
    }

    @Test
    fun `should capitalize the first letter of each word in a string with multiple spaces, tabs, newlines, and carriage returns`() {
        val expression = "\$PROPER(\"hello \n\t  world\r\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello \n\t  World\r"
    }
}