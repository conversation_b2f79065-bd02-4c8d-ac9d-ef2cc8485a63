package services.oneteam.ai.flow.execution

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.shared.otSerializer

class FlowExecutionTest {

    @Test
    fun `should deserialize foreach`() {
        val flowExecution = otSerializer.decodeFromString<FlowExecution.ForJson>(
            this::class.java.getResource("/flows/flow-execution-document-foreach.json")!!.readText()
        )
        assertThat(flowExecution).isNotNull
        // subflows
        assertThat(flowExecution.steps[Step.Id("step1")]?.subFlows).isNotEmpty
        assertThat(flowExecution.steps[Step.Id("step1")]?.subFlows["step1_1"]).isNotNull
        // state
        assertThat(flowExecution.state.steps?.entities[FlowExecution.State.Step.Id("step1")]).isNotNull
        assertThat(
            flowExecution.steps[Step.Id("step1")]?.subFlows["step1_1"]?.state?.steps?.entities[FlowExecution.State.Step.Id(
                "subStep1"
            )]
        ).isNotNull
    }

    @Test
    fun `should deserialize filter`() {
        val flowExecution = otSerializer.decodeFromString<FlowExecution.ForJson>(
            this::class.java.getResource("/flows/flow-execution-document-filter.json")!!.readText()
        )
        assertThat(flowExecution).isNotNull
        // subflows
        assertThat(flowExecution.steps[Step.Id("step1")]?.subFlows).isNotEmpty
        assertThat(flowExecution.steps[Step.Id("step1")]?.subFlows["step1_1"]).isNotNull
        // state
        assertThat(flowExecution.state.steps?.entities[FlowExecution.State.Step.Id("step1")]).isNotNull
        assertThat(
            flowExecution.steps[Step.Id("step1")]?.subFlows["step1_1"]?.state?.steps?.entities[FlowExecution.State.Step.Id(
                "filterCondition"
            )]
        ).isNotNull
        assertThat(
            flowExecution.steps[Step.Id("step1")]?.subFlows["step1_1"]?.state?.steps?.entities[FlowExecution.State.Step.Id(
                "filterSetVariables"
            )]
        ).isNotNull
    }

    @Test
    fun `should deserialize aggregate`() {
        val flowExecution = otSerializer.decodeFromString<FlowExecution.ForJson>(
            this::class.java.getResource("/flows/flow-execution-document-aggregate.json")!!.readText()
        )
        assertThat(flowExecution).isNotNull
        // subflows
        assertThat(flowExecution.steps[Step.Id("step1")]?.subFlows).isNotEmpty
        assertThat(flowExecution.steps[Step.Id("step1")]?.subFlows["step1_1"]).isNotNull
        // state
        assertThat(flowExecution.state.steps?.entities[FlowExecution.State.Step.Id("step1")]).isNotNull
        assertThat(
            flowExecution.steps[Step.Id("step1")]?.subFlows["step1_1"]?.state?.steps?.entities[FlowExecution.State.Step.Id(
                "subStep1"
            )]
        ).isNotNull
    }

}