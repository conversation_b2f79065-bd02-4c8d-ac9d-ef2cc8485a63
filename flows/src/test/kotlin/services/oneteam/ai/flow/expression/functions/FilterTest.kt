package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class FilterTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Filter.functionName, Filter.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns empty list when both range and criteria are empty`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER([], [])")
        val result = jsonata.evaluate(null)
        result shouldBe emptyList<Any>()
    }

    @Test
    fun `throws error when range is null`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER(null, [true])")
        val exception = assertThrows<IllegalArgumentException> {
            jsonata.evaluate(null)
        }
        exception.message shouldBe "Expected 'range' to be a List, but got null"
    }

    @Test
    fun `throws error when criteria is null`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER([\"1\", \"2\"], null)")
        val exception = assertThrows<IllegalArgumentException> {
            jsonata.evaluate(null)
        }
        exception.message shouldContain "Expected 'criteria' to be a List"
    }

//    TODO: test fails, minor issue with jsonata behaviour I believe
//    @Test
//    fun `throws error when range is not a list`() {
//        val jsonata = jsonataWithFunctionRegistered("\$FILTER(\"not a list\", [true])")
//        val exception = assertThrows<IllegalArgumentException> {
//            jsonata.evaluate(null)
//        }
//        exception.message shouldContain "Expected 'range' to be a List"
//    }

    @Test
    fun `throws error when criteria contains non-boolean values`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER([\"1\", \"2\"], [true, \"not boolean\"])")
        val exception = assertThrows<IllegalArgumentException> {
            jsonata.evaluate(null)
        }
        exception.message shouldBe "All elements in 'criteria' must be Booleans"
    }

    @Test
    fun `returns empty list when criteria contains all false`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER([\"1\", \"2\"], [false, false])")
        val result = jsonata.evaluate(null)
        result shouldBe emptyList<Any>()
    }

    @Test
    fun `handles range with null values`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER([\"1\", null, \"3\"], [true, true, false])")
        val result = jsonata.evaluate(null)
        result shouldBe listOf("1", null)
    }

    fun getTableAnswer(): JsonArray {
        return JsonArray(
            listOf(
                JsonObject(
                    mapOf(
                        "bihoQERn53" to JsonPrimitive("tofilter"),
                        "columnQuestionId" to JsonPrimitive(1),
                        "_rowId" to JsonPrimitive("06Mo6fCQ490_Y3HHVEWHG"),
                        "_rowIndex" to JsonPrimitive(1)
                    )
                ),
                JsonObject(
                    mapOf(
                        "bihoQERn53" to JsonPrimitive("good value"),
                        "columnQuestionId" to JsonPrimitive(4),
                        "_rowId" to JsonPrimitive("vo8h74_wwrGf2Fg7jmHvB"),
                        "_rowIndex" to JsonPrimitive(2)
                    )
                ),
                JsonObject(
                    mapOf(
                        "bihoQERn53" to JsonPrimitive("good value 2"),
                        "columnQuestionId" to JsonPrimitive(6),
                        "_rowId" to JsonPrimitive("IYC7PUjhfhUJ8kVesGXAb"),
                        "_rowIndex" to JsonPrimitive(3)
                    )
                )
            )
        )
    }

    @Test
    fun `filters tableAnswer with boolean array`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER(${getTableAnswer()}, [true,true,false])")
        val result = jsonata.evaluate(null)

        assert(result is List<*>) { "Result should be of type List<*>" }
        result shouldBe listOf(
            mapOf(
                "bihoQERn53" to "tofilter",
                "columnQuestionId" to 1,
                "_rowId" to "06Mo6fCQ490_Y3HHVEWHG",
                "_rowIndex" to 1
            ),
            mapOf(
                "bihoQERn53" to "good value",
                "columnQuestionId" to 4,
                "_rowId" to "vo8h74_wwrGf2Fg7jmHvB",
                "_rowIndex" to 2
            )
        )
    }

    @Test
    fun `filters tableAnswer with LISTALL`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER(${getTableAnswer()}, \$LISTALL(true,true,false))")
        jsonata.registerFunction(ListAll.functionName, ListAll.evaluatorFunction)
        val result = jsonata.evaluate(null)

        assert(result is List<*>) { "Result should be of type List<*>" }
        result shouldBe listOf(
            mapOf(
                "bihoQERn53" to "tofilter",
                "columnQuestionId" to 1,
                "_rowId" to "06Mo6fCQ490_Y3HHVEWHG",
                "_rowIndex" to 1
            ),
            mapOf(
                "bihoQERn53" to "good value",
                "columnQuestionId" to 4,
                "_rowId" to "vo8h74_wwrGf2Fg7jmHvB",
                "_rowIndex" to 2
            )
        )
    }

    @Test
    fun `filters tableAnswer with map function`() {
        val jsonata =
            jsonataWithFunctionRegistered("\$FILTER(${getTableAnswer()}, \$map([10,20,30], function(\$v, \$i){\$v>15}))")
        val result = jsonata.evaluate(null)

        assert(result is List<*>) { "Result should be of type List<*>" }
        result shouldBe listOf(
            mapOf(
                "bihoQERn53" to "good value",
                "columnQuestionId" to 4,
                "_rowId" to "vo8h74_wwrGf2Fg7jmHvB",
                "_rowIndex" to 2
            ),
            mapOf(
                "bihoQERn53" to "good value 2",
                "columnQuestionId" to 6,
                "_rowId" to "IYC7PUjhfhUJ8kVesGXAb",
                "_rowIndex" to 3
            )
        )
    }

    @Test
    fun `filters array of strings with boolean array`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER([\"1\",\"2\",\"3\"], [true,true,false])")
        val result = jsonata.evaluate(null)
        result shouldBe listOf("1", "2")
    }

    @Test
    fun `filters array of strings when boolean array has fewer elements`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER([\"1\",\"2\",\"3\"], [true,true])")
        val result = jsonata.evaluate(null)
        result shouldBe listOf("1", "2")
    }

    @Test
    fun `filters array of strings with extra false in boolean array`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER([\"1\",\"2\"], [true, true, false])")
        val result = jsonata.evaluate(null)
        result shouldBe listOf("1", "2")
    }

    @Test
    fun `filters array of strings with extra true in boolean array`() {
        val jsonata = jsonataWithFunctionRegistered("\$FILTER([\"1\",\"2\"], [true, true, true])")
        val result = jsonata.evaluate(null)
        result shouldBe listOf("1", "2", null)
    }

//    TODO: Fix test cases similar to below using EQUAL, LESS_THAN, GREATER_THAN, etc once formulas support arrays
//    @Test
//    fun `filters numbers greater than a value`() {
//        val jsonata = jsonataWithFunctionRegistered("\$FILTER([1, 2, 3, 4, 5], \$GREATER_THAN([1, 2, 3, 4, 5], 2))")
//        val result = jsonata.evaluate(null)
//        result shouldBe listOf(3, 4, 5)
//    }
//
//    @Test
//    fun `filters strings equal to a value`() {
//        val jsonata =
//            jsonataWithFunctionRegistered("\$FILTER(['apple', 'banana', 'cherry'], '[\"apple\",\"banana\",\"cherry\"]=\"banana\"')")
//        val result = jsonata.evaluate(null)
//        result shouldBe listOf("banana")
//    }
//
//    @Test
//    fun `filters table using number values`() {
//        val table = """
//        [
//          {
//            "bihoQERn53": "tofilter",
//            "colQid": 1,
//            "_rowId": "06Mo6fCQ490_Y3HHVEWHG",
//            "_rowIndex": 1
//          },
//          {
//            "bihoQERn53": "good value",
//            "colQid": 4,
//            "_rowId": "vo8h74_wwrGf2Fg7jmHvB",
//            "_rowIndex": 2
//          },
//          {
//            "bihoQERn53": "good value 2",
//            "colQid": 6,
//            "_rowId": "IYC7PUjhfhUJ8kVesGXAb",
//            "_rowIndex": 3
//          }
//        ]
//    """.trimIndent()
//
//        val jsonata = jsonataWithFunctionRegistered("\$FILTER($table, '[1,4,6]=4')")
//        val result = jsonata.evaluate(null)
//        result.toString() shouldBe "[{bihoQERn53=good value, colQid=4, _rowId=vo8h74_wwrGf2Fg7jmHvB, _rowIndex=2}]"
//
//    }
//
//    @Test
//    fun `filters table using string values`() {
//        val table = """
//        [
//          {
//            "bihoQERn53": "tofilter",
//            "colQid": "apple",
//            "_rowId": "06Mo6fCQ490_Y3HHVEWHG",
//            "_rowIndex": 1
//          },
//          {
//            "bihoQERn53": "good value",
//            "colQid": "banana",
//            "_rowId": "vo8h74_wwrGf2Fg7jmHvB",
//            "_rowIndex": 2
//          },
//          {
//            "bihoQERn53": "good value 2",
//            "colQid": "cherry",
//            "_rowId": "IYC7PUjhfhUJ8kVesGXAb",
//            "_rowIndex": 3
//          }
//        ]
//        """.trimIndent()
//        val jsonata = jsonataWithFunctionRegistered("\$FILTER($table, '[\"apple\",\"banana\",\"cherry\"]=\"banana\"')")
//        val result = jsonata.evaluate(null)
//        result.toString() shouldBe "[{bihoQERn53=good value, colQid=banana, _rowId=vo8h74_wwrGf2Fg7jmHvB, _rowIndex=2}]"
//    }
//
//    @Test
//    fun `filters with AND condition for numbers`() {
//        val jsonata =
//            jsonataWithFunctionRegistered("\$FILTER([1, 2, 3, 4, 5], '\$AND([1, 2, 3, 4, 5]<5, [1, 2, 3, 4, 5]>1)')")
//        val result = jsonata.evaluate(null)
//        result shouldBe listOf(2, 3, 4)
//    }
//
//    @Test
//    fun `filters with OR condition for numbers`() {
//        val jsonata =
//            jsonataWithFunctionRegistered("\$FILTER([1, 2, 3, 4, 5], '\$OR([1, 2, 3, 4, 5]<2, [1, 2, 3, 4, 5]>4)')")
//        val result = jsonata.evaluate(null)
//        result shouldBe listOf(1, 5)
//    }
//
//    @Test
//    fun `returns empty list for no matches`() {
//        val jsonata = jsonataWithFunctionRegistered("\$FILTER([1, 2, 3], '[1, 2, 3]>5')")
//        val result = jsonata.evaluate(null)
//        result shouldBe emptyList<Any>()
//    }
//
//    @Test
//    fun `returns with different arrays of the same size in OR criteria string`() {
//        val jsonata =
//            jsonataWithFunctionRegistered("\$FILTER([\"invalid\", \"valid\", \"valid\", \"invalid\", \"invalid\"], '\$OR([100, 2, 100, 100, 100]<3, [9, 10, 11, 4, 5]>10)')")
//        val result = jsonata.evaluate(null)
//        result shouldBe listOf("valid", "valid")
//    }
//
//    @Test
//    fun `returns with different arrays of the same size in AND criteria string`() {
//        val jsonata =
//            jsonataWithFunctionRegistered("\$FILTER([\"invalid\", \"valid\", \"valid\", \"invalid\", \"invalid\"], '\$AND([100, 2, 2, 100, 100]<3, [9, 11, 11, 11, 5]>10)')")
//        val result = jsonata.evaluate(null)
//        result shouldBe listOf("valid", "valid")
//    }
//
//    @Test
//    fun `returns with spaces in strings`() {
//        val jsonata =
//            jsonataWithFunctionRegistered("\$FILTER(['apple', 'ban ana', 'cherry'], '[\"apple\",\"ban ana\",\"cherry\"]=\"ban ana\"')")
//        val result = jsonata.evaluate(null)
//        result shouldBe listOf("ban ana")
//    }

//    TODO: Handle brackets appearing inside of criteria string items
//    @Test
//    fun `handles brackets inside of criteria array strings`() {
//        val jsonata = jsonataWithFunctionRegistered("\$FILTER(['apple', 'banana][', 'cherry'], '[\"apple\",\"banana][\",\"cherry\"]=\"banana][\"')")
//        val result = jsonata.evaluate(null)
//        result shouldBe listOf("banana][")
//}

//    TODO: Implement graceful handling of invalid criteria
//    @Test
//    fun `handles invalid criteria gracefully`() {
//        val jsonata = jsonataWithFunctionRegistered("\$FILTER([1, 2, 3], 'invalid')")
//        val result = jsonata.evaluate(null)
//        result shouldBe "#REF!"
//    }
}