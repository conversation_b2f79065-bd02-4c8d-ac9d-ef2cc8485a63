package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class AddMonthsTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(AddMonths.functionName, AddMonths.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns date after adding months`() {
        val expression = "\$ADDMONTHS(\"2021-01-01\", 1)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2021-02-01"
    }

    @Test
    fun `returns date after minus months`() {
        val expression = "\$ADDMONTHS(\"2021-01-01\", -1)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2020-12-01"
    }

    @Test
    fun `returns date after adding heaps of months`() {
        val expression = "\$ADDMONTHS(\"2021-01-01\", 24)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2023-01-01"
    }
}