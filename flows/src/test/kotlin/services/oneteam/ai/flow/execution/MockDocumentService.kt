package services.oneteam.ai.flow.execution

import com.google.gson.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.collection.form.FormAlertRequestSyncServerBody
import services.oneteam.ai.shared.domains.collection.form.SetAnswerParams
import services.oneteam.ai.shared.domains.workspace.CollaborationDocument
import services.oneteam.ai.shared.domains.workspace.document.DocumentId
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.domains.workspace.document.ValidationResult
import java.lang.reflect.Type
import java.time.Instant
import kotlin.reflect.KClass
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

class MockDocumentService : IDocumentService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val gson: Gson = GsonBuilder()
        .registerTypeAdapter(Instant::class.java, InstantSerializer())
        .registerTypeAdapter(Instant::class.java, InstantDeserializer())
        .create()

    val documents = mutableMapOf<String, MutableList<Any>>()

    @OptIn(ExperimentalUuidApi::class)
    override suspend fun <T : CollaborationDocument> create(
        document: T,
        type: KClass<T>,
        timeoutMillis: Long?
    ): String {
        val id = Uuid.random().toString()
        documents[id] = mutableListOf(document)
        return id
    }

    override suspend fun validate(document: String): ValidationResult {
        return ValidationResult()
    }

    override suspend fun answerQuestion(
        documentId: String,
        cookie: String?,
        answers: List<SetAnswerParams>
    ): String {
        if (documents[documentId] == null) {
            throw Exception("Document not found")
        }
        documents[documentId]?.add(answers)
        return """{"documentId": "$documentId", "message": "Success"}"""
    }

    override suspend fun alertAnnotation(
        annotationDocumentId: DocumentId,
        cookie: String?,
        answers: List<FormAlertRequestSyncServerBody>
    ): String {
        if (documents[annotationDocumentId] == null) {
            throw Exception("Document not found")
        }
        documents[annotationDocumentId]?.add(answers)
        return """{"documentId": "$annotationDocumentId", "message": "Success"}"""
    }

    override suspend fun <T : CollaborationDocument> show(
        id: String,
        cookie: String?,
        type: KClass<T>,
        _ignoreUnknownKeys: Boolean
    ): T {
        logger.debug(gson.toJson(documents[id]))
        return documents[id]?.get(0) as T
    }

    override suspend fun <T : CollaborationDocument> update(
        documentId: String,
        cookie: String?,
        content: T,
        path: String,
        type: KClass<T>
    ): String {
        if (documents[documentId] == null) {
            throw Exception("Document not found")
        }
        documents[documentId]?.add(content)
        return """{"documentId": "$documentId", "message": "Success"}"""
    }

    override suspend fun <T : Any> upsertAtPosition(
        documentId: String,
        cookie: String?,
        position: String,
        content: T,
        type: KClass<T>,
        updateDelegate: (T) -> Unit
    ) {
        documents[documentId]?.add(
            mapOf(
                "action" to "upsert",
                "position" to position,
                "content" to content
            )
        )
    }

    override fun toString(): String {
        return gson.toJson(documents)
    }

}

class InstantDeserializer : JsonDeserializer<Instant> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Instant {
        return Instant.parse(json.getAsString());
    }
}

class InstantSerializer : JsonSerializer<Instant> {
    override fun serialize(src: Instant, typeOfSrc: Type, context: JsonSerializationContext): JsonElement {
        return context.serialize(src.toString())
    }
}

