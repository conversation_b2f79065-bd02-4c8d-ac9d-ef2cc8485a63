package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import io.kotest.assertions.throwables.shouldThrow
import org.junit.jupiter.api.Test

class FindTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Find.functionName, Find.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns correct index without start_num`() {
        val expression = "\$FIND('a', 'cat')"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(2)
    }

    @Test
    fun `returns correct index with start_num`() {
        val expression = "\$FIND('a', 'catapult', 4)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(4)
    }

    @Test
    fun `throws error when substring not found`() {
        val expression = "\$FIND('x', 'cat')"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(null)
        }
    }
}
