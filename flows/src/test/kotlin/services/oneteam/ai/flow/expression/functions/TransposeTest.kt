package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class TransposeTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Transpose.functionName, Transpose.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `transposes 1D array`() {
        val expression = "\$TRANSPOSE([1, 2, 3, 4, 5])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(listOf(listOf(1), listOf(2), listOf(3), listOf(4), listOf(5)))
    }

    @Test
    fun `transposes 2D array with single row`() {
        val expression = "\$TRANSPOSE([[1, 2, 3, 4, 5]])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(listOf(listOf(1), listOf(2), listOf(3), listOf(4), listOf(5)))
    }

    @Test
    fun `transposes 2D array with single column`() {
        val expression = "\$TRANSPOSE([[1], [2], [3], [4], [5]])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(listOf(listOf(1, 2, 3, 4, 5)))
    }

    @Test
    fun `transposes 2D array with multiple columns`() {
        val expression = "\$TRANSPOSE([[1, 2, 3], [4, 5, 6]])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(listOf(listOf(1, 4), listOf(2, 5), listOf(3, 6)))
    }

    @Test
    fun `transposes 2D array with multiple rows`() {
        val expression = "\$TRANSPOSE([[1, 4], [2, 5], [3, 6]])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Any>())
        result.shouldBe(listOf(listOf(1, 2, 3), listOf(4, 5, 6)))
    }
}