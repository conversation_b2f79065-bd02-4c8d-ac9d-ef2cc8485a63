package services.oneteam.ai.flow.expression.functions

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class FuzzySumTest {

    private val fuzzySum = FuzzySum()

    @Test
    fun `match should return true for all numbers`() {
        fuzzySum.match(listOf(1, 2, 3)) shouldBe true
    }

    @Test
    fun `match should return true for numbers and numeric strings`() {
        fuzzySum.match(listOf(1, "2", 3.5)) shouldBe true
    }

    @Test
    fun `match should ignore non-numeric inputs`() {
        fuzzySum.match(listOf(1, "abc", 3)) shouldBe true
    }

    @Test
    fun `sum should return correct result for all numbers`() {
        fuzzySum.sum(listOf(1, 2, 3)) shouldBe BigDecimal(6)
    }

    @Test
    fun `sum should return correct result for numbers and numeric strings`() {
        fuzzySum.sum(listOf(1, "2", 3.5)) shouldBe BigDecimal(6.5)
    }

    @Test
    fun `sum should ignore non-numeric inputs`() {
        fuzzySum.sum(listOf(1, "abc", 3)) shouldBe BigDecimal(4)
    }
}