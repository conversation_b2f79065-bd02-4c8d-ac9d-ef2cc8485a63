package services.oneteam.ai.flow.support.pairwise

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PairwiseConcatTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    data class Scenario(
        val name: String, val values: List<PairwiseValue>, val expected: PairwiseValue
    )

    fun provider(): List<Scenario> {
        return listOf(
            Scenario(
                "case 0: [[A, B], [C, D]] - [[E, F], [G, H]] = [[AE, BF], [CG, DH]]",
                listOf(
                    PairwiseValue.of(listOf(listOf("A", "B"), listOf("C", "D"))),
                    PairwiseValue.of(listOf(listOf("E", "F"), listOf("G", "H"))),
                ),
                PairwiseValue.of(listOf(listOf("AE", "BF"), listOf("CG", "DH"))),
            ),
            Scenario(
                "case 1: [[A], [B]] + C = [[AC], [BC]]",
                listOf(
                    PairwiseValue.of(listOf(listOf("A"), listOf("B"))),
                    PairwiseValue.of("C"),
                ),
                PairwiseValue.of(listOf(listOf("AC"), listOf("BC"))),
            ),
        )
    }


    @ParameterizedTest
    @MethodSource("provider")
    fun `scenario `(scenario: Scenario) {

        logger.debug("Scenario: {}", scenario.name)
        logger.debug("Values: {}", scenario.values)
        logger.debug("Expected: {}", scenario.expected)
        Pairwise(PairwiseOperation.CONCATENATE).pairwise(scenario.values).let { it1 ->
            logger.debug("Pairwise result: {}", it1)
            logger.debug("{} = {}", scenario.values.joinToString(" + ") { it.value.toString() }, it1.value)
            assertThat(it1).isEqualTo(scenario.expected)
        }
    }

}