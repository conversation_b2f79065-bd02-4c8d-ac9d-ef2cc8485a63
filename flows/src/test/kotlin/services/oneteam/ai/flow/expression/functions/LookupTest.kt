package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import io.kotest.assertions.throwables.shouldThrow
import org.junit.jupiter.api.Test

class LookupTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Lookup.functionName, Lookup.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns exact match`() {
        val expression = "\$LOOKUP(5, [1,3,5,7], [10,30,50,70])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(50)
    }

    @Test
    fun `returns approximate match`() {
        val expression = "\$LOOKUP(4, [1,3,5,7], [10,30,50,70])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(30)
    }

    @Test
    fun `returns approximate match with string`() {
        val expression = "\$LOOKUP(\"4\", [1,3,5,7], [10,30,50,70])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(30)
    }

    @Test
    fun `shouldn't return approximate match because values are strings`() {
        val expression = "\$LOOKUP(\"hi\", [\"hii\",3,5,7], [10,30,50,70])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("#N/A")
    }

    @Test
    fun `should match with strings`() {
        val expression = "\$LOOKUP(\"hi\", [\"hi\",3,5,7], [10,30,50,70])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(10)
    }

    @Test
    fun `throws error when no match`() {
        val expression = "\$LOOKUP(0, [1,3,5,7])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(null)
        }
    }
}
