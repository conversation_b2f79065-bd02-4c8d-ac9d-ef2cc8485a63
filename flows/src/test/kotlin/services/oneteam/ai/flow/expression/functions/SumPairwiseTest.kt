package services.oneteam.ai.flow.expression.functions

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class SumPairwiseTest {

    private val sumPairwise = SumPairwise()

    @Test
    fun `match should always return true`() {
        sumPairwise.match(listOf(listOf(1, 2, 3), listOf(4, 5, 6))) shouldBe true
    }

    @Test
    fun `sum should return pairwise sum of two lists`() {
        val result = sumPairwise.sum(listOf(listOf(1, 2, 3), listOf(4, 5, 6)))
        result shouldBe listOf(BigDecimal(5), BigDecimal(7), BigDecimal(9))
    }

    @Test
    fun `sum should handle lists of different lengths`() {
        val result = sumPairwise.sum(listOf(listOf(1, 2), listOf(3, 4, 5)))
        result shouldBe listOf(BigDecimal(4), BigDecimal(6), BigDecimal(5))
    }

    @Test
    fun `sum should handle nested lists`() {
        val result = sumPairwise.sum(
            listOf(
                listOf(listOf(1, 2), listOf(3, 4)),
                listOf(listOf(5, 6), listOf(7, 8))
            )
        )
        result shouldBe listOf(listOf(BigDecimal(6), BigDecimal(8)), listOf(BigDecimal(10), BigDecimal(12)))
    }

    @Test
    fun `sum should handle single list input`() {
        val result = sumPairwise.sum(listOf(listOf(1, 2, 3)))
        result shouldBe listOf(1, 2, 3)
    }

    @Test
    fun `min should handle different dimension lists`() {
        /*
        => [[1, 2], [2, 1]] + [-3, 4, -5]
        => [[1, 2], [2, 1]] + [[-3, 4, -5], [-3, 4, -5]]
        => [[1, 2] + [-3, 4, -5], [2, 1] + [-3, 4, -5]],
        => [[1, 2, 0] + [-3, 4, -5], [2, 1, 0] + [-3, 4, -5]],
        => [[-2, 6, -5], [-1, 5, -5]],
         */
        val result = sumPairwise.sum(
            listOf(
                listOf(
                    listOf(1, 2),
                    listOf(2, 1)
                ),
                listOf(-3, 4, -5)
            )
        )
        result shouldBe
                listOf(
                    listOf(BigDecimal(-2), BigDecimal(6), BigDecimal(-5)),
                    listOf(BigDecimal(-1), BigDecimal(5), BigDecimal(-5))
                )
    }

}