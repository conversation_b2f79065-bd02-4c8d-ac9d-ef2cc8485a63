package services.oneteam.ai.flow.expression

import io.kotest.common.runBlocking
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Timeout
import java.util.concurrent.TimeUnit

class JsonataExpressionEvaluatorTest {

    @Test
    fun `should evaluate context expression`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "payload"
        val context = mapOf("payload" to "Hello World")
        val result = jsonataExpressionEvaluator.evaluate(expression, context)
        assertEquals("Hello World", result)
    }

    @Test
    fun `should evaluate arithmetic expression`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "1+1"
        val context = mapOf<Any, Any>()
        val result = jsonataExpressionEvaluator.evaluate(expression, context)
        assertEquals(2, result)
    }

    @Test
    fun `string test`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "\"partA\""
        val context = mapOf<Any, Any>()
        val result = jsonataExpressionEvaluator.evaluate(expression, context)
        assertEquals("partA", result)
    }

    @Test
    fun `should evaluate arithmetic expression using variables`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "form.id + 1"
        val context = mapOf("form" to mapOf("id" to 400))
        val result = jsonataExpressionEvaluator.evaluate(expression, context)
        assertEquals(401, result)
    }

    @Test
    fun `should evaluate Yes to boolean true`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "Yes"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(true, result)
    }

    @Test
    fun `should evaluate TRUE to boolean true`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "TRUE"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(true, result)
    }

    @Test
    fun `should evaluate true to boolean true`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "true"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(true, result)
    }

    @Test
    fun `should evaluate No to boolean false`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "No"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(false, result)
    }

    @Test
    fun `should evaluate FALSE to boolean false`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "FALSE"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(false, result)
    }

    @Test
    fun `should evaluate false to boolean false`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "false"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(false, result)
    }

    @Test
    // this timeout means if the test is failing then it will fail quickly
    @Timeout(
        value = 10,
        unit = TimeUnit.SECONDS,
        threadMode = Timeout.ThreadMode.SEPARATE_THREAD
    )
    fun `should time out`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator(timeoutMs = 1000)
        // https://docs.jsonata.org/programming#recursive-functions
        val expression = """
            (
              ${'$'}func := function(${'$'}x) {
                (${'$'}x < 0) ?
                  ${'$'}func(${'$'}x) /* This will cause an infinite loop for negative numbers */
                : (${'$'}x = 0) ? "Zero" : "Positive"

              };
              ${'$'}result := ${'$'}map([1, -1, 0, 2], ${'$'}func)
            )
        """.trimIndent()

        assertThatThrownBy {
            runBlocking {
                jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
            }
        }.hasMessageContaining("JSonataException Expression evaluation timeout: Check for infinite loop")

    }

    @Test
    // this timeout means if the test is failing then it will fail quickly
    @Timeout(
        value = 10,
        unit = TimeUnit.SECONDS,
        threadMode = Timeout.ThreadMode.SEPARATE_THREAD
    )
    fun `should hit max depth`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator(timeoutMs = 2000, maxRecursionDepth = 5)
        // https://docs.jsonata.org/programming#recursive-functions
        val expression = """
            (
                  ${'$'}factorial := function(${'$'}x) {
                    ${'$'}x <= 1 ? 1 : ${'$'}x * ${'$'}factorial(${'$'}x-1)
                  };
                  ${'$'}factorial(1700000000000)
                )
        """.trimIndent()

        assertThatThrownBy {
            runBlocking {
                jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
            }
        }.hasMessageContaining("JSonataException Stack overflow error: Check for non-terminating recursive function.  Consider rewriting as tail-recursive.")

    }

}