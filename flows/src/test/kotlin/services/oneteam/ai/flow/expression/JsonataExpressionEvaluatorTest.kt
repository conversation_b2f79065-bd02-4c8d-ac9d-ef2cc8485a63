package services.oneteam.ai.flow.expression

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class JsonataExpressionEvaluatorTest {

    @Test
    fun `should evaluate context expression`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "payload"
        val context = mapOf("payload" to "Hello World")
        val result = jsonataExpressionEvaluator.evaluate(expression, context)
        assertEquals("Hello World", result)
    }

    @Test
    fun `should evaluate arithmetic expression`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "1+1"
        val context = mapOf<Any, Any>()
        val result = jsonataExpressionEvaluator.evaluate(expression, context)
        assertEquals(2, result)
    }

    @Test
    fun `string test`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "\"partA\""
        val context = mapOf<Any, Any>()
        val result = jsonataExpressionEvaluator.evaluate(expression, context)
        assertEquals("partA", result)
    }

    @Test
    fun `should evaluate arithmetic expression using variables`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "form.id + 1"
        val context = mapOf("form" to mapOf("id" to 400))
        val result = jsonataExpressionEvaluator.evaluate(expression, context)
        assertEquals(401, result)
    }

    @Test
    fun `should evaluate Yes to boolean true`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "Yes"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(true, result)
    }

    @Test
    fun `should evaluate TRUE to boolean true`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "TRUE"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(true, result)
    }

    @Test
    fun `should evaluate true to boolean true`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "true"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(true, result)
    }

    @Test
    fun `should evaluate No to boolean false`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "No"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(false, result)
    }

    @Test
    fun `should evaluate FALSE to boolean false`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "FALSE"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(false, result)
    }

    @Test
    fun `should evaluate false to boolean false`() = runTest {
        val jsonataExpressionEvaluator = JsonataExpressionEvaluator()
        val expression = "false"
        val result = jsonataExpressionEvaluator.evaluate(expression, mapOf<Any, Any>())
        assertEquals(false, result)
    }

}