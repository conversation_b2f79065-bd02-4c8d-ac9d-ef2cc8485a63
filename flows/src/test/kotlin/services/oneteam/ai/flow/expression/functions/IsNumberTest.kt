package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class IsNumberTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(IsNumber.functionName, IsNumber.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should check if a value is a number`() {
        val expression = "\$ISNUMBER(1.23456789)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe true
    }

    @Test
    fun `should check if a value is not a number`() {
        val expression = "\$ISNUMBER(\"1.23456789\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe false
    }
}