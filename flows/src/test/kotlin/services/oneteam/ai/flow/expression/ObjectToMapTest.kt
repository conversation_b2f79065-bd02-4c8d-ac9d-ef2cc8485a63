package services.oneteam.ai.flow.expression

import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion

class ObjectToMapTest {

    @Test
    fun `should convert to map`() {
        val context = FlowContext.GlobalVariables(
            Workspace.Id(1),
            WorkspaceVersion.Id(1),
            1,
            FlowConfiguration.Id("flow-id"),
            FlowConfiguration.Name("step-id")
        )
        val map = ObjectToMap.convert(context)
        // expect {flowConfigurationId={value=flow-id}, flowConfigurationName={value=step-id}, workspaceId={value=1}}
        println(map)
    }

    @Test
    fun `should convert to map with nulls`() {
        val context = TestContextA(1, TestContextB("2", TestContextB("3", null)))
        val map = ObjectToMap.convert(context)
        // expect {flowConfigurationId={value=flow-id}, flowConfigurationName={value=step-id}, workspaceId={value=1}}
        println(map)
    }

    data class TestContextA(val x: Int, val b: TestContextB)

    data class TestContextB(val y: String, val c: TestContextB?)
}