package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonPrimitive
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class VariableTest {

    fun specProvider() = listOf(
        // some things don't change
        Spec(VariableInstance.Variable(JsonPrimitive("value"), VariableDataType.TEXT, "test"), JsonPrimitive("value")),
        Spec(
            VariableInstance.Variable(JsonPrimitive("2025-12-31"), VariableDataType.DATE, "test"),
            JsonPrimitive("2025-12-31")
        ),

        // primitives should be transformed when needed
        Spec(VariableInstance.Variable(JsonPrimitive("5"), VariableDataType.NUMBER, "test"), JsonPrimitive(5)),
        Spec(VariableInstance.Variable(JsonPrimitive("true"), VariableDataType.BOOLEAN, "test"), JsonPrimitive(true)),

        // when primitives already have the correct type, they should not be transformed
        Spec(VariableInstance.Variable(JsonPrimitive(5), VariableDataType.NUMBER, "test"), JsonPrimitive(5)),
        Spec(VariableInstance.Variable(JsonPrimitive(true), VariableDataType.BOOLEAN, "test"), JsonPrimitive(true)),

        // complex types should return unchanged, and we expect the to contain elements with the correct types
        Spec(
            VariableInstance.Variable(JsonArray(listOf(JsonPrimitive("2025-12-31"))), VariableDataType.JSON, "test"),
            JsonArray(listOf(JsonPrimitive("2025-12-31")))
        ),
    )

    data class Spec(
        val variable: VariableInstance.Variable, val expected: Any
    )

    @ParameterizedTest
    @MethodSource("specProvider")
    fun `variables should convert to relevant type`(spec: Spec) {
        assertThat(spec.variable.toProperType()).isEqualTo(spec.expected)
    }
}