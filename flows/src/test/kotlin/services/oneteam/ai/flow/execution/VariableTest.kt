package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonPrimitive
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import services.oneteam.ai.flow.execution.VariableDefinition.Variable

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class VariableTest {

    fun specProvider() = listOf(
        // some things don't change
        Spec(Variable(JsonPrimitive("value"), "text", "test"), JsonPrimitive("value")),
        Spec(Variable(JsonPrimitive("2025-12-31"), "date", "test"), JsonPrimitive("2025-12-31")),

        // primitives should be transformed when needed
        Spec(Variable(JsonPrimitive("5"), "number", "test"), JsonPrimitive(5)),
        Spec(Variable(JsonPrimitive("true"), "boolean", "test"), JsonPrimitive(true)),

        // when primitives already have the correct type, they should not be transformed
        Spec(Variable(JsonPrimitive(5), "number", "test"), JsonPrimitive(5)),
        Spec(Variable(JsonPrimitive(true), "boolean", "test"), JsonPrimitive(true)),

        // complex types should return unchanged, and we expect the to contain elements with the correct types
        Spec(Variable(JsonArray(listOf(JsonPrimitive("2025-12-31"))), "json", "test"), JsonArray(listOf(JsonPrimitive("2025-12-31")))),
    )

    data class Spec(
        val variable: Variable,
        val expected: Any
    )

    @ParameterizedTest
    @MethodSource("specProvider")
    fun `variables should convert to relevant type`(spec: Spec) {
        assertThat(spec.variable.toProperType()).isEqualTo(spec.expected)
    }

}