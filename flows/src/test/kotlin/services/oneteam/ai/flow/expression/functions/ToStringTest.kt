package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ToStringTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(ToString.functionName, ToString.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns product of two integers`() {
        val expression = "\$TOSTRING(2, 3)";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("[2,3]")
    }

    @Test
    fun `returns string from list of integers`() {
        val expression = "\$TOSTRING([2, 3])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("[2,3]")
    }

    @Test
    fun `when using built-in string returns product of two integers`() {
        val expression = "\$string([2, 3])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("[2,3]")
    }

    @Test
    fun `should convert bigdecimal list`() {
        val input = listOf(1.0, 2.0, 3.5)
        val expected = "[1,2,3.5]"
        val result = ToString.evaluatorFunction.call(null, input)
        assertEquals(expected, result)
    }
}