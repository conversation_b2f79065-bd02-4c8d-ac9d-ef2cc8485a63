package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class ListAllTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(ListAll.functionName, ListAll.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should list all elements of an array`() {
        val expression = "\$LISTALL(1, 2, 3)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe listOf(1, 2, 3)
    }

    @Test
    fun `should list all elements of an array with a single element`() {
        val expression = "\$LISTALL(1)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe listOf(1)
    }

    @Test
    fun `should list elements of different types`() {
        val expression = "\$LISTALL(1, \"two\", 3.0, true)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe listOf(1, "two", 3.0, true)
    }
}