package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class RepeatValuesTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(RepeatValues.functionName, RepeatValues.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `repeats a string value multiple times`() {
        val expression = "\$REPEATVALUES(\"Hello\", 3)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Number>()) as List<*>
        result.shouldContainExactly(listOf("Hello", "Hello", "Hello"))
    }

    @Test
    fun `repeats an integer value multiple times`() {
        val expression = "\$REPEATVALUES(42, 2)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Number>()) as List<*>
        result.shouldContainExactly(listOf(42, 42))
    }

    @Test
    fun `returns an empty list when noOfTimes is zero`() {
        val expression = "\$REPEATVALUES(\"Test\", 0)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(mapOf<String, Number>()) as List<*>
        result.shouldContainExactly(emptyList<String>())
    }

    @Test
    fun `throws exception for negative noOfTimes`() {
        val expression = "\$REPEATVALUES(\"Oops\", -3)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(mapOf<String, Number>())
        }.message.shouldBe("noOfTimes argument must be non-negative")
    }
}