package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class RightTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Right.functionName, Right.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return the rightmost characters of a string`() {
        val expression = "\$RIGHT('Hello, world!', 6)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "world!"
    }

    @Test
    fun `should return the rightmost characters of a string when the length is greater than the string`() {
        val expression = "\$RIGHT('Hello', 10)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello"
    }

    @Test
    fun `should return the rightmost characters of a string when the length is equal to the string`() {
        val expression = "\$RIGHT('Hello', 5)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello"
    }

    @Test
    fun `should return an empty string when the length is 0`() {
        val expression = "\$RIGHT('Hello', 0)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe ""
    }

    @Test
    fun `should return an empty string when the string is empty`() {
        val expression = "\$RIGHT('', 5)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe ""
    }
}