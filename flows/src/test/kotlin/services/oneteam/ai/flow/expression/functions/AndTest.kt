package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class AndTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(And.functionName, And.evaluatorFunction)
        jsonataExpression.registerFunction(Multiply.functionName, Multiply.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return true with function`() {
        val expression = "\$AND(\$MULTIPLY(2, 3) = 6.0, true)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe true
    }

    @Test
    fun `should return false when the first condition is false`() {
        val expression = "\$AND(false, true)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe false
    }

    @Test
    fun `should return false when the second condition is false`() {
        val expression = "\$AND(true, false)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe false
    }

    @Test
    fun `should return true when both conditions are true`() {
        val expression = "\$AND(true, true)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe true
    }

    @Test
    fun `should return false when both conditions are false`() {
        val expression = "\$AND(false, false)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe false
    }
}