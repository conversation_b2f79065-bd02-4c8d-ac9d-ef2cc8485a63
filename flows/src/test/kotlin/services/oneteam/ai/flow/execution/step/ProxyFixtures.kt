package services.oneteam.ai.flow.execution.step

import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.ProxyService.ProxyEndpointResponse

object ProxyFixtures {

    fun mockForSuccess(response: String): ProxyService {
        val httpResponse = ProxyEndpointResponse(
            response = response,
            status = ProxyService.ProxyEndpointResponseStatus.SUCCESS,
            error = null
        )
        val proxyService = mockk<ProxyService>()
        coEvery { proxyService.call(any()) } returns httpResponse
        mockkObject(ProxyService)
        coEvery { ProxyService.buildInternalTenantUrl(any()) } returns "http://localhost:8080/ai/api/sync/form/1/answer"
        return proxyService

    }
}