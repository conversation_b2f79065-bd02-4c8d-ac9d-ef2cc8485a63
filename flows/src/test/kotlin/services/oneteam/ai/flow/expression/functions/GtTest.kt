package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GtTest {

    private val fn = Gt

    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(fn.functionName, fn.evaluatorFunction)
        return jsonataExpression
    }

    data class Spec(
        val input: String,
        val expected: Any,
    )

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName('2025-12-01', '2025-12-01')", false),
            Spec("$$functionName('2025-12-02', '2025-12-01')", true),

            Spec("$$functionName(1, 1)", false),
            Spec("$$functionName(3, 2)", true),
            Spec("$$functionName([30, 10], [20, 10])", listOf(true, false)),
            Spec("$$functionName([10, 20], 10)", listOf(false, true)),
            Spec("$$functionName([10, -5], [10])", listOf(false, false)),
            Spec("$$functionName([-10, 5], [10])", listOf(false, true)),
            Spec("$$functionName('a','b')", false),
            Spec("$$functionName('c','b')", true),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        val jsonataExpression = jsonataWithFunctionRegistered(spec.input)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(spec.expected)
    }

}