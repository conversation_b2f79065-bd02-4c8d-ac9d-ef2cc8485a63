package services.oneteam.ai.flow.execution.variables

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.GlobalVariables
import services.oneteam.ai.flow.execution.VariableDefinition
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion

class FileVariableResolverTest {
    val global = GlobalVariables(
        Workspace.Id(1),
        WorkspaceVersion.Id(1),
        1,
        FlowConfiguration.Id("flow-id"),
        FlowConfiguration.Name("flow-name"),
    )

    val event = Event.ForApi(
        Workspace.Id(1),
        StartFlowManuallyFromFormProperties(
            "string",
            null,
            null,
        ),
        Event.Id("1"),
        1
    )

    fun makeFileValue(name: String, path: String? = "/mockPath/$name"): JsonElement {
        return JsonObject(
            mapOf(
                "name" to JsonPrimitive(name),
                "path" to JsonPrimitive(path)
            )
        )
    }

    private val fileValue = buildJsonArray {
        add(makeFileValue("10.docx"))
        add(makeFileValue("20.docx"))
        add(makeFileValue("30.docx"))
    }

    val context = FlowContextWithLocalStep(
        Step.Id("1"),
        FlowContext(
            global,
            mutableMapOf(
                "fileVariable" to Variable(
                    Json.encodeToJsonElement(fileValue),
                    "file",
                    "fileVariable",
                    VariableDefinition.FileVariableProperties(
                        fileOperation = VariableDefinition.FileOperation.SET_FILES
                    )
                )
            ),
            event,
        )
    )

    @Test
    fun `should set file for SET_FILES operation when single object`() = runTest {
        // given
        val newFileValue = """
            ${makeFileValue("10.docx")}
        """.trimIndent()
        val variable = Variable(
            Json.decodeFromString(newFileValue),
            "file",
            "fileVariable",
            VariableDefinition.FileVariableProperties(
                fileOperation = VariableDefinition.FileOperation.SET_FILES
            )
        )

        val resolver = FileVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(makeFileValue("10.docx"))
            },
            result.value
        )
    }

    @Test
    fun `should set file for SET_FILES operation when list of files`() = runTest {
        // given
        val newFileValue = """
            [${makeFileValue("40.docx")}, ${makeFileValue("50.docx")}, ${makeFileValue("60.docx")}]
        """.trimIndent()
        val variable = Variable(
            Json.decodeFromString(newFileValue),
            "file",
            "fileVariable",
            VariableDefinition.FileVariableProperties(
                fileOperation = VariableDefinition.FileOperation.SET_FILES
            )
        )

        val resolver = FileVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(makeFileValue("40.docx"))
                add(makeFileValue("50.docx"))
                add(makeFileValue("60.docx"))
            },
            result.value
        )
    }

    @Test
    fun `should append item to the file for ADD_FILE operation`() = runTest {
        // given
        val newValue = makeFileValue("40.docx")
        val variable = Variable(
            Json.encodeToJsonElement(newValue),
            "file",
            "fileVariable",
            VariableDefinition.FileVariableProperties(
                fileOperation = VariableDefinition.FileOperation.ADD_FILES
            )
        )

        val resolver = FileVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(makeFileValue("10.docx"))
                add(makeFileValue("20.docx"))
                add(makeFileValue("30.docx"))
                add(makeFileValue("40.docx"))
            },
            result.value
        )
    }

    @Test
    fun `should remove item at specific index for REMOVE_ITEM operation`() = runTest {
        // given
        val variable = Variable(
            Json.encodeToJsonElement(""),
            "file",
            "fileVariable",
            VariableDefinition.FileVariableProperties(
                fileOperation = VariableDefinition.FileOperation.REMOVE_FILES,
                itemIndex = JsonPrimitive(2)
            )
        )

        val resolver = FileVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(makeFileValue("10.docx"))
                add(makeFileValue("30.docx"))
            },
            result.value
        )
    }
}