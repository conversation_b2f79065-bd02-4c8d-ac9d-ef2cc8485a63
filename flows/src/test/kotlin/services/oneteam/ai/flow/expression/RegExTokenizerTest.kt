package services.oneteam.ai.flow.expression

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

class RegExTokenizerTest {

    @Test
    fun `should tokenize expression with delimiters`() {
        val tokenizer = RegExTokenizer()
        val expression = "This is a {{test}} string with {{multiple}} delimiters."
        val expectedTokens = listOf("test", "multiple")
        val tokens = tokenizer.tokenize(expression)
        assertEquals(expectedTokens, tokens)
    }

    @Disabled("For later, if we want to be more selective about tokens and optimise the regex")
    @Test
    fun `should not find tokens with non-alphanumeric characters`() {
        val tokenizer = RegExTokenizer()
        val expression = "This is a {{t$ !est}} string {{☺️}} with {{multiple}} delimiters."
        val expectedTokens = listOf("multiple")
        val tokens = tokenizer.tokenize(expression)
        assertEquals(expectedTokens, tokens)
    }

    @Test
    fun `should return empty list when no delimiters`() {
        val tokenizer = RegExTokenizer()
        val expression = "This is a test string with no delimiters."
        val expectedTokens = emptyList<String>()
        val tokens = tokenizer.tokenize(expression)
        assertEquals(expectedTokens, tokens)
    }

    @Test
    fun `should replace token with value`() {
        val tokenizer = RegExTokenizer()
        val expression = "This is a {{test}} string with {{multiple}} {{test}} delimiters."
        val variable = "test"
        val value = "replaced"
        val expectedExpression = "This is a replaced string with {{multiple}} replaced delimiters."
        val result = tokenizer.replace(expression, variable, value)
        assertEquals(expectedExpression, result)
    }

}