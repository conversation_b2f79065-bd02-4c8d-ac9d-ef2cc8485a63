package services.oneteam.ai.flow.expression.conditional

import io.kotest.matchers.shouldBe
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.expression.functions.Contains
import services.oneteam.ai.flow.expression.functions.DoesNotContain
import services.oneteam.ai.flow.expression.functions.IsEmpty

class ConditionSerializationTest {

    fun getJson(): Json {
        return Json(builderAction = {
            prettyPrint = true
            encodeDefaults = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }

    @Test
    fun `test deserialization with simple condition`() {
        val jsonCondition = """
            {
              "lhs": 5000,
              "operator": ">",
              "rhs": 0
            }
        """.trimIndent()

        val condition = getJson().decodeFromString(Condition.serializer(), jsonCondition)

        val expectedOutput = "5000 > 0"
        Assertions.assertEquals(expectedOutput, condition.toExpression())
    }

    @Test
    fun `test deserialization with simple condition with string`() {
        val jsonCondition = """
            {
              "lhs": "\"hello world\"",
              "operator": "contains",
              "rhs": "\"hello\""
            }
        """.trimIndent()

        val condition = getJson().decodeFromString(Condition.serializer(), jsonCondition)

        val expectedOutput = "$${Contains.functionName}(\"hello world\", \"hello\")"
        Assertions.assertEquals(expectedOutput, condition.toExpression())
    }

    @Test
    fun `test deserialization with simple condition with date`() {
        val jsonCondition = """
            {
              "lhs": "'2025-01-01'",
              "operator": ">",
              "rhs": "'2020-01-01'"
            }
        """.trimIndent()

        val condition = getJson().decodeFromString(Condition.serializer(), jsonCondition)

        val expectedOutput = "'2025-01-01' > '2020-01-01'"
        Assertions.assertEquals(expectedOutput, condition.toExpression())
    }

    @Test
    fun `test deserialization with simple condition with boolean`() {
        val jsonCondition = """
            {
              "lhs": false,
              "operator": "=",
              "rhs": false
            }
        """.trimIndent()

        val condition = getJson().decodeFromString(Condition.serializer(), jsonCondition)

        val expectedOutput = "false = false"
        Assertions.assertEquals(expectedOutput, condition.toExpression())
    }

    @Test
    fun `test deserialization with isEmpty`() {
        val jsonCondition = """
            {
              "AND": [
                {
                  "lhs": "\"\"",
                  "operator": "isEmpty"
                },
                {
                  "lhs": "\"not empty string\"",
                  "operator": "isEmpty"
                }
              ]
            }
        """.trimIndent()

        val condition = getJson().decodeFromString(Condition.serializer(), jsonCondition)

        val expectedOutput = "($${IsEmpty.functionName}(\"\") and $${IsEmpty.functionName}(\"not empty string\"))"
        Assertions.assertEquals(expectedOutput, condition.toExpression())
    }

    @Test
    fun `test deserialization with variable to be replaced`() {
        val jsonCondition = """
            {
              "lhs": "{{variable}}",
              "operator": "=",
              "rhs": "\"hello\""
            }
        """.trimIndent()

        val condition = getJson().decodeFromString(Condition.serializer(), jsonCondition)

        val expectedOutput = "{{variable}} = \"hello\""
        Assertions.assertEquals(expectedOutput, condition.toExpression())
    }


    @Test
    fun `test deserialization with complex condition`() {
        val jsonCondition = """
            {
              "AND": [
                {
                  "lhs": 5000,
                  "operator": ">",
                  "rhs": 0
                },
                {
                  "lhs": "\"\"",
                  "operator": "isEmpty"
                },
                {
                  "OR": [
                    {
                      "lhs": "\"hello world\"",
                      "operator": "contains",
                      "rhs": "\"hello\""
                    },
                    {
                      "lhs": "'2025-01-01'",
                      "operator": ">",
                      "rhs": "'2020-01-01'"
                    },
                    {
                      "AND": [
                        {
                          "lhs": false,
                          "operator": "=",
                          "rhs": false
                        },
                        {
                          "lhs": true,
                          "operator": "!=",
                          "rhs": false
                        }
                      ]
                    }
                  ]
                },
                {
                  "AND": [
                    {
                      "lhs": 5000,
                      "operator": "=",
                      "rhs": 5000
                    },
                    {
                      "lhs": "\"hello world\"",
                      "operator": "doesNotContain",
                      "rhs": "\"goodbye\""
                    }
                  ]
                }
              ]
            }
        """.trimIndent()

        val condition = getJson().decodeFromString(Condition.serializer(), jsonCondition)

        val expectedOutput =
            "(5000 > 0 and $${IsEmpty.functionName}(\"\") and ($${Contains.functionName}(\"hello world\", \"hello\") or '2025-01-01' > '2020-01-01' or (false = false and true != false)) and (5000 = 5000 and $${DoesNotContain.functionName}(\"hello world\", \"goodbye\")))"
        Assertions.assertEquals(expectedOutput, condition.toExpression())
    }

    @Test
    fun `test deserialization serialization with complex condition`() {

        val conditionBefore = Condition(
            left = Expression(""),
            operator = LogicalOperator.AND,
            right = listOf(
                Condition(
                    left = Expression("5000"),
                    operator = Operator.GREATER_THAN,
                    right = listOf(Expression("0"))
                ),
                Condition(
                    left = Expression("\"\""),
                    operator = Operator.IS_EMPTY
                ),
                Condition(
                    left = Expression("\"\""),
                    operator = LogicalOperator.OR,
                    right = listOf(
                        Condition(
                            left = Expression("\"hello world\""),
                            operator = Operator.CONTAINS,
                            right = listOf(Expression("\"hello\""))
                        ),
                        Condition(
                            left = Expression("\"2025-01-01\""),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("\"2020-01-01\""))
                        ),
                        Condition(
                            left = Expression(""),
                            operator = LogicalOperator.AND,
                            right = listOf(
                                Condition(
                                    left = Expression("false"),
                                    operator = Operator.EQUAL,
                                    right = listOf(Expression("false"))
                                ),
                                Condition(
                                    left = Expression("true"),
                                    operator = Operator.NOT_EQUAL,
                                    right = listOf(Expression("false"))
                                )
                            )
                        )
                    )
                ),
                Condition(
                    left = Expression(""),
                    operator = LogicalOperator.AND,
                    right = listOf(
                        Condition(
                            left = Expression("5000"),
                            operator = Operator.EQUAL,
                            right = listOf(Expression("5000"))
                        ),
                        Condition(
                            left = Expression("\"hello world\""),
                            operator = Operator.DOES_NOT_CONTAIN,
                            right = listOf(Expression("\"goodbye\""))
                        )
                    )
                )
            )
        )

        val json = getJson().encodeToString(Condition.serializer(), conditionBefore)
        val conditionAfter = getJson().decodeFromString(Condition.serializer(), json)

        conditionAfter.toExpression().shouldBe(conditionBefore.toExpression())
    }
}