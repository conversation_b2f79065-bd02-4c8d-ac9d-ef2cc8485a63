package services.oneteam.ai.flow.expression.functions

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import java.math.BigDecimal

class MinPairwiseTest {

    private val minPairwise = Pairwise(PairwiseOperation.MIN, BigDecimal.ZERO)

    @Test
    fun `match should always return true`() {
        minPairwise.match(listOf(listOf(1, 2, 3), listOf(4, 5, 6))) shouldBe true
    }

    @Test
    fun `min should handle single number input`() {
        val result = minPairwise.perform(listOf(1))
        result shouldBe 1
    }

    @Test
    fun `min should handle list of number inputs`() {
        val result = minPairwise.perform(listOf(1, 2, 3))
        result shouldBe BigDecimal(1)
    }

    @Test
    fun `min should handle single list input`() {
        val result = minPairwise.perform(listOf(listOf(1, 2, 3)))
        result shouldBe listOf(1, 2, 3)
    }

    @Test
    fun `min should return pairwise min of two lists`() {
        val result = minPairwise.perform(listOf(listOf(1, 2, 3), listOf(4, -5, 6)))
        result shouldBe listOf(BigDecimal(1), BigDecimal(-5), BigDecimal(3))
    }

    @Test
    fun `min should handle lists of different lengths`() {
        val result = minPairwise.perform(listOf(listOf(1, 2), listOf(3, 4, 5)))
        result shouldBe listOf(BigDecimal(1), BigDecimal(2), BigDecimal(0))
    }

    @Test
    fun `min should handle nested lists`() {
        val result = minPairwise.perform(
            listOf(
                listOf(listOf(1, 2), listOf(7, 4)),
                listOf(listOf(5, 6), listOf(3, 8))
            )
        )
        result shouldBe listOf(listOf(BigDecimal(1), BigDecimal(2)), listOf(BigDecimal(3), BigDecimal(4)))
    }

    @Test
    fun `min should handle different dimension lists`() {
        /*
        => [[1, 2], [2, 1]] + [-3, 4, -5]
        => [[1, 2], [2, 1]] + [[-3, 4, -5], [-3, 4, -5]]
        => [[1, 2] + [-3, 4, -5], [2, 1] + [-3, 4, -5]],
        => [[1, 2, 0] + [-3, 4, -5], [2, 1, 0] + [-3, 4, -5]],
        => [[-2, 6, -5], [-1, 5, -5]],
         */
        val result = minPairwise.perform(
            listOf(
                listOf(
                    listOf(1, 2),
                    listOf(2, 1)
                ),
                listOf(-3, 4, -5)
            )
        )
        result shouldBe
                listOf(
                    listOf(BigDecimal(-3), BigDecimal(2), BigDecimal(-5)),
                    listOf(BigDecimal(-3), BigDecimal(1), -BigDecimal(5))
                )
    }
}