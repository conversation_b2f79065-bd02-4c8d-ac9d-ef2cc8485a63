package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class SubstituteTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Substitute.functionName, Substitute.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns substituted string`() {
        val expression = "\$SUBSTITUTE('hello world', 'world', 'universe')"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("hello universe")
    }

    @Test
    fun `returns substituted string with limit`() {
        val expression = "\$SUBSTITUTE('hello world world', 'world', 'universe')"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("hello universe universe")
    }
}