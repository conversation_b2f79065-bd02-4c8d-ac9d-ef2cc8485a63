package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class LenTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Len.functionName, Len.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return the length of a string`() {
        val expression = "\$LEN('Hello, world!')"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 13
    }

    @Test
    fun `should return 0 when the string is empty`() {
        val expression = "\$LEN('')"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 0
    }
}