package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class IsEmptyTest {
    private fun assertIsEmpty(expression: String, expected: Boolean) {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(IsEmpty.functionName, IsEmpty.evaluatorFunction)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(expected)
    }

    @Test
    fun `returns true for null`() {
        assertIsEmpty(expression = "\$IS_EMPTY(null)", expected = true)
    }

    @Test
    fun `returns true for undefined`() {
        assertIsEmpty(expression = "\$IS_EMPTY(undefined)", expected = true)
    }

    @Test
    fun `returns true for empty string`() {
        assertIsEmpty(expression = "\$IS_EMPTY('')", expected = true)
        assertIsEmpty(expression = "\$IS_EMPTY(\"\")", expected = true)
    }

    @Test
    fun `returns false for non-empty string 'Hello'`() {
        assertIsEmpty(expression = "\$IS_EMPTY('Hello')", expected = false)
    }

    @Test
    fun `returns false for non-empty string ' ' (space)`() {
        assertIsEmpty(expression = "\$IS_EMPTY(' ')", expected = false)
    }

    @Test
    fun `returns false for non-empty string '  '`() {
        assertIsEmpty(expression = "\$IS_EMPTY('  ')", expected = false)
    }

    @Test
    fun `return false for number 123`() {
        assertIsEmpty(expression = "\$IS_EMPTY(123)", expected = false)
    }

    @Test
    fun `return false for number 0`() {
        assertIsEmpty(expression = "\$IS_EMPTY(0)", expected = false)
    }

    @Test
    fun `return false for date 2023-10-01`() {
        assertIsEmpty(expression = "\$IS_EMPTY('2023-10-01')", expected = false)
    }

    @Test
    fun `return false for boolean true`() {
        assertIsEmpty(expression = "\$IS_EMPTY(true)", expected = false)
    }

    @Test
    fun `return false for boolean false`() {
        assertIsEmpty(expression = "\$IS_EMPTY(false)", expected = false)
    }

    @Test
    fun `return true for empty object {}`() {
        assertIsEmpty(expression = "\$IS_EMPTY({})", expected = true)
    }

    @Test
    fun `return false for non-empty object`() {
        assertIsEmpty(expression = "\$IS_EMPTY({'key': 'value'})", expected = false)
    }

    @Test
    fun `return true for empty array`() {
        assertIsEmpty(expression = "\$IS_EMPTY([])", expected = true)
    }

    @Test
    fun `return true for array with empty strings`() {
        assertIsEmpty(expression = "\$IS_EMPTY([\"\", \"\"])", expected = true)
    }

    @Test
    fun `return true for nested arrays with empty strings`() {
        assertIsEmpty(expression = "\$IS_EMPTY([[\"\", \"\"],[\"\", \"\"]])", expected = true)
    }

    @Test
    fun `return true for array with empty values`() {
        assertIsEmpty(expression = "\$IS_EMPTY([null, null])", expected = true)
    }

    @Test
    fun `return true for arrays in arrays with empty values`() {
        assertIsEmpty(expression = "\$IS_EMPTY([[\"\"], []])", expected = true)
    }

    @Test
    fun `return false for array with non-empty strings`() {
        assertIsEmpty(expression = "\$IS_EMPTY(['Hello', 'World', ''])", expected = false)
    }

    @Test
    fun `return false for array with non-empty values`() {
        assertIsEmpty(expression = "\$IS_EMPTY([1, 2, 3])", expected = false)
    }

    @Test
    fun `return false for array with mixed values`() {
        assertIsEmpty(expression = "\$IS_EMPTY([null, 1, true])", expected = false)
    }

    @Test
    fun `return false for floating values`() {
        assertIsEmpty(expression = "\$IS_EMPTY(1.5)", expected = false)
    }

    @Test
    fun `return true for deeply nested empty arrays`() {
        assertIsEmpty(expression = "\$IS_EMPTY([[[[]]]])", expected = true)
    }

    @Test
    fun `return true for nested empty arrays`() {
        assertIsEmpty(expression = "\$IS_EMPTY([[],[]])", expected = true)
    }

    @Test
    fun `return true for nested empty arrays and objects`() {
        assertIsEmpty(expression = "\$IS_EMPTY([{}, {}, {}, [{}, {}]])", expected = true)
    }

    @Test
    fun `return false for deeply nested arrays with values`() {
        assertIsEmpty(expression = "\$IS_EMPTY([[[1]]])", expected = false)
    }

    @Test
    fun `return false for nested objects with values`() {
        assertIsEmpty(expression = "\$IS_EMPTY({'key': {'nestedKey': 'value'}})", expected = false)
    }

    @Test
    fun `return false for nested empty objects`() {
        assertIsEmpty(expression = "\$IS_EMPTY({'key': {}})", expected = false)
    }

    @Test
    fun `return false for string with special characters`() {
        assertIsEmpty(expression = "\$IS_EMPTY('!@#$%^&*()')", expected = false)
    }

    @Test
    fun `return false for very large number`() {
        assertIsEmpty(expression = "\$IS_EMPTY(999999999999999999)", expected = false)
    }

    @Test
    fun `return false for very long string`() {
        assertIsEmpty(expression = "\$IS_EMPTY('${"a".repeat(10000)}')", expected = false)
    }
}