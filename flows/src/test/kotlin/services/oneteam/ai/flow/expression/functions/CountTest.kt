package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class CountTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Count.functionName, Count.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns count of elements in array`() {
        val expression = "\$COUNT([1, 2, 3])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(3)
    }

    @Test
    fun `returns count of elements in a string array`() {
        val expression = "\$COUNT(['a', 'b', 'c'])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(3)
    }

    @Test
    fun `returns count of elements in empty array`() {
        val expression = "\$COUNT([])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(0)
    }
}