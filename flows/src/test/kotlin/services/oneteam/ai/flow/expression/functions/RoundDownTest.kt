package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class RoundDownTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(RoundDown.functionName, RoundDown.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should round down a number to a specified number of decimal places`() {
        val expression = "\$ROUNDDOWN(1.23456789, 2)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1.23
    }

    @Test
    fun `should round down a number to a whole number string`() {
        val expression = "\$ROUNDDOWN(\"1.23456789\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `should round down a number to a whole number`() {
        val expression = "\$ROUNDDOWN(1.73456789)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }
}