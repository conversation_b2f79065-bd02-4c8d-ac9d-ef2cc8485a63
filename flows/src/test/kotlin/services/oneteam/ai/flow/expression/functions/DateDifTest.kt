package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class DateDifTest {
    private fun jsonataWithFunction(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(DateDif.functionName, DateDif.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `difference in years`() {
        val expression = "\$DATEDIF('2020-01-01', '2023-01-01', 'Y')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "3"
    }

    @Test
    fun `difference in months`() {
        val expression = "\$DATEDIF('2020-01-15', '2020-04-14', 'M')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "2"
    }

    @Test
    fun `difference in days`() {
        val expression = "\$DATEDIF('2020-01-01', '2020-01-31', 'D')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "30"
    }

    @Test
    fun `difference in MD`() {
        val expression = "\$DATEDIF('2021-01-31', '2021-02-28', 'MD')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "28"
    }

    @Test
    fun `difference in YM`() {
        val expression = "\$DATEDIF('2021-01-31', '2021-02-28', 'YM')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "0"
    }

    @Test
    fun `difference in YD`() {
        val expression = "\$DATEDIF('2020-01-01', '2021-01-01', 'YD')"
        val result = jsonataWithFunction(expression).evaluate(null)
        result shouldBe "0"
    }
}
