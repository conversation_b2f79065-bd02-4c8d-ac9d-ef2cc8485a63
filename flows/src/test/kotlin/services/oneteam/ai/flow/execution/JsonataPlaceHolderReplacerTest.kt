package services.oneteam.ai.flow.execution

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.buildJsonObject
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class JsonataPlaceHolderReplacerTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    val jsonObject = buildJsonObject {
        put("key1", JsonPrimitive("value"))
        put("key2", JsonPrimitive(5))
        put("key3", JsonPrimitive(true))
        put("key4", JsonPrimitive(7.1))
    }

    val jsonArray = buildJsonArray {
        add(JsonPrimitive("a"))
        add(JsonPrimitive(2))
        add(JsonPrimitive("c"))
    }

    val placeHolderReplacer = JsonataPlaceHolderReplacer()
    val placeHolderValues = StaticPlaceholderValueProvider(buildJsonObject {
        put("name", JsonPrimitive("John"))
        put("escapedString", JsonPrimitive("My \'[name]\' 'is' \"Slim Shady\""))
        put("array", jsonArray)
        put("object", jsonObject)
    })

    data class Spec(
        val input: String,
        val expected: String,
        val evaluated: Any
    )

    fun provider(): List<Spec> {
        return listOf(
            Spec(
                input = "\$length({{name}})",
                expected = "\$length(\"John\")",
                evaluated = 4
            ),
            Spec(
                input = "\$count({{array}})",
                expected = "\$count([\"a\",2,\"c\"])",
                evaluated = 3
            ),
            Spec(
                input = "1 + 1",
                expected = "1 + 1",
                evaluated = 2
            ),
            Spec(
                input = "{{escapedString}}",
                expected = """ 
                    "My '[name]' 'is' \"Slim Shady\""
                """.trim(),
                evaluated = """
                    My '[name]' 'is' "Slim Shady" 
                """.trim()
            ),
            Spec(
                input = "{{object}}",
                expected = """ 
                    {"key1":"value","key2":5,"key3":true,"key4":7.1}
                """.trim(),
                evaluated = mapOf(
                    "key1" to "value",
                    "key2" to 5,
                    "key3" to true,
                    "key4" to 7.1
                )
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `should replace STRING placeholder with value`(spec: Spec) = runTest {
        val result = placeHolderReplacer.replacePlaceholders(spec.input, placeHolderValues)
        assertEquals(spec.expected, result)

        // check that the resulting expression can be evaluated
        val evaluated = JsonataExpressionEvaluator().evaluate(result, mapOf<Any, Any>())
        assertEquals(spec.evaluated, evaluated)
    }

}