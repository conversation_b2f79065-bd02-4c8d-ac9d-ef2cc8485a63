package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class IferrorTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Iferror.functionName, Iferror.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns value_if_error when value is an error`() {
        val expression = "\$IFERROR('#ERROR', 'default')"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("default")
    }

    @Test
    fun `returns original value when not an error`() {
        val expression = "\$IFERROR(100, 'default')"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(100)
    }
}
