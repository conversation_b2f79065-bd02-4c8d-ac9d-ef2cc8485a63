package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class SortTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Sort.functionName, Sort.evaluatorFunction)
        return jsonataExpression
    }

    val data = mapOf(
        "$" to listOf(
            mapOf("name" to "<PERSON>", "age" to 28, "salary" to 40000),
            mapOf("name" to "<PERSON>", "age" to 28, "salary" to 50000),
            mapOf("name" to "<PERSON>", "age" to 25, "salary" to 45000),
            mapOf("name" to "<PERSON>", "age" to 34, "salary" to 52000)
        )
    )

    @Test
    fun `sorts list of strings in ascending order with return array`() {
        val expression = "\$SORT([\"banana\", \"apple\", \"cherry\", \"date\"], \"asc\", [\"banana\", \"apple\", \"cherry\", \"date\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("apple", "banana", "cherry", "date"))
    }

    @Test
    fun `can sort if only one array is provided but no return array`() {
        val expression = "\$SORT([\"banana\", \"apple\", \"cherry\", \"date\"], \"asc\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("apple", "banana", "cherry", "date"))
    }

    @Test
    fun `sorts list of numbers in ascending order`() {
        val expression = "\$SORT([5, 3, 8, 1, 2], \"asc\", [5, 3, 8, 1, 2])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf(1, 2, 3, 5, 8))
    }

    @Test
    fun `sorts names by age ascending`() {
        val expression = "\$SORT($.*.name, \"asc\", $.*.name)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(data) as List<*>
        result.shouldContainExactly(listOf("Alice", "Bob", "Charlie", "Dave"))
    }

    @Test
    fun `sorts names by age ascending then salary descending`() {
        val expression = "\$SORT($.*.age, \"asc\", $.*.salary, \"desc\", $.*.name)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(data) as List<*>
        result.shouldContainExactly(listOf("Charlie", "Bob", "Alice", "Dave"))
    }

    @Test
    fun `sorts names by salary descending`() {
        val expression = "\$SORT($.*.salary, \"desc\", $.*.name)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(data) as List<*>
        result.shouldContainExactly(listOf("Dave", "Bob", "Charlie", "Alice"))
    }

    @Test
    fun `sorts ages by name ascending`() {
        val expression = "\$SORT($.*.name, \"asc\", $.*.age)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(data) as List<*>
        result.shouldContainExactly(listOf(28, 28, 25, 34))
    }

    @Test
    fun `throws exception for invalid sorting criteria`() {
        val expression = "\$SORT($.*.age, \"ascending\", $.*.name)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(data)
        }.message.shouldBe("Invalid sorting criteria arguments")
    }

    @Test
    fun `throws exception when returnArray length does not match sortArray length`() {
        val expression = "\$SORT($.*.age, \"asc\", [\"Alice\", \"Bob\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(data)
        }.message.shouldBe("Length of returnArray does not match length of sortArray")
    }

    @Test
    fun `throws exception when sortArrays have different lengths`() {
        val expression = "\$SORT($.*.age, \"asc\", [28, 25], \"desc\", $.*.name)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(data)
        }.message.shouldBe("All sort arrays must have the same length")
    }
}
