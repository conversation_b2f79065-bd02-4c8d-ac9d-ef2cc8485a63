package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class LeftTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Left.functionName, Left.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return the leftmost characters of a string`() {
        val expression = "\$LEFT('Hello, world!', 5)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello"
    }

    @Test
    fun `should return the leftmost characters of a string when the length is greater than the string`() {
        val expression = "\$LEFT('Hello', 10)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello"
    }

    @Test
    fun `should return the leftmost characters of a string when the length is equal to the string`() {
        val expression = "\$LEFT('Hello', 5)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello"
    }

    @Test
    fun `should return an empty string when the length is 0`() {
        val expression = "\$LEFT('Hello', 0)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe ""
    }

    @Test
    fun `should return an empty string when the string is empty`() {
        val expression = "\$LEFT('', 5)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe ""
    }
}