package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator

class StringTest {

    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(IsEmpty.functionName, IsEmpty.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `test evaluateCondition with empty string is empty`() {
        val jsonata = jsonataWithFunctionRegistered("$${IsEmpty.functionName}('')")
        val result = jsonata.evaluate(null)
        result shouldBe true
    }

    @Test
    fun `test evaluateCondition if non empty string is empty`() {
        val jsonata = jsonataWithFunctionRegistered("$${IsEmpty.functionName}('hello')")
        val result = jsonata.evaluate(null)
        result shouldBe false
    }

    @Test
    fun `test evaluateCondition with hello is not empty`() = runTest {
        val condition = "$${IsNotEmpty.functionName}('hello')"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>())
        result shouldBe true
    }

    @Test
    fun `test evaluateCondition with empty string is not empty`() = runTest {
        val condition = "$${IsNotEmpty.functionName}('')"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>())
        result shouldBe false
    }

    @Test
    fun `test evaluateCondition with hello world contains hello`() = runTest {
        val condition = "$${Contains.functionName}('hello world', 'hello')"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>())
        result shouldBe true
    }

    @Test
    fun `test evaluateCondition with hello world contains hello and 1 equals 1`() = runTest {
        val condition = "(1 = 1) and ($${Contains.functionName}('hello world', 'hello'))"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>())
        result shouldBe true
    }

    @Test
    fun `test evaluateCondition with hello world does not contain goodbye`() = runTest {
        val condition = "$${DoesNotContain.functionName}('hello world', 'goodbye')"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>())
        result shouldBe true
    }

    @Test
    fun `should convert array to string`() = runTest {
        val expression = "\$string(['hello', 'world'])"
        val result = JsonataExpressionEvaluator().evaluate(expression, emptyMap<String, String>())
        result shouldBe """
            ["hello","world"]
        """.trimIndent()
    }


    @Test
    fun `should convert array of numbers to string`() = runTest {
        val expression = "\$string([100, 200])"
        val result = JsonataExpressionEvaluator().evaluate(expression, emptyMap<String, String>())
        result shouldBe "[100,200]"
    }
}