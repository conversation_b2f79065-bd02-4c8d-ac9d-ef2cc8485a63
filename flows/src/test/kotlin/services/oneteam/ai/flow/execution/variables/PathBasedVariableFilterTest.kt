package services.oneteam.ai.flow.execution.variables

import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.VariableDefinition.Variable

class PathBasedVariableFilterTest {

    @Test
    fun `should filter variables based on path`() {
        val filter = PathBasedVariableFilter(setOf("a.b.c", "d.e", "f[1].g"))
        val variableA = Variable.of(JsonPrimitive("valueA"), "text", "a")
        val variableB = Variable.of(JsonPrimitive("valueB"), "text", "b")
        val variableC = Variable.of(JsonPrimitive("valueC"), "text", "c")
        val variableD = Variable.of(JsonPrimitive("valueD"), "text", "d")
        val variableE = Variable.of(JsonPrimitive("valueE"), "text", "e")
        val variableF = Variable.of(JsonPrimitive("valueF"), "text", "f")

        assertTrue(filter.filter(variableA))
        assertFalse(filter.filter(variableB))
        assertFalse(filter.filter(variableC))
        assertTrue(filter.filter(variableD))
        assertFalse(filter.filter(variableE))
        assertTrue(filter.filter(variableF))
    }
}