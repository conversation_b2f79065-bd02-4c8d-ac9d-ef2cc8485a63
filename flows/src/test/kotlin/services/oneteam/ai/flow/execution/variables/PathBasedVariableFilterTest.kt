package services.oneteam.ai.flow.execution.variables

import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.VariableDataType

class PathBasedVariableFilterTest {

    @Test
    fun `should filter variables based on path`() {
        val filter = PathBasedVariableFilter(setOf("a.b.c", "d.e", "f[1].g"))
        val variableA = VariableInstance.Variable(JsonPrimitive("valueA"), VariableDataType.TEXT, "a")
        val variableB = VariableInstance.Variable(JsonPrimitive("valueB"), VariableDataType.TEXT, "b")
        val variableC = VariableInstance.Variable(JsonPrimitive("valueC"), VariableDataType.TEXT, "c")
        val variableD = VariableInstance.Variable(JsonPrimitive("valueD"), VariableDataType.TEXT, "d")
        val variableE = VariableInstance.Variable(JsonPrimitive("valueE"), VariableDataType.TEXT, "e")
        val variableF = VariableInstance.Variable(JsonPrimitive("valueF"), VariableDataType.TEXT, "f")

        assertTrue(filter.filter(variableA))
        assertFalse(filter.filter(variableB))
        assertFalse(filter.filter(variableC))
        assertTrue(filter.filter(variableD))
        assertFalse(filter.filter(variableE))
        assertTrue(filter.filter(variableF))
    }
}