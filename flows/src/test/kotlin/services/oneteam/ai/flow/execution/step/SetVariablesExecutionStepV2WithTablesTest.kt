package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonPrimitive
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.DefaultMapBuilder
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.variables.DefaultVariableOperation
import services.oneteam.ai.flow.execution.variables.TableVariableOperation
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.TableOperation
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.flow.variables.VariableProperties
import services.oneteam.ai.shared.otSerializer

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SetVariablesExecutionStepV2WithTablesTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val contextToJsonObjectBuilder = ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "1")

    data class Spec(
        val rawVariables: List<VariableInstance>, val expectedVariables: List<VariableInstance>
    )

    @Test
    fun `should resolve table`() {
        `should resolve properly`(
            Spec(
                listOf(
                    VariableInstance.Variable(
                        JsonPrimitive("{{form.VEOTahOeLj.answer}}"),
                        VariableDataType.TABLE,
                        "output",
                        properties = VariableProperties.TableVariableProperties(TableOperation.SET_TABLE)
                    )
                ), listOf(
                    VariableInstance.Variable(
                        otSerializer.decodeFromString(
                            JsonArray.serializer(),
                            "[{\"O1REtNdy50\":\"aa\",\"eALIKF2hFv\":11,\"_rowId\":\"Q7JjeX6MIGdhGaqU9n1TH\",\"_rowIndex\": 1},{\"O1REtNdy50\":\"bb\",\"eALIKF2hFv\":22,\"_rowId\":\"ryqry17ixv4vg5DtIQrp8\",\"_rowIndex\": 2},{\"O1REtNdy50\":\"cc\",\"eALIKF2hFv\":33,\"_rowId\":\"JEU4NFPAQpCQFFJKFXaQ1\",\"_rowIndex\": 3}]"
                        ),
                        VariableDataType.TABLE,
                        "output",
                        properties = VariableProperties.TableVariableProperties(TableOperation.SET_TABLE)
                    )
                )
            ),
        )
    }

    fun `should resolve properly`(spec: Spec) = runTest {

        val setVariablesStep = SetVariablesExecutionStepV2(
            Step(
                Step.Id("1"), Step.Variant.SET_VARIABLES, "name", Step.Properties(
                    typePrimaryIdentifier = null, variables = spec.rawVariables.toMutableList()
                )
            ), ContextToJsonObjectBuilder(Fixtures.Context.buildMapBuilders(), "3"), variableOperations = listOf(
                TableVariableOperation(false), DefaultVariableOperation(false)
            ), expressionEvaluator = JsonataExpressionEvaluator()
        )

        val context = Fixtures.context()

        context.flowContext.set(Fixtures.Vars.form)

        setVariablesStep.populate(context)
        setVariablesStep.execute(context)

        logger.debug("Flow context: {}", otSerializer.encodeToString(context))
        logger.debug("Actual. : {}", context.flowContext.variables["output"])
        logger.debug("Expected: {}", spec.expectedVariables[0])

        assertThat(context.flowContext.variables.values).containsAll(spec.expectedVariables)
    }

}