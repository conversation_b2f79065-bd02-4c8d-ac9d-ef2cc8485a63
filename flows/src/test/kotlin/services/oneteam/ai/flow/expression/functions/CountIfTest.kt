package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class CountIfTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(CountIf.functionName, CountIf.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns count of integers with value`() {
        val expression = "\$COUNTIF([1, 2, 3, 4, 5], 2)";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result shouldBe "1"
    }

    @Test
    fun `returns count of strings with value`() {
        val expression = "\$COUNTIF(['a', 'b', 'c', 'd', 'e'], 'b')";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result shouldBe "1"
    }

    @Test
    fun `returns count of arrays with value`() {
        val expression = "\$COUNTIF([[1, 2], [3, 4], [5, 6]], [3, 4])";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result shouldBe "1"
    }

    @Test
    fun `returns count of integers with predicate`() {
        val expression = "\$COUNTIF([1, 2, 2, 4, 5], \$COUNTIF([1, 2, 2, 4, 5], 2))";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result shouldBe "2"
    }

    @Test
    fun `returns count 0 for invalid data`() {
        val expression = "\$COUNTIF(\"\", \"Bonus\")";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result shouldBe "0"
    }

    @Test
    fun `using relational operator greater than`() {
        val expression = "\$COUNTIF([1,2,3], '>1')"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2"
    }

    @Test
    fun `no matching elements returns zero`() {
        val expression = "\$COUNTIF([1,2,3], '<0')"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "0"
    }

    @Test
    fun `range with null values`() {
        val expression = "\$COUNTIF([null,2,3], 2)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "1"
    }

    @Test
    fun `relational operator less than or equal`() {
        val expression = "\$COUNTIF([1,2,3,4], '<=2')"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2"
    }

    @Test
    fun `explicit equals operator`() {
        val expression = "\$COUNTIF([1,2,3], '=2')"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "1"
    }

    @Test
    fun `negative numbers with relational operator`() {
        val expression = "\$COUNTIF([-1,-2,0,1], '<0')"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2"
    }
}