package services.oneteam.ai.flow.support.pairwise

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.math.BigDecimal

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PairwiseDivideTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    data class Scenario(
        val name: String, val values: List<PairwiseValue>, val expected: PairwiseValue
    )

    fun provider(): List<Scenario> {
        return listOf(
            Scenario(
                "case 0: ",
                listOf(
                    PairwiseValue.of(listOf(listOf(100, 64), listOf(81, 32))),
                    PairwiseValue.of(listOf(listOf(10, 8), listOf(9, 6))),
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(BigDecimal("10"), BigDecimal(8)),
                        listOf(BigDecimal(9), BigDecimal("5.333333333333333"))
                    )
                )
            ),
        )
    }


    @ParameterizedTest
    @MethodSource("provider")
    fun `scenario `(scenario: Scenario) {

        logger.debug("Scenario: {}", scenario.name)
        logger.debug("Values: {}", scenario.values)
        logger.debug("Expected: {}", scenario.expected)
        Pairwise(PairwiseOperation.DIVIDE).pairwise(scenario.values).let { it1 ->
            logger.debug("Pairwise result: {}", it1)
            logger.debug("{} = {}", scenario.values.joinToString(" / ") { it.value.toString() }, it1.value)
            assertThat(it1).isEqualTo(scenario.expected)
        }
    }

}