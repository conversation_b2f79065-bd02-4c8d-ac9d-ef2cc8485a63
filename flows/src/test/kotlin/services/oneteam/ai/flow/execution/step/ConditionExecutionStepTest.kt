package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.DefaultMapBuilder
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.conditional.Condition
import services.oneteam.ai.flow.expression.conditional.Expression
import services.oneteam.ai.flow.expression.conditional.LogicalOperator
import services.oneteam.ai.flow.expression.conditional.Operator
import services.oneteam.ai.shared.domains.workspace.Workspace
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ConditionExecutionStepTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun `test execute with simple condition - matching branch 1`() = runTest {
        val flowExecutionStep = FlowExecution.Step(
            id = FlowExecution.Step.Id("1"),
            variant = FlowExecution.Step.Variant.CONDITION,
            properties = FlowExecution.Step.Properties(
                typePrimaryIdentifier = "condition",
                branches = listOf(
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "branch1",
                        condition = Condition(
                            left = Expression("5000"),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("0"))
                        ),
                        next = FlowExecution.Step.Id("2")
                    )
                ).toMutableList()
            ),
            next = FlowExecution.Step.Id("3")
        )
        val context = mockFlowContext()

        val step = ConditionExecutionStep(
            flowExecutionStep, ContextToJsonObjectBuilder(listOf(), "testCacheKey"),
            JsonataExpressionEvaluator()
        )

        val next = step.execute(buildStepContext(flowExecutionStep, context))

        Assertions.assertEquals(FlowExecution.Step.Id("2"), next)
    }

    private fun buildStepContext(
        flowExecutionStep: FlowExecution.Step,
        context: FlowContext
    ): FlowContextWithLocalStep = FlowContextWithLocalStep(
        flowExecutionStep.id,
        context,
        mutableMapOf(),
    )

    @Test
    fun `test execute with simple condition - matching else`() = runTest {
        val flowExecutionStep = FlowExecution.Step(
            id = FlowExecution.Step.Id("1"),
            variant = FlowExecution.Step.Variant.CONDITION,
            properties = FlowExecution.Step.Properties(
                typePrimaryIdentifier = "condition",
                branches = listOf(
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "branch1",
                        condition = Condition(
                            left = Expression("5000"),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("5001"))
                        ),
                        next = FlowExecution.Step.Id("2")
                    )
                ).toMutableList()
            ),
            next = FlowExecution.Step.Id("3")
        )
        val context = mockFlowContext()

        val step = ConditionExecutionStep(
            flowExecutionStep, ContextToJsonObjectBuilder(listOf(), "testCacheKey"),
            JsonataExpressionEvaluator()
        )

        val next = step.execute(buildStepContext(flowExecutionStep, context))
        Assertions.assertEquals(FlowExecution.Step.Id("3"), next)
    }

    @Test
    fun `test execute with 2 branches - matching branch 1`() = runTest {
        val flowExecutionStep = FlowExecution.Step(
            id = FlowExecution.Step.Id("1"),
            variant = FlowExecution.Step.Variant.CONDITION,
            properties = FlowExecution.Step.Properties(
                typePrimaryIdentifier = "condition",
                branches = listOf(
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "branch1",
                        condition = Condition(
                            left = Expression("5000"),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("0"))
                        ),
                        next = FlowExecution.Step.Id("2")
                    ),
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "branch2",
                        condition = Condition(
                            left = Expression("5000"),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("5001"))
                        ),
                        next = FlowExecution.Step.Id("3")
                    )
                ).toMutableList()
            ),
            next = FlowExecution.Step.Id("4")
        )
        val context = mockFlowContext()

        val step = ConditionExecutionStep(
            flowExecutionStep, ContextToJsonObjectBuilder(listOf(), "testCacheKey"),
            JsonataExpressionEvaluator()
        )

        val next = step.execute(buildStepContext(flowExecutionStep, context))
        Assertions.assertEquals(FlowExecution.Step.Id("2"), next)
    }

    @Test
    fun `test execute with 2 branches - matching branch 2`() = runTest {
        val flowExecutionStep = FlowExecution.Step(
            id = FlowExecution.Step.Id("1"),
            variant = FlowExecution.Step.Variant.CONDITION,
            properties = FlowExecution.Step.Properties(
                typePrimaryIdentifier = "condition",
                branches = listOf(
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "branch1",
                        condition = Condition(
                            left = Expression("5000"),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("5000"))
                        ),
                        next = FlowExecution.Step.Id("2")
                    ),
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "branch2",
                        condition = Condition(
                            left = Expression("5000"),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("0"))
                        ),
                        next = FlowExecution.Step.Id("3")
                    )
                ).toMutableList()
            ),
            next = FlowExecution.Step.Id("4")
        )
        val context = mockFlowContext()

        val step = ConditionExecutionStep(
            flowExecutionStep, ContextToJsonObjectBuilder(listOf(), "testCacheKey"),
            JsonataExpressionEvaluator()
        )

        val next = step.execute(buildStepContext(flowExecutionStep, context))
        Assertions.assertEquals(FlowExecution.Step.Id("3"), next)
    }

    @Test
    fun `test execute with 2 branches - matching else`() = runTest {
        val flowExecutionStep = FlowExecution.Step(
            id = FlowExecution.Step.Id("1"),
            variant = FlowExecution.Step.Variant.CONDITION,
            properties = FlowExecution.Step.Properties(
                typePrimaryIdentifier = "condition",
                branches = listOf(
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "branch1",
                        condition = Condition(
                            left = Expression("5000"),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("5000"))
                        ),
                        next = FlowExecution.Step.Id("2")
                    ),
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "branch2",
                        condition = Condition(
                            left = Expression("5000"),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("5001"))
                        ),
                        next = FlowExecution.Step.Id("3")
                    )
                ).toMutableList()
            ),
            next = FlowExecution.Step.Id("4")
        )
        val context = mockFlowContext()

        val step = ConditionExecutionStep(
            flowExecutionStep, ContextToJsonObjectBuilder(listOf(), "testCacheKey"),
            JsonataExpressionEvaluator()
        )

        val next = step.execute(buildStepContext(flowExecutionStep, context))
        Assertions.assertEquals(FlowExecution.Step.Id("4"), next)
    }

    data class Spec(val context: FlowContext, val nextStepId: FlowExecution.Step.Id?)

    fun specProvider(): Stream<Spec> {
        return Stream.of(
            Spec(
                FlowContext(
                    global = Fixtures.global,
                    workspace = Fixtures.workspaceContext,
                    event = Fixtures.event.copy(workspaceId = Workspace.Id(9)),
                ), FlowExecution.Step.Id("step2")
            ),
            Spec(
                FlowContext(
                    global = Fixtures.global,
                    workspace = Fixtures.workspaceContext,
                    event = Fixtures.event.copy(workspaceId = Workspace.Id(8)),
                ), FlowExecution.Step.Id("step3")
            ),
            Spec(
                FlowContext(
                    global = Fixtures.global,
                    workspace = Fixtures.workspaceContext,
                    event = Fixtures.event.copy(workspaceId = Workspace.Id(7)),
                ), FlowExecution.Step.Id("step3")
            ),
            Spec(
                FlowContext(
                    global = Fixtures.global,
                    workspace = Fixtures.workspaceContext,
                    event = Fixtures.event.copy(workspaceId = Workspace.Id(5)),
                ), FlowExecution.Step.Id("step4")
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("specProvider")
    fun `test execute with nested branches - matching else`(spec: Spec) = runTest {

        val flowExecutionStep = FlowExecution.Step(
            id = FlowExecution.Step.Id("step1"),
            variant = FlowExecution.Step.Variant.CONDITION,
            next = FlowExecution.Step.Id("step4"),
            properties = FlowExecution.Step.Properties(
                typePrimaryIdentifier = "",
                branches = listOf(
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "If",
                        next = FlowExecution.Step.Id("step2"),
                        condition = Condition(
                            left = Expression(""),
                            operator = LogicalOperator.AND,
                            right = listOf(
                                Condition(
                                    left = Expression("{{event.workspaceId}}"),
                                    operator = Operator.GREATER_THAN_OR_EQUAL,
                                    right = listOf(Expression("7"))
                                ),
                                Condition(
                                    left = Expression("{{event.workspaceId}}"),
                                    operator = Operator.GREATER_THAN,
                                    right = listOf(Expression("8"))
                                )
                            )
                        ),
                    ),
                    FlowExecution.Step.Properties.ConditionBranch(
                        name = "If 2",
                        condition = Condition(
                            left = Expression("{{event.workspaceId}}"),
                            operator = Operator.GREATER_THAN,
                            right = listOf(Expression("5"))
                        ),
                        next = FlowExecution.Step.Id("step3")
                    )
                ).toMutableList()
            )
        )

        val step = ConditionExecutionStep(
            flowExecutionStep,
            ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "testCacheKey"),
            JsonataExpressionEvaluator()
        )

        var stepContext = buildStepContext(flowExecutionStep, spec.context)
        step.populate(stepContext)
        val next = step.execute(stepContext)
        Assertions.assertEquals(spec.nextStepId, next)
    }

}


fun mockFlowContext(): FlowContext {
    return FlowContext(
        global = Fixtures.global,
        workspace = Fixtures.workspaceContext,
        event = Fixtures.event.copy(workspaceId = Workspace.Id(7)),
    )
}