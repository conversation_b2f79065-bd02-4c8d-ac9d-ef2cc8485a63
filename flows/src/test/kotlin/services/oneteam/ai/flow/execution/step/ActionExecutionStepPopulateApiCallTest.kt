package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.listeners.FlowListenerLogger
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration

class ActionExecutionStepPopulateApiCallTest {

    @Test
    fun `should populate apiCall without JSONata expressions`() = runTest {

        // PREPARE

        var response = """
            {
              "foundation": {
                "id": "F1"
              }
            }
        """.trimIndent()
        val proxyService = ProxyFixtures.mockForSuccess(response)

        val flowStepTypeConfiguration = FlowStepTypeConfiguration(
            1, "myAction", "action", "My Action", null,
            FlowStepType.Properties(
                icon = FlowStepType.Properties.Icon("search"),
                isLocked = true,
                configuration = FlowStepType.Properties.Configuration(
                    apiCall = FlowStepType.Properties.Configuration.ApiCall(
                        url = "/ai/api/foundations/obtain",
                        body = JsonObject(
                            mapOf(
                                "key" to JsonPrimitive("{{thisStep.key}}")
                            )
                        ),
                        method = "POST",
                        internal = true,
                        response = FlowStepType.Properties.Configuration.ApiCall.Response(
                            type = "json",
                            properties = FlowStepType.Properties.Configuration.ApiCall.Response.Property(
                                items = listOf(
                                    FlowStepType.Properties.Configuration.ApiCall.Response.Item(
                                        type = "foundation.minimal",
                                        identifier = "foundation"
                                    )
                                )
                            )
                        )
                    ),
                    variableMappings = listOf(
                        FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping(
                            type = "foundation.{{thisStep.foundationConfigurationId}}",
                            value = JsonPrimitive("{{thisStep.response.foundation.id}}"),
                            identifier = "{{thisStep.foundationVariableName}}"
                        )
                    ),
                ),
            ),
            1
        )

        val context = Fixtures.context()
        context.flowContext.listeners.add(FlowListenerLogger())

        context.thisStep["key"] = JsonPrimitive("value")
        context.thisStep["foundationConfigurationId"] = JsonPrimitive("myFoundationConfigurationId")
        context.thisStep["foundationVariableName"] = JsonPrimitive("myFoundationVariableName")

        val mapBuilders = Fixtures.Context.buildMapBuilders()

        val step = ActionExecutionStep(
            Step(
                id = Step.Id("1"),
                name = "My Action",
                variant = Step.Variant.ACTION,
                properties = Step.Properties(
                    typePrimaryIdentifier = "myAction",
                    variables = mutableListOf(),
                ),
                next = Step.Id("2")
            ),
            flowStepTypeConfiguration,
            proxyService = proxyService,
            internalProxyService = proxyService,
            contextToJsonObjectBuilder = ContextToJsonObjectBuilder(
                mapBuilders,
                Step.Id("1").value
            )
        )

        // PERFORM

        step.populate(context)

        // VERIFY

        // api call template should be populated with values from the context
        assertEquals(
            Json.decodeFromString<JsonElement>(
                """
                {
                  "url": "/ai/api/foundations/obtain",
                  "method": "POST",
                  "body": {
                    "key": "value"
                  },
                  "response": {
                    "type": "json",
                    "properties": {
                      "items": [
                        {
                          "type": "foundation.minimal",
                          "identifier": "foundation"
                        }
                      ]
                    }
                  },
                  "internal": true
                }
            """.trimIndent()
            ),
            step.step.properties.apiCall
        )

        // PERFORM

        step.execute(context)

        // VERIFY

        // response from api call should be added to the step properties so it is recorded in the flow execution document
        assertEquals(
            Json.decodeFromString<JsonElement>(response),
            step.step.properties.apiCall!!["response"]
        )

        // values from response should be mapped to variables based on VariableMappings
        assertEquals(
            context.flowContext.variables["myFoundationVariableName"],
            Variable(
                JsonPrimitive("F1"),
                "foundation.myFoundationConfigurationId",
                "myFoundationVariableName"
            )
        )
    }

    @Test
    fun `should populate apiCall with JSONata expressions`() = runTest {

        // PREPARE

        var response = """
            [{
              "documentId": "document1",
              "message": "Answers processed successfully"
            }]
        """.trimIndent()
        val proxyService = ProxyFixtures.mockForSuccess(response)

        val flowStepTypeConfiguration = FlowStepTypeConfiguration(
            1, "SetMultipleAnswersAction", "action", "Set multiple answers Action", null,
            FlowStepType.Properties(
                icon = FlowStepType.Properties.Icon("edit_document"),
                isLocked = true,
                configuration = FlowStepType.Properties.Configuration(
                    apiCall = FlowStepType.Properties.Configuration.ApiCall(
                        url = "/ai/api/forms/{{thisStep.formId}}/answers",
                        body = JsonObject(
                            mapOf(
                                "answers" to JsonPrimitive("\$map({{thisStep.answers}}, function(\$answer) { { \"value\": \$answer.answer, \"questionId\": \$answer.questionId, \"operation\": (\$answer.operation = \"\" ? null : \$answer.operation), \"rowId\": (\$answer.rowId = \"\" ? null : \$answer.rowId), \"rowIndex\": (\$answer.rowIndex = \"\" ? null : \$answer.rowIndex), \"columnId\": (\$answer.columnId = \"\" ? null : \$answer.columnId), \"valueRowIdShouldUpdate\": (\$answer.valueRowIdShouldUpdate = \"\" ? null : \$answer.valueRowIdShouldUpdate), \"listOperation\": (\$answer.listOperation = \"\" ? null : \$answer.listOperation), \"itemId\": (\$answer.itemId = \"\" ? null : \$answer.itemId), \"itemIndex\": (\$answer.itemIndex = \"\" ? null : \$answer.itemIndex), \"fileOperation\": (\$answer.fileOperation = \"\" ? null : \$answer.fileOperation), \"fileIndex\": (\$answer.fileIndex = \"\" ? null : \$answer.fileIndex) } })")
                            )
                        ),
                        bodyProperties = mapOf(
                            "answers" to mapOf(
                                "isExpression" to true
                            )
                        ),
                        method = "PUT",
                        internal = true,
                        response = FlowStepType.Properties.Configuration.ApiCall.Response(
                            type = "json",
                            properties = FlowStepType.Properties.Configuration.ApiCall.Response.Property(
                                items = listOf(
                                    FlowStepType.Properties.Configuration.ApiCall.Response.Item(
                                        type = "string",
                                        identifier = "message"
                                    )
                                )
                            )
                        )
                    ),
                    variableMappings = listOf(),
                ),
            ),
            1
        )

        val context = Fixtures.context()
        context.flowContext.listeners.add(FlowListenerLogger())

        context.thisStep["formId"] = JsonPrimitive("1")
        context.thisStep["answers"] = Json.parseToJsonElement("""
            [
                {"questionId": "questionId1", "answer": "answer1"},
                {"questionId": "questionId2", "answer": "answer2"}
            ]
        """.trimIndent())

        val mapBuilders = Fixtures.Context.buildMapBuilders()

        val step = ActionExecutionStep(
            Step(
                id = Step.Id("1"),
                name = "Set multiple answers Action",
                variant = Step.Variant.ACTION,
                properties = Step.Properties(
                    typePrimaryIdentifier = "SetMultipleAnswersAction",
                    variables = mutableListOf(),
                ),
                next = Step.Id("2")
            ),
            flowStepTypeConfiguration,
            proxyService = proxyService,
            internalProxyService = proxyService,
            contextToJsonObjectBuilder = ContextToJsonObjectBuilder(
                mapBuilders,
                Step.Id("1").value
            )
        )

        // PERFORM

        step.populate(context)

        // VERIFY

        // api call template should be populated with values from the context
        // The regular placeholder should be replaced with its value
        // The JSONata expression should be evaluated
        assertEquals(
            Json.decodeFromString<JsonElement>(
                """
                {
                  "url": "/ai/api/forms/1/answers",
                  "method": "PUT",
                  "body": {
                    "answers": [
                        {"questionId": "questionId1", "value": "answer1"},
                        {"questionId": "questionId2", "value": "answer2"}
                    ]           
                  },
                  "bodyProperties": {
                    "answers": {
                      "isExpression": true
                    }
                  },
                  "response": {
                    "type": "json",
                    "properties": {
                      "items": [
                        {
                          "type": "string",
                          "identifier": "message"
                        }
                      ]
                    }
                  },
                  "internal": true
                }
            """.trimIndent()
            ),
            step.step.properties.apiCall
        )
    }

}
