package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.listeners.FlowListenerLogger
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.otSerializer

class ActionExecutionStepPopulateApiCallTest {

    @Test
    fun `should populate apiCall without JSONata expressions`() = runTest {

        // PREPARE
        val filePressService = mock(FilePressService::class.java)

        var response = """
            {
              "foundation": {
                "id": "F1"
              }
            }
        """.trimIndent()
        val proxyService = ProxyFixtures.mockForSuccess(response)

        val flowStepTypeConfiguration = FlowStepTypeConfiguration(
            1, "myAction", "action", "My Action", null, FlowStepType.Properties(
                icon = FlowStepType.Properties.Icon("search"),
                isLocked = true,
                configuration = FlowStepType.Properties.Configuration(
                    apiCall = FlowStepType.Properties.Configuration.ApiCall(
                        url = "/ai/api/foundations/select-many",
                        body = JsonObject(
                            mapOf(
                                "key" to JsonPrimitive("{{thisStep.key}}")
                            )
                        ),
                        method = "POST",
                        internal = true,
                        response = FlowStepType.Properties.Configuration.ApiCall.Response(
                            type = "json", properties = FlowStepType.Properties.Configuration.ApiCall.Response.Property(
                                items = listOf(
                                    FlowStepType.Properties.Configuration.ApiCall.Response.Item(
                                        type = "list",
                                        identifier = "foundations",
                                        properties = FlowStepType.Properties.Configuration.ApiCall.Response.Property(
                                            items = listOf(
                                                FlowStepType.Properties.Configuration.ApiCall.Response.Item(
                                                    type = "foundation.minimal", identifier = "foundation"
                                                )
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    ),
                    variableMappings = listOf(
                        FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping(
                            type = "foundation.{{thisStep.foundationConfigurationId}}",
                            value = JsonPrimitive("{{thisStep.response.foundation.id}}"),
                            identifier = "{{thisStep.foundationVariableName}}"
                        )
                    ),
                ),
            ), 1
        )

        val context = Fixtures.context()
        context.flowContext.listeners.add(FlowListenerLogger())

        context.thisStep["key"] = JsonPrimitive("value")
        context.thisStep["foundationConfigurationId"] = JsonPrimitive("myFoundationConfigurationId")
        context.thisStep["foundationVariableName"] = JsonPrimitive("myFoundationVariableName")

        val mapBuilders = Fixtures.Context.buildMapBuilders()

        val step = ActionExecutionStep(
            Step(
                id = Step.Id("1"), name = "My Action", variant = Step.Variant.ACTION, properties = Step.Properties(
                    typePrimaryIdentifier = "myAction",
                    variables = mutableListOf(),
                ), next = Step.Id("2")
            ),
            flowStepTypeConfiguration,
            proxyService = proxyService,
            internalProxyService = proxyService,
            filePressService = filePressService,
            contextToJsonObjectBuilder = ContextToJsonObjectBuilder(
                mapBuilders, Step.Id("1").value
            ),
            expressionEvaluator = JsonataExpressionEvaluator()
        )

        // PERFORM

        step.populate(context)

        // VERIFY

        // api call template should be populated with values from the context
        assertEquals(
            otSerializer.decodeFromString<JsonElement>(
                """
                {"url":"/ai/api/foundations/select-many","method":"POST","headers":null,"body":{"key":"value"},"bodyProperties":null,"queryParams":null,"response":{"type":"json","properties":{"items":[{"type":"list","identifier":"foundations","properties":{"items":[{"type":"foundation.minimal","identifier":"foundation","properties":null}]}}]}},"internal":true}
            """.trimIndent()
            ), step.step.properties.apiCall
        )

        // PERFORM
        step.execute(context)

        // VERIFY

        // response from api call should be added to the step properties so it is recorded in the flow execution document
        assertEquals(
            otSerializer.decodeFromString<JsonElement>(response), step.step.properties.apiCall!!["response"]
        )

        // values from response should be mapped to variables based on VariableMappings
        assertEquals(
            context.flowContext.variables["myFoundationVariableName"], VariableInstance.Variable(
                JsonPrimitive("F1"),
                VariableDataType.fromString("foundation.myFoundationConfigurationId"),
                "myFoundationVariableName"
            )
        )
    }

    @Test
    fun `should populate apiCall with JSONata expressions`() = runTest {

        // PREPARE
        val filePressService = mock(FilePressService::class.java)

        var response = """
            [{
              "documentId": "document1",
              "message": "Answers processed successfully"
            }]
        """.trimIndent()
        val proxyService = ProxyFixtures.mockForSuccess(response)

        val flowStepTypeConfiguration = FlowStepTypeConfiguration(
            1, "SetMultipleAnswersAction", "action", "Set multiple answers Action", null, FlowStepType.Properties(
                icon = FlowStepType.Properties.Icon("edit_document"),
                isLocked = true,
                configuration = FlowStepType.Properties.Configuration(
                    apiCall = FlowStepType.Properties.Configuration.ApiCall(
                        url = "/ai/api/forms/{{thisStep.formId}}/answers",
                        body = JsonObject(
                            mapOf(
                                "answers" to JsonPrimitive("\$map({{thisStep.answers}}, function(\$answer) { { \"value\": \$answer.answer, \"questionId\": \$answer.questionId, \"operation\": (\$answer.operation = \"\" ? null : \$answer.operation), \"rowId\": (\$answer.rowId = \"\" ? null : \$answer.rowId), \"rowIndex\": (\$answer.rowIndex = \"\" ? null : \$answer.rowIndex), \"columnId\": (\$answer.columnId = \"\" ? null : \$answer.columnId), \"valueRowIdShouldUpdate\": (\$answer.valueRowIdShouldUpdate = \"\" ? null : \$answer.valueRowIdShouldUpdate), \"listOperation\": (\$answer.listOperation = \"\" ? null : \$answer.listOperation), \"itemId\": (\$answer.itemId = \"\" ? null : \$answer.itemId), \"itemIndex\": (\$answer.itemIndex = \"\" ? null : \$answer.itemIndex), \"fileOperation\": (\$answer.fileOperation = \"\" ? null : \$answer.fileOperation), \"fileIndex\": (\$answer.fileIndex = \"\" ? null : \$answer.fileIndex) } })")
                            )
                        ),
                        bodyProperties = mapOf(
                            "answers" to mapOf(
                                "isExpression" to true
                            )
                        ),
                        method = "PUT",
                        internal = true,
                        response = FlowStepType.Properties.Configuration.ApiCall.Response(
                            type = "json", properties = FlowStepType.Properties.Configuration.ApiCall.Response.Property(
                                items = listOf(
                                    FlowStepType.Properties.Configuration.ApiCall.Response.Item(
                                        type = "string", identifier = "message"
                                    )
                                )
                            )
                        )
                    ),
                    variableMappings = listOf(),
                ),
            ), 1
        )

        val context = Fixtures.context()
        context.flowContext.listeners.add(FlowListenerLogger())

        context.thisStep["formId"] = JsonPrimitive("1")
        context.thisStep["answers"] = otSerializer.parseToJsonElement(
            """
            [
                {"questionId": "questionId1", "answer": "answer1"},
                {"questionId": "questionId2", "answer": "answer2"}
            ]
        """.trimIndent()
        )

        val mapBuilders = Fixtures.Context.buildMapBuilders()

        val step = ActionExecutionStep(
            Step(
                id = Step.Id("1"),
                name = "Set multiple answers Action",
                variant = Step.Variant.ACTION,
                properties = Step.Properties(
                    typePrimaryIdentifier = "SetMultipleAnswersAction",
                    variables = mutableListOf(),
                ),
                next = Step.Id("2")
            ),
            flowStepTypeConfiguration,
            proxyService = proxyService,
            internalProxyService = proxyService,
            filePressService = filePressService,
            contextToJsonObjectBuilder = ContextToJsonObjectBuilder(
                mapBuilders, Step.Id("1").value
            ),
            expressionEvaluator = JsonataExpressionEvaluator()
        )

        // PERFORM

        step.populate(context)

        // VERIFY

        // api call template should be populated with values from the context
        // The regular placeholder should be replaced with its value
        // The JSONata expression should be evaluated
        assertEquals(
            otSerializer.decodeFromString<JsonElement>(
                """
                {
                  "url": "/ai/api/forms/1/answers",
                  "method": "PUT",
                  "headers": null,
                  "body": {
                    "answers": [
                      { "value": "answer1", "questionId": "questionId1" },
                      { "value": "answer2", "questionId": "questionId2" }
                    ]
                  },
                  "bodyProperties": { "answers": { "isExpression": true } },
                  "queryParams": null,
                  "response": {
                    "type": "json",
                    "properties": {
                      "items": [
                        {
                          "type": "string",
                          "identifier": "message",
                          "properties": null
                        }
                      ]
                    }
                  },
                  "internal": true
                }
            """.trimIndent()
            ), step.step.properties.apiCall
        )
    }

}
