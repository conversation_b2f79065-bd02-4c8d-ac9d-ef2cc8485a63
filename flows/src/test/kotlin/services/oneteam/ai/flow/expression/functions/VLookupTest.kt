package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import io.kotest.assertions.throwables.shouldThrow
import org.junit.jupiter.api.Test

class VlookupTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Vlookup.functionName, Vlookup.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns exact match result`() {
        val table = "[['a', 1], ['b', 2], ['c', 3]]"
        val expression = "\$VLOOKUP('b', $table, 2, false)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(2)
    }

    @Test
    fun `returns approximate match result`() {
        val table = "[[1, 'one'], [3, 'three'], [5, 'five']]"
        val expression = "\$VLOOKUP(4, $table, 2, true)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("three")
    }

    @Test
    fun `throws error for exact match not found`() {
        val table = "[['a', 1], ['b', 2]]"
        val expression = "\$VLOOKUP('c', $table, 2, false)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(null)
        }
    }
}
