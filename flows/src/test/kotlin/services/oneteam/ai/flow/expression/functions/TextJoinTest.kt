package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class TextJoinTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(TextJoin.functionName, TextJoin.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `joins text with space delimiter`() {
        val expression = "\$TEXTJOIN(\" \", \"true\", \"The\", \"sun\", \"will\", \"come\", \"up\", \"tomorrow.\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "The sun will come up tomorrow."
    }

    @Test
    fun `joins text with comma delimiter`() {
        val expression = "\$TEXTJOIN(\",\", \"false\", \"apple\", \"orange\", \"banana\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "apple,orange,banana"
    }

    @Test
    fun `handles empty strings when ignoreEmpty is false`() {
        val expression = "\$TEXTJOIN(\"-\", \"false\", \"apple\", \"\", \"banana\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "apple--banana"
    }

    @Test
    fun `ignores empty strings when ignoreEmpty is true`() {
        val expression = "\$TEXTJOIN(\"-\", \"true\", \"apple\", \"\", \"banana\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "apple-banana"
    }

    @Test
    fun `handles array of values`() {
        val expression = "\$TEXTJOIN(\", \", \"false\", [\"apple\", \"orange\", \"banana\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "apple, orange, banana"
    }

    @Test
    fun `handles array with empty values when ignoreEmpty is false`() {
        val expression = "\$TEXTJOIN(\",\", \"false\", [\"apple\", \"\", \"banana\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "apple,,banana"
    }

    @Test
    fun `handles array with empty values when ignoreEmpty is true`() {
        val expression = "\$TEXTJOIN(\",\", \"true\", [\"apple\", \"\", \"banana\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "apple,banana"
    }

    @Test
    fun `handles empty delimiter`() {
        val expression = "\$TEXTJOIN(\"\", \"false\", \"a\", \"b\", \"c\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "abc"
    }

    @Test
    fun `handles multi-character delimiter`() {
        val expression = "\$TEXTJOIN(\" -- \", \"false\", \"apple\", \"orange\", \"banana\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "apple -- orange -- banana"
    }

    @Test
    fun `handles special characters in delimiter`() {
        val expression = "\$TEXTJOIN(\".*.\", \"false\", \"apple\", \"orange\", \"banana\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "apple.*.orange.*.banana"
    }

    @Test
    fun `throws exception when insufficient arguments are provided`() {
        val expression = "\$TEXTJOIN(\",\", \"true\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(null)
        }.message shouldBe "TEXTJOIN requires at least 3 arguments"
    }

    @Test
    fun `joins numbers and other data types`() {
        val expression = "\$TEXTJOIN(\"-\", \"false\", 1, 2.5, true, \"text\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe "1-2.5-true-text"
    }

    @Test
    fun `returns empty string when no items to join after ignoring empty`() {
        val expression = "\$TEXTJOIN(\",\", \"true\", \"\", \"\", \"\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as String
        result shouldBe ""
    }

    @Test
    fun `returns empty string when no text items provided`() {
        val expression = "\$TEXTJOIN(\",\", \"false\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(null)
        }.message shouldBe "TEXTJOIN requires at least 3 arguments"
    }

    @Test
    fun `works with contextual data`() {
        val data = mapOf(
            "separator" to ", ",
            "items" to listOf("apple", "orange", "banana")
        )
        val expression = "\$TEXTJOIN(separator, \"false\", items)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(data) as String
        result shouldBe "apple, orange, banana"
    }

    @Test
    fun `handles multiple lists as arguments`() {
        val expression = "\$TEXTJOIN(\", \", \"false\", [\"apple\", \"orange\"], [\"banana\", \"kiwi\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result shouldBe listOf("apple, banana", "orange, kiwi")
    }

    @Test
    fun `handles multiple lists with ignoreEmpty=true`() {
        val expression = "\$TEXTJOIN(\", \", \"true\", [\"apple\", \"\"], [\"\", \"kiwi\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result shouldBe listOf("apple", "kiwi")
    }

    @Test
    fun `handles mix of empty and non-empty lists`() {
        val expression = "\$TEXTJOIN(\"-\", \"false\", [], [\"apple\"], [], [\"banana\", \"orange\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result shouldBe listOf("-apple--banana", "---orange")
    }

    @Test
    fun `handles empty lists with ignoreEmpty=false`() {
        val expression = "\$TEXTJOIN(\"-\", \"false\", [\"apple\"], [], [\"banana\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result shouldBe listOf("apple--banana")
    }

    @Test
    fun `handles lists with mixed data types`() {
        val expression = "\$TEXTJOIN(\"|\", \"false\", [1, 2.5], [true, false], [\"text\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result shouldBe listOf("1|true|text", "2.5|false|")
    }

    @Test
    fun `handles complex mix of single values and lists`() {
        val expression = "\$TEXTJOIN(\" \", \"true\", \"Start:\", [\"item1\", \"\"], \"Middle:\", [\"item2\", \"item3\"], \"End\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result shouldBe listOf("Start: item1 Middle: item2 End", "item3")
    }

    @Test
    fun `handles large number of list arguments`() {
        val expression = "\$TEXTJOIN(\",\", \"false\", [\"1\"], [\"2\"], [\"3\"], [\"4\"], [\"5\"], [\"6\"], [\"7\"], [\"8\"], [\"9\"], [\"10\"])"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result shouldBe listOf("1,2,3,4,5,6,7,8,9,10")
    }
}