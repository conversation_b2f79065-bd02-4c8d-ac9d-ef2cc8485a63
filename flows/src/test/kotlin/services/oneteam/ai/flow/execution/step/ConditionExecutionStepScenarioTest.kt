package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.DefaultMapBuilder
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.conditional.Operator
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ConditionExecutionStepScenarioTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    data class Spec(
        val context: FlowContext,
        val nextStepId: FlowExecution.Step.Id,
        val variable: VariableInstance,
        val stepConfiguration: FlowExecution.Step,
    )

    fun provider(): Stream<Spec> {
        return Stream.of(
            // 1
            Spec(
                FlowContext(
                    global = Fixtures.global,
                    workspace = Fixtures.workspaceContext,
                    event = Fixtures.event
                ),
                FlowExecution.Step.Id("next2"),
                VariableInstance.Variable(
                    JsonPrimitive("START"),
                    VariableDataType.TEXT,
                    "answer"
                ),
                ConditionFixtures.condition(
                    ConditionFixtures.branch("{{answer}}", Operator.CONTAINS, "\"START\"", "next2"), "next1"
                )
            ),
            // 2
            Spec(
                FlowContext(
                    global = Fixtures.global,
                    workspace = Fixtures.workspaceContext,
                    event = Fixtures.event
                ),
                FlowExecution.Step.Id("next1"),
                VariableInstance.Variable(
                    JsonPrimitive("END"),
                    VariableDataType.TEXT,
                    "answer"
                ),
                ConditionFixtures.condition(
                    ConditionFixtures.branch("{{answer}}", Operator.CONTAINS, "\"START\"", "next2"), "next1"
                )
            ),
            // 3
            Spec(
                FlowContext(
                    global = Fixtures.global,
                    workspace = Fixtures.workspaceContext,
                    event = Fixtures.event
                ),
                FlowExecution.Step.Id("next2"),
                VariableInstance.Variable(
                    JsonPrimitive("END"),
                    VariableDataType.TEXT,
                    "answer"
                ),
                ConditionFixtures.condition(
                    ConditionFixtures.branch("{{answer}}", Operator.IS_NOT_EMPTY, null, "next2"), "next1"
                )
            ),
            // 4
            // commented out until IS_NOT_EMPTY supports numbers
//            Spec(
//                FlowContext(
//                    global = Fixtures.global,
//                    event = Fixtures.event
//                ),
//                FlowExecution.Step.Id("next2"),
//                Variable(
//                    JsonPrimitive("2"),
//                    "number",
//                    "answer"
//                ),
//                ConditionFixtures.condition(
//                    ConditionFixtures.branch("{{answer}}", Operator.IS_NOT_EMPTY, null, "next2"), "next1"
//                )
//            )
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `should correctly evaluate condition`(spec: Spec) = runTest {
        // prepare
        logger.debug("Step configuration: {}", spec.stepConfiguration)

        val step = ConditionExecutionStep(
            spec.stepConfiguration,
            ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "testCacheKey"),
            JsonataExpressionEvaluator()
        )

        var stepContext = FlowContextWithLocalStep(
            spec.stepConfiguration.id,
            spec.context,
            mutableMapOf(),
        )
        stepContext.flowContext.set(spec.variable)

        // perform
        step.populate(stepContext)
        val next = step.execute(stepContext)

        // verify
        Assertions.assertEquals(spec.nextStepId, next)
    }
}
