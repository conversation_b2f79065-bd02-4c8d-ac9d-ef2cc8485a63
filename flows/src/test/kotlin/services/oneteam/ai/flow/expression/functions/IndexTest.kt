package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class IndexTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Index.functionName, Index.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `finds item in 1D array`() {
        val jsonata = jsonataWithFunctionRegistered("\$INDEX([1, 2, 3], 1)")
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `finds item in 1D array as strings`() {
        val jsonata = jsonataWithFunctionRegistered("\$INDEX([1, 2, 3], \"1\")")
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `finds a string item in 1D array`() {
        val jsonata = jsonataWithFunctionRegistered("\$INDEX([1, \"2\", 3], 1)")
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `fails to find index`() {
        val jsonata = jsonataWithFunctionRegistered("\$INDEX([1, \"2\", 3], 5)")
        val result = jsonata.evaluate(null)
        result shouldBe "#REF!"
    }
}