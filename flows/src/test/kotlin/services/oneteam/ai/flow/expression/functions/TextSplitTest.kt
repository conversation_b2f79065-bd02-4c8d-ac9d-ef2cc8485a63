package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class TextSplitTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(TextSplit.functionName, TextSplit.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `splits text with column delimiter only`() {
        val expression = "\$TEXTSPLIT(\"apple,orange,banana\", \",\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("apple", "orange", "banana"))
    }

    @Test
    fun `splits text with both column and row delimiters`() {
        val expression = "\$TEXTSPLIT(\"apple,orange,banana;kiwi,melon,peach\", \",\", \";\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<List<*>>
        result.size shouldBe 2
        result[0].shouldContainExactly(listOf("apple", "orange", "banana"))
        result[1].shouldContainExactly(listOf("kiwi", "melon", "peach"))
    }

    @Test
    fun `handles empty cells when ignoreEmpty is false`() {
        val expression = "\$TEXTSPLIT(\"apple,,banana\", \",\", null, \"FALSE\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("apple", "", "banana"))
    }

    @Test
    fun `ignores empty cells when ignoreEmpty is true`() {
        val expression = "\$TEXTSPLIT(\"apple,,banana\", \",\", null, \"TRUE\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("apple", "banana"))
    }

    @Test
    fun `performs case-sensitive matching by default`() {
        val expression = "\$TEXTSPLIT(\"appleCOrangeabanana\", \"a\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("", "ppleCOr", "nge", "b", "n", "n", ""))
    }

    @Test
    fun `performs case-insensitive matching when matchMode is 1`() {
        val expression = "\$TEXTSPLIT(\"appleCOrangeAbanana\", \"a\", null, \"FALSE\", 1)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        // Should also match uppercase 'A'
        result.shouldContainExactly(listOf("", "ppleCOr", "nge", "b", "n", "n", ""))
    }

    @Test
    fun `pads rows with different column counts`() {
        val expression = "\$TEXTSPLIT(\"apple,orange,banana;kiwi,melon\", \",\", \";\", \"FALSE\", 0, \"EMPTY\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<List<*>>
        result.size shouldBe 2
        result[0].shouldContainExactly(listOf("apple", "orange", "banana"))
        result[1].shouldContainExactly(listOf("kiwi", "melon", "EMPTY"))
    }

    @Test
    fun `handles text with consecutive delimiters`() {
        val expression = "\$TEXTSPLIT(\"apple,,banana,,orange\", \",\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("apple", "", "banana", "", "orange"))
    }

    @Test
    fun `handles text with consecutive row delimiters`() {
        val expression = "\$TEXTSPLIT(\"apple,orange;;banana,kiwi\", \",\", \";\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<List<*>>
        result.size shouldBe 3  // Including empty row
        result[0].shouldContainExactly(listOf("apple", "orange"))
        result[1].shouldContainExactly(listOf(""))
        result[2].shouldContainExactly(listOf("banana", "kiwi"))
    }

    @Test
    fun `handles multi-character delimiters`() {
        val expression = "\$TEXTSPLIT(\"apple---orange---banana\", \"---\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("apple", "orange", "banana"))
    }

    @Test
    fun `handles special characters in delimiters`() {
        val expression = "\$TEXTSPLIT(\"apple.*.orange.*.banana\", \".*.\", null, \"FALSE\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("apple", "orange", "banana"))
    }

    @Test
    fun `handles empty input text`() {
        val expression = "\$TEXTSPLIT(\"\", \",\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf(""))
    }

    @Test
    fun `handles text with no delimiters found`() {
        val expression = "\$TEXTSPLIT(\"apple\", \",\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<*>
        result.shouldContainExactly(listOf("apple"))
    }

    @Test
    fun `throws exception when columnDelimiter argument is missing`() {
        val expression = "\$TEXTSPLIT(\"apple,orange,banana\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        shouldThrow<IllegalArgumentException> {
            jsonataExpression.evaluate(null)
        }.message shouldBe "columnDelimiter argument is missing"
    }

    @Test
    fun `handles complex input with mixed delimiters and all parameters`() {
        val expression = "\$TEXTSPLIT(\"Apple,ORANGE,banana;Kiwi,,Peach\", \",\", \";\", \"TRUE\", 1, \"NONE\")"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null) as List<List<*>>
        result.size shouldBe 2
        result[0].shouldContainExactly(listOf("Apple", "ORANGE", "banana"))
        result[1].shouldContainExactly(listOf("Kiwi", "Peach", "NONE"))
    }

    @Test
    fun `returns correct results with contextual data`() {
        val data = mapOf(
            "text" to "apple,orange,banana",
            "colDelim" to ","
        )
        val expression = "\$TEXTSPLIT(text, colDelim)"
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(data) as List<*>
        result.shouldContainExactly(listOf("apple", "orange", "banana"))
    }
}