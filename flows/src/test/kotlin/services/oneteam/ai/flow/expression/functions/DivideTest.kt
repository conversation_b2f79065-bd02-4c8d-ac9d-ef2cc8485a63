package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DivideTest {
    private val fn = Divide

    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(fn.functionName, fn.evaluatorFunction)
        return jsonataExpression
    }

    data class Spec(
        val input: String,
        val expected: Any,
    )

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName(1, 1)", BigDecimal(1)),
            Spec("$$functionName([1,2], 1)", listOf(BigDecimal(1), BigDecimal(2))),
            Spec("$$functionName([1,2], [1,2])", listOf(BigDecimal(1), BigDecimal(1))),
            Spec("$$functionName([1,2], [1,0])", listOf(BigDecimal(1), null)),
            Spec(
                "$$functionName([[1,2],[3,4]], 2)", listOf(
                    listOf(BigDecimal("0.5"), BigDecimal("1")), listOf(
                        BigDecimal("1.5"), BigDecimal("2")
                    )
                )
            ),
            Spec(
                "$$functionName([[1,2],[3,4]], 0)",
                listOf(listOf(null, null), listOf(null, null))
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `equals test`(spec: Spec) {
        val jsonataExpression = jsonataWithFunctionRegistered(spec.input)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(spec.expected)
    }

}