package services.oneteam.ai.flow.expression.functions

import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DivideTest {
    private val fn = Divide

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName(1, 1)", BigDecimal(1)),
            Spec(
                "$$functionName([])",
                null,
                exception = InvalidParametersException::class,
                "Single list is not valid"
            ),
            Spec("$$functionName([1,2], 1)", listOf(BigDecimal(1), BigDecimal(2))),
            Spec("$$functionName([1,2], [1,2])", listOf(BigDecimal(1), BigDecimal(1))),
            Spec("$$functionName([1,2], [1,0])", listOf(BigDecimal(1), null)),
            Spec(
                "$$functionName([[10,8],[6,4]], 2)", listOf(
                    listOf(BigDecimal("5"), BigDecimal("4")), listOf(
                        BigDecimal("3"), BigDecimal("2")
                    )
                )
            ),
            Spec(
                "$$functionName([[1,2],[3,4]], 2)", listOf(
                    listOf(BigDecimal("0.5"), BigDecimal("1")), listOf(
                        BigDecimal("1.5"), BigDecimal("2")
                    )
                )
            ),
            Spec(
                "$$functionName([[1,2],[3,4]], 0)",
                listOf(listOf(null, null), listOf(null, null))
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        functionTest(fn, spec)
    }

}