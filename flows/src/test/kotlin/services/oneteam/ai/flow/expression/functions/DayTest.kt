package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.time.format.DateTimeParseException

class DayTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Day.functionName, Day.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return the day of the month`() {
        val expression = "\$DAY(\"2021-01-01\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `should return the day of the month for a date in a different timezone`() {
        val expression = "\$DAY(\"2021-01-01\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `should throw an error when the input is not a date`() {
        shouldThrow<DateTimeParseException> {
        val expression = "\$DAY(\"hello world\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
            jsonata.evaluate(null)
        }
    }
}