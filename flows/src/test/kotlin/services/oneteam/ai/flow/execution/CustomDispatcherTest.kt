package services.oneteam.ai.flow.execution

import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.CoroutineContext
import kotlin.random.Random
import kotlin.time.Duration.Companion.milliseconds

class CustomDispatcherTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    val maxConcurrentCoroutines = 8

    private val customDispatcher =
        CustomDispatcher("test", numberOfThreads = 2, maxConcurrentCoroutines = maxConcurrentCoroutines)

    @AfterEach
    fun tearDown() {
        customDispatcher.shutdown()
    }

    /*

    From the log we can see:
    - We have 2 threads (test-1 and test-2)
    - Multiple coroutines per thread
    - Concurrency is limited to as per configuration
    - We immediately submit 16 tasks, but only 8 run concurrently
    - The rest are queued until one of the running tasks completes

    2025-05-23 10:26:29.426 [] [Test worker] INFO  s.o.a.f.execution.CustomDispatcher - Starting test dispatcher with 2 threads and max concurrency of 8
    2025-05-23 10:26:29.435 [] [test-1 @coroutine#3] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-1 @coroutine#3
    2025-05-23 10:26:29.435 [] [test-2 @coroutine#4] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-2 @coroutine#4
    2025-05-23 10:26:29.435 [] [Test worker @coroutine#2] DEBUG s.o.a.f.e.CustomDispatcherTest - 16 tasks started
    2025-05-23 10:26:29.441 [] [test-2 @coroutine#5] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-2 @coroutine#5
    2025-05-23 10:26:29.441 [] [test-1 @coroutine#6] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-1 @coroutine#6
    2025-05-23 10:26:29.441 [] [test-2 @coroutine#7] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-2 @coroutine#7
    2025-05-23 10:26:29.442 [] [test-1 @coroutine#8] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-1 @coroutine#8
    2025-05-23 10:26:29.442 [] [test-2 @coroutine#9] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-2 @coroutine#9
    2025-05-23 10:26:29.442 [] [test-1 @coroutine#10] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-1 @coroutine#10
    2025-05-23 10:26:29.490 [] [test-2 @coroutine#9] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-2 @coroutine#9
    2025-05-23 10:26:29.490 [] [test-2 @coroutine#9] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 7 Completed: 1
    2025-05-23 10:26:29.492 [] [test-1 @coroutine#11] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-1 @coroutine#11
    2025-05-23 10:26:29.614 [] [test-2 @coroutine#8] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-2 @coroutine#8
    2025-05-23 10:26:29.615 [] [test-2 @coroutine#8] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 7 Completed: 2
    2025-05-23 10:26:29.615 [] [test-1 @coroutine#12] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-1 @coroutine#12
    2025-05-23 10:26:29.621 [] [test-2 @coroutine#3] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-2 @coroutine#3
    2025-05-23 10:26:29.621 [] [test-2 @coroutine#3] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 7 Completed: 3
    2025-05-23 10:26:29.622 [] [test-1 @coroutine#13] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-1 @coroutine#13
    2025-05-23 10:26:29.635 [] [test-1 @coroutine#13] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-1 @coroutine#13
    2025-05-23 10:26:29.636 [] [test-1 @coroutine#13] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 7 Completed: 4
    2025-05-23 10:26:29.636 [] [test-2 @coroutine#14] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-2 @coroutine#14
    2025-05-23 10:26:29.826 [] [test-1 @coroutine#4] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-1 @coroutine#4
    2025-05-23 10:26:29.826 [] [test-1 @coroutine#4] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 7 Completed: 5
    2025-05-23 10:26:29.826 [] [test-2 @coroutine#16] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-2 @coroutine#16
    2025-05-23 10:26:29.858 [] [test-1 @coroutine#11] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-1 @coroutine#11
    2025-05-23 10:26:29.859 [] [test-1 @coroutine#11] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 7 Completed: 6
    2025-05-23 10:26:29.859 [] [test-2 @coroutine#15] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-2 @coroutine#15
    2025-05-23 10:26:29.982 [] [test-1 @coroutine#10] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-1 @coroutine#10
    2025-05-23 10:26:29.982 [] [test-1 @coroutine#10] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 7 Completed: 7
    2025-05-23 10:26:29.983 [] [test-2 @coroutine#17] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-2 @coroutine#17
    2025-05-23 10:26:30.021 [] [test-1 @coroutine#6] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-1 @coroutine#6
    2025-05-23 10:26:30.021 [] [test-1 @coroutine#6] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 7 Completed: 8
    2025-05-23 10:26:30.021 [] [test-2 @coroutine#18] DEBUG s.o.a.f.e.CustomDispatcherTest - Task starting: test-2 @coroutine#18
    2025-05-23 10:26:30.049 [] [test-1 @coroutine#16] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-1 @coroutine#16
    2025-05-23 10:26:30.050 [] [test-1 @coroutine#16] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 7 Completed: 9
    2025-05-23 10:26:30.134 [] [test-2 @coroutine#5] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-2 @coroutine#5
    2025-05-23 10:26:30.135 [] [test-2 @coroutine#5] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 6 Completed: 10
    2025-05-23 10:26:30.161 [] [test-1 @coroutine#7] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-1 @coroutine#7
    2025-05-23 10:26:30.161 [] [test-1 @coroutine#7] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 5 Completed: 11
    2025-05-23 10:26:30.248 [] [test-2 @coroutine#15] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-2 @coroutine#15
    2025-05-23 10:26:30.248 [] [test-2 @coroutine#15] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 4 Completed: 12
    2025-05-23 10:26:30.277 [] [test-1 @coroutine#12] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-1 @coroutine#12
    2025-05-23 10:26:30.278 [] [test-1 @coroutine#12] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 3 Completed: 13
    2025-05-23 10:26:30.541 [] [test-2 @coroutine#18] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-2 @coroutine#18
    2025-05-23 10:26:30.541 [] [test-2 @coroutine#18] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 2 Completed: 14
    2025-05-23 10:26:30.554 [] [test-1 @coroutine#17] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-1 @coroutine#17
    2025-05-23 10:26:30.554 [] [test-1 @coroutine#17] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 1 Completed: 15
    2025-05-23 10:26:30.567 [] [test-2 @coroutine#14] DEBUG s.o.a.f.e.CustomDispatcherTest - Task completed: test-2 @coroutine#14
    2025-05-23 10:26:30.567 [] [test-2 @coroutine#14] DEBUG s.o.a.f.e.CustomDispatcherTest - Running: 0 Completed: 16
    2025-05-23 10:26:30.568 [] [Test worker @coroutine#2] DEBUG s.o.a.f.e.CustomDispatcherTest - Current running log: [2, 1, 3, 4, 5, 6, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 6, 5, 4, 3, 2, 1, 0]
    2025-05-23 10:26:30.570 [] [Test worker] INFO  s.o.a.f.execution.CustomDispatcher - Shutting down test dispatcher
    2025-05-23 10:26:30.571 [] [Test worker] INFO  s.o.a.f.execution.CustomDispatcher - Shut down test dispatcher complete
    */
    @Test
    fun `should run tasks with limited concurrency`() = runBlocking {
        val runningTasks = AtomicInteger(0)
        val completedTasks = AtomicInteger(0)
        val currentRunningLog: MutableList<Int> = Collections.synchronizedList(mutableListOf<Int>())

        // create more tasks than the maxConcurrentCoroutines so we see the concurrency limit in action
        val tasksToStart = maxConcurrentCoroutines * 2

        val tasks = List(tasksToStart) {
            customDispatcher.run(TestContext("should run tasks with limited concurrency")) {
                logger.debug("Task starting: ${Thread.currentThread().name}")
                runningTasks.incrementAndGet()

                // log number of running tasks so we can see the concurrency limit in action
                currentRunningLog.add(runningTasks.get())

                assert(runningTasks.get() <= maxConcurrentCoroutines) { "More than $maxConcurrentCoroutines tasks running concurrently" }

                // Simulate work
                val delay = Random.nextInt(10, 1000)
                delay(delay.milliseconds)

                runningTasks.decrementAndGet()
                completedTasks.incrementAndGet()

                // log number of running tasks so we can see the concurrency limit in action
                currentRunningLog.add(runningTasks.get())

                logger.debug("Task completed: ${Thread.currentThread().name}")
                logger.debug("Running: {} Completed: {}", runningTasks, completedTasks)
            }
        }

        // shows that all tasks have been submitted, but not all have started
        logger.debug("$tasksToStart tasks started")
        // wait for all to finish
        tasks.forEach { it.join() }

        // shows that tasks running ramps up to maxConcurrentCoroutines, then ramps down to 0
        // Current running log: [1, 2, 3, 4, 5, 6, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 8, 7, 6, 5, 4, 3, 2, 1, 0]
        logger.debug("Current running log: {}", currentRunningLog)

        assertEquals(tasksToStart, completedTasks.get(), "Not all tasks completed")
    }

    @Test
    fun `should shut down properly`() {
        customDispatcher.shutdown()
        assert(customDispatcher.isShutdown()) { "Thread pool was not shut down" }
    }
}

data class TestContext(val id: String) : CoroutineContext.Element {
    companion object Key : CoroutineContext.Key<TestContext>

    override val key: CoroutineContext.Key<*> get() = Key
}