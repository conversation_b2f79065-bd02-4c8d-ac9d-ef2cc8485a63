package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class RoundUpTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(RoundUp.functionName, RoundUp.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should round up a number to a specified number of decimal places`() {
        val expression = "\$ROUNDUP(1.23456789, 2)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 1.24
    }

    @Test
    fun `should round up a number to a whole number`() {
        val expression = "\$ROUNDUP(1.23456789)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 2
    }

    @Test
    fun `should round up a number to a whole number as string`() {
        val expression = "\$ROUNDUP(\"1.23456789\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 2
    }
}