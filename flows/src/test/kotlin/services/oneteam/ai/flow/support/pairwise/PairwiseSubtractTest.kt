package services.oneteam.ai.flow.support.pairwise

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.math.BigDecimal

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PairwiseSubtractTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    data class Scenario(
        val name: String, val values: List<PairwiseValue>, val expected: PairwiseValue
    )

    fun provider(): List<Scenario> {
        return listOf(
            Scenario(
                "case 0: [[1, 2], [3, 4]] - [[5, 6], [7, 8]] = [[-4.0, -4.0], [-4.0, -4.0]]",
                listOf(
                    PairwiseValue.of(listOf(listOf(1, 2), listOf(3, 4))),
                    PairwiseValue.of(listOf(listOf(5, 6), listOf(7, 8))),
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(BigDecimal("-4"), BigDecimal("-4")),
                        listOf(BigDecimal("-4"), BigDecimal("-4"))
                    )
                )
            ),
        )
    }


    @ParameterizedTest
    @MethodSource("provider")
    fun `scenario `(scenario: Scenario) {

        logger.debug("Scenario: {}", scenario.name)
        logger.debug("Values: {}", scenario.values)
        logger.debug("Expected: {}", scenario.expected)
        Pairwise(PairwiseOperation.SUBTRACT).pairwise(scenario.values).let { it1 ->
            logger.debug("Pairwise result: {}", it1)
            logger.debug("{} = {}", scenario.values.joinToString(" - ") { it.value.toString() }, it1.value)
            assertThat(it1).isEqualTo(scenario.expected)
        }
    }

}