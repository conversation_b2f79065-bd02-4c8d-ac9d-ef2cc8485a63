package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class MatchTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Match.functionName, Match.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `finds index of element in 1D array`() {
        val jsonata = jsonataWithFunctionRegistered("\$MATCH(2, [1, 2, 3], 0)")
        val result = jsonata.evaluate(null)
        result shouldBe 2
    }

    @Test
    fun `finds index of element in 1D array with match mode 1`() {
        val jsonata = jsonataWithFunctionRegistered("\$MATCH(2, [1, 3], 1)")
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }

    @Test
    fun `finds index of element in 1D array with match mode -1`() {
        val jsonata = jsonataWithFunctionRegistered("\$MATCH(2, [1, 3], -1)")
        val result = jsonata.evaluate(null)
        result shouldBe 2
    }

    @Test
    fun `finds index of string element in 1D array with match mode -1`() {
        val jsonata = jsonataWithFunctionRegistered("\$MATCH(\"2\", [1, 2], 0)")
        val result = jsonata.evaluate(null)
        result shouldBe 2
    }

    @Test
    fun `finds index of string element in 1D array`() {
        val jsonata = jsonataWithFunctionRegistered("\$MATCH(\"hello\", [\"hello\", \"goodbye\"], 0)")
        val result = jsonata.evaluate(null)
        result shouldBe 1
    }
}