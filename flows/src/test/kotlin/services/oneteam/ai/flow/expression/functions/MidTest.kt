package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class MidTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Mid.functionName, Mid.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return the middle characters of a string`() {
        val expression = "\$MID('Hello, world!', 8, 5)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "world"
    }

    @Test
    fun `should return the middle characters of a string when the length is greater than the string`() {
        val expression = "\$MID('Hello', 1, 10)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello"
    }

    @Test
    fun `should return the middle characters of a string when the length is equal to the string`() {
        val expression = "\$MID('Hello', 1, 5)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe "Hello"
    }

    @Test
    fun `should return an empty string when the length is 0`() {
        val expression = "\$MID('Hello', 1, 0)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe ""
    }

    @Test
    fun `should return an empty string when the string is empty`() {
        val expression = "\$MID('', 1, 5)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe ""
    }
}