package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class MultiplyTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Multiply.functionName, Multiply.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns product of two integers`() {
        val expression = "\$MULTIPLY(2, 3)";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(BigDecimal("6"))
    }

    @Test
    fun `returns product of two doubles`() {
        val expression = "\$MULTIPLY(2.5, 3.5)";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(BigDecimal("8.75"))
    }

    @Test
    fun `returns product of two negative numbers`() {
        val expression = "\$MULTIPLY(-2, -3)";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(BigDecimal("6"))
    }

    @Test
    fun `multiply two arrays together`() {
        val expression = "\$MULTIPLY([1, 2, 3], [4, 5, 6])"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe listOf(BigDecimal(4), BigDecimal(10), BigDecimal(18))
    }

    @Test
    fun `multiply two uneven arrays together`() {
        val expression = "\$MULTIPLY([1, 2, 3, 5], [4, 5, 6])"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe listOf(BigDecimal("4"), BigDecimal("10"), BigDecimal("18"), BigDecimal("0"))
    }
}