package services.oneteam.ai.flow.expression.functions

import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MultiplyTest {

    private val fn = Multiply

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            // use cases from ticket https://oneteam-services.atlassian.net/browse/OA-1900
            Spec("$$functionName(1, 2, 3)", BigDecimal(6)),
            Spec("$$functionName([1, 2, 3])", BigDecimal(6)),
            Spec("$$functionName([1, 2, 3], [1, 2, 3])", listOf(BigDecimal(1), BigDecimal(4), BigDecimal(9))),
            Spec(
                "$$functionName([[1,2,3],[1,2,3]], [[5,6,7],[5,6,7]])",
                listOf(
                    listOf(BigDecimal(5), BigDecimal(12), BigDecimal(21)),
                    listOf(BigDecimal(5), BigDecimal(12), BigDecimal(21))
                )
            ),
            Spec("$$functionName([1,2,3], [1,2])", listOf(BigDecimal(1), BigDecimal(4), BigDecimal(0))),
            Spec("$$functionName([1,2,3], 4, [[5,6]])", listOf(listOf(BigDecimal(20), BigDecimal(48), BigDecimal(0)))),

            // others
            Spec("$$functionName(2.5, 3.5)", BigDecimal(8.75)),
            Spec("$$functionName(-2, -3)", BigDecimal(6)),
            Spec(
                "$$functionName([1, 2, 3, 5], [4, 5, 6])",
                listOf(BigDecimal("4"), BigDecimal("10"), BigDecimal("18"), BigDecimal("0"))
            ),
            Spec(
                "$$functionName([[1,2],[3,4]], 2)", listOf(
                    listOf(BigDecimal("2"), BigDecimal("4")), listOf(
                        BigDecimal("6"), BigDecimal("8")
                    )
                )
            ),
            Spec(
                "$$functionName([[1,2],[3,4]], 0)",
                listOf(listOf(BigDecimal.ZERO, BigDecimal.ZERO), listOf(BigDecimal.ZERO, BigDecimal.ZERO))
            ),
            Spec(
                "$$functionName([[1,2],[3,4]], 0)",
                listOf(listOf(BigDecimal.ZERO, BigDecimal.ZERO), listOf(BigDecimal.ZERO, BigDecimal.ZERO))
            )
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        functionTest(fn, spec)
    }

}