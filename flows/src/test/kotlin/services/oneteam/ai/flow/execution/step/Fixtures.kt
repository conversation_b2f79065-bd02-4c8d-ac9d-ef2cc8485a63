package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import services.oneteam.ai.flow.execution.DefaultMapBuilder
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.MapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.FormJsonMapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.FoundationJsonMapBuilder
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.otSerializer

object Fixtures {

    val global = FlowContext.GlobalVariables(
        Workspace.Id(1),
        WorkspaceVersion.Id(1),
        1,
        FlowConfiguration.Id("flow-id"),
        FlowConfiguration.Name("flow-name"),
    )

    val workspaceContext = FlowContext.WorkspaceContext(
        documentId = Workspace.DocumentId("1"),
        id = Workspace.Id(1),
        key = Workspace.Key("WORKSPACE1"),
        name = Workspace.Name("workspace1"),
        workspaceFoundationId = Foundation.Id(1),
        variables = emptyMap()
    )

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            "string",
            null,
            null,
        ), Event.Id("1"), 1
    )

    fun context(): FlowContextWithLocalStep {
        return FlowContextWithLocalStep(
            Step.Id("1"), FlowContext(
                global = global,
                workspace = workspaceContext,
                variables = mutableMapOf(),
                event = event,
            )
        )
    }

    fun contextWithVars(): FlowContextWithLocalStep {
        return FlowContextWithLocalStep(
            Step.Id("1"), FlowContext(
                global = global,
                workspace = workspaceContext,
                variables = mutableMapOf(
                    "numberVar" to Vars.numberVar,
                    "stringVar" to Vars.stringVar,
                    "booleanVar" to Vars.booleanVar,
                    "listVar" to Vars.listVar,
                    "objectVar" to Vars.objectVar,
                ),
                event = event,
            )
        )
    }

    object Vars {
        val numberVar = VariableInstance.Variable(
            JsonPrimitive("5"), VariableDataType.NUMBER, "numberVar"
        )

        val stringVar = VariableInstance.Variable(
            JsonPrimitive("string literal"), VariableDataType.TEXT, "stringVar"
        )

        val booleanVar = VariableInstance.Variable(
            JsonPrimitive(true), VariableDataType.BOOLEAN, "booleanVar"
        )

        val listVar = VariableInstance.Variable(
            JsonArray(listOf(JsonPrimitive(1), JsonPrimitive(2), JsonPrimitive(3))), VariableDataType.LIST, "listVar"
        )

        val objectVar = VariableInstance.Variable(
            JsonObject(mapOf("key" to JsonPrimitive("value"))), VariableDataType.JSON, "objectVar"
        )

        val type = VariableInstance.Variable(
            JsonPrimitive("text"), VariableDataType.TEXT, "type"
        )

        val identifier = VariableInstance.Variable(
            JsonPrimitive("output"), VariableDataType.TEXT, "identifier"
        )

        val form = VariableInstance.Variable(
            JsonPrimitive(1), VariableDataType.fromString("form.aqt"), "form"
        )

    }

    object Context {

        suspend fun buildMapBuilders(): List<MapBuilder> {
            val foundationService = mock(FoundationService::class.java)
            val formService = mock(FormService::class.java)
            val documentService = mock(IDocumentService::class.java)
            val cookie = "cookie"

            val workspaceForJson = otSerializer.decodeFromString(
                Workspace.ForJson.serializer(),
                this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
            )

            val workspaceId = Workspace.Id(1)

            val foundation = Foundation.ForApi(
                Foundation.Id(4),
                Foundation.Name("foundationName"),
                Foundation.Key("foundationKey"),
                FoundationConfiguration.Id("aQXFmGHm2w1LUvSVi9bjw"),
                workspaceId,
                Foundation.Id(5),
                EntityMetadata.now()
            )

            val formConfiguration = workspaceForJson.findForm(FormConfiguration.Id("aqt"))

            val formForApi = Form.ForApi(
                Form.Id(1),
                FormConfiguration.Id(formConfiguration.id),
                foundation.id,
                if (!formConfiguration.seriesId.isNullOrBlank()) SeriesConfiguration.Id(formConfiguration.seriesId!!) else null,
                if (!formConfiguration.seriesId.isNullOrBlank()) IntervalId("2024") else null,
                Form.DocumentId("documentId"),
                Form.AnnotationDocumentId("annotationDocumentId"),
                workspaceId,
                EntityMetadata.now(),
                foundation
            )

            val formAnswerForJson = otSerializer.decodeFromString(
                FormAnswer.ForJson.serializer(),
                this::class.java.getResource("/sample-data/sgpit/sgpit-answers-aqt.json")!!.readText()
            )

            `when`(formService.get(Form.Id(1))).thenReturn(formForApi)
            `when`(foundationService.get(foundation.id)).thenReturn(foundation)


            `when`(
                documentService.show<FormAnswer.ForJson>(
                    formForApi.documentId!!.value, cookie, FormAnswer.ForJson::class, true
                )
            ).thenReturn(
                formAnswerForJson
            )

            return listOf(
                FoundationJsonMapBuilder(foundationService, workspaceForJson), FormJsonMapBuilder(
                    formService, foundationService, documentService, cookie, workspaceForJson
                ), DefaultMapBuilder() // must come last
            )

        }

    }
}