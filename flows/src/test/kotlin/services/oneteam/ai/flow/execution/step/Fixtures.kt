package services.oneteam.ai.flow.execution.step

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.flow.execution.mapBuilders.FormJsonMapBuilder
import services.oneteam.ai.flow.execution.mapBuilders.FoundationJsonMapBuilder
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.ValueTypes
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService

object Fixtures {

    val global = GlobalVariables(
        Workspace.Id(1),
        WorkspaceVersion.Id(1),
        1,
        FlowConfiguration.Id("flow-id"),
        FlowConfiguration.Name("flow-name"),
    )

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            "string",
            null,
            null,
        ), Event.Id("1"), 1
    )

    fun context(): FlowContextWithLocalStep {
        return FlowContextWithLocalStep(
            Step.Id("1"), FlowContext(
                global = global,
                variables = mutableMapOf(),
                event = event,
            )
        )
    }

    fun contextWithVars(): FlowContextWithLocalStep {
        return FlowContextWithLocalStep(
            Step.Id("1"), FlowContext(
                global = global,
                variables = mutableMapOf(
                    "numberVar" to Vars.numberVar,
                    "stringVar" to Vars.stringVar,
                    "booleanVar" to Vars.booleanVar,
                    "listVar" to Vars.listVar,
                    "objectVar" to Vars.objectVar,
                ),
                event = event,
            )
        )
    }

    object Vars {
        val numberVar = Variable(
            JsonPrimitive("5"), ValueTypes.NUMBER, "numberVar"
        )

        val stringVar = Variable(
            JsonPrimitive("string literal"), ValueTypes.TEXT, "stringVar"
        )

        val booleanVar = Variable(
            JsonPrimitive(true), ValueTypes.BOOLEAN, "booleanVar"
        )

        val listVar = Variable(
            JsonArray(listOf(JsonPrimitive(1), JsonPrimitive(2), JsonPrimitive(3))), ValueTypes.LIST, "listVar"
        )

        val objectVar = Variable(
            JsonObject(mapOf("key" to JsonPrimitive("value"))), ValueTypes.JSON, "objectVar"
        )

        val type = Variable(
            JsonPrimitive("text"), ValueTypes.TEXT, "type"
        )

        val identifier = Variable(
            JsonPrimitive("output"), ValueTypes.TEXT, "identifier"
        )

        val form = Variable(
            JsonPrimitive(1), "form.aqt", "form"
        )

    }

    object Context {

        suspend fun buildMapBuilders(): List<MapBuilder> {
            val foundationService = mock(FoundationService::class.java)
            val formService = mock(FormService::class.java)
            val documentService = mock(IDocumentService::class.java)
            val cookie = "cookie"

            val workspaceForJson = Json.decodeFromString(
                Workspace.ForJson.serializer(),
                this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
            )

            val workspaceId = Workspace.Id(1)

            val foundation = Foundation.ForApi(
                Foundation.Id(4),
                Foundation.Name("foundationName"),
                Foundation.Key("foundationKey"),
                FoundationConfiguration.Id("wor"),
                workspaceId,
                Foundation.Id(5),
                EntityMetadata.now()
            )

            val formConfiguration = workspaceForJson.findForm(FormConfiguration.Id("aqt"))

            val formForApi = Form.ForApi(
                Form.Id(1),
                FormConfiguration.Id(formConfiguration.id),
                foundation.id,
                if (!formConfiguration.seriesId.isNullOrBlank()) SeriesConfiguration.Id(formConfiguration.seriesId!!) else null,
                if (!formConfiguration.seriesId.isNullOrBlank()) IntervalId("2024") else null,
                Form.DocumentId("documentId"),
                Form.AnnotationDocumentId("annotationDocumentId"),
                workspaceId,
                EntityMetadata.now(),
                foundation
            )

            val formAnswerForJson = Json.decodeFromString(
                FormAnswer.ForJson.serializer(),
                this::class.java.getResource("/sample-data/sgpit/sgpit-answers-aqt.json")!!.readText()
            )

            `when`(formService.get(Form.Id(1))).thenReturn(formForApi)
            `when`(foundationService.get(foundation.id)).thenReturn(foundation)


            `when`(
                documentService.show<FormAnswer.ForJson>(
                    formForApi.documentId!!.value, cookie, FormAnswer.ForJson::class, true
                )
            ).thenReturn(
                formAnswerForJson
            )

            return listOf(
                FoundationJsonMapBuilder(foundationService, workspaceForJson), FormJsonMapBuilder(
                    formService, foundationService, documentService, cookie, workspaceForJson
                ), DefaultMapBuilder() // must come last
            )

        }

    }
}