package services.oneteam.ai.flow.execution.variables

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.GlobalVariables
import services.oneteam.ai.flow.execution.VariableDefinition
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion

class TableVariableResolverTest {
    val global = GlobalVariables(
        Workspace.Id(1),
        WorkspaceVersion.Id(1),
        1,
        FlowConfiguration.Id("flow-id"),
        FlowConfiguration.Name("flow-name"),
    )

    val event = Event.ForApi(
        Workspace.Id(1),
        StartFlowManuallyFromFormProperties(
            "string",
            null,
            null,
        ),
        Event.Id("1"),
        1
    )

    val tableValue = buildJsonArray {
        add(buildJsonObject {
            put("rowId", JsonPrimitive("a1"))
            put("name", JsonPrimitive("John"))
            put("age", JsonPrimitive(30))
        })
        add(buildJsonObject {
            put("rowId", JsonPrimitive("a2"))
            put("name", JsonPrimitive("Jane"))
            put("age", JsonPrimitive(25))
        })
    }
    val context = FlowContextWithLocalStep(
        Step.Id("1"),
        FlowContext(
            global = global,
            variables = mutableMapOf(
                "tableVariable" to Variable(
                    Json.encodeToJsonElement(tableValue),
                    "table",
                    "tableVariable",
                    VariableDefinition.TableVariableProperties(
                        operation = VariableDefinition.TableOperation.SET_TABLE,
                        columns = listOf(
                            VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                            VariableDefinition.VariableConfiguration("name", "text", "name"),
                            VariableDefinition.VariableConfiguration("age", "number", "age")
                        )
                    )
                ),
                "testArray" to Variable(
                    buildJsonArray { add(JsonPrimitive(89)) },
                    "array",
                    "testArray"
                ),
            ),
            event = event,
        )
    )

    @Test
    fun `should resolve setTable`() = runTest {
        // given
        val tableValue = """
            [
                {
                    "name": "Lucas",
                    "age": 30
                },
                {
                    "name": "Jenifer",
                    "age": 20
                }
            ]
        """.trimIndent()

        val variable = Variable(
            Json.decodeFromString(tableValue),
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_TABLE,
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("name", JsonPrimitive("Lucas"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("name", JsonPrimitive("Jenifer"))
                    put("age", JsonPrimitive(20))
                })
            },
            result.value
        )
    }

    @Test
    fun `should add row to the existing table for setRow with empty rowId and rowIndex`() = runTest {
        // given
        val tableValue = """
            {
                "rowId": "a3",
                "name": "Luke",
                "age": 30
            }
        """.trimIndent()
        val variable = Variable(
            Json.decodeFromString(tableValue),
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_ROW,
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(25))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a3"))
                    put("name", JsonPrimitive("Luke"))
                    put("age", JsonPrimitive(30))
                })
            },
            result.value
        )
    }

    @Test
    fun `should replace row in the existing table for setRow with rowIndex`() = runTest {
        // given
        val tableValue = """
            {
                "rowId": "a2",
                "name": "Luke",
                "age": 30
            }
        """.trimIndent()
        val variable = Variable(
            Json.decodeFromString(tableValue),
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_ROW,
                rowIndex = JsonPrimitive(2),
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Luke"))
                    put("age", JsonPrimitive(30))
                })
            },
            result.value
        )
    }

    @Test
    fun `should replace row in the existing table for setRow with rowId`() = runTest {
        // given
        val tableValue = """
            {
                "rowId": "a2",
                "name": "Luke",
                "age": 30
            }
        """.trimIndent()
        val variable = Variable(
            Json.decodeFromString(tableValue),
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_ROW,
                rowIdentifier = "a2",
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Luke"))
                    put("age", JsonPrimitive(30))
                })
            },
            result.value
        )
    }

    @Test
    fun `should replace cell in the existing table for setCell with rowIndex`() = runTest {
        // given
        val tableValue = "100"
        val variable = Variable(
            Json.decodeFromString(tableValue),
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_CELL,
                rowIndex = JsonPrimitive(2),
                columnIdentifier = "age",
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(100))
                })
            },
            result.value
        )
    }

    @Test
    fun `should replace cell in the existing table for setCell with rowIdentifier`() = runTest {
        // given
        val tableValue = "100"
        val variable = Variable(
            Json.decodeFromString(tableValue),
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_CELL,
                rowIdentifier = "a2",
                columnIdentifier = "age",
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(100))
                })
            },
            result.value
        )
    }

    @Test
    fun `should replace column for setColumn`() = runTest {
        // given
        val tableValue = """
            [ 89, 90 ]
        """.trimIndent()
        val variable = Variable(
            Json.decodeFromString(tableValue),
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_COLUMN,
                columnIdentifier = "age",
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(89))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(90))
                })
            },
            result.value
        )
    }

    @Test
    fun `setColumn - column size is longer than table row size`() = runTest {
        // given
        val tableValue = """
            [ 89, 90, 91 ]
        """.trimIndent()
        val variable = Variable(
            Json.decodeFromString(tableValue),
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_COLUMN,
                columnIdentifier = "age",
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(89))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(90))
                })
                add(buildJsonObject {
                    put("age", JsonPrimitive(91))
                })
            },
            result.value
        )
    }

    @Test
    fun `setColumn - column size is shorter than table row size`() = runTest {
        // given
        val tableValue = """
            [ 89 ]
        """.trimIndent()
        val variable = Variable(
            Json.decodeFromString(tableValue),
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_COLUMN,
                columnIdentifier = "age",
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(89))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonNull)
                })
            },
            result.value
        )
    }

    @Test
    fun `should resolve when value is JsonArray`() = runTest {
        // given
        val tableValue = """
            {{testArray}}
        """.trimIndent()
        val variable = Variable(
            buildJsonArray { add(JsonPrimitive(89)) },
            "table",
            "tableVariable",
            VariableDefinition.TableVariableProperties(
                operation = VariableDefinition.TableOperation.SET_COLUMN,
                columnIdentifier = "age",
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", "text", "rowId"),
                    VariableDefinition.VariableConfiguration("name", "text", "name"),
                    VariableDefinition.VariableConfiguration("age", "number", "age")
                )
            )
        )

        val resolver = TableVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(89))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonNull)
                })
            },
            result.value
        )
    }
}