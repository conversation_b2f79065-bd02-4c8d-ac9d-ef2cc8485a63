package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotSame
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion

class FlowContextWithLocalStepTest {

    @Test
    fun `should create a deep copy of FlowContextWithLocalStep`() {
        // Original object setup
        val global = FlowContext.GlobalVariables(
            workspaceId = Workspace.Id(1),
            workspaceVersionId = WorkspaceVersion.Id(1),
            tenantId = 1,
            flowConfigurationId = FlowConfiguration.Id("1"),
            flowConfigurationName = FlowConfiguration.Name("flow")
        )
        val workspace = FlowContext.WorkspaceContext(
            documentId = Workspace.DocumentId("1"),
            id = Workspace.Id(1),
            key = Workspace.Key("WORKSPACE1"),
            name = Workspace.Name("workspace1"),
            workspaceFoundationId = Foundation.Id(1),
            variables = emptyMap()
        )
        val event = Event.ForApi(
            Workspace.Id(1), Event.EventProperties.StartFlowManuallyFromFormProperties(
                buttonLabel = "string", form = null, userId = 1
            ), Event.Id("1"), 1
        )
        val flowContext = FlowContext(
            global = global, variables = mutableMapOf(
                "key" to VariableInstance.Variable(
                    JsonPrimitive("value"), VariableDataType.TEXT, "key", null
                )
            ), workspace = workspace, event = event
        )
        val original = FlowContextWithLocalStep(
            stepId = Step.Id("step1"),
            flowContext = flowContext,
            thisStep = mutableMapOf("key" to JsonPrimitive("value"))
        )

        // Perform deep copy
        val copy = original.deepCopy()

        // Verify content equality
        assertEquals(original, copy)

        // Verify reference inequality
        assertNotSame(original, copy)
        assertNotSame(original.flowContext, copy.flowContext)

        assertNotSame(original.thisStep, copy.thisStep)

        assertNotSame(original.flowContext.variables, copy.flowContext.variables)

    }

}