package services.oneteam.ai.flow.execution.payload.filter

import io.kotest.matchers.nulls.beNull
import io.kotest.matchers.shouldNot
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.int
import kotlinx.serialization.json.jsonPrimitive
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.workspace.BaseSection
import services.oneteam.ai.shared.domains.workspace.CommonQuestionProperties
import services.oneteam.ai.shared.domains.workspace.QuestionType

class JsonSchemaQuestionFilterTest {

    private fun createPrimitiveQuestion(
        id: String,
        description: String,
        identifier: String,
        text: String,
        type: QuestionType
    ): BaseSection.BaseQuestion {
        return when (type) {
            QuestionType.TEXT -> BaseSection.TextQuestion(
                id = BaseSection.Id(id),
                description = description,
                identifier = identifier,
                text = text,
                properties = CommonQuestionProperties.TextQuestionProperties(required = false)
            )

            QuestionType.NUMBER -> BaseSection.NumberQuestion(
                id = BaseSection.Id(id),
                description = description,
                identifier = identifier,
                text = text,
                properties = CommonQuestionProperties.NumberQuestionProperties(required = false)
            )

            QuestionType.BOOLEAN -> BaseSection.BooleanQuestion(
                id = BaseSection.Id(id),
                description = description,
                identifier = identifier,
                text = text,
                properties = CommonQuestionProperties.BooleanQuestionProperties(required = false)
            )

            else -> throw IllegalArgumentException("Unsupported question type: $type")
        }
    }

    private fun createJsonQuestion(
        id: String = "q_json",
        items: List<BaseSection.BaseQuestion>? = emptyList(),
    ): BaseSection.JsonQuestion {
        return BaseSection.JsonQuestion(
            id = BaseSection.Id(id),
            description = "desc",
            identifier = id,
            text = "Json Question",
            properties = CommonQuestionProperties.JsonQuestionProperties(
                items = items
            )
        )
    }

    @Test
    fun `should filter payload for list of json input`() {
        val schema = BaseSection.ListQuestion(
            id = BaseSection.Id("q_list"),
            description = "desc",
            identifier = "listQ",
            text = "List Question",
            properties = CommonQuestionProperties.ListQuestionProperties(
                items = listOf(
                    createJsonQuestion(
                        items = listOf(
                            createPrimitiveQuestion(
                                "item1",
                                "Item 1 description",
                                "item1",
                                "Item 1",
                                QuestionType.TEXT
                            ),
                            createPrimitiveQuestion(
                                "item2",
                                "Item 2 description",
                                "item2",
                                "Item 2",
                                QuestionType.NUMBER
                            )
                        )
                    ),
                )
            )
        )
        val payload = buildJsonArray { // make list of json questions
            add(
                buildJsonObject {
                    put("item1", JsonPrimitive("value1"))
                    put("item2", JsonPrimitive(123))
                    put("shouldBeFiltered", JsonPrimitive("this should not be here"))
                }
            )
            add(
                buildJsonObject {
                    put("item1", JsonPrimitive("value2"))
                    put("item2", JsonPrimitive(456))
                    put("shouldBeFiltered", JsonPrimitive("this should not be here either"))
                }
            )
        }

        val filtered = JsonSchemaQuestionFilter.filter(payload, schema)

        val filteredJsonArray = filtered as? JsonArray ?: error("Json array should not be null")
        filteredJsonArray shouldNot beNull()

        assertThat(filteredJsonArray.size).isEqualTo(2)
        filteredJsonArray.forEach { jsonElement ->
            val jsonObject = jsonElement as? JsonObject ?: error("Json element should be a JsonObject")
            assertThat(jsonObject.keys).containsExactlyInAnyOrder("item1", "item2")
            assertThat(jsonObject["item1"]?.jsonPrimitive?.content).isNotEmpty()
            assertThat(jsonObject["item2"]?.jsonPrimitive?.int).isNotZero()
        }
    }

    @Test
    fun `should filter payload for list of json input and table schema`() {
        val schema = BaseSection.TableQuestion(
            id = BaseSection.Id("q_list"),
            description = "desc",
            identifier = "listQ",
            text = "List Question",
            properties = CommonQuestionProperties.TableQuestionProperties(
                columns = listOf(
                    createPrimitiveQuestion(
                        "item1",
                        "Item 1 description",
                        "item1",
                        "Item 1",
                        QuestionType.TEXT
                    ),
                    createPrimitiveQuestion(
                        "item2",
                        "Item 2 description",
                        "item2",
                        "Item 2",
                        QuestionType.NUMBER
                    )
                )
            )
        )
        val payload = buildJsonArray { // make list of json questions
            add(
                buildJsonObject {
                    put("item1", JsonPrimitive("value1"))
                    put("item2", JsonPrimitive(123))
                    put("shouldBeFiltered", JsonPrimitive("this should not be here"))
                }
            )
            add(
                buildJsonObject {
                    put("item1", JsonPrimitive("value2"))
                    put("item2", JsonPrimitive(456))
                    put("shouldBeFiltered", JsonPrimitive("this should not be here either"))
                }
            )
        }

        val filtered = JsonSchemaQuestionFilter.filter(payload, schema)

        val filteredJsonArray = filtered as? JsonArray ?: error("Json array should not be null")
        filteredJsonArray shouldNot beNull()

        assertThat(filteredJsonArray.size).isEqualTo(2)
        filteredJsonArray.forEach { jsonElement ->
            val jsonObject = jsonElement as? JsonObject ?: error("Json element should be a JsonObject")
            assertThat(jsonObject.keys).containsExactlyInAnyOrder("item1", "item2")
            assertThat(jsonObject["item1"]?.jsonPrimitive?.content).isNotEmpty()
            assertThat(jsonObject["item2"]?.jsonPrimitive?.int).isNotZero()
        }
    }

    @Test
    fun `should filter payload for text input in json input`() {
        val schema =
            createJsonQuestion(
                items = listOf(
                    createPrimitiveQuestion(
                        "q_text",
                        "desc",
                        "textQ",
                        "Text Question",
                        QuestionType.TEXT
                    )
                )
            )
        val payload = buildJsonObject {
            put("textQ", JsonPrimitive("the answer"))
            put("irrelevant", JsonPrimitive("shouldBeFiltered"))
        }

        val filtered = JsonSchemaQuestionFilter.filter(payload, schema)

        val filteredJsonObject = filtered as? JsonObject
        filteredJsonObject shouldNot beNull()
        filteredJsonObject!!
        assertThat(filteredJsonObject.keys).containsExactly("textQ")
        assertThat(filteredJsonObject["textQ"]?.jsonPrimitive?.content).isEqualTo("the answer")
    }

    @Test
    fun `should not filter payload for primitive schema`() {
        val schema = createPrimitiveQuestion(
            "q_text",
            "desc",
            "textQ",
            "Text Question",
            QuestionType.TEXT
        )
        val payload = JsonPrimitive("test payload")

        val filtered = JsonSchemaQuestionFilter.filter(payload, schema)

        val filteredJsonPrimitive = filtered as? JsonPrimitive
        filteredJsonPrimitive shouldNot beNull()
        filteredJsonPrimitive!!
        assertThat(filteredJsonPrimitive.content).isEqualTo("test payload")
    }

    @Test
    fun `should filter payload for json input`() {
        val schema = createJsonQuestion(
            items = listOf(
                createPrimitiveQuestion(
                    "item1",
                    "Item 1 description",
                    "item1",
                    "Item 1",
                    QuestionType.TEXT
                ),
                createPrimitiveQuestion(
                    "item2",
                    "Item 2 description",
                    "item2",
                    "Item 2",
                    QuestionType.NUMBER
                )
            )
        )
        val payload = buildJsonObject {
            put("item1", JsonPrimitive(123))
            put("item2", JsonPrimitive("abc"))
            put("other", JsonPrimitive("shouldBeFiltered"))
        }

        val filtered = JsonSchemaQuestionFilter.filter(payload, schema)

        val filteredJsonObject = filtered as? JsonObject
        filteredJsonObject shouldNot beNull()
        filteredJsonObject!!
        assertThat(filteredJsonObject.keys).containsExactlyInAnyOrder("item1", "item2")
        assertThat(filteredJsonObject["item1"]?.jsonPrimitive?.int).isEqualTo(123)
        assertThat(filteredJsonObject["item2"]?.jsonPrimitive?.content).isEqualTo("abc")
    }

    @Test
    fun `should filter nested json payload`() {
        // Schema
        val schema = createJsonQuestion(
            items = listOf(
                createJsonQuestion(
                    id = "form",
                    items = listOf(
                        createPrimitiveQuestion(
                            id = "id",
                            description = "id",
                            identifier = "id",
                            text = "id",
                            type = QuestionType.NUMBER
                        )
                    )
                ),
                createPrimitiveQuestion(
                    id = "number_1",
                    description = "number_1",
                    identifier = "number_1",
                    text = "number_1",
                    type = QuestionType.NUMBER
                ),
                createPrimitiveQuestion(
                    id = "text_1",
                    description = "text_1",
                    identifier = "text_1",
                    text = "text_1",
                    type = QuestionType.TEXT
                )
            )
        )

        // Payload with additional fields
        val payload = buildJsonObject {
            put("form", buildJsonObject {
                put("id", JsonPrimitive(17))
                put("shouldBeFiltered", JsonPrimitive("this should not be here"))
            })
            put("number_1", JsonPrimitive(123))
            put("text_1", JsonPrimitive("qwe"))
            put("shouldBeFiltered", JsonPrimitive("this should not be here either"))
        }

        // Filtering the payload
        val filtered = JsonSchemaQuestionFilter.filter(payload, schema)

        val filteredJsonObject = filtered as? JsonObject
        filteredJsonObject shouldNot beNull()
        filteredJsonObject!!
        assertThat(filteredJsonObject.keys).containsExactlyInAnyOrder("form", "number_1", "text_1")
        val formObject = filteredJsonObject["form"] as? JsonObject
        formObject shouldNot beNull()
        formObject!!
        assertThat(formObject.keys).containsExactlyInAnyOrder("id")
        assertThat(formObject["id"]?.jsonPrimitive?.int).isEqualTo(17)
        assertThat(filteredJsonObject["number_1"]?.jsonPrimitive?.int).isEqualTo(123)
        assertThat(filteredJsonObject["text_1"]?.jsonPrimitive?.content).isEqualTo("qwe")
        assertThat(filteredJsonObject["shouldBeFiltered"]).isNull() // should be filtered out

    }

    @Test
    fun `should filter empty json payload`() {
        val schema = createJsonQuestion(
            items = listOf(
                createPrimitiveQuestion(
                    "item1",
                    "Item 1 description",
                    "item1",
                    "Item 1",
                    QuestionType.TEXT
                )
            )
        )
        val payload = buildJsonObject {}

        val filtered = JsonSchemaQuestionFilter.filter(payload, schema)

        val filteredJsonObject = filtered as? JsonObject
        filteredJsonObject shouldNot beNull()
        filteredJsonObject!!
        assertThat(filteredJsonObject.keys).isEmpty()
    }

    @Test
    fun `should not filter json payload with no schema`() {
        val schema = createJsonQuestion(
            items = listOf()
        )
        val payload = buildJsonObject {
            put("item1", JsonPrimitive("value1"))
            put("irrelevant", JsonPrimitive("shouldBeFiltered"))
        }

        val filtered = JsonSchemaQuestionFilter.filter(payload, schema)

        val filteredJsonObject = filtered as? JsonObject
        filteredJsonObject shouldNot beNull()
        filteredJsonObject!!
        assertThat(filteredJsonObject.keys).containsExactlyInAnyOrder("item1", "irrelevant")
        assertThat(filteredJsonObject["item1"]?.jsonPrimitive?.content).isEqualTo("value1")
    }


    @Test
    fun `should not filter list payload with no schema`() {
        val schema = BaseSection.ListQuestion(
            id = BaseSection.Id("q_list"),
            description = "desc",
            identifier = "listQ",
            text = "List Question",
            properties = CommonQuestionProperties.ListQuestionProperties(
                items = listOf()
            )
        )
        val payload = buildJsonArray {
            add(buildJsonObject {
                put("item1", JsonPrimitive("value1"))
                put("irrelevant", JsonPrimitive("shouldBeFiltered"))
            })
        }

        val filtered = JsonSchemaQuestionFilter.filter(payload, schema)

        val filteredJsonArray = filtered as? JsonArray
        filteredJsonArray shouldNot beNull()
        filteredJsonArray!!
        assertThat(filteredJsonArray.size).isEqualTo(1)
        val jsonObject = filteredJsonArray[0] as? JsonObject
        jsonObject shouldNot beNull()
        jsonObject!!
        assertThat(jsonObject.keys).containsExactlyInAnyOrder("item1", "irrelevant")
        assertThat(jsonObject["item1"]?.jsonPrimitive?.content).isEqualTo("value1")
    }
}