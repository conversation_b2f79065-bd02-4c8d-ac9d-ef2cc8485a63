package services.oneteam.ai.flow.expression.functions

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class FunctionsHelperTest {

    @Test
    fun `test true values`() {
        assertEquals(true, parseBooleanArg(true))
        assertEquals(true, parseBooleanArg("TRUE"))
        assertEquals(true, parseBooleanArg(1))
        assertEquals(true, parseBooleanArg("Yes"))

    }

    @Test
    fun `test false values`() {
        assertEquals(false, parseBooleanArg(false))
        assertEquals(false, parseBooleanArg("FALSE"))
        assertEquals(false, parseBooleanArg(0))
        assertEquals(false, parseBooleanArg("No"))
    }

    @Test
    fun `test null with default value`() {
        assertEquals(true, parseBooleanArg("null", true))
        assertEquals(false, parseBooleanArg("null", false))
    }

    @Test
    fun `test invalid values throw exception`() {
        assertThrows<IllegalArgumentException> {
            parseBooleanArg("maybe")
        }
        assertThrows<IllegalArgumentException> {
            parseBooleanArg("2")
        }
        assertThrows<IllegalArgumentException> {
            parseBooleanArg("")
        }
    }
}
