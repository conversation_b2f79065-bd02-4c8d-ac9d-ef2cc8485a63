package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class NetworkDaysTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(NetworkDays.functionName, NetworkDays.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should calculate the number of working days between two dates + holidays`() {
        val expression = "\$NETWORKDAYS('2025-03-01', '2025-03-07', ['2025-03-07', '2025-03-06'])"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 3
    }

    @Test
    fun `should calculate the number of working days between two dates`() {
        val expression = "\$NETWORKDAYS('2025-03-01', '2025-03-07')"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 5
    }
}