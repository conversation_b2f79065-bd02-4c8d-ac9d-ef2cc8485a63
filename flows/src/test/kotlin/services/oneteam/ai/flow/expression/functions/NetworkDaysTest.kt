package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class NetworkDaysTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(NetworkDays.functionName, NetworkDays.evaluatorFunction)
        return jsonataExpression
    }

    data class Spec(
        val startDate: String,
        val endDate: String,
        val holidays: List<String> = emptyList(),
        val expected: Int,
    ) {
        fun toExpression(): String {
            return if (holidays.isEmpty()) {
                "\$NETWORKDAYS('${startDate}', '${endDate}')"
            } else {
                "\$NETWORKDAYS('${startDate}', '${endDate}', [${holidays.joinToString(",") { "'${it}'" }}])"
            }
        }
    }

    fun dataProvider(): Stream<Spec> {
        return Stream.of(
            Spec("2025-03-01", "2025-03-07", listOf("2025-03-07", "2025-03-06"), 3),
            Spec("2025-03-01", "2025-03-07", emptyList(), 5),
            Spec("2025-07-16", "2025-07-31", emptyList(), 12),
            Spec("2025-07-16", "2025-10-31", emptyList(), 78),
            Spec("2025-07-16", "2025-10-31", listOf("2025-07-17", "2025-07-18"), 76),
            Spec("2025-07-16", "2025-07-31", emptyList(), 12),
            Spec("2025-07-16", "2025-07-29", emptyList(), 10),
            Spec("2025-07-18", "2025-07-21", emptyList(), 2),
            Spec("2025-07-19", "2025-07-21", emptyList(), 1),
            Spec("2025-07-20", "2025-07-21", emptyList(), 1),
            Spec("2025-07-19", "2025-07-20", emptyList(), 0),
            Spec("2025-07-16", "2025-07-19", emptyList(), 3),
            Spec("2025-07-16", "2025-07-21", listOf("2025-07-20"), 4), // Holiday on Sunday
            Spec("2025-07-19", "2025-07-16", emptyList(), -3),
            Spec("2025-07-12", "2025-07-17", emptyList(), 4), // starting on weekend
            Spec("2025-07-20", "2025-07-23", emptyList(), 3), // starting on weekend
            Spec("2025-07-23", "2025-07-20", emptyList(), -3), // starting on weekend
            Spec("2025-07-18", "2025-07-22", emptyList(), 3), // over a weekend
            Spec("2025-07-16", "2025-07-21", listOf("2025-07-16", "2025-07-17", "2025-07-18", "2025-07-19", "2025-07-20", "2025-07-21"), 0), // every-day a holiday!
            Spec("2024-07-01", "2025-07-05", emptyList(), 265), // full year + first week
            Spec("2020-07-01", "2030-07-05", emptyList(), 2613), // 10 years + first week
        )
    }

    @ParameterizedTest
    @MethodSource("dataProvider")
    fun `test calculate network days`(spec: Spec) {
        val jsonata = jsonataWithFunctionRegistered(spec.toExpression())
        val result = jsonata.evaluate(null)
        assertNotNull(result)
        result shouldBe spec.expected
    }

}