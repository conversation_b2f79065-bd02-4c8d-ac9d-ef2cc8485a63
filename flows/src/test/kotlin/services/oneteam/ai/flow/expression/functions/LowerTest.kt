package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class LowerTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Lower.functionName, Lower.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns lowercase string`() {
        val expression = "\$LOWER('Hello')";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("hello")
    }

    @Test
    fun `returns lowercase string of empty string`() {
        val expression = "\$LOWER('')";
        val jsonataExpression = jsonataWithFunctionRegistered(expression)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe("")
    }
}