package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class IsBlankTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(IsBlank.functionName, IsBlank.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should check if a value is blank`() {
        val expression = "\$ISBLANK(\"\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe true
    }

    @Test
    fun `should check if a value is not blank`() {
        val expression = "\$ISBLANK(\"1.23456789\")"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe false
    }
}