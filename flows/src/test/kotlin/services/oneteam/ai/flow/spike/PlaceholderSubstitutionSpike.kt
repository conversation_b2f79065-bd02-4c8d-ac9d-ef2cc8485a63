package services.oneteam.ai.flow.spike

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.expression.JsonPathExpressionEvaluator
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.RegExTokenizer
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PlaceholderSubstitutionSpike {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun specProvider() = listOf(
        // 2033
        Spec(
            "\$count([{{name}}, {{age}}, {{date}}, {{isAdult}}, {{stringList}}, {{numberList}}, {{object}}]) + 1 + \$YEAR({{date}})",
            "\$count([\"John\", 30, \"2025-12-31\", true, [\"a\",\"b\"], [1,2], {\"key1\":\"value1\",\"key2\":2}]) + 1 + \$YEAR(\"2025-12-31\")",
            JsonPrimitive(2033)
        ),
        // {TroPGwZZrc=AssigneeId, I1tiDtrnAi=AssigneeName, on6bf5h-i4=AssignmentPolicy, nndTff2sKk=PolicyType, ixLE34c666=Yes, jYqWsMZXO5=Yes, RhfF2TFKeJ=First year on assignment, utYgarXaK_=No, _2qwFnLFof=5}
        Spec(
            "{ {{AuthList.Qg1l7LvjeN.columns.TroPGwZZrc.id}}:{{AssigneeId}},{{AuthList.Qg1l7LvjeN.columns.I1tiDtrnAi.id}}:{{AssigneeName}},{{AuthList.Qg1l7LvjeN.columns.on6bf5h-i4.id}}:{{AssignmentPolicy}},{{AuthList.Qg1l7LvjeN.columns.nndTff2sKk.id}}:{{PolicyType}},{{AuthList.Qg1l7LvjeN.columns.ixLE34c666.id}}: \"Yes\",{{AuthList.Qg1l7LvjeN.columns.jYqWsMZXO5.id}}: \"Yes\", {{AuthList.Qg1l7LvjeN.columns.RhfF2TFKeJ.id}}: \"First year on assignment\",{{AuthList.Qg1l7LvjeN.columns.utYgarXaK_.id}}: \"No\", {{AuthList.Qg1l7LvjeN.columns._2qwFnLFof.id}}:{{YearsAuthorised}}}",
            "{ \"TroPGwZZrc\":\"AssigneeId\",\"I1tiDtrnAi\":\"AssigneeName\",\"on6bf5h-i4\":\"AssignmentPolicy\",\"nndTff2sKk\":\"PolicyType\",\"ixLE34c666\": \"Yes\",\"jYqWsMZXO5\": \"Yes\", \"RhfF2TFKeJ\": \"First year on assignment\",\"utYgarXaK_\": \"No\", \"_2qwFnLFof\":5}",
            Json.decodeFromString<JsonElement>(
                """
                {
                  "TroPGwZZrc": "AssigneeId",
                  "I1tiDtrnAi": "AssigneeName",
                  "on6bf5h-i4": "AssignmentPolicy",
                  "nndTff2sKk": "PolicyType",
                  "ixLE34c666": "Yes",
                  "jYqWsMZXO5": "Yes",
                  "RhfF2TFKeJ": "First year on assignment",
                  "utYgarXaK_": "No",
                  "_2qwFnLFof": 5
                }
            """.trimIndent()
            )
        ),
        // 2
        Spec(
            "\$count([\"abc\", {{object}}])",
            "\$count([\"abc\", {\"key1\":\"value1\",\"key2\":2}])",
            JsonPrimitive(2)
        ),
        // Hello John
        Spec(
            "\$join([\"Hello\", \" \", {{name}}])",
            "\$join([\"Hello\", \" \", \"John\"])",
            JsonPrimitive("Hello John")
        ),
        // 2
        Spec(
            "{{numberList}}[1]", // access an element in an array
            "[1,2][1]",
            JsonPrimitive(2)
        ),
        // 2
        Spec(
            "{{object}}.key2", // access a property in an object
            "{\"key1\":\"value1\",\"key2\":2}.key2",
            JsonPrimitive(2)
        ),
        // John
        Spec(
            "{{name}}", // string
            "\"John\"",
            JsonPrimitive("John")
        ),
        // 30
        Spec(
            "{{age}}", // number
            "30",
            JsonPrimitive(30)
        ),
        // John says "Hello"
        Spec(
            "{{valueWithQuote}}",
            """
                "John says \"Hello\""
            """.trimIndent(),
            JsonPrimitive("John says \"Hello\"") // json structure will be invalid due to quoting so we don't want to try running via jsonata
        ),

        /*
         * scenarios that would not do what we want
         */

        // { "type": "form."John"" }
        Spec(
            """
                { "type": "form.{{name}}" }
            """.trimIndent(),
            """
                { "type": "form."John"" }
            """.trimIndent(),
            null // json structure will be invalid due to quoting so we don't want to try running via jsonata
        ),

        Spec(
            // { "type": ""John says \"Hello\"" "John says \"Hello\""" }
            """
                { "type": "{{valueWithQuote}} {{valueWithQuote}}" }
            """.trimIndent(),
            """
               { "type": ""John says \"Hello\"" "John says \"Hello\""${'"'} }
            """.trimIndent(),
            null // json structure will be invalid due to quoting so we don't want to try running via jsonata
        ),
    )

    data class Spec(val template: String, val replaced: String, val expected: Any?)

    @ParameterizedTest
    @MethodSource("specProvider")
    fun `should replace placeholders with values and evaluate with Jsonata`(input: Spec) = runTest {
        // Given
        val placeholders = buildJsonObject {
            // put values into the json object using their correct types
            put("name", JsonPrimitive("John"))
            put("valueWithQuote", JsonPrimitive("John says \"Hello\""))
            put("age", JsonPrimitive(30))
            put("date", JsonPrimitive("2025-12-31")) // dates are strings
            put("isAdult", JsonPrimitive(true))
            put("stringList", buildJsonArray {
                add(JsonPrimitive("a"))
                add(JsonPrimitive("b"))
            })
            put("numberList", buildJsonArray {
                add(JsonPrimitive(1))
                add(JsonPrimitive(2))
            })
            put("object", buildJsonObject {
                put("key1", JsonPrimitive("value1"))
                put("key2", JsonPrimitive(2))
            })

            put("AuthList", buildJsonObject {
                put("Qg1l7LvjeN", buildJsonObject {
                    put("columns", buildJsonObject {
                        put("TroPGwZZrc", buildJsonObject { put("id", JsonPrimitive("TroPGwZZrc")) })
                        put("I1tiDtrnAi", buildJsonObject { put("id", JsonPrimitive("I1tiDtrnAi")) })
                        put("on6bf5h-i4", buildJsonObject { put("id", JsonPrimitive("on6bf5h-i4")) })
                        put("nndTff2sKk", buildJsonObject { put("id", JsonPrimitive("nndTff2sKk")) })
                        put("ixLE34c666", buildJsonObject { put("id", JsonPrimitive("ixLE34c666")) })
                        put("jYqWsMZXO5", buildJsonObject { put("id", JsonPrimitive("jYqWsMZXO5")) })
                        put("RhfF2TFKeJ", buildJsonObject { put("id", JsonPrimitive("RhfF2TFKeJ")) })
                        put("utYgarXaK_", buildJsonObject { put("id", JsonPrimitive("utYgarXaK_")) })
                        put("_2qwFnLFof", buildJsonObject { put("id", JsonPrimitive("_2qwFnLFof")) })
                    })
                })
            })
            put("AssigneeId", JsonPrimitive("AssigneeId"))
            put("AssigneeName", JsonPrimitive("AssigneeName"))
            put("AssignmentPolicy", JsonPrimitive("AssignmentPolicy"))
            put("PolicyType", JsonPrimitive("PolicyType"))
            put("YearsAuthorised", JsonPrimitive(5))
        }

        var expression = input.template

        val tokenizer = RegExTokenizer()
        val jsonPath = JsonPathExpressionEvaluator()

        val tokens = tokenizer.tokenize(expression)

        tokens.forEach {
            val value = jsonPath.evaluate(it, placeholders)
            expression = tokenizer.replace(expression, it, value.toString())
        }

        logger.debug("Expression after substitution: $expression")
        assertThat(expression).isEqualTo(input.replaced)

        if (input.expected != null) {
            var evaluate = JsonataExpressionEvaluator().evaluate(expression, mapOf<Any, Any>())

            logger.debug("Result of expression evaluation is `{}`", evaluate)

            // jsonata doesn't return a jsonElement, it returns a LinkedHashMap or string, number, boolean, null
            // so that we can compare, convert the response to a JsonElement
            assertThat(TypeToJsonElementConverter.toJsonElement(evaluate)).isEqualTo(input.expected)
        }
    }
}