package services.oneteam.ai.flow.expression.functions

import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MaxTest {
    private val fn = Max

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName([])", BigDecimal(0), null, "Returns 0 for an empty array"),
            Spec("$$functionName(2, 3)", BigDecimal(3), null, "Returns the maximum of two numbers"),
            Spec("$$functionName(3, 2, 'a')", BigDecimal(3), null, "Ignores non-numeric values"),
            Spec(
                "$$functionName(-1, \"1\")",
                BigDecimal(1),
                null,
                "Handles numbers as strings and returns the maximum"
            ),
            Spec("$$functionName([1, 2, 3, 4, 5])", BigDec<PERSON><PERSON>(5), null, "Returns the maximum value in an array"),
            Spec(
                "$$functionName([1, 2, 'a', 4, 5])",
                BigDecimal(5),
                null,
                "Returns the maximum value in an array, ignoring non-numeric values"
            ),
            Spec(
                "$$functionName([\"1\", 2, \"3\", 4, 5])",
                BigDecimal(5),
                null,
                "Handles mixed types in an array and returns the maximum"
            ),
            Spec(
                "$$functionName([1, 2, 3], -1)",
                listOf(BigDecimal(1), BigDecimal(2), BigDecimal(3)),
                null,
                "Performs pairwise comparison with a larger number"
            ),
            Spec(
                "$$functionName([1, 2, 3], 4)",
                listOf(BigDecimal(4), BigDecimal(4), BigDecimal(4)),
                null,
                "Performs pairwise comparison with a smaller number"
            ),
            Spec(
                "$$functionName([4, 5], [[1, 2, 3], [5, 4, 3]])",
                listOf(
                    listOf(BigDecimal(4), BigDecimal(5), BigDecimal(3)),
                    listOf(BigDecimal(5), BigDecimal(5), BigDecimal(3))
                ), null,
                "Handles nested arrays and returns the pairwise maximum"
            ),
            Spec("$$functionName(0.1, 0.2)", BigDecimal("0.2"), null, "Returns the maximum of two fractional numbers"),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        functionTest(fn, spec)
    }

}