package services.oneteam.ai.flow.expression

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.expression.conditional.Condition
import services.oneteam.ai.flow.expression.conditional.Expression
import services.oneteam.ai.flow.expression.conditional.Operator
import services.oneteam.ai.flow.expression.functions.Contains
import services.oneteam.ai.flow.expression.functions.DoesNotContain
import services.oneteam.ai.flow.expression.functions.IsEmpty

class FlowConditionEvaluateTest {

    @Test
    fun `test evaluateCondition with true condition`() = runTest {
        val condition =
            "'Run Audit' = 'Run Audit' and 1 = 1"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test evaluateCondition with 'false' condition`() = runTest {
        val condition = "'Run Audit' = 'Run Audit' and 1 = 0"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertFalse(result)
    }

    @Test
    fun `test evaluateCondition with 1 less than 2`() = runTest {
        val condition = "1 < 2"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test evaluateCondition with 1 less than 2 and 3 less than 6`() = runTest {
        val condition = "1 < 2 and 3 < 6"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test evaluateCondition with 1 less than 2 and 3 less than 1`() = runTest {
        val condition = "1 < 2 and 3 < 1"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertFalse(result)
    }

    @Test
    fun `test evaluateCondition with 1 less than 2 or 3 less than 1`() = runTest {
        val condition = "1 < 2 or 3 < 1"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test evaluateCondition with 1 less than 2 or 3 less than 1 and 1 less than 2`() = runTest {
        val condition = "(1 < 2 or 3 < 1) and 1 < 2"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test evaluateCondition with 1 less than or equal to 2`() = runTest {
        val condition = "1 <= 2"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test evaluateCondition with 1 less than or equal to -2 point 01`() = runTest {
        val condition = "1 <= -2.01"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertFalse(result)
    }

    @Test
    fun `test evaluateCondition with 'false' equals 'false'`() = runTest {
        val condition = "'false' = 'false'"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test evaluateCondition with 'false' not equals true`() = runTest {
        val condition = "'false' != true"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test evaluateCondition with date comparison`() = runTest {
        val condition = "\"2025-01-01\" > \"2020-01-01\""
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test long condition`() = runTest {
        val condition =
            "(5000 > 0 and $${IsEmpty.functionName}('') and ($${Contains.functionName}('hello world', 'hello') or '2025-01-01' > '2020-01-01' or ('false' = 'false' and true != 'false')) and (5000 = 5000 and $${DoesNotContain.functionName}('hello world', 'goodbye')))"
        val result = JsonataExpressionEvaluator().evaluate(condition, emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

    @Test
    fun `test evaluateCondition with date comparison with Condition class`() = runTest {
        val condition = Condition(
            Expression("\"2025-01-01\""),
            Operator.GREATER_THAN,
            listOf(Expression("\"2020-01-01\""))
        )
        val result = JsonataExpressionEvaluator().evaluate(condition.toString(), emptyMap<String, String>()) as Boolean
        assertTrue(result)
    }

}