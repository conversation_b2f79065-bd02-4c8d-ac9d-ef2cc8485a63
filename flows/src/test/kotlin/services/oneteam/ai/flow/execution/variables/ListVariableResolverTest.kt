package services.oneteam.ai.flow.execution.variables

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.encodeToJsonElement
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.GlobalVariables
import services.oneteam.ai.flow.execution.VariableDefinition
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion

class ListVariableResolverTest {
    val global = GlobalVariables(
        Workspace.Id(1),
        WorkspaceVersion.Id(1),
        1,
        FlowConfiguration.Id("flow-id"),
        FlowConfiguration.Name("flow-name"),
    )

    val event = Event.ForApi(
        Workspace.Id(1),
        StartFlowManuallyFromFormProperties(
            "string",
            null,
            null,
        ),
        Event.Id("1"),
        1
    )

    private val listValue = buildJsonArray {
        add(JsonPrimitive(10))
        add(JsonPrimitive(20))
        add(JsonPrimitive(30))
    }

    val context = FlowContextWithLocalStep(
        Step.Id("1"),
        FlowContext(
            global = global,
            variables = mutableMapOf(
                "listVariable" to Variable(
                    Json.encodeToJsonElement(listValue),
                    "list",
                    "listVariable",
                    VariableDefinition.ListVariableProperties(
                        listOperation = VariableDefinition.ListOperation.SET_LIST
                    )
                )
            ),
            event = event,
        )
    )

    @Test
    fun `should set list for SET_LIST operation`() = runTest {
        // given
        val newListValue = """
            [40, 50, 60]
        """.trimIndent()
        val variable = Variable(
            Json.decodeFromString(newListValue),
            "list",
            "listVariable",
            VariableDefinition.ListVariableProperties(
                listOperation = VariableDefinition.ListOperation.SET_LIST
            )
        )

        val resolver = ListVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(JsonPrimitive(40))
                add(JsonPrimitive(50))
                add(JsonPrimitive(60))
            },
            result.value
        )
    }

    @Test
    fun `should update item at specific index for SET_ITEM operation`() = runTest {
        // given
        val newValue = 25
        val variable = Variable(
            Json.encodeToJsonElement(newValue),
            "list",
            "listVariable",
            VariableDefinition.ListVariableProperties(
                listOperation = VariableDefinition.ListOperation.SET_ITEM,
                itemIndex = JsonPrimitive(2)
            )
        )

        val resolver = ListVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(JsonPrimitive(10))
                add(JsonPrimitive(25))
                add(JsonPrimitive(30))
            },
            result.value
        )
    }

    @Test
    fun `should append item to the list for SET_ITEM operation when index is null`() = runTest {
        // given
        val newValue = 40
        val variable = Variable(
            Json.encodeToJsonElement(newValue),
            "list",
            "listVariable",
            VariableDefinition.ListVariableProperties(
                listOperation = VariableDefinition.ListOperation.SET_ITEM
            )
        )

        val resolver = ListVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(JsonPrimitive(10))
                add(JsonPrimitive(20))
                add(JsonPrimitive(30))
                add(JsonPrimitive(40))
            },
            result.value
        )
    }

    @Test
    fun `should remove item at specific index for REMOVE_ITEM operation`() = runTest {
        // given
        val variable = Variable(
            Json.encodeToJsonElement(""),
            "list",
            "listVariable",
            VariableDefinition.ListVariableProperties(
                listOperation = VariableDefinition.ListOperation.REMOVE_ITEM,
                itemIndex = JsonPrimitive(2)
            )
        )

        val resolver = ListVariableOperation(true)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(JsonPrimitive(10))
                add(JsonPrimitive(30))
            },
            result.value
        )
    }
}