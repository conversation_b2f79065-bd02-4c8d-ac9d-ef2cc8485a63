package services.oneteam.ai.flow.execution

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SimplePlaceHolderReplacerTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val placeHolderReplacer = SimplePlaceHolderReplacer(NoValueStrategy.BLANK)

    @Test
    fun `should replace placeholder with value`() = runTest {
        val input = "Hello, {{name}}!"
        val expected = "Hello, John!"
        val placeholderValues = StaticPlaceholderValueProvider(buildJsonObject { put("name", JsonPrimitive("John")) })
        val result = placeHolderReplacer.replacePlaceholders(input, placeholderValues)
        logger.debug(result)
        assertEquals(expected, result)
    }

    @Test
    fun `should replace unknown placeholder with empty string`() = runTest {
        val input = "Hello, {{name}}!"
        val expected = "Hello, !"
        val placeholderValues = StaticPlaceholderValueProvider(buildJsonObject { })
        val result = placeHolderReplacer.replacePlaceholders(input, placeholderValues)
        logger.debug(result)
        assertEquals(expected, result)
    }

    @Test
    fun `should replace unknown placeholder with null`() = runTest {
        val input = "Hello, {{name}}!"
        val expected = "Hello, null!"
        val placeholderValues = StaticPlaceholderValueProvider(buildJsonObject { })
        val result = SimplePlaceHolderReplacer(NoValueStrategy.NULL).replacePlaceholders(input, placeholderValues)
        logger.debug(result)
        assertEquals(expected, result)
    }

    fun actionProvider(): Stream<String> {
        return Stream.of("action", "condition", "trigger")
    }

    @ParameterizedTest
    @MethodSource("actionProvider")
    fun `should replace placeholders for action`(name: String) = runTest {

        val actionStepConfiguration =
            this::class.java.getResource("/services/oneteam/ai/flow/execution/$name.template.json")!!.readText()
        val context =
            this::class.java.getResource("/services/oneteam/ai/flow/execution/$name.context.json")!!.readText()
        val expected =
            this::class.java.getResource("/services/oneteam/ai/flow/execution/$name.expected.json")!!.readText()

        val actual =
            placeHolderReplacer.replacePlaceholders(
                actionStepConfiguration,
                StaticPlaceholderValueProvider(Json.decodeFromString<JsonObject>(context))
            )

        // check that the resulting json is valid
        Json.decodeFromString<JsonObject>(actual)

        assertEquals(expected.trim(), actual.trim())

    }
}