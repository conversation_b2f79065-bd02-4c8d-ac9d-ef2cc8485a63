package test

import org.openjdk.jmh.profile.GCProfiler
import org.openjdk.jmh.runner.Runner
import org.openjdk.jmh.runner.options.OptionsBuilder
import org.openjdk.jmh.runner.options.TimeValue
import java.util.concurrent.TimeUnit

fun main() {
    val opts = OptionsBuilder()
        .include(JvmTestBenchmark::class.java.simpleName)
        .forks(1)
        .warmupIterations(0)
        .measurementIterations(1)
        .measurementTime(TimeValue.seconds(1))
        .timeUnit(TimeUnit.SECONDS)
        .addProfiler(GCProfiler::class.java)
        .build()

    Runner(opts).run()
}
