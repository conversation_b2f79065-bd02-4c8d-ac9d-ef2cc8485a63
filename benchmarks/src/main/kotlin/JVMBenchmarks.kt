package test

import org.openjdk.jmh.annotations.*
import java.util.concurrent.TimeUnit

@State(Scope.Benchmark)
@Fork(1)
@Warmup(iterations = 0)
@Measurement(iterations = 1, time = 5, timeUnit = TimeUnit.SECONDS)
class JvmTestBenchmark {

    @Benchmark
    fun memoryIntensiveBenchmark(): Int {
        val size = 10 * 1024 * 1024
        val data = ByteArray(size)
        for (i in data.indices) {
            data[i] = (i and 0xFF).toByte()
        }
        var sum = 0
        for (b in data) {
            sum += b.toInt()
        }
        return sum
    }
}
