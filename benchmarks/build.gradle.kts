import okhttp3.internal.format
import org.gradle.jvm.tasks.Jar

//reference: https://github.com/Kotlin/kotlinx-benchmark/blob/master/docs/kotlin-jvm-project-setup.md

plugins {
    java
    application
    id("project.conventions")
    alias(libs.plugins.benchmark)
    alias(libs.plugins.allopen)
}

group = "services.oneteam"
version = "0.0.1"

repositories {
    mavenCentral()
}

application {
    mainClass.set("benchmarks.main")
}

allOpen {
    annotation("org.openjdk.jmh.annotations.State")
}

benchmark {
    targets {
        register("main")
        format("json")
    }
}

dependencies {
    implementation(libs.benchmarks)
}

tasks.test {
    useJUnitPlatform()
}

tasks.register("listConfigs") {
    group = "help"
    description = "Print all configuration names"
    doLast {
        configurations.forEach { println(it.name) }
    }
}

tasks.register<JavaExec>("benchmarkWithGC") {
    group = "benchmark"
    description = "Run JMH with GCProfiler + JSON"
    dependsOn("mainBenchmarkJar")

    val jmhJar = tasks.named<Jar>("mainBenchmarkJar").flatMap { it.archiveFile }
    classpath = files(jmhJar)
    mainClass.set("org.openjdk.jmh.Main")

    doFirst {
        layout.buildDirectory
            .dir("reports")
            .get()
            .asFile
            .mkdirs()
    }

    val reportFile = layout.buildDirectory
        .file("reports/benchmark-results.json")
        .get()
        .asFile
        .absolutePath

    args = listOf(
        "JvmTestBenchmark",
        "-rf", "json",
        "-rff", reportFile,
        "-prof", "org.openjdk.jmh.profile.GCProfiler"
    )
}


