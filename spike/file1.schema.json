{"$id": "file1.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["variant", "properties2", "id"], "properties": {"variant": {"type": "string", "enum": ["setVar2"]}, "id": {"type": "boolean"}}, "if": {"properties": {"variant": {"const": "setVar2"}}}, "then": {"properties": {"properties2": {"properties": {"variables3": {"minItems": 4}}, "required": ["variables3"]}}, "required": ["properties2"]}}