{"$schema": "documentSchema.json", "flows": {"entities": {"qj6bBHh3PIiLPb1SVkLT9": {"description": "", "id": "qj6bBHh3PIiLPb1SVkLT9", "metadata": {"createdAt": "2025-03-04T06:57:20.587Z", "updatedAt": "2025-03-04T06:57:20.587Z"}, "name": "f1", "start": "emhTVFhfBOggbV2rDKA0o", "steps": {"emhTVFhfBOggbV2rDKA0o": {"id": "emhTVFhfBOggbV2rDKA0o", "metadata": {"createdAt": "2025-03-04T06:57:45.219Z", "updatedAt": "2025-03-04T06:57:45.219Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": []}, "variant": "setVariables"}}, "triggers": {"5BWOr_4YvI-sezIBESKEy": {"id": "5BWOr_4YvI-sezIBESKEy", "metadata": {"createdAt": "2025-03-04T06:57:34.162Z", "updatedAt": "2025-03-04T06:57:34.162Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"foundationVariableName": "foundation", "foundationConfigurationId": "foundationConfigurationId", "buttonLabel": "buttonLabel"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}}, "order": ["qj6bBHh3PIiLPb1SVkLT9"]}, "forms": {}, "foundations": {"entities": {}, "order": []}, "id": 97, "key": "V5", "metadata": {"createdAt": "2025-03-04T05:18:26.200171Z", "updatedAt": "2025-03-04T05:18:26.200173Z"}, "name": "v5", "series": {}, "type": "WORKSPACE_CONFIGURATION"}