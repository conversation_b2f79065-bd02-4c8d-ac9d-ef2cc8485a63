{"$id": "documentSchema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["flows", "forms", "foundations", "id", "key", "metadata", "name", "series", "type"], "additionalProperties": false, "definitions": {"key": {"type": "string", "minLength": 2, "pattern": "^[a-zA-Z0-9_]*$", "maxLength": 20}, "name": {"type": "string", "minLength": 2, "maxLength": 100}, "order": {"type": "array", "items": {"type": "string"}}, "metadata": {"type": "object", "required": ["createdAt", "updatedAt"], "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "step": {"description": "FlowConfiguration.Step", "type": "object", "required": ["id2", "name"], "properties": {"variant": {"type": "string", "enum": ["action", "setVariables", "condition", "iterator", "trigger"]}}, "if": {"properties": {"variant": {"const": "setVariables"}}}, "then": {"properties": {"properties": {"variables": {"minItems": 1}}}, "required": ["properties"]}}}, "properties": {"errors": {"type": "array"}, "description": {"type": ["string", "null"]}, "flows": {"type": "object", "required": ["entities", "order"], "properties": {"entities": {"type": "object", "patternProperties": {"^[a-zA-Z0-9_-]+$": {"type": "object", "required": ["description", "id", "metadata", "name", "steps"], "properties": {"steps": {"description": "FlowConfiguration.ForJson.steps - Must have at least one step", "type": "object", "patternProperties": {"^[a-zA-Z0-9_-]+$": {"$ref": "#/definitions/step"}}}, "description": {"type": ["string", "null"]}, "id": {"type": "string"}, "metadata": {"$ref": "#/definitions/metadata"}, "name": {"$ref": "#/definitions/name"}}}}}, "order": {"$ref": "#/definitions/order"}}}, "forms": {"type": "object", "patternProperties": {"^[a-zA-Z0-9_-]+$": {"type": "object", "required": ["description", "id", "key", "metadata", "name"], "properties": {"description": {"type": "string"}, "id": {"type": "string"}, "key": {"$ref": "#/definitions/key"}, "metadata": {"$ref": "#/definitions/metadata"}, "name": {"$ref": "#/definitions/name"}}}}}, "foundations": {"type": "object", "required": ["entities", "order"], "properties": {"entities": {"type": "object", "patternProperties": {"^[a-zA-Z0-9_-]+$": {"type": "object", "required": ["id", "metadata", "name", "relationship"], "properties": {"description": {"type": ["string", "null"]}, "id": {"type": "string"}, "metadata": {"$ref": "#/definitions/metadata"}, "name": {"$ref": "#/definitions/name"}, "relationship": {"type": "string"}, "type": {"type": "string"}}}}}, "order": {"$ref": "#/definitions/order"}}}, "id": {"type": "integer"}, "key": {"$ref": "#/definitions/key"}, "metadata": {"$ref": "#/definitions/metadata"}, "name": {"$ref": "#/definitions/name"}, "type": {"type": "string"}, "series": {"type": "object", "patternProperties": {"^[a-zA-Z0-9_-]+$": {"type": "object", "required": ["id", "description", "metadata", "name"], "properties": {"id": {"type": "string"}, "metadata": {"$ref": "#/definitions/metadata"}, "name": {"$ref": "#/definitions/name"}, "description": {"type": "string"}, "intervals": {"type": "object", "properties": {"order": {"$ref": "#/definitions/order"}, "entities": {"type": "object", "patternProperties": {"^[a-zA-Z0-9_-]+$": {"type": "object", "properties": {"name": {"$ref": "#/definitions/name"}, "id": {"type": "string"}}}}}}}}}}}}}