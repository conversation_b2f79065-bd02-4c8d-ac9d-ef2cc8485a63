# Project Setup

> Note:
>
>
See [Dev Environment Setup](https://oneteam-services.atlassian.net/wiki/spaces/ONECALC/pages/3326246913/Dev+Environment+Setup)
for the latest
> setup instructions.
>
>
See [Common Issues & FAQ](https://oneteam-services.atlassian.net/wiki/spaces/ONECALC/pages/3529768990/Common+Issues+FAQ)
> for useful tips.

# Prerequisites

1. IntelliJ IDEA (community edition) -> https://www.jetbrains.com/idea/
1. Install Java 21.x (LTS) -> https://adoptium.net/en-GB/
1. Postgres 16.x -> https://www.postgresql.org/download/ (or via Brew)

# Setting up the project locally

* After installing Postgres, run the following scripts to create the database and the database users:

```shell
./scripts/db-recreate-local.mjs
```

* To set up [.env.local](.env.local) run

```shell
./scripts/env.mjs --nginx
```

You will need to set up other variables in the `.env.local` file, such as keys for blob storage:

- https://oneteam-services.atlassian.net/wiki/spaces/ONECALC/pages/3700916269/Setup+File+Storage+Locally


* Open the project in IntelliJ IDEA.
* Run the `OneTeam AI` run configuration to start the server - this will apply migrations to create
  tables.
    1. This uses the [.env.local](.env.local) environment variables.

To destroy and recreate the database, run the following script:

```shell
./scripts/db-recreate-local.mjs
```

# Structure

- [api-collection/otai](api-collection/otai)
    - Collection of API requests for Bruno -> https://www.usebruno.com/
- `src/main/resources/db` contains SQL migration scripts for the database which are run via flyway
    - see [FlywayMigration.kt](src/main/kotlin/services/oneteam/FlywayMigration.kt) for details

# Project Structure

- Kotlin using Ktor framework with Exposed ORM
    - https://ktor.io/
    - https://jetbrains.github.io/Exposed/home.html
- Dependency Injection: Koin
- Build: Gradle
    - We use renovate to automatically check for updates to
      dependencies https://developer.mend.io/bitbucket/devops-martinit/oneteam-ai-be
- Testing: JUnit 5, Ktor Test
    - to run ALL tests, use `./gradlew check`
    - to run UNIT tests, use `./gradlew test`
    - to run H2 tests, use `./gradlew h2test`
    - to run POSTGRES tests, use `./gradlew postgrestest`
- Logging: Logback with SLF4J
    - to obtain a logger in a class, use:
        - `val logger = org.slf4j.LoggerFactory.getLogger(javaClass)`
- API Documentation: Swagger
- Code Quality: SonarCloud (TBD)
- CI/CD: BitBucket Pipelines (TBD)
- Deployment: Azure AppService (TBD)
- Frontend: React (separate repository)
- Security: JWT (TBD)
- Caching: Redis (TBD)
- Containerization: Docker (TBD)
- Database: Postgres + CosmosDB (TBD)
- Websockets: Azure PubSub (TBD)
