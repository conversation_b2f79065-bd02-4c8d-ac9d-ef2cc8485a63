<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="OneTeam AI" type="KtorApplicationConfigurationType" factoryName="Ktor">
    <option name="ALTERNATIVE_JRE_PATH" value="temurin-21" />
    <option name="envFilePaths">
      <option value="$PROJECT_DIR$/.env.local" />
    </option>
    <option name="MAIN_CLASS_NAME" value="io.ktor.server.netty.EngineMain" />
    <module name="services.oneteam.otai.app.main" />
    <option name="PASS_PARENT_ENVS" value="false" />
    <option name="VM_PARAMETERS" value="-Dlogback.configurationFile=app/src/main/resources/logback-local-$OSUser$.xml" />
    <option name="alternativeJrePath" value="temurin-21" />
    <option name="alternativeJrePathEnabled" value="false" />
    <option name="includeProvidedScope" value="false" />
    <option name="mainClass" value="io.ktor.server.netty.EngineMain" />
    <option name="passParentEnvs" value="false" />
    <option name="programParameters" value="" />
    <option name="shortenCommandLine" />
    <option name="vmParameters" value="-Dlogback.configurationFile=app/src/main/resources/logback-local-$OSUser$.xml" />
    <option name="workingDirectory" value="$PROJECT_DIR$" />
    <method v="2">
      <option name="Make" enabled="true" />
      <option name="RunConfigurationTask" enabled="true" run_configuration_name="Init developer log configuration" run_configuration_type="ShConfigurationType" />
    </method>
  </configuration>
</component>