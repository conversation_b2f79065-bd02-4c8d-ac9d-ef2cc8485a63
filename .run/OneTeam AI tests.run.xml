<component name="ProjectRunConfigurationManager">
    <configuration default="false" name="OneTeam AI tests" type="GradleRunConfiguration" factoryName="Gradle">
        <ExternalSystemSettings>
            <option name="executionName"/>
            <option name="externalProjectPath" value="$PROJECT_DIR$"/>
            <option name="externalSystemIdString" value="GRADLE"/>
            <option name="scriptParameters" value=""/>
            <option name="taskDescriptions">
                <list/>
            </option>
            <option name="taskNames">
                <list>
                    <option value=":test"/>
                </list>
            </option>
            <option name="vmOptions"/>
        </ExternalSystemSettings>
        <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
        <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
        <DebugAllEnabled>false</DebugAllEnabled>
        <RunAsTest>true</RunAsTest>
        <method v="2"/>
    </configuration>
</component>