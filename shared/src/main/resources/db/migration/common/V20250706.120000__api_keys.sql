-- Since the old data is not usable anymore, it is easier to drop the table and recreate it.
DROP TABLE IF EXISTS api_keys;

CREATE TABLE api_keys
(
    id              BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name            TEXT NOT NULL,
    description     TEXT,
    client_id       TEXT NOT NULL,
    client_secret   TEXT NOT NULL,
    tenant_id       BIGINT NOT NULL,
    created_at      TIMESTAMP NOT NULL,
    updated_at      TIMESTAMP NOT NULL,

    CONSTRAINT tenant_name_unique
        UNIQUE (tenant_id, name),
    CONSTRAINT tenant_api_key_unique
        UNIQUE (tenant_id, client_id)
);
COMMENT ON COLUMN api_keys.client_secret IS 'client_secret is like a password so this is a hash of the actual value';
