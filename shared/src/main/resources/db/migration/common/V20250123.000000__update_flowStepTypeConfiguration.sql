ALTER TABLE flow_step_type_configuration
    ADD COLUMN primary_identifier text UNIQUE;

CREATE OR REPLACE FUNCTION prevent_primary_identifier_update()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.primary_identifier IS DISTINCT FROM OLD.primary_identifier THEN
        RAISE EXCEPTION 'primary_identifier cannot be updated after insertion';
END IF;
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER enforce_primary_identifier_immutable
    BEFORE UPDATE ON flow_step_type_configuration
    FOR EACH ROW
    WHEN (OLD.primary_identifier IS NOT NULL)
    EXECUTE FUNCTION prevent_primary_identifier_update();