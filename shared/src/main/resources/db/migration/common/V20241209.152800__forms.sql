CREATE TABLE forms
(
    id                    bigint GENERATED BY DEFAULT AS IDENTITY
        CONSTRAINT forms_pkey1
            PRIMARY KEY,
    created_at            timestamp NOT NULL,
    updated_at            timestamp,
    form_configuration_id text      NOT NULL,
    foundation_id         bigint    NOT NULL
        CONSTRAINT fk_forms_foundation_id__id
            REFERENCES foundations
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    interval_id           text,
    document_id           text,
    tenant_id             bigint    NOT NULL
        CONSTRAINT fk_forms_tenant_id__id
            REFERENCES tenants
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    workspace_id          bigint    NOT NULL
        CONSTRAINT fk_forms_workspace_id__id
            REFERENCES workspaces
            ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE TABLE form_versions
(
    id            bigint GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    form_id       bigint    NOT NULL,
    workspace_id  bigint    NOT NULL,
    tenant_id     bigint    NOT NULL
        CONSTRAINT fk_form_versions_tenant_id__id
            REFERENCES tenants
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    configuration jsonb     NOT NULL,
    created_at    timestamp NOT NULL,
    updated_at    timestamp NOT NULL
);
