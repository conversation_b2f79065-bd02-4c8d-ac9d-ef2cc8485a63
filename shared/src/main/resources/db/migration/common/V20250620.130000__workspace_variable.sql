CREATE TABLE workspace_secured_values
(
    id              BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    value           TEXT NOT NULL, -- Encrypted value
    workspace_id    BIGINT NOT NULL,
    tenant_id       BIGINT NOT NULL,
    created_at      TIMESTAMP NOT NULL,
    updated_at      TIMESTAMP NOT NULL
);

CREATE TABLE workspace_variables
(
    id                          BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    ref                         TEXT NOT NULL,
    value                       TEXT,
    is_secured                  BOOLEAN NOT NULL DEFAULT FALSE,
    workspace_secured_value_id  BIGINT REFERENCES workspace_secured_values(id) ON DELETE CASCADE, -- Optional reference to a SecuredValueEntity if the variable is secured
    workspace_id                BIGINT REFERENCES workspaces(id) NOT NULL,
    workspace_version_id        BIGINT REFERENCES workspace_versions(id), -- NULL=unpublished, NOTNULL=published to a specific version
    tenant_id                   BIGINT REFERENCES tenants(id) NOT NULL,
    created_at                  TIMESTAMP NOT NULL,
    updated_at                  TIMESTAMP NOT NULL,

    CONSTRAINT workspace_version_ref_unique
        UNIQUE (tenant_id, workspace_id, ref, workspace_version_id),

    CONSTRAINT value_xor_secured_value_check
        CHECK (
            (is_secured = FALSE AND workspace_secured_value_id IS NULL AND value IS NOT NULL) OR
            (is_secured = TRUE AND workspace_secured_value_id IS NOT NULL AND value IS NULL)
        )
);
