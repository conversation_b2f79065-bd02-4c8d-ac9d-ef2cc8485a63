-- -- https://dev.to/yugabyte/postgresql-row-level-security-with-an-array-of-tenants-2136
-- -- https://stackoverflow.com/questions/69381358/postgresql-row-level-security-involving-foreign-key-with-other-table?rq=3
-- -- https://www.postgresql.org/docs/current/ddl-rowsecurity.html#:~:text=As%20policies%20are%20table%2Dspecific,AND%20(for%20restrictive%20policies).
--
--
-- CREATE TABLE workspace_users
-- (
--     id           bigint GENERATED BY DEFAULT AS IDENTITY
--         PRIMARY KEY,
--     workspace_id bigint    not null
-- -- disable this FK because
-- --  1. exposed is doing an "insert returning" when inserting  a workspace
-- --       so we need to insert workspace_users  before workspaces
-- --       so we need to disable this FK until we have a solution to do an insert without returning
-- --         CONSTRAINT fk_workspace_users_workspace_id
-- --             REFERENCES workspaces
-- --             ON UPDATE RESTRICT ON DELETE RESTRICT
--     ,
--     user_id      bigint    NOT NULL
--         CONSTRAINT fk_workspace_users_user_id
--             REFERENCES users
--             ON UPDATE RESTRICT ON DELETE RESTRICT,
--
--     status       text      NOT NULL,
--     access_level jsonb     NOT NULL,
--     created_at   timestamp NOT NULL,
--     updated_at   timestamp NOT NULL,
--     tenant_id    bigint    NOT NULL
--         CONSTRAINT fk_workspace_users_tenant_id__id
--             REFERENCES tenants
--             ON UPDATE RESTRICT ON DELETE RESTRICT,
--     CONSTRAINT workspace_users_unique
--         UNIQUE (workspace_id, user_id)
-- );
--
-- -- we'll be finding users of a workspace
-- CREATE INDEX idx_workspace_users_workspace_id_tenant_id
--     ON workspace_users (workspace_id, tenant_id);
--
-- -- we'll be finding workspaces of a user
-- CREATE INDEX idx_workspace_users_user_id_tenant_id
--     ON workspace_users (user_id, tenant_id);
--
-- -- so we can manually get the next id for workspace insert
-- -- we need to insert workspace_users and workspaces - so we need to provide the ID, not return it after insert
-- GRANT USAGE ON SEQUENCE workspaces_id_seq TO otai_admin;
