CREATE TABLE flow_step_type_configuration
(
    id                    bigint GENERATED BY DEFAULT AS IDENTITY
            PRIMARY KEY,
    created_at            timestamp NOT NULL,
    updated_at            timestamp,
    tenant_id             bigint    NOT NULL
        CONSTRAINT fk_flow_step_type_configuration_tenant_id__id
            REFERENCES tenants
            ON UPDATE RESTRICT ON DELETE RESTRICT,
   type                   text NOT NULL
       CHECK (type IN ('action', 'trigger')),
   name                   text NOT NULL,
   description            text,
   properties             jsonb NOT NULL
);