CREATE TABLE IF NOT EXISTS flow_executions (
    id                      bigint GENERATED BY DEFAULT AS IDENTITY
                            CONSTRAINT flow_executions_pkey1 
                            PRIMARY KEY,
    workspace_id            bigint   NOT NULL
                            CONSTRAINT fk_flow_executions_workspace_id__id
                            REFERENCES workspaces
                            ON UPDATE RESTRICT ON DELETE RESTRICT,
    tenant_id               bigint    NOT NULL
                            CONSTRAINT fk_flow_executions_tenant_id__id
                            REFERENCES tenants
                            ON UPDATE RESTRICT ON DELETE RESTRICT,
    properties              jsonb,
    status                  text,
    document_id             text,
    flow_configuration_id   text,
    updated_at              timestamp,
    created_at              timestamp NOT NULL
);