CREATE TABLE api_keys
(
    id          BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name        TEXT NOT NULL,
    description TEXT,
    api_key     TEXT NOT NULL,
    tenant_id   BIGINT NOT NULL,
    created_at  TIMESTAMP NOT NULL,
    updated_at  TIMESTAMP NOT NULL,

    CONSTRAINT tenant_name_unique
        UNIQUE (tenant_id, name),
    CONSTRAINT tenant_api_key_unique
        UNIQUE (tenant_id, api_key)
);
