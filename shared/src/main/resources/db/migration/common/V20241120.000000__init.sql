CREATE TABLE tenants
(
    id         bigint GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    created_at timestamp NOT NULL,
    updated_at timestamp,
    name       text      NOT NULL,
    origin_url text      NOT NULL
        CONSTRAINT tenants_origin_url_unique
            UNIQUE
);

GRANT DELETE, INSERT, SELECT, UPDATE ON tenants TO ${DB_USER};

CREATE TABLE users
(
    id         bigint GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    created_at timestamp NOT NULL,
    updated_at timestamp,
    email      text      NOT NULL,
    tenant_id  bigint    NOT NULL,
    CONSTRAINT users_email_tenant_id_unique
        UNIQUE (email, tenant_id)
);

GRANT DELETE, INSERT, SELECT, UPDATE ON users TO ${DB_USER};

CREATE TABLE workspaces
(
    id            bigint GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    created_at    timestamp NOT NULL,
    updated_at    timestamp,
    name          text      NOT NULL,
    description   text,
    tenant_id     bigint    NOT NULL
        CONSTRAINT fk_workspaces_tenant_id__id
            REFERENCES tenants
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    "key"         text      NOT NULL
        CONSTRAINT workspaces_key_length
            CHECK ((CHAR_LENGTH("key") >= 2) AND (CHAR_LENGTH("key") <= 20)),
    configuration jsonb,
    document_id   text,
    CONSTRAINT workspaces_name_tenant_id_unique
        UNIQUE (name, tenant_id),
    CONSTRAINT workspaces_key_tenant_id_unique
        UNIQUE ("key", tenant_id)
);

GRANT DELETE, INSERT, SELECT, UPDATE ON workspaces TO ${DB_USER};

CREATE TABLE audits
(
    id         bigint GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    user_id    bigint,
    tenant_id  bigint,
    table_name text                     NOT NULL,
    table_id   bigint,
    action     text                     NOT NULL,
    "value"    jsonb,
    created_at timestamp with time zone NOT NULL
);

-- make table insert only
REVOKE ALL ON TABLE audits FROM PUBLIC;
GRANT INSERT ON TABLE audits TO ${DB_SUPER_USER};
GRANT INSERT ON TABLE audits TO ${DB_USER};


CREATE TABLE automerge_data
(
    id        text   NOT NULL
        PRIMARY KEY,
    data      bytea  NOT NULL,
    tenant_id bigint NOT NULL
        CONSTRAINT fk_automerge_data_tenant_id__id
            REFERENCES tenants
            ON UPDATE RESTRICT ON DELETE RESTRICT
);

GRANT DELETE, INSERT, SELECT, UPDATE ON automerge_data TO ${DB_USER};


CREATE TABLE workspace_versions
(
    id            bigint GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    workspace_id  bigint    NOT NULL,
    configuration jsonb     NOT NULL,
    tenant_id     bigint    NOT NULL
        CONSTRAINT fk_workspace_versions_tenant_id__id
            REFERENCES tenants
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    created_at    timestamp NOT NULL,
    updated_at    timestamp NOT NULL
);

GRANT DELETE, INSERT, SELECT, UPDATE ON workspace_versions TO ${DB_USER};

CREATE TABLE foundations
(
    id                          bigint GENERATED BY DEFAULT AS IDENTITY
        CONSTRAINT foundations_pkey1
            PRIMARY KEY,
    created_at                  timestamp NOT NULL,
    updated_at                  timestamp,
    name                        text      NOT NULL,
    "key"                       text      NOT NULL
        CONSTRAINT foundations_key_length
            CHECK ((CHAR_LENGTH("key") >= 2) AND (CHAR_LENGTH("key") <= 20)),
    foundation_configuration_id text      NOT NULL,
    parent_id                   bigint
        CONSTRAINT fk_foundations_parent_id__id
            REFERENCES foundations
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    tenant_id                   bigint    NOT NULL
        CONSTRAINT fk_foundations_tenant_id__id
            REFERENCES tenants
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    workspace_id                bigint    NOT NULL
        CONSTRAINT fk_foundations_workspace_id__id
            REFERENCES workspaces
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT foundations_name_tenant_id_workspace_id_unique
        UNIQUE (name, tenant_id, workspace_id)
);

