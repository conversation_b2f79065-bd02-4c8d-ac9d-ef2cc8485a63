-- -- add all users to all workspaces for initial conversion
-- INSERT INTO workspace_users (user_id, workspace_id, status, access_level, created_at, updated_at, tenant_id)
-- SELECT u.id        AS user_id,
--        w.id        AS workspace_id,
--        'ACTIVE'    AS status,
--        '[
--          "SETTINGS",
--          "CONFIGURATION",
--          "COLLECTION"
--        ]'::jsonb   AS access_level,
--        NOW()       AS created_at,
--        NOW()       AS updated_at,
--        w.tenant_id AS tenant_id
-- FROM users u
--          CROSS JOIN
--      workspaces w
-- where u.tenant_id = w.tenant_id
--   AND NOT EXISTS (SELECT 1
--                   FROM workspace_users wu
--                   WHERE wu.user_id = u.id
--                     AND wu.workspace_id = w.id);
