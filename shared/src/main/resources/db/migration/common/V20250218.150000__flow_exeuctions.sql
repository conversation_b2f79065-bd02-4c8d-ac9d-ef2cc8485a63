-- add workspace_version_id column with FK constraints
ALTER TABLE flow_executions
    ADD COLUMN workspace_version_id bigint NULL
        CONSTRAINT fk_flow_executions_workspace_version_id__id
        REFERENCES workspace_versions;

-- Updated existing flow_executions to have a workspace_version_id
-- Best guess is to use the latest workspace_version created before the flow_execution
UPDATE flow_executions fe
SET workspace_version_id = (
    SELECT wv.id
    FROM workspace_versions wv
    WHERE wv.workspace_id = fe.workspace_id
      AND wv.created_at <= fe.created_at
    ORDER BY created_at DESC
    LIMIT 1
)
WHERE workspace_version_id IS NULL;

-- update the workspace_version_id column to enforce NOT NULL
ALTER TABLE flow_executions
    ALTER COLUMN workspace_version_id SET NOT NULL;
