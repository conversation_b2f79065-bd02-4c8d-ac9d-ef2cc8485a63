-- Drop existing unique constraints or indexes if necessary
DROP INDEX IF EXISTS foundations_key_tenant_workspace_unique;
DROP INDEX IF EXISTS foundations_name_tenant_id_workspace_id_unique;

CREATE UNIQUE INDEX foundations_key_config_tenant_workspace_unique ON foundations
    (UPPER("key"), foundation_configuration_id, tenant_id, workspace_id);

CREATE UNIQUE INDEX foundations_name_config_tenant_workspace_unique ON foundations
    (UPPER("name"),foundation_configuration_id, tenant_id, workspace_id);