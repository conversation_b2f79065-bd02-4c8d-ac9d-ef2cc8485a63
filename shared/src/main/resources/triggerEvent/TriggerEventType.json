{"triggerEventTypes": {"START_flow_manually_from_form": {"key": "START_flow_manually_from_form", "workspaceId": {"type": "text", "required": true}, "tenantId": {"type": "text", "required": true}, "createdAt": {"type": "date", "required": true}, "createdBy": {"type": "text", "required": true}, "eventProperties": {"identifier": "eventProperties", "type": "json", "properties": {"items": [{"type": "text", "identifier": "buttonLabel"}, {"type": "form.minimal", "identifier": "form"}]}}}, "START_flow_manually_from_foundation": {"key": "START_flow_manually_from_foundation", "workspaceId": {"type": "text", "required": true}, "tenantId": {"type": "text", "required": true}, "createdAt": {"type": "date", "required": true}, "createdBy": {"type": "text", "required": true}, "eventProperties": {"identifier": "eventProperties", "type": "json", "properties": {"items": [{"type": "text", "identifier": "buttonLabel"}, {"type": "foundation.minimal", "identifier": "foundation"}]}}}, "CREATE_collection_foundation": {"key": "CREATE_collection_foundation", "workspaceId": {"type": "text", "required": true}, "tenantId": {"type": "text", "required": true}, "createdAt": {"type": "date", "required": true}, "createdBy": {"type": "text", "required": true}, "eventProperties": {"identifier": "eventProperties", "type": "json", "properties": {"items": [{"type": "foundation.minimal", "identifier": "foundation"}]}}}, "UPDATE_collection_foundation": {"key": "UPDATE_collection_foundation", "workspaceId": {"type": "text", "required": true}, "tenantId": {"type": "text", "required": true}, "createdAt": {"type": "date", "required": true}, "createdBy": {"type": "text", "required": true}, "eventProperties": {"identifier": "eventProperties", "type": "json", "properties": {"items": [{"type": "foundation.minimal", "identifier": "foundation"}]}}}, "DELETE_collection_foundation": {"key": "DELETE_collection_foundation", "workspaceId": {"type": "text", "required": true}, "tenantId": {"type": "text", "required": true}, "createdAt": {"type": "date", "required": true}, "createdBy": {"type": "text", "required": true}, "eventProperties": {"identifier": "eventProperties", "type": "json", "properties": {"items": [{"type": "foundation.minimal", "identifier": "foundation"}]}}}, "CREATE_collection_form": {"key": "CREATE_collection_form", "workspaceId": {"type": "text", "required": true}, "tenantId": {"type": "text", "required": true}, "createdAt": {"type": "date", "required": true}, "createdBy": {"type": "text", "required": true}, "eventProperties": {"identifier": "eventProperties", "type": "json", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}]}}}, "UPDATE_collection_form": {"key": "UPDATE_collection_form", "workspaceId": {"type": "text", "required": true}, "tenantId": {"type": "text", "required": true}, "createdAt": {"type": "date", "required": true}, "createdBy": {"type": "text", "required": true}, "eventProperties": {"identifier": "eventProperties", "type": "json", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}]}}}, "DELETE_collection_form": {"key": "DELETE_collection_form", "workspaceId": {"type": "text", "required": true}, "tenantId": {"type": "text", "required": true}, "createdAt": {"type": "date", "required": true}, "createdBy": {"type": "text", "required": true}, "eventProperties": {"identifier": "eventProperties", "type": "json", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}]}}}, "UPDATE_collection_form_answer": {"key": "UPDATE_collection_form_answer", "workspaceId": {"type": "text", "required": true}, "tenantId": {"type": "text", "required": true}, "createdAt": {"type": "date", "required": true}, "createdBy": {"type": "text", "required": true}, "eventProperties": {"identifier": "eventProperties", "type": "json", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}, {"type": "question", "identifier": "question", "properties": {"required": true}}]}}}}}