[{"constraint": "foundations_name_config_tenant_workspace_unique", "localizationKey": "errors.common.name.duplicate", "context": "name"}, {"constraint": "foundations_key_config_tenant_workspace_unique", "localizationKey": "errors.common.key.duplicate", "context": "key"}, {"constraint": "workspaces_key_tenant_id_unique", "localizationKey": "errors.common.key.duplicate", "context": "key"}, {"constraint": "workspaces_name_tenant_id_unique", "localizationKey": "errors.common.name.duplicate", "context": "name"}]