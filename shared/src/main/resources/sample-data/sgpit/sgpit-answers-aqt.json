{"answers": {"a1sUhILky8o": {"questionId": "a1sUhILky8o", "type": "json", "value": {"a0MmBa2Z4m9": {"questionId": "a0MmBa2Z4m9", "type": "boolean"}, "a7amxY9Mgw3": {"questionId": "a7amxY9Mgw3", "type": "text"}, "Jr2yvUvarF": {"questionId": "Jr2yvUvarF", "type": "date"}, "Wx3v4jjJEx": {"questionId": "Wx3v4jjJEx", "type": "number"}, "kRaRD19Evg": {"questionId": "kRaRD19Evg", "type": "number"}, "smJF6zl1Dl": {"questionId": "smJF6zl1Dl", "type": "date"}, "v81aMab7he": {"questionId": "v81aMab7he", "type": "number"}}}, "JQIswpnlvm": {"questionId": "JQIswpnlvm", "type": "boolean", "value": true}, "VEOTahOeLj": {"questionId": "VEOTahOeLj", "type": "table", "value": {"entities": {"JEU4NFPAQpCQFFJKFXaQ1": {"columns": {"O1REtNdy50": {"questionId": "O1REtNdy50", "type": "text", "value": "cc"}, "eALIKF2hFv": {"questionId": "eALIKF2hFv", "type": "number", "value": "33"}}, "id": "JEU4NFPAQpCQFFJKFXaQ1"}, "Q7JjeX6MIGdhGaqU9n1TH": {"columns": {"O1REtNdy50": {"questionId": "O1REtNdy50", "type": "text", "value": "aa"}, "eALIKF2hFv": {"questionId": "eALIKF2hFv", "type": "number", "value": "11"}}, "id": "Q7JjeX6MIGdhGaqU9n1TH"}, "ryqry17ixv4vg5DtIQrp8": {"columns": {"O1REtNdy50": {"questionId": "O1REtNdy50", "type": "text", "value": "bb"}, "eALIKF2hFv": {"questionId": "eALIKF2hFv", "type": "number", "value": "22"}}, "id": "ryqry17ixv4vg5DtIQrp8"}}, "order": ["Q7JjeX6MIGdhGaqU9n1TH", "ryqry17ixv4vg5DtIQrp8", "JEU4NFPAQpCQFFJKFXaQ1"]}}, "mHhmVG6X7z": {"questionId": "mHhmVG6X7z", "type": "select", "value": "1"}, "np9GiUMruw": {"questionId": "np9GiUMruw", "type": "number", "value": "3.00"}, "oDI9O0xAKo": {"questionId": "oDI9O0xAKo", "type": "table", "value": {"entities": {"YzKDeaFCp7Qn2goL4anek": {"columns": {}, "id": "YzKDeaFCp7Qn2goL4anek"}}, "order": ["YzKDeaFCp7Qn2goL4anek"]}}, "plBrtlhc3W": {"questionId": "plBrtlhc3W", "type": "text", "value": "Hi"}, "r5QWKQYgKe": {"questionId": "r5QWKQYgKe", "type": "date", "value": "2025-04-01"}}, "id": 1314, "type": "FORM_ANSWER"}