{"id": 0, "answers": {"a401": {"questionId": "a401", "value": "ANZ", "type": "text"}, "a402": {"questionId": "a402", "value": "T09LL0001B", "type": "text"}, "a403": {"questionId": "a403", "value": "1001 Corporate Drive, Suite 400,\nMetropolis, NY, 10020, USA", "type": "text"}, "a410": {"questionId": "a410", "type": "table", "value": {"order": ["aa410row1"], "entities": {"aa410row1": {"id": "aa410row1", "columns": {"a500": {"questionId": "a500", "value": "<PERSON>", "type": "text"}, "a501": {"questionId": "a501", "value": "Company Secretary", "type": "text"}, "a502": {"questionId": "a502", "value": "<EMAIL>", "type": "text"}}}}}}, "a411": {"questionId": "a411", "type": "table", "value": {"order": ["aa411row1", "aa411row2", "aa411row3", "aa411row4"], "entities": {"aa411row1": {"id": "aa411row1", "columns": {"a504": {"questionId": "a504", "value": "ST", "type": "text"}, "a505": {"questionId": "a505", "value": "Short Term Assignment", "type": "text"}, "a506": {"questionId": "a506", "value": true, "type": "boolean"}, "a507": {"questionId": "a507", "value": "First and last year only", "type": "text"}, "a508": {"questionId": "a508", "value": "Not authorised", "type": "text"}}}, "aa411row2": {"id": "aa411row2", "columns": {"a504": {"questionId": "a504", "value": "LT", "type": "text"}, "a505": {"questionId": "a505", "value": "Long Term Assignment", "type": "text"}, "a506": {"questionId": "a506", "value": true, "type": "boolean"}, "a507": {"questionId": "a507", "value": "All years on assignment, plus trailing income", "type": "text"}, "a508": {"questionId": "a508", "value": "All years on assignment, plus trailing income", "type": "text"}}}, "aa411row3": {"id": "aa411row3", "columns": {"a504": {"questionId": "a504", "value": "Perm", "type": "text"}, "a505": {"questionId": "a505", "value": "Permanent Transfer", "type": "text"}, "a506": {"questionId": "a506", "value": false, "type": "boolean"}, "a507": {"questionId": "a507", "value": "First year only", "type": "text"}, "a508": {"questionId": "a508", "value": "First year only", "type": "text"}}}, "aa411row4": {"id": "aa411row4", "columns": {"a504": {"questionId": "a504", "value": "Rotator", "type": "text"}, "a505": {"questionId": "a505", "value": "Rotational Assignment", "type": "text"}, "a506": {"questionId": "a506", "value": false, "type": "boolean"}, "a507": {"questionId": "a507", "value": "All years on assignment, plus trailing income", "type": "text"}, "a508": {"questionId": "a508", "value": "All years on assignment, plus trailing income", "type": "text"}}}}}}, "a412": {"questionId": "a412", "type": "table", "value": {"order": ["aa412row1"], "entities": {"aa412row1": {"id": "aa412row1", "columns": {"a509": {"questionId": "a509", "value": "Ordinary Hours", "type": "text"}, "a510": {"questionId": "a510", "value": "Base Salary", "type": "text"}, "a511": {"questionId": "a511", "value": true, "type": "boolean"}, "a512": {"questionId": "a512", "value": false, "type": "boolean"}, "a513": {"questionId": "a513", "value": true, "type": "boolean"}}}}}}, "413": {"questionId": "UYH79GY0R5", "type": "json", "value": {"L8m588nAT-": {"questionId": "L8m588nAT-", "type": "number", "value": "1234"}, "MoFXrOG8uR": {"questionId": "MoFXrOG8uR", "type": "text", "value": "some text"}}}}}