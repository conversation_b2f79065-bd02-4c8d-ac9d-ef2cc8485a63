{"description": "SGPIT workspace", "errors": [], "flows": {"entities": {"E2Eamanualatriggerafromafoundationaflow": {"description": "E2Eamanualatriggerafromafoundationaflow", "id": "E2Eamanualatriggerafromafoundationaflow", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "E2Eamanualatriggerafromafoundationaflow", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Sample step just to keep the flow valid for now", "properties": {"variables": [{"identifier": "test", "type": "boolean", "value": true}]}, "variant": "setVariables"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "Send Email", "foundationConfigurationId": "cli", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "RDE2zjfrLkqmDCfa3E4r4": {"description": "A simple flow gets an answer from a form and sets it in the context", "id": "RDE2zjfrLkqmDCfa3E4r4", "labels": [], "metadata": {"createdAt": "2025-01-01T22:55:45.267Z", "updatedAt": "2025-01-01T22:55:45.267Z"}, "name": "flow2", "start": "step1", "startingVariables": [{"identifier": "notused", "properties": {"required": false}, "type": "list"}], "steps": {"step1": {"id": "step1", "name": "Get answer from form in context - string", "next": "step2", "properties": {"variables": [{"identifier": "s1value", "type": "text", "value": "{{form.plBrtlhc3W.answer}}"}]}, "variant": "setVariables"}, "step2": {"id": "step2", "name": "Get answer from form in context - number and date", "next": "step3", "properties": {"variables": [{"identifier": "s2value", "type": "number", "value": "{{form.np9GiUMruw.answer}}"}, {"identifier": "mydate", "type": "number", "value": "$YEAR({{form.r5QWKQYgKe.answer}})"}]}, "variant": "setVariables"}, "step3": {"id": "step3", "name": "Put table answers into variable - table", "next": "step4", "properties": {"variables": [{"identifier": "table", "properties": {"operation": "setTable"}, "type": "table", "value": "{{form.VEOTahOeLj.answer}}"}]}, "variant": "setVariables"}, "step4": {"id": "step4", "name": "Get answer from form in context - table", "properties": {"variables": [{"identifier": "s4value", "type": "text", "value": "{{form.VEOTahOeLj.answer[1].O1REtNdy50}}"}, {"identifier": "s5value", "type": "text", "value": "{{table[1].O1REtNdy50}}"}, {"identifier": "tableColumnValues", "type": "json", "value": "{{table.*.eALIKF2hFv}}"}, {"identifier": "tableColumnValueMultiplied", "type": "number", "value": "{{table[1].eALIKF2hFv}} * 10"}]}, "variant": "setVariables"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Flow 2", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "WRXHnwzRNXW46Rk5UjHi5": {"description": "A simple flow that sets variables in the context", "id": "WRXHnwzRNXW46Rk5UjHi5", "labels": [], "metadata": {"createdAt": "2025-01-01T22:55:45.267Z", "updatedAt": "2025-01-01T22:55:45.267Z"}, "name": "flow 1", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Literal addition", "next": "step2", "properties": {"variables": [{"identifier": "s1value", "type": "number", "value": "1 + 1"}, {"identifier": "s1valueTimes10", "type": "number", "value": "{{s1value}} * 10"}]}, "variant": "setVariables"}, "step2": {"id": "step2", "name": "Variable multiplication", "next": "step3", "properties": {"variables": [{"identifier": "s2value", "type": "number", "value": "{{s1value}} * 3"}]}, "variant": "setVariables"}, "step3": {"id": "step3", "name": "Variable summation", "next": "step4", "properties": {"variables": [{"identifier": "s3value", "type": "number", "value": "$sum([{{s1value}}, {{s2value}}])"}, {"identifier": "hardcodedArray", "type": "json", "value": "[1,2,3]"}, {"identifier": "hardcodedArraySize", "type": "number", "value": "$count({{hardcodedArray}})"}, {"identifier": "hardcodedStringArray", "type": "json", "value": "[\"a\",\"b\",\"c\"]"}, {"identifier": "calculatedStringArraySize", "type": "number", "value": "$count({{hardcodedStringArray}})"}, {"identifier": "calculatedStringArraySizeMultiplied", "type": "number", "value": "{{calculatedStringArraySize}} * 5"}]}, "variant": "setVariables"}, "step4": {"id": "step4", "name": "Literals", "next": "step5", "properties": {"variables": [{"identifier": "s4valueA", "type": "text", "value": "'hello world'"}, {"identifier": "s4valueB", "type": "number", "value": 5.5}]}, "variant": "setVariables"}, "step5": {"id": "step5", "name": "Set variable(s)", "properties": {"variables": [{"identifier": "TableColumnSum", "type": "number", "value": "$sum({{formVariable.VEOTahOeLj.columns.eALIKF2hFv.answer}})"}]}, "variant": "setVariables"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Flow 1", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a1079aE2Eamanualatriggerafromaformaflow": {"description": "a1079aE2Eamanualatriggerafromaformaflow", "id": "a1079aE2Eamanualatriggerafromaformaflow", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "1079aE2Eamanualatriggerafromaformaflow", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Set form answer", "properties": {"inputs": {"answer": "0.3", "formId": "{{form.id}}", "questionId": "krM9xaFvah", "questionTypeWithOperation": "no", "rowId": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Run Flow", "formConfigurationId": "a1132aE2Eaform", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a1106aE2Eacreateaformawhenafoundationacreated": {"description": "a1106aE2Eacreateaformawhenafoundationacreated", "id": "a1106aE2Eacreateaformawhenafoundationacreated", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "1106-E2E-create-form-when-foundation-created", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Create a form", "properties": {"inputs": {"formConfigurationId": "a1132aE2Eaform", "formVariableName": "form", "foundationId": "{{foundation.id}}", "intervalId": ""}, "typePrimaryIdentifier": "createForm"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "When a foundation is created", "next": "", "properties": {"inputs": {"foundationConfigurationId": "emp", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "foundationCreated"}, "variant": "trigger"}}}, "a1107aE2Easetavariableastep": {"description": "a1107aE2Easetavariableastep", "id": "a1107aE2Easetavariableastep", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "1107-E2E-set-variable-step", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Set variable(s)", "next": "step2", "properties": {"variables": [{"identifier": "TaxPayable", "type": "text", "value": "MULTIPLY({{form.O42Cd6cSeZ.answer}}* {{form.krM9xaFvah.answer}})"}]}, "variant": "setVariables"}, "step2": {"id": "step2", "name": "Set form answer", "properties": {"inputs": {"answer": "{{TaxPayable}}", "formId": "{{form.id}}", "questionId": "{{form.acxx72aoNN.id}}", "questionTypeWithOperation": "no", "rowId": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "When answer(s) changed in a form", "next": "", "properties": {"inputs": {"changedQuestionVariableName": "answer", "formConfigurationId": "a1132aE2Eaform", "formVariableName": "form", "questionIds": "[O42Cd6cSeZ]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "a1487aflowainaflowasetatableaanswerainaiterator": {"description": "", "id": "a1487aflowainaflowasetatableaanswerainaiterator", "labels": [], "metadata": {"createdAt": "2025-04-09T08:15:49.908Z", "updatedAt": "2025-04-09T08:15:49.908Z"}, "name": "1487 flow in flow set table answer in iterator", "start": "a4TrMGI19iRQKtIaW2pa7Y", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.a1872aformawithatable"}], "steps": {"a4TrMGI19iRQKtIaW2pa7Y": {"id": "a4TrMGI19iRQKtIaW2pa7Y", "metadata": {"createdAt": "2025-04-09T08:16:33.045Z", "updatedAt": "2025-04-09T08:16:33.045Z"}, "name": "For each", "next": "a5VAm6xu3LnGXU1gAFv7Dx", "properties": {"configuration": {"labels": [], "start": "a7F1V56yECRnBNl4wvkvit", "startingVariables": [{"identifier": "item_a4TrMGI19iRQKtIaW2pa7Y", "type": "json"}, {"identifier": "item_a4TrMGI19iRQKtIaW2pa7Y_index", "type": "number"}, {"identifier": "item_a4TrMGI19iRQKtIaW2pa7Y_output", "type": "unknown"}], "steps": {"Qr5MLh6vF9sZxh0vMnYeX": {"id": "Qr5MLh6vF9sZxh0vMnYeX", "metadata": {"createdAt": "2025-04-09T08:21:49.240Z", "updatedAt": "2025-04-09T08:21:49.240Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{answer}}", "columnId": "{{form.Io1TX1OJM2.columns.bihoQERn53.id}}", "formConfigurationId": "a1872aformawithatable", "formId": "{{form.id}}", "operation": "setCell", "questionId": "bihoQERn53", "questionTypeWithOperation": "table", "rowIndex": "{{item_a4TrMGI19iRQKtIaW2pa7Y_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "a7F1V56yECRnBNl4wvkvit": {"id": "a7F1V56yECRnBNl4wvkvit", "metadata": {"createdAt": "2025-04-09T08:49:26.064Z", "updatedAt": "2025-04-09T08:49:26.064Z"}, "name": "Set variable(s)", "next": "Qr5MLh6vF9sZxh0vMnYeX", "properties": {"variables": [{"identifier": "answer", "type": "text", "value": "$CONCAT(\"test_\", {{item_a4TrMGI19iRQKtIaW2pa7Y_index}})"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "false", "itemVariableName": "item_a4TrMGI19iRQKtIaW2pa7Y", "list": "{{form.Io1TX1OJM2.answer}}", "resultVariableName": "output__step_a4TrMGI19iRQKtIaW2pa7Y", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}, "a5VAm6xu3LnGXU1gAFv7Dx": {"id": "a5VAm6xu3LnGXU1gAFv7Dx", "metadata": {"createdAt": "2025-04-09T08:54:14.787Z", "updatedAt": "2025-04-09T08:54:14.787Z"}, "name": "For each", "next": "", "properties": {"configuration": {"labels": [], "start": "s67OyozaIMLt2kvzqfWBS", "startingVariables": [{"identifier": "item_a5VAm6xu3LnGXU1gAFv7Dx", "type": "json"}, {"identifier": "item_a5VAm6xu3LnGXU1gAFv7Dx_index", "type": "number"}, {"identifier": "item_a5VAm6xu3LnGXU1gAFv7Dx_output", "type": "unknown"}], "steps": {"s67OyozaIMLt2kvzqfWBS": {"id": "s67OyozaIMLt2kvzqfWBS", "metadata": {"createdAt": "2025-04-09T08:54:22.553Z", "updatedAt": "2025-04-09T08:54:22.553Z"}, "name": "Flow", "next": "", "properties": {"inputs": {"flowConfigurationId": "a1487asetatableaanswer", "form": "{{form.id}}", "index": "{{item_a5VAm6xu3LnGXU1gAFv7Dx_index}}"}}, "variant": "flow"}}}, "inputs": {"isReturn": "false", "itemVariableName": "item_a5VAm6xu3LnGXU1gAFv7Dx", "list": "{{form.Io1TX1OJM2.answer}}", "resultVariableName": "output__step_a5VAm6xu3LnGXU1gAFv7Dx", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"a21hqYTVik4ILv0RanBtsm": {"id": "a21hqYTVik4ILv0RanBtsm", "metadata": {"createdAt": "2025-04-09T08:15:57.021Z", "updatedAt": "2025-04-09T08:15:57.021Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "1487 flow in flow set table answer in iterator", "formConfigurationId": "a1872aformawithatable", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a1487aflowsainaflowsa3alevelsasetavariable": {"description": "", "endingVariables": [], "id": "a1487aflowsainaflowsa3alevelsasetavariable", "labels": [], "metadata": {"createdAt": "2025-04-11T03:17:27.978Z", "updatedAt": "2025-04-11T03:17:27.978Z"}, "name": "1487 flows in flows 3 levels set variable", "start": "eDfbrGPIvhwa3dWH5V4a3", "startingVariables": [{"identifier": "foundationFlow3", "properties": {"required": true}, "type": "foundation.wor"}], "steps": {"eDfbrGPIvhwa3dWH5V4a3": {"id": "eDfbrGPIvhwa3dWH5V4a3", "metadata": {"createdAt": "2025-04-15T04:03:24.474Z", "updatedAt": "2025-04-15T04:03:24.474Z"}, "name": "1487 flows in flows set variable", "next": "", "properties": {"inputs": {"flowConfigurationId": "a1487aflowsainaflowsasetavariable", "foundationFlow2": "{{foundationFlow3.id}}", "teesttt": "{{foundation.id}}"}}, "variant": "flow"}}, "triggers": {"hQTPormksEw40QFVFMUgc": {"id": "hQTPormksEw40QFVFMUgc", "metadata": {"createdAt": "2025-04-11T03:17:31.066Z", "updatedAt": "2025-04-11T03:17:31.066Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "1487 flows in flows 3 levels set variable", "foundationConfigurationId": "wor", "foundationVariableName": "foundationFlow3"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "a1487aflowsainaflowsasetavariable": {"description": "", "endingVariables": [{"identifier": "set_variable_foundation_id", "type": "text"}], "id": "a1487aflowsainaflowsasetavariable", "labels": [], "metadata": {"createdAt": "2025-04-08T06:16:49.767Z", "updatedAt": "2025-04-08T06:16:49.767Z"}, "name": "1487 flows in flows set variable", "start": "htToBa07NH6F9auhEnEOq", "startingVariables": [{"identifier": "foundationFlow2", "properties": {"required": true}, "type": "foundation.wor"}], "steps": {"htToBa07NH6F9auhEnEOq": {"id": "htToBa07NH6F9auhEnEOq", "metadata": {"createdAt": "2025-04-16T02:49:55.416Z", "updatedAt": "2025-04-16T02:49:55.416Z"}, "name": "1487 set variable helper", "next": "", "properties": {"inputs": {"flowConfigurationId": "a1487asetavariableahelper", "foundation": "{{foundationFlow2.id}}"}}, "variant": "flow"}}, "triggers": {"KrAaCspkB5UkUDgprMuNq": {"id": "KrAaCspkB5UkUDgprMuNq", "metadata": {"createdAt": "2025-04-08T06:16:53.287Z", "updatedAt": "2025-04-08T06:16:53.287Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "1487 flows in flows set variable", "foundationConfigurationId": "wor", "foundationVariableName": "foundationFlow2"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "a1487asetatableaanswer": {"description": "", "id": "a1487asetatableaanswer", "labels": [], "metadata": {"createdAt": "2025-04-09T08:16:44.074Z", "updatedAt": "2025-04-09T08:16:44.074Z"}, "name": "1487 set table answer - not to be manually triggered", "start": "x3IkP6aAAM1Eym9FcNH98", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.a1872aformawithatable"}, {"identifier": "index", "properties": {"required": true}, "type": "number"}], "steps": {"k3bWqm0hayLH9gJYxTzVa": {"id": "k3bWqm0hayLH9gJYxTzVa", "metadata": {"createdAt": "2025-04-09T08:51:00.447Z", "updatedAt": "2025-04-09T08:51:00.447Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{answer}}", "columnId": "{{form.Io1TX1OJM2.columns.bihoQERn53.id}}", "formConfigurationId": "a1872aformawithatable", "formId": "{{form.id}}", "operation": "setCell", "questionId": "bihoQERn53", "questionTypeWithOperation": "table", "rowIndex": "{{index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "x3IkP6aAAM1Eym9FcNH98": {"id": "x3IkP6aAAM1Eym9FcNH98", "metadata": {"createdAt": "2025-04-09T08:50:55.739Z", "updatedAt": "2025-04-09T08:50:55.739Z"}, "name": "Set variable(s)", "next": "k3bWqm0hayLH9gJYxTzVa", "properties": {"variables": [{"identifier": "answer", "type": "text", "value": "$CONCAT(\"testFlowInFlow_\",{{index}})"}]}, "variant": "setVariables"}}, "triggers": {"a1LYoN7caZziV6qlFXKDx0": {"id": "a1LYoN7caZziV6qlFXKDx0", "metadata": {"createdAt": "2025-04-09T08:16:47.858Z", "updatedAt": "2025-04-09T08:16:47.858Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "1487 set table answer - not to be manually triggered", "formConfigurationId": "a1872aformawithatable", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a1487asetavariableahelper": {"description": "", "endingVariables": [{"identifier": "set_variable_foundation_id", "type": "text"}], "id": "a1487asetavariableahelper", "labels": [], "metadata": {"createdAt": "2025-04-08T06:17:28.930Z", "updatedAt": "2025-04-08T06:17:28.930Z"}, "name": "1487 set variable helper", "start": "WYCHF6GrBCVsDEbDpxWA9", "startingVariables": [{"identifier": "foundation", "properties": {"required": true}, "type": "foundation.wor"}], "steps": {"WYCHF6GrBCVsDEbDpxWA9": {"id": "WYCHF6GrBCVsDEbDpxWA9", "metadata": {"createdAt": "2025-04-09T04:33:29.771Z", "updatedAt": "2025-04-09T04:33:29.771Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "set_variable_foundation_id", "type": "text", "value": "{{foundation.id}}"}]}, "variant": "setVariables"}}, "triggers": {"a542UVmNduYBZmmZKScwaX": {"id": "a542UVmNduYBZmmZKScwaX", "metadata": {"createdAt": "2025-04-08T06:17:34.407Z", "updatedAt": "2025-04-08T06:17:34.407Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "1487 set variable helper", "foundationConfigurationId": "wor", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "a1872afilteraformulaabasic": {"description": "", "endingVariables": [], "id": "a1872afilteraformulaabasic", "labels": [], "metadata": {"createdAt": "2025-04-30T02:07:09.164Z", "updatedAt": "2025-04-30T02:07:09.164Z"}, "name": "1872 filter formula basic", "start": "u6RLpBcmXVtBUfhwPVlD1", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.a1872aformawithatable"}], "steps": {"Zrh0PjhepRZ0Yrar5apu4": {"id": "Zrh0PjhepRZ0Yrar5apu4", "metadata": {"createdAt": "2025-05-02T06:44:58.653Z", "updatedAt": "2025-05-02T06:44:58.653Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{variable_1_first_2_rows}}", "formConfigurationId": "a1872aformawithatable", "formId": "{{form.id}}", "operation": "setTable", "questionId": "Io1TX1OJM2", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "u6RLpBcmXVtBUfhwPVlD1": {"id": "u6RLpBcmXVtBUfhwPVlD1", "metadata": {"createdAt": "2025-04-30T02:07:21.334Z", "updatedAt": "2025-04-30T02:07:21.334Z"}, "name": "Set variable(s)", "next": "Zrh0PjhepRZ0Yrar5apu4", "properties": {"variables": [{"identifier": "variable_1_first_2_rows", "properties": {"operation": "setTable"}, "type": "table", "value": "$FILTER({{form.Io1TX1OJM2.answer}}, [true,true,false])"}, {"identifier": "variable_2", "properties": {"operation": "setTable"}, "type": "table", "value": "$FILTER({{form.Io1TX1OJM2.answer}}, $LISTALL(true,true,false))"}, {"identifier": "variable_3", "properties": {"operation": "setTable"}, "type": "table", "value": "$FILTER({{form.Io1TX1OJM2.answer}}, $map([10,20,30], function($v, $i){$v>15}))"}, {"identifier": "variable_4", "properties": {"operation": "setTable"}, "type": "table", "value": "$FILTER({{form.Io1TX1OJM2.answer}}, $map({{form.Io1TX1OJM2.columns.Psnak4wzY6.answer}}, function($v, $i){$v<15}))"}, {"identifier": "variable_5", "properties": {"listOperation": "setList"}, "type": "list", "value": "$FILTER([\"1\",\"2\",\"3\"], [true,true,false])"}]}, "variant": "setVariables"}}, "triggers": {"Ju685WFbayW8uCvcw2yXY": {"id": "Ju685WFbayW8uCvcw2yXY", "metadata": {"createdAt": "2025-04-30T02:07:13.451Z", "updatedAt": "2025-04-30T02:07:13.451Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "1872 filter formula basic", "formConfigurationId": "a1872aformawithatable", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a35KCpseEhiQyUytboCi1": {"description": "", "endingVariables": [], "id": "a35KCpseEhiQyUytboCi1", "labels": [], "metadata": {"createdAt": "2025-06-02T06:12:42.015Z", "updatedAt": "2025-06-02T06:12:42.015Z"}, "name": "Pet Flow", "start": "abLb3gEsIDRKYo3rZ1aKC", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.a0ZjxHUe06Mm0VZSwKfSE"}], "status": "active", "steps": {"abLb3gEsIDRKYo3rZ1aKC": {"id": "abLb3gEsIDRKYo3rZ1aKC", "metadata": {"createdAt": "2025-06-02T06:15:32.686Z", "updatedAt": "2025-06-02T06:15:32.686Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "variable_1", "type": "text", "value": "{{form.id}}"}]}, "variant": "setVariables"}}, "triggers": {"ahEsErNnearKirRua62H5": {"id": "ahEsErNnearKirRua62H5", "metadata": {"createdAt": "2025-06-02T06:12:51.175Z", "updatedAt": "2025-06-02T06:12:51.175Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Pet Flow", "formConfigurationId": "a0ZjxHUe06Mm0VZSwKfSE", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a57wFN4WFBLAcFdfZ0ZHE": {"description": "", "endingVariables": [], "id": "a57wFN4WFBLAcFdfZ0ZHE", "labels": [], "metadata": {"createdAt": "2025-06-02T22:55:10.855Z", "updatedAt": "2025-06-02T22:55:10.855Z"}, "name": "Send Api Request", "start": "aj7ZKisgxGRhWshZIt3VK", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "status": "active", "steps": {"aj7ZKisgxGRhWshZIt3VK": {"id": "aj7ZKisgxGRhWshZIt3VK", "metadata": {"createdAt": "2025-06-02T22:55:44.148Z", "updatedAt": "2025-06-02T22:55:44.148Z"}, "name": "Send an API Request", "next": "", "properties": {"inputs": {"bodyParameters": "body parameter 1", "headers": [{"key": "VAL1", "value": "A"}, {"key": "VAL2", "value": "B"}, {"key": "VAL3", "value": "{{form.id}}"}], "httpMethod": "GET", "responseVariableName": "response__step_aj7ZKisgxGRhWshZIt3VK", "url": "http://localhost:8000/ai/api/build"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}}, "triggers": {"acxbiPJUpwV4f5tVN8Vfs": {"id": "acxbiPJUpwV4f5tVN8Vfs", "metadata": {"createdAt": "2025-06-02T22:55:18.122Z", "updatedAt": "2025-06-02T22:55:18.122Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Send Api Request", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aa411a1078aE2Eaupdateaansweraandaraiseaalert": {"description": "aa411a1078aE2Eaupdateaansweraandaraiseaalert", "id": "aa411a1078aE2Eaupdateaansweraandaraiseaalert", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "411-1078-E2E-update-answer-and-raise-alert", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Condition", "next": "step2", "properties": {"branches": [{"condition": {"lhs": "{{answer.answer}}", "operator": ">", "rhs": "100000"}, "name": "If", "next": "step2"}, {"condition": {"lhs": "", "operator": "=", "rhs": ""}, "name": "else"}]}, "variant": "condition"}, "step2": {"id": "step2", "name": "Set form alert", "properties": {"inputs": {"formId": "a1132aE2Eaform", "message": "Answer provided is greater than 100,000. Please check.", "questionId": "O42Cd6cSeZ", "variant": "blocker"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "When answer(s) changed in a form", "next": "", "properties": {"inputs": {"changedQuestionVariableName": "answer", "formConfigurationId": "a1132aE2Eaform", "formVariableName": "form", "questionIds": "[O42Cd6cSeZ]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "actiona1": {"description": "A simple flow that run the action of set answer", "id": "actiona1", "labels": [], "metadata": {"createdAt": "2025-01-01T22:55:45.267Z", "updatedAt": "2025-01-01T22:55:45.267Z"}, "name": "set answer action", "start": "step1", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"step1": {"id": "step1", "name": "Set answer", "next": "", "properties": {"inputs": {"answer": "by flow4", "formConfigurationId": "aqt", "formId": "{{form.id}}", "questionId": "plBrtlhc3W", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Action 1", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "af9Hjd4d7xd4N7vsCOv1Y": {"description": "", "endingVariables": [], "id": "af9Hjd4d7xd4N7vsCOv1Y", "labels": [], "metadata": {"createdAt": "2025-05-14T05:56:32.906Z", "updatedAt": "2025-05-14T05:56:32.906Z"}, "name": "Pairwise", "start": "avWITIW7XqxTzUzzMbCKd", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aK5h7h9Rhc8inDd0vho1K": {"id": "aK5h7h9Rhc8inDd0vho1K", "metadata": {"createdAt": "2025-05-14T05:57:06.423Z", "updatedAt": "2025-05-14T05:57:06.423Z"}, "name": "Set variable(s)", "next": "aq2KXnMpfHClFlTOyiRtU", "properties": {"variables": [{"identifier": "variable_1", "type": "json", "value": "$SUM([1,2], [3,4], [5,6])"}]}, "variant": "setVariables"}, "aq2KXnMpfHClFlTOyiRtU": {"id": "aq2KXnMpfHClFlTOyiRtU", "metadata": {"createdAt": "2025-05-14T06:08:23.516Z", "updatedAt": "2025-05-14T06:08:23.516Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "variable_z", "type": "json", "value": "$SUM([0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1])"}]}, "variant": "setVariables"}, "avWITIW7XqxTzUzzMbCKd": {"id": "avWITIW7XqxTzUzzMbCKd", "metadata": {"createdAt": "2025-05-14T06:03:58.571Z", "updatedAt": "2025-05-14T06:03:58.571Z"}, "name": "Set variable(s)", "next": "aK5h7h9Rhc8inDd0vho1K", "properties": {"variables": [{"identifier": "variable_a", "type": "json", "value": "[1,2]"}, {"identifier": "variable_b", "type": "json", "value": "[3,4]"}, {"identifier": "variable_c", "type": "json", "value": "[5,6]"}, {"identifier": "variable_d", "type": "json", "value": "$SUM({{variable_a}}, {{variable_b}}, {{variable_c}})"}]}, "variant": "setVariables"}}, "triggers": {"ahNmSIvqpCuCQc1CmgTrT": {"id": "ahNmSIvqpCuCQc1CmgTrT", "metadata": {"createdAt": "2025-05-14T05:56:42.428Z", "updatedAt": "2025-05-14T05:56:42.428Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Pairwise", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "conditiona1": {"description": "A condition example", "id": "conditiona1", "labels": [], "metadata": {"createdAt": "2025-01-01T22:55:45.267Z", "updatedAt": "2025-01-01T22:55:45.267Z"}, "name": "condition1", "start": "step1", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aOje31VdHjyVjR09EGx9T": {"id": "aOje31VdHjyVjR09EGx9T", "metadata": {"createdAt": "2025-05-27T05:28:59.669Z", "updatedAt": "2025-05-27T05:28:59.669Z"}, "name": "Condition", "next": "aoZMfab8njLx1BrQCufHs", "properties": {"branches": [{"condition": {"lhs": "{{form.np9GiUMruw.answer}}", "operator": "<", "rhs": "3"}, "name": "Check if number answer is less than 3", "next": "ahTW8BlNpNBttEdrgTkgr"}]}, "variant": "condition"}, "ahTW8BlNpNBttEdrgTkgr": {"id": "ahTW8BlNpNBttEdrgTkgr", "metadata": {"createdAt": "2025-05-28T04:20:13.488Z", "updatedAt": "2025-05-28T04:20:13.488Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "ConditionResult2", "type": "text", "value": "\"Number answer is < 3\""}]}, "variant": "setVariables"}, "akQBXMz0DtMF0X5teMfNL": {"id": "akQBXMz0DtMF0X5teMfNL", "metadata": {"createdAt": "2025-05-28T04:19:16.175Z", "updatedAt": "2025-05-28T04:19:16.175Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "ConditionResult1", "type": "text", "value": "\"Number answer is > 5\""}]}, "variant": "setVariables"}, "aoZMfab8njLx1BrQCufHs": {"id": "aoZMfab8njLx1BrQCufHs", "metadata": {"createdAt": "2025-05-28T04:20:46.132Z", "updatedAt": "2025-05-28T04:20:46.132Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "ConditionResult3", "type": "text", "value": "\"Number answer is between 3 and 5\""}]}, "variant": "setVariables"}, "step1": {"id": "step1", "name": "Simple Condition", "next": "aOje31VdHjyVjR09EGx9T", "properties": {"branches": [{"condition": {"AND": [{"lhs": "{{form.np9GiUMruw.answer}}", "operator": ">", "rhs": 5}]}, "name": "Check if number answer is greater than 5", "next": "step2"}]}, "variant": "condition"}, "step2": {"id": "step2", "name": "Triggered if condition passes", "next": "akQBXMz0DtMF0X5teMfNL", "properties": {"variables": [{"identifier": "conditionPassed", "type": "boolean", "value": true}]}, "variant": "setVariables"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Condition 1", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "iteratoraaggregate": {"description": "", "id": "iteratoraaggregate", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "iteratoraaggregate", "start": "kF4WCW2si5HYwWZdXnnFw", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"kF4WCW2si5HYwWZdXnnFw": {"id": "kF4WCW2si5HYwWZdXnnFw", "metadata": {"createdAt": "2025-03-13T06:04:11.116Z", "updatedAt": "2025-03-13T06:04:11.116Z"}, "name": "Aggregate", "next": "", "properties": {"configuration": {"labels": [], "start": "JLtazbmsG3RaymFOm0YWz", "steps": {"JLtazbmsG3RaymFOm0YWz": {"id": "JLtazbmsG3RaymFOm0YWz", "metadata": {"createdAt": "2025-03-13T06:07:14.218Z", "updatedAt": "2025-03-13T06:07:14.218Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "item_total", "type": "number", "value": "{{item_total}}+{{item.eALIKF2hFv}}"}]}, "variant": "setVariables"}}}, "inputs": {"itemVariableName": "item", "list": "{{form.VEOTahOeLj.answer}}", "resultVariableName": "item_total", "resultVariableType": "number"}, "typePrimaryIdentifier": "iteratorAggregate"}, "variant": "iterator"}}, "triggers": {"igO5lJWkpaau6DN4dHH8l": {"id": "igO5lJWkpaau6DN4dHH8l", "metadata": {"createdAt": "2025-03-13T06:03:39.706Z", "updatedAt": "2025-03-13T06:03:39.706Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Iterator Aggregate", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "iteratorafilter": {"description": "", "id": "iteratorafilter", "labels": [], "metadata": {"createdAt": "2025-03-13T04:10:53.984Z", "updatedAt": "2025-03-13T04:10:53.984Z"}, "name": "iteratorafilter", "start": "a368d0wtXJuSulauJ3AgiY", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"a368d0wtXJuSulauJ3AgiY": {"id": "a368d0wtXJuSulauJ3AgiY", "metadata": {"createdAt": "2025-03-13T04:11:21.058Z", "updatedAt": "2025-03-13T04:11:21.058Z"}, "name": "Filter", "next": "", "properties": {"configuration": {"labels": [], "start": "xaUvKzawHph6fJbP8vTjG", "steps": {"s1rY3yNBh5ipHSDUaaVqJ": {"id": "s1rY3yNBh5ipHSDUaaVqJ", "metadata": {"createdAt": "2025-03-13T04:13:27.073Z", "updatedAt": "2025-03-13T04:13:27.073Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "item_output", "type": "text", "value": "true"}]}, "variant": "setVariables"}, "xaUvKzawHph6fJbP8vTjG": {"id": "xaUvKzawHph6fJbP8vTjG", "metadata": {"createdAt": "2025-03-13T04:12:31.925Z", "updatedAt": "2025-03-13T04:12:31.925Z"}, "name": "Condition", "next": "", "properties": {"branches": [{"condition": {"lhs": "{{item_index}}", "operator": "<", "rhs": "3"}, "name": "If", "next": "s1rY3yNBh5ipHSDUaaVqJ"}]}, "variant": "condition"}}}, "inputs": {"itemValueType": "json", "itemVariableName": "item", "list": "{{form.VEOTahOeLj.answer}}", "resultVariableName": "filtered"}, "typePrimaryIdentifier": "iteratorFilter"}, "variant": "iterator"}}, "triggers": {"i4Y72MkiLnopCauYTZXs6": {"id": "i4Y72MkiLnopCauYTZXs6", "metadata": {"createdAt": "2025-03-13T04:10:57.060Z", "updatedAt": "2025-03-13T04:10:57.060Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Iterator Filter", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "iteratoraforeach": {"description": "", "id": "iteratoraforeach", "labels": [], "metadata": {"createdAt": "2025-03-14T00:40:25.298Z", "updatedAt": "2025-03-14T00:40:25.298Z"}, "name": "foreach", "start": "Jdp6LxciQWosjRBKT41pW", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"Jdp6LxciQWosjRBKT41pW": {"id": "Jdp6LxciQWosjRBKT41pW", "metadata": {"createdAt": "2025-03-14T00:40:50.530Z", "updatedAt": "2025-03-14T00:40:50.530Z"}, "name": "For each", "next": "", "properties": {"configuration": {"labels": [], "start": "oxvBCoWoUGfdgWMcrNaF4", "steps": {"oxvBCoWoUGfdgWMcrNaF4": {"id": "oxvBCoWoUGfdgWMcrNaF4", "metadata": {"createdAt": "2025-03-14T01:05:34.592Z", "updatedAt": "2025-03-14T01:05:34.592Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "item_output", "type": "text", "value": "{{item.O1REtNdy50}}"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "true", "itemVariableName": "item", "list": "{{form.VEOTahOeLj.answer}}", "resultVariableName": "output", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"nbKgpQl6GlMatQ4KShwGE": {"id": "nbKgpQl6GlMatQ4KShwGE", "metadata": {"createdAt": "2025-03-14T00:40:32.079Z", "updatedAt": "2025-03-14T00:40:32.079Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Iterator For Each", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "tablesa1": {"description": "A simple flow that sets values in a table", "id": "tablesa1", "labels": [], "metadata": {"createdAt": "2025-03-14T00:40:25.298Z", "updatedAt": "2025-03-14T00:40:25.298Z"}, "name": "tablesa1", "start": "a9QLLYeSf1fuyrp1vLqar8", "steps": {"a9QLLYeSf1fuyrp1vLqar8": {"id": "a9QLLYeSf1fuyrp1vLqar8", "metadata": {"createdAt": "2025-04-22T05:25:45.665Z", "updatedAt": "2025-04-22T05:25:45.665Z"}, "name": "Set variable(s)", "next": "step1", "properties": {"variables": [{"identifier": "variable_1", "type": "json", "value": "[\"a\",\"b\",\"c\"]"}]}, "variant": "setVariables"}, "step1": {"id": "step1", "metadata": {"createdAt": "2025-03-24T02:55:30.952Z", "updatedAt": "2025-03-24T02:55:30.952Z"}, "name": "Set form answer", "properties": {"inputs": {"answer": "{{variable_1}}", "columnId": "O1REtNdy50", "formConfigurationId": "aqt", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "VEOTahOeLj", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"trigger-tablesa1": {"id": "trigger-tablesa1", "metadata": {"createdAt": "2025-03-14T00:40:32.079Z", "updatedAt": "2025-03-14T00:40:32.079Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Tables 1", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}}, "order": ["a1872afilteraformulaabasic", "a1487asetatableaanswer", "a1487aflowainaflowasetatableaanswerainaiterator", "a1487asetavariableahelper", "a1487aflowsainaflowsasetavariable", "a1487aflowsainaflowsa3alevelsasetavariable", "tablesa1", "WRXHnwzRNXW46Rk5UjHi5", "RDE2zjfrLkqmDCfa3E4r4", "actiona1", "iteratoraforeach", "iteratorafilter", "iteratoraaggregate", "conditiona1", "a1106aE2Eacreateaformawhenafoundationacreated", "aa411a1078aE2Eaupdateaansweraandaraiseaalert", "a1079aE2Eamanualatriggerafromaformaflow", "a1107aE2Easetavariableastep", "E2Eamanualatriggerafromafoundationaflow", "af9Hjd4d7xd4N7vsCOv1Y", "a35KCpseEhiQyUytboCi1", "a57wFN4WFBLAcFdfZ0ZHE"]}, "forms": {"a0ZjxHUe06Mm0VZSwKfSE": {"content": [{"content": [{"content": [{"description": "", "id": "aYgCq4JRAI", "identifier": "DescribeYourFavouritePet", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Describe your favourite pet", "type": "text"}, {"description": "", "id": "atepF23vts", "identifier": "Pets", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "axhXIrL7sC", "identifier": "Name", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Name", "type": "text"}, {"description": "", "id": "az3aG1NYmI", "identifier": "Type", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "Cat", "value": "cat"}, {"label": "Dog", "value": "dog"}], "placeholder": "", "required": false}, "text": "Type", "type": "select"}], "required": false}, "text": "Pets", "type": "table"}], "id": "a1g7hQMcgn", "level": 2, "name": "General"}], "id": "aQAhBuAYIo", "level": 1, "name": "Pets"}], "description": "", "foundationId": "wor", "id": "a0ZjxHUe06Mm0VZSwKfSE", "key": "SIMPLEFORM", "labels": [], "level": 0, "metadata": {"createdAt": "2025-06-02T06:08:52.773Z", "updatedAt": "2025-06-02T06:08:52.774Z"}, "name": "Pet Form", "seriesId": "years"}, "a1132aE2Eaform": {"content": [{"content": [{"content": [{"description": "", "id": "krM9xaFvah", "identifier": "TaxRate", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Tax rate", "type": "number"}, {"description": "", "id": "O42Cd6cSeZ", "identifier": "TaxableIncome", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Taxable income", "type": "number"}, {"description": "", "id": "acxx72aoNN", "identifier": "TaxPayable", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Tax payable", "type": "number"}], "id": "a58abDgftqN", "level": 2, "name": "General"}], "id": "a8iFOZnmMlp", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "emp", "id": "a1132aE2Eaform", "key": "E2EFORM", "level": 0, "metadata": {"createdAt": "2024-12-19T22:46:59.388Z", "updatedAt": "2024-12-19T22:46:59.388Z"}, "name": "1132-E2E-form", "seriesId": "years"}, "a1872aformawithatable": {"content": [{"content": [{"content": [{"description": "", "id": "Io1TX1OJM2", "identifier": "Table", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "bihoQERn53", "identifier": "TableText", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "TableText", "type": "text"}, {"description": "", "id": "Psnak4wzY6", "identifier": "TableNumber", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "TableNumber", "type": "number"}]}, "text": "Table", "type": "table"}], "id": "za8cU4pPfq", "level": 2, "name": "General"}], "id": "RihXLevhKX", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "wor", "id": "a1872aformawithatable", "key": "FORMWITHTABLE", "level": 0, "metadata": {"createdAt": "2025-04-15T06:03:26.544Z", "updatedAt": "2025-04-15T06:03:26.544Z"}, "name": "1872 1487 form with table", "seriesId": ""}, "al": {"content": [{"content": [{"content": [{"id": "a4000", "identifier": "Contacts", "properties": {"columns": [{"id": "aa5000", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"required": true}, "text": "Assignee Name", "type": "text"}, {"id": "aa5010", "identifier": "AssignmentPolicy", "properties": {"options": [{"label": "ST", "value": "ST"}, {"label": "LT", "value": "LT"}, {"label": "Perm", "value": "Perm"}], "required": true}, "text": "Assignment Policy", "type": "select"}, {"id": "aa5020", "identifier": "HomeCountry", "properties": {"required": true}, "text": "Home Country", "type": "text"}, {"id": "a5030", "identifier": "HostCountry", "properties": {"required": true}, "text": "Host Country", "type": "text"}, {"id": "aa5040", "identifier": "AssignmentStart", "properties": {"required": true}, "text": "Assignment Start", "type": "date"}, {"id": "aa5050", "identifier": "AssignmentEnd", "properties": {"required": true}, "text": "Assignment End", "type": "date"}]}, "text": "Contacts", "type": "table"}], "id": "aa3000", "level": 2, "name": "General"}], "id": "aa2000", "level": 1, "name": "Assignment Information"}, {"content": [{"content": [{"id": "aa4010", "identifier": "AuthorisationList", "properties": {"columns": [{"id": "aa5100", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"required": true}, "text": "Assignee Name", "type": "text"}, {"id": "aa5110", "identifier": "AssignmentPolicy", "properties": {"options": [{"label": "ST", "value": "ST"}, {"label": "LT", "value": "LT"}, {"label": "Perm", "value": "Perm"}], "required": true}, "text": "Assignment Policy", "type": "select"}, {"id": "aa5120", "identifier": "PolicyType", "properties": {"options": [{"label": "Short Term Assignment", "value": "Short Term Assignment"}, {"label": "Long Term Assignment", "value": "Long Term Assignment"}, {"label": "Permanent Transfer", "value": "Permanent Transfer"}, {"label": "Rotational Assignment", "value": "Rotational Assignment"}], "required": true}, "text": "Policy Type", "type": "select"}, {"id": "aa5130", "identifier": "AuthorisationTaxReturn", "properties": {"required": true}, "text": "Authorisation - Tax Return", "type": "boolean"}, {"id": "a5140", "identifier": "AuthorisationTaxBriefing", "properties": {"required": true}, "text": "Authorisation - Tax Briefing", "type": "boolean"}, {"id": "a5150", "identifier": "AuthorisationReason", "properties": {"required": true}, "text": "Authorisation Reason", "type": "text"}, {"id": "a5160", "identifier": "SpouseAuthorisation", "properties": {"required": true}, "text": "Spouse Authorisation", "type": "boolean"}]}, "text": "Authorisation List", "type": "table"}], "id": "aa3010", "level": 2, "name": "General"}], "id": "aa2010", "level": 1, "name": "Authorisation List"}], "description": "Authorisation List", "foundationId": "cli", "id": "al", "key": "AL", "level": 0, "metadata": {"createdAt": "2024-11-01T14:00:00Z", "updatedAt": "2024-11-01T14:00:00Z"}, "name": "Authorisation List", "seriesId": "years"}, "aqt": {"content": [{"content": [{"content": [{"description": "", "id": "plBrtlhc3W", "identifier": "Text1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "default", "maxLength": 10, "minLength": 2, "placeholder": "enter 2 to 10 characters", "required": false}, "text": "Text", "type": "text"}, {"description": "", "id": "np9GiUMruw", "identifier": "Number", "properties": {"allowReuseAcrossForms": false, "decimalPlaces": 2, "defaultValue": 5.11, "max": 10.12, "min": 0.05, "placeholder": "Enter a number between 0 and 10", "required": false}, "text": "Number", "type": "number"}, {"description": "", "id": "mHhmVG6X7z", "identifier": "Select", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "Option1", "value": "1"}, {"label": "Option2", "value": "2"}], "placeholder": "Select something", "required": false}, "text": "Select", "type": "select"}, {"description": "", "id": "JQIswpnlvm", "identifier": "Boolean", "properties": {"allowReuseAcrossForms": false, "falseText": "NAH!", "required": false, "trueText": "YEAH!"}, "text": "Boolean", "type": "boolean"}, {"description": "", "id": "VEOTahOeLj", "identifier": "Table", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "O1REtNdy50", "identifier": "Tabletext", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "TableText1", "type": "text"}, {"description": "", "id": "eALIKF2hFv", "identifier": "Tablenumber", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "TableNumber1", "type": "number"}]}, "text": "Table", "type": "table"}], "id": "PtpLRE4nao", "level": 2, "name": "General"}, {"content": [{"description": "", "id": "r5QWKQYgKe", "identifier": "DateQuestion", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Date Question", "type": "date"}], "id": "JpLnfpfkMB", "level": 2, "name": "Nested Section"}], "id": "mQYkqKd53A", "level": 1, "name": "Untitled"}, {"content": [{"content": [{"description": "", "id": "kM03gL62Rj", "identifier": "Number_1", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number", "type": "number"}, {"description": "", "id": "RaEwuSTmEd", "identifier": "Accounting", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false, "type": "accounting"}, "text": "Accounting", "type": "number"}, {"description": "", "id": "a8FSiagy4Sc", "identifier": "Percentage", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false, "type": "percentage"}, "text": "Percentage", "type": "number"}], "id": "C1vc81cdpH", "level": 2, "name": "General"}], "id": "BvBf8mal3H", "level": 1, "name": "Number Question ypes"}, {"content": [{"content": [{"description": "", "id": "t3Caagn0aZ", "identifier": "Date", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Date", "type": "date"}, {"description": "", "id": "p4war6g2xR", "identifier": "DateWithRange", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "max": "2030-02-01", "min": "2020-02-01", "required": false}, "text": "Date with range", "type": "date"}], "id": "z7KpgNcpZB", "level": 2, "name": "General"}], "id": "NuphYvYJMA", "level": 1, "name": "Date Question Types"}, {"content": [{"content": [{"description": "", "id": "akxUewEj3Q", "identifier": "Select_1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "a"}, {"label": "", "value": "b"}, {"label": "", "value": "c"}], "placeholder": "", "required": false}, "text": "Select", "type": "select"}, {"description": "", "id": "IROlcRdJSA", "identifier": "MultiSelect", "properties": {"allowReuseAcrossForms": false, "isMultiSelect": true, "options": [{"label": "", "value": "a"}, {"label": "", "value": "b"}, {"label": "", "value": "c"}], "placeholder": "", "required": false}, "text": "Multi Select", "type": "multiSelect"}], "id": "fSpJzEdjAx", "level": 2, "name": "General"}], "id": "sUbaxCCmnh", "level": 1, "name": "Select Question Types"}, {"content": [{"content": [{"description": "", "id": "oDI9O0xAKo", "identifier": "Table_1", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "K2mO2ZztGH", "identifier": "Text", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text", "type": "text"}, {"description": "", "id": "zTHSWvUxu5", "identifier": "Number", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number", "type": "number"}, {"description": "", "id": "UYpXBe7SkM", "identifier": "Numberaccounting", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false, "type": "accounting"}, "text": "Number(Accounting)", "type": "number"}, {"description": "", "id": "UuvVH4Ru70", "identifier": "Numberpercentage", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number(Percentage)", "type": "number"}, {"description": "", "id": "PBToKkez0K", "identifier": "Date", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Date", "type": "date"}, {"description": "", "id": "a7HBVtb1kLz", "identifier": "DateWithRange", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "max": "2030-02-02", "min": "2020-02-01", "required": false}, "text": "Date with range", "type": "date"}, {"description": "", "id": "YIFgX4PrMl", "identifier": "Select", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "a", "value": "a"}, {"label": "b", "value": "b"}, {"label": "c", "value": "c"}], "placeholder": "", "required": false}, "text": "Select", "type": "select"}, {"description": "", "id": "LXJKuURzuI", "identifier": "MultiSelect", "properties": {"allowReuseAcrossForms": false, "isMultiSelect": true, "options": [{"label": "d", "value": "d"}, {"label": "e", "value": "e"}, {"label": "f", "value": "f"}], "placeholder": "", "required": false}, "text": "Multi select", "type": "multiSelect"}, {"description": "", "id": "ckrCdRRoW3", "identifier": "Boolean", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Boolean", "type": "boolean"}]}, "text": "Table", "type": "table"}], "id": "dtEZZ5cilF", "level": 2, "name": "General"}], "id": "a8n6VJUh5x3", "level": 1, "name": "Table with all question types"}, {"content": [{"content": [{"description": "", "id": "a1sUhILky8o", "identifier": "Json", "properties": {"allowReuseAcrossForms": false, "items": [{"description": "", "id": "a7amxY9Mgw3", "identifier": "Text", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text", "type": "text"}, {"description": "", "id": "v81aMab7he", "identifier": "Number", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number", "type": "number"}, {"description": "", "id": "Wx3v4jjJEx", "identifier": "Numberaccounting", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number(Accounting)", "type": "number"}, {"description": "", "id": "kRaRD19Evg", "identifier": "Numberpercentage", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number(Percentage)", "type": "number"}, {"description": "", "id": "smJF6zl1Dl", "identifier": "Date", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Date", "type": "date"}, {"description": "", "id": "Jr2yvUvarF", "identifier": "DateWithRange", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "max": "2030-02-01", "min": "2020-02-01", "required": false}, "text": "Date with range", "type": "date"}, {"description": "", "id": "a0MmBa2Z4m9", "identifier": "Boolean", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Boolean", "type": "boolean"}]}, "text": "JSON", "type": "json"}], "id": "uJPfbT8OKA", "level": 2, "name": "General"}], "id": "sFPQVPLMT8", "level": 1, "name": "JSON with all question types"}, {"content": [{"content": [{"description": "", "id": "VA7AgyzZST", "identifier": "Upload", "properties": {"allowReuseAcrossForms": false, "max": 10, "maxFileSizeMB": 100, "required": false}, "text": "Upload", "type": "files"}], "id": "wgAeQ7agOE", "level": 2, "name": "General"}], "id": "HxyZLPxe6y", "level": 1, "name": "Upload Document"}], "description": "", "foundationId": "wor", "id": "aqt", "key": "AQT", "level": 0, "metadata": {"createdAt": "2024-12-18T02:18:55.090Z", "updatedAt": "2024-12-18T02:18:55.090Z"}, "name": "All question types", "seriesId": ""}, "cp": {"content": [{"content": [{"content": [{"id": "a401", "identifier": "EmployerName", "properties": {"required": true}, "text": "Employer Name", "type": "text"}, {"id": "a402", "identifier": "UEN", "properties": {"required": true}, "text": "UEN", "type": "text"}, {"id": "a403", "identifier": "AddressOfEmployer", "properties": {"isTextArea": true, "required": true}, "text": "Address of Employer", "type": "text"}, {"id": "a410", "identifier": "Contacts", "properties": {"columns": [{"id": "a500", "identifier": "Name", "properties": {"required": true}, "text": "Name", "type": "text"}, {"id": "a501", "identifier": "Designation", "properties": {"required": true}, "text": "Designation", "type": "text"}, {"id": "a502", "identifier": "Email", "properties": {"required": true}, "text": "Email", "type": "text"}]}, "text": "Contacts", "type": "table"}], "id": "a300", "level": 2, "name": "General"}], "id": "a200", "level": 1, "name": "Basic Information"}, {"content": [{"content": [{"id": "a411", "identifier": "AuthorisationPolicy", "properties": {"columns": [{"id": "a504", "identifier": "AssignmentPolicy", "properties": {"options": [{"label": "ST", "value": "ST"}, {"label": "LT", "value": "LT"}, {"label": "Perm", "value": "Perm"}, {"label": "Rotator", "value": "Rotator"}], "required": true}, "text": "Assignment Policy", "type": "select"}, {"id": "a505", "identifier": "PolicyType", "properties": {"options": [{"label": "Short Term Assignment", "value": "Short Term Assignment"}, {"label": "Long Term Assignment", "value": "Long Term Assignment"}, {"label": "Permanent Transfer", "value": "Permanent Transfer"}, {"label": "Rotational Assignment", "value": "Rotational Assignment"}], "required": false}, "text": "Policy Type", "type": "select"}, {"id": "a506", "identifier": "Equalised", "properties": {"required": true}, "text": "Equalised", "type": "boolean"}, {"id": "a507", "identifier": "Authorisation", "properties": {"required": true}, "text": "Authorisation", "type": "text"}, {"id": "a508", "identifier": "SpouseAuthorisation", "properties": {"required": true}, "text": "Spouse Authorisation", "type": "text"}]}, "text": "Authorisation Policy", "type": "table"}], "id": "a301", "level": 2, "name": "General"}], "id": "a201", "level": 1, "name": "Authorisation"}, {"content": [{"content": [{"id": "a412", "identifier": "CompensationDataMapping", "properties": {"columns": [{"id": "a509", "identifier": "PayrollDescription", "properties": {"required": true}, "text": "Payroll Description", "type": "text"}, {"id": "a510", "identifier": "StandardMapping", "properties": {"required": true}, "text": "Standard Mapping", "type": "text"}, {"id": "a511", "identifier": "Equalised", "properties": {"falseText": "False", "required": true, "trueText": "True"}, "text": "Equalised", "type": "boolean"}, {"id": "a512", "identifier": "TaxProtected", "properties": {"falseText": "False", "required": true, "trueText": "True"}, "text": "Tax Protected", "type": "boolean"}, {"id": "a513", "identifier": "GrossUp", "properties": {"falseText": "False", "required": true, "trueText": "True"}, "text": "Gross Up", "type": "boolean"}]}, "text": "Compensation Data Mapping", "type": "table"}], "id": "a302", "level": 2, "name": "General"}], "id": "a202", "level": 1, "name": "Compensation Data"}], "description": "Client Positions", "foundationId": "cli", "id": "cp", "key": "CP", "level": 0, "metadata": {"createdAt": "2024-11-01T14:00:00Z", "updatedAt": "2024-11-01T14:00:00Z"}, "name": "Client Positions", "seriesId": "years"}, "ef": {"content": [{"content": [{"content": [{"description": "", "id": "bghZGAFyqa", "identifier": "Untitled", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Untitled", "type": "text"}, {"description": "", "id": "T0zmaImvZa", "identifier": "TotalIncome", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false, "type": "accounting"}, "text": "What's the employee's total yearly income?", "type": "number"}], "id": "HcaMTY38zl", "level": 2, "name": "General"}], "id": "gnYiNzjJQq", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "emp", "id": "ef", "key": "EF", "level": 0, "metadata": {"createdAt": "2024-12-19T22:46:59.388Z", "updatedAt": "2024-12-19T22:46:59.388Z"}, "name": "Employee Form", "seriesId": "years"}}, "foundations": {"entities": {"cli": {"description": "Client", "id": "cli", "metadata": {"createdAt": "2024-12-15T22:15:29.922111Z", "updatedAt": "2024-12-15T22:15:29.922111Z"}, "name": "Client", "relationship": "OneToMany"}, "emp": {"description": "Employee", "id": "emp", "metadata": {"createdAt": "2024-12-15T22:15:29.926127Z", "updatedAt": "2024-12-15T22:15:29.926127Z"}, "name": "Employee", "relationship": "OneToMany"}, "wor": {"id": "wor", "metadata": {"createdAt": "2024-12-15T22:15:29.945221Z", "updatedAt": "2024-12-15T22:15:29.945221Z"}, "name": "Workspace", "relationship": "OneToMany"}}, "order": ["wor", "cli", "emp"]}, "id": 303, "key": "PXRSGPIT4013", "labels": {}, "metadata": {"createdAt": "2024-12-15T22:15:29.931694Z", "updatedAt": "2024-12-15T22:15:29.931696Z"}, "name": "PXR SGPIT 4013", "series": {"years": {"description": "", "id": "years", "intervals": {"entities": {"aa2024": {"id": "aa2024", "name": "2024"}, "aa2025": {"id": "aa2025", "name": "2025"}}, "order": ["aa2024", "aa2025"]}, "metadata": {"createdAt": "2024-12-15T22:16:39.908Z", "updatedAt": "2024-12-15T22:16:39.908Z"}, "name": "Years"}}, "type": "WORKSPACE_CONFIGURATION"}