{"annotations": {"aTz40IF0Qi3c3QuMQljTD": {"attachments": [], "createdAt": "2025-06-28T01:17:36.472Z", "createdBy": {"type": "user", "userId": 1}, "id": "aTz40IF0Qi3c3QuMQljTD", "location": {"questionId": "aC2mcecXgh", "variant": "question"}, "message": "Note: List schema flows will fail now. If we support it later, follow below:\nTo trigger Receive Webhook list schema\nUse Bruno\nPOST request to: http://localhost:8000/ai/api/webhooks/workspaces/{{workspaceId}}/flows/2eWIo3r9W0GlPt0o3PIr0\nWith body:\n[{\n\"number_1\": 123,\n\"text_1\": \"qwe\",\n\"boolean_1\": true,\n\"form\": {\n\"shouldBeFiltered\": 123,\n\"shouldBeFiltered2\": \"abc\",\n\"id\": {{form.id}}\n}\n}]", "updatedAt": "2025-07-21T05:44:09.458Z", "updatedBy": {"type": "user", "userId": 1}, "variant": "comment"}, "ahcHtNGggROnbTwT7sj4m": {"attachments": [], "createdAt": "2025-06-28T01:15:49.737Z", "createdBy": {"type": "user", "userId": 1}, "id": "ahcHtNGggROnbTwT7sj4m", "location": {"questionId": "aC2mcecXgh", "variant": "question"}, "message": "1. Run \"Start Schema Tests\" to clear and populate the Actual Result column.\n2. In Expected Result for both Receive Webhook tests, replace form.id with actual value.\n 3. Replace Actual Result with any value for list schema tests (eg: {}).\n4. Run \"Check Test Results\" to clear and populate the last column.", "updatedAt": "2025-06-28T01:17:36.472Z", "updatedBy": {"type": "user", "userId": 1}, "variant": "comment"}, "aqNNhKLaKMB0iTZQ2aFft": {"attachments": [], "createdAt": "2025-06-28T01:33:21.540Z", "createdBy": {"type": "user", "userId": 1}, "id": "aqNNhKLaKMB0iTZQ2aFft", "location": {"questionId": "aC2mcecXgh", "variant": "question"}, "message": "Note: List schema flows will fail now. If we support it later, follow below:\nStart Schema tests fail because of Send API Request list schema. Will need below in DefaultRoute:\nget(\"/build2\") {\n        val jsonString = \"\"\"[{\n            \"number_1\": 123,\n            \"text_1\": \"qwe\",\n            \"boolean_1\": true\n        }]\"\"\".trimIndent()\n        call.respondText(text = jsonString, contentType = ContentType.Text.JavaScript)\n    }", "updatedAt": "2025-07-21T05:44:18.246Z", "updatedBy": {"type": "user", "userId": 1}, "variant": "comment"}}, "id": 117, "state": {"sectionsCollapsed": {"agyF5CgniJ": false}}}