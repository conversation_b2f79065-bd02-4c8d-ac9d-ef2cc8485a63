{"errors": [], "flows": {"entities": {"aA6A7kQGadU9zCG0YEdF3": {"description": "", "endingVariables": [{"identifier": "AssigneeTaxReturnId", "type": "text"}], "id": "aA6A7kQGadU9zCG0YEdF3", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-12T11:55:38.806Z", "updatedAt": "2025-04-14T03:32:04.603Z"}, "name": "6. Approve authorisation list and create tax return forms", "start": "bhWe6FQKoNyKTbDpuRTG2", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.U3mqHJmaz9kEdWsGISca6"}], "steps": {"SmfCGaJUkJt9fPyU7SMcL": {"id": "SmfCGaJUkJt9fPyU7SMcL", "metadata": {"createdAt": "2025-03-12T11:56:20.815Z", "updatedAt": "2025-03-12T11:56:20.815Z"}, "name": "Send tax returns", "next": "", "properties": {"configuration": {"start": "J4nnhqcWaIKQ7qiLL8yhZ", "steps": {"aUCzjcRAACTGXW7x6ztxR": {"id": "aUCzjcRAACTGXW7x6ztxR", "metadata": {"createdAt": "2025-03-12T12:07:56.085Z", "updatedAt": "2025-03-12T12:07:56.085Z"}, "name": "Contains = True?", "next": "", "properties": {"branches": [{"condition": {"lhs": "{{YearsAuthorised}}", "operator": "contains", "rhs": "{{form.a2Kh0a6jFL.answer}}"}, "name": "If", "next": "UMNQ6wW1RxUwIZqCCgaM1"}]}, "variant": "condition"}, "J4nnhqcWaIKQ7qiLL8yhZ": {"id": "J4nnhqcWaIKQ7qiLL8yhZ", "metadata": {"createdAt": "2025-03-12T12:05:37.637Z", "updatedAt": "2025-03-12T12:05:37.637Z"}, "name": "Set variable(s)", "next": "aUCzjcRAACTGXW7x6ztxR", "properties": {"variables": [{"identifier": "YearsAuthorised", "type": "text", "value": "$INDEX({{form.Qg1l7LvjeN.columns.a2qwFnLFof.answer}},{{Authorisations_index}})"}, {"identifier": "CONTAINS", "type": "text", "value": "$CONTAINS({{YearsAuthorised}},{{form.a2Kh0a6jFL.answer}})"}, {"identifier": "Assignee<PERSON><PERSON>", "type": "text", "value": "$INDEX({{form.Qg1l7LvjeN.columns.TroPGwZZrc.answer}},{{Authorisations_index}})"}]}, "variant": "setVariables"}, "UMNQ6wW1RxUwIZqCCgaM1": {"id": "UMNQ6wW1RxUwIZqCCgaM1", "metadata": {"createdAt": "2025-03-13T06:02:30.615Z", "updatedAt": "2025-03-13T06:02:30.615Z"}, "name": "Select Foundation", "next": "g3BN8Aa6pU0uBdmpFHAqr", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "AssigneeFoundation", "key": "{{AssigneeId}}", "parentId": ""}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "g3BN8Aa6pU0uBdmpFHAqr": {"id": "g3BN8Aa6pU0uBdmpFHAqr", "metadata": {"createdAt": "2025-04-18T03:20:51.086Z", "updatedAt": "2025-04-18T03:20:51.086Z"}, "name": "Get assignee tax return form", "next": "z6kNdr8t0pewLd9ag9dM7", "properties": {"inputs": {"formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "As<PERSON>eeForm", "foundationId": "{{AssigneeFoundation.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectOrCreateForm"}, "variant": "action"}, "z6kNdr8t0pewLd9ag9dM7": {"id": "z6kNdr8t0pewLd9ag9dM7", "metadata": {"createdAt": "2025-04-18T03:14:47.352Z", "updatedAt": "2025-04-18T03:14:47.352Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "AssigneeTaxReturnId", "properties": {}, "type": "text", "value": "{{AssigneeForm.id}}"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "false", "itemVariableName": "Authorisations", "list": "{{form.Qg1l7LvjeN.answer}}", "resultVariableName": "output__step_SmfCGaJUkJt9fPyU7SMcL", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}, "bhWe6FQKoNyKTbDpuRTG2": {"id": "bhWe6FQKoNyKTbDpuRTG2", "metadata": {"createdAt": "2025-04-18T03:14:38.999Z", "updatedAt": "2025-04-18T03:14:38.999Z"}, "name": "Set variable(s)", "next": "SmfCGaJUkJt9fPyU7SMcL", "properties": {"variables": [{"identifier": "AssigneeTaxReturnId", "type": "text", "value": ""}]}, "variant": "setVariables"}}, "triggers": {"aZexb5H7JEa2zdGW3Q2li": {"id": "aZexb5H7JEa2zdGW3Q2li", "metadata": {"createdAt": "2025-03-12T11:55:41.313Z", "updatedAt": "2025-03-12T11:55:41.313Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Send Tax Returns", "formConfigurationId": "U3mqHJmaz9kEdWsGISca6", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "asSPezA0cnfSzSC83ZnjZ": {"description": "", "id": "asSPezA0cnfSzSC83ZnjZ", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T06:15:08.761Z", "updatedAt": "2025-04-14T03:34:13.035Z"}, "name": "Set answer from another form", "start": "qp9L3oS5f8lLoWIKQNzSN", "startingVariables": [{"identifier": "form", "type": "form.oXUOfkMKayra9lBi5NOvj"}], "steps": {"CktqMgqTbwIKNruhNunuE": {"id": "CktqMgqTbwIKNruhNunuE", "metadata": {"createdAt": "2025-03-05T06:15:46.852Z", "updatedAt": "2025-03-05T06:15:46.852Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{ClientForm.Dal4SiZ9kT.answer}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "questionId": "t60PjuSTaB", "questionTypeWithOperation": "no", "rowId": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "qp9L3oS5f8lLoWIKQNzSN": {"id": "qp9L3oS5f8lLoWIKQNzSN", "metadata": {"createdAt": "2025-03-05T06:15:25.653Z", "updatedAt": "2025-03-05T06:15:25.653Z"}, "name": "Select form", "next": "CktqMgqTbwIKNruhNunuE", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "eatFwcRAAByzHwV92fmEf", "formVariableName": "ClientForm", "foundationId": "{{form.foundation.parentId}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}}, "triggers": {"alRrvE8q4zIGaSzmP3y8R": {"id": "alRrvE8q4zIGaSzmP3y8R", "metadata": {"createdAt": "2025-03-05T06:15:10.949Z", "updatedAt": "2025-03-05T06:15:10.949Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "GET CLIENT ANSWER", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aTPYchZaa77BEaguqh3YP": {"description": "", "id": "aTPYchZaa77BEaguqh3YP", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-02-28T00:57:38.857Z", "updatedAt": "2025-04-14T03:30:53.633Z"}, "name": "12. Calculate days in SG during sourcing period", "start": "IZOhyaXdTaK2MXMJlRq77", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.SG14waZgbati8MISSWyVN"}], "status": "active", "steps": {"C7bTtFsZ8W4sUXxhaVRlT": {"id": "C7bTtFsZ8W4sUXxhaVRlT", "metadata": {"createdAt": "2025-03-20T05:56:47.949Z", "updatedAt": "2025-03-20T05:56:47.949Z"}, "name": "Set variable(s)", "next": "XY1Mtcz6f20bVpbdIl0ec", "properties": {"variables": [{"identifier": "HomeCountry", "type": "text", "value": "$INDEX({{AssigneeForm.T7IoTXkA0l.columns.MGJXlvwkBj.answer}},1)"}, {"identifier": "HostCountry", "type": "text", "value": "$INDEX({{AssigneeForm.T7IoTXkA0l.columns.apRPubvsrG.answer}},1)"}, {"identifier": "AssignmentStart", "type": "date", "value": "$INDEX({{AssigneeForm.T7IoTXkA0l.columns.av7k23SmGg.answer}},1)"}, {"identifier": "AssignmentEnd", "type": "text", "value": "$INDEX({{AssigneeForm.T7IoTXkA0l.columns.VuaSF5PWY9.answer}},1)"}]}, "variant": "setVariables"}, "IZOhyaXdTaK2MXMJlRq77": {"id": "IZOhyaXdTaK2MXMJlRq77", "metadata": {"createdAt": "2025-03-20T05:56:09.798Z", "updatedAt": "2025-03-20T05:56:09.798Z"}, "name": "Get assignee form", "next": "C7bTtFsZ8W4sUXxhaVRlT", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "V0UCzZ49ccFOYy39zOPOh", "formVariableName": "As<PERSON>eeForm", "foundationId": "{{form.foundation.id}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "XY1Mtcz6f20bVpbdIl0ec": {"id": "XY1Mtcz6f20bVpbdIl0ec", "metadata": {"createdAt": "2025-03-20T06:01:11.512Z", "updatedAt": "2025-03-20T06:01:11.512Z"}, "name": "For each", "next": "", "properties": {"configuration": {"start": "d8h2yDveFjb1NPGg4C6aA", "steps": {"aSskWRUqGaa4EBzB04x1Q": {"id": "aSskWRUqGaa4EBzB04x1Q", "metadata": {"createdAt": "2025-03-20T06:02:48.013Z", "updatedAt": "2025-03-20T06:02:48.013Z"}, "name": "Assignment started during the sourcing period?", "next": "NH43Xk3tj9XgoHl2nzqDa", "properties": {"branches": [{"condition": {"AND": [{"lhs": "{{AssignmentStart}}", "operator": ">=", "rhs": "{{SourcingStart}}"}, {"lhs": "{{AssignmentStart}}", "operator": "<=", "rhs": "{{SourcingEnd}}"}]}, "name": "If", "next": "uBwNQaaKFFgcXwmtpS61a"}]}, "variant": "condition"}, "awYwa26B9N84ImJPov1Rn": {"id": "awYwa26B9N84ImJPov1Rn", "metadata": {"createdAt": "2025-03-20T06:09:36.140Z", "updatedAt": "2025-03-20T06:09:36.140Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{DaysInSG}}", "columnId": "{{form.hDaERkazBA.columns.pGsLVC62OH.id}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setCell", "questionId": "hDaERkazBA", "questionTypeWithOperation": "table", "rowIndex": "{{PayrollItem_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "JPYJZpIRPw1jtQ9DHLgEr": {"id": "JPYJZpIRPw1jtQ9DHLgEr", "metadata": {"createdAt": "2025-03-20T06:06:43.496Z", "updatedAt": "2025-03-20T06:06:43.496Z"}, "name": "Set variable(s)", "next": "idPb3RK1BZeiNpM8B8kA5", "properties": {"variables": [{"identifier": "DaysInSG", "type": "number", "value": "$SUM($DATEDIF({{AssignmentStart}},{{AssignmentEnd}},\"D\"),1)"}]}, "variant": "setVariables"}, "NH43Xk3tj9XgoHl2nzqDa": {"id": "NH43Xk3tj9XgoHl2nzqDa", "metadata": {"createdAt": "2025-03-20T06:11:42.291Z", "updatedAt": "2025-03-20T06:11:42.291Z"}, "name": "Assignment ended during the period?", "next": "RNoZkV3e9CLfRocQVZerP", "properties": {"branches": [{"condition": {"AND": [{"lhs": "{{SourcingEnd}}", "operator": ">=", "rhs": "{{AssignmentEnd}}"}, {"lhs": "{{AssignmentEnd}}", "operator": ">=", "rhs": "{{SourcingStart}}"}]}, "name": "Yes", "next": "gtbNrZUTYniE1wdu8Poml"}]}, "variant": "condition"}, "O6C8rkcyaSaxGjQ7PWSJE": {"id": "O6C8rkcyaSaxGjQ7PWSJE", "metadata": {"createdAt": "2025-03-20T06:14:14.241Z", "updatedAt": "2025-03-20T06:14:14.241Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{DaysInSG}}", "columnId": "{{form.hDaERkazBA.columns.pGsLVC62OH.id}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setCell", "questionId": "hDaERkazBA", "questionTypeWithOperation": "table", "rowIndex": "{{PayrollItem_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "RNoZkV3e9CLfRocQVZerP": {"id": "RNoZkV3e9CLfRocQVZerP", "metadata": {"createdAt": "2025-03-20T06:44:48.235Z", "updatedAt": "2025-03-20T06:44:48.235Z"}, "name": "Sourcing period is in the assignment period?", "next": "eJNaQdlA7T7cUztHd8Jmv", "properties": {"branches": [{"condition": {"AND": [{"lhs": "{{SourcingStart}}", "operator": ">=", "rhs": "{{AssignmentStart}}"}, {"lhs": "{{SourcingEnd}}", "operator": "<=", "rhs": "{{AssignmentEnd}}"}]}, "name": "If", "next": "RaPMDccRCZpT01nz2X057"}]}, "variant": "condition"}, "RaPMDccRCZpT01nz2X057": {"id": "RaPMDccRCZpT01nz2X057", "metadata": {"createdAt": "2025-03-20T06:12:45.956Z", "updatedAt": "2025-03-20T06:12:45.956Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{TotalDays}}", "columnId": "{{form.hDaERkazBA.columns.pGsLVC62OH.id}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setCell", "questionId": "hDaERkazBA", "questionTypeWithOperation": "table", "rowIndex": "{{PayrollItem_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "d8h2yDveFjb1NPGg4C6aA": {"id": "d8h2yDveFjb1NPGg4C6aA", "metadata": {"createdAt": "2025-03-20T06:02:45.397Z", "updatedAt": "2025-03-20T06:02:45.398Z"}, "name": "Set variable(s)", "next": "ixYaWfLV0ZUK1bX1ORmHO", "properties": {"variables": [{"identifier": "SourcingStart", "type": "date", "value": "$INDEX({{form.hDaERkazBA.columns.ac1pNirTze.answer}},{{PayrollItem_index}})"}, {"identifier": "SourcingEnd", "type": "date", "value": "$INDEX({{form.hDaERkazBA.columns.jBqasTxyl2.answer}},{{PayrollItem_index}})"}, {"identifier": "TotalDays", "type": "number", "value": "$SUM($DATEDIF({{SourcingStart}},{{SourcingEnd}},\"D\"),1)"}]}, "variant": "setVariables"}, "eJNaQdlA7T7cUztHd8Jmv": {"id": "eJNaQdlA7T7cUztHd8Jmv", "metadata": {"createdAt": "2025-03-20T06:46:44.349Z", "updatedAt": "2025-03-20T06:46:44.349Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "0", "columnId": "{{form.hDaERkazBA.columns.pGsLVC62OH.id}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setCell", "questionId": "hDaERkazBA", "questionTypeWithOperation": "table", "rowIndex": "{{PayrollItem_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "gtbNrZUTYniE1wdu8Poml": {"id": "gtbNrZUTYniE1wdu8Poml", "metadata": {"createdAt": "2025-03-20T06:13:36.924Z", "updatedAt": "2025-03-20T06:13:36.924Z"}, "name": "Set variable(s)", "next": "O6C8rkcyaSaxGjQ7PWSJE", "properties": {"variables": [{"identifier": "DaysInSG", "properties": {}, "type": "number", "value": "$SUM($DATEDIF({{SourcingStart}},{{AssignmentEnd}},\"D\"),1)"}]}, "variant": "setVariables"}, "h0T5jPBpXAWO6rDnym1tU": {"id": "h0T5jPBpXAWO6rDnym1tU", "metadata": {"createdAt": "2025-03-20T06:08:39.143Z", "updatedAt": "2025-03-20T06:08:39.143Z"}, "name": "Set variable(s)", "next": "awYwa26B9N84ImJPov1Rn", "properties": {"variables": [{"identifier": "DaysInSG", "properties": {}, "type": "number", "value": "$SUM($DATEDIF({{AssignmentStart}},{{SourcingEnd}},\"D\"),1)"}]}, "variant": "setVariables"}, "idPb3RK1BZeiNpM8B8kA5": {"id": "idPb3RK1BZeiNpM8B8kA5", "metadata": {"createdAt": "2025-03-20T06:07:49.106Z", "updatedAt": "2025-03-20T06:07:49.106Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{DaysInSG}}", "columnId": "{{form.hDaERkazBA.columns.pGsLVC62OH.id}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setCell", "questionId": "hDaERkazBA", "questionTypeWithOperation": "table", "rowIndex": "{{PayrollItem_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "ixYaWfLV0ZUK1bX1ORmHO": {"id": "ixYaWfLV0ZUK1bX1ORmHO", "metadata": {"createdAt": "2025-03-20T07:06:41.105Z", "updatedAt": "2025-03-20T07:06:41.105Z"}, "name": "Set total days", "next": "vaJCKzwyFUNZIlr0yKfJe", "properties": {"inputs": {"answer": "{{TotalDays}}", "columnId": "{{form.hDaERkazBA.columns.K8J5lh76BL.id}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setCell", "questionId": "hDaERkazBA", "questionTypeWithOperation": "table", "rowIndex": "{{PayrollItem_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "uBwNQaaKFFgcXwmtpS61a": {"id": "uBwNQaaKFFgcXwmtpS61a", "metadata": {"createdAt": "2025-03-20T06:05:24.897Z", "updatedAt": "2025-03-20T06:05:24.897Z"}, "name": "Assignment Ended during the sourcing period?", "next": "h0T5jPBpXAWO6rDnym1tU", "properties": {"branches": [{"condition": {"lhs": "{{SourcingEnd}}", "operator": ">=", "rhs": "{{AssignmentEnd}}"}, "name": "If", "next": "JPYJZpIRPw1jtQ9DHLgEr"}]}, "variant": "condition"}, "vaJCKzwyFUNZIlr0yKfJe": {"id": "vaJCKzwyFUNZIlr0yKfJe", "metadata": {"createdAt": "2025-03-20T06:05:01.248Z", "updatedAt": "2025-03-20T06:05:01.248Z"}, "name": "Inbound assignee?", "next": "", "properties": {"branches": [{"condition": {"lhs": "{{HostCountry}}", "operator": "=", "rhs": "\"Singapore\""}, "name": "If", "next": "aSskWRUqGaa4EBzB04x1Q"}]}, "variant": "condition"}}}, "inputs": {"isReturn": "false", "itemVariableName": "PayrollItem", "list": "{{form.hDaERkazBA.answer}}", "resultVariableName": "output__step_XY1Mtcz6f20bVpbdIl0ec", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"jLr5UFpz1GjuMGPPHbAyx": {"id": "jLr5UFpz1GjuMGPPHbAyx", "metadata": {"createdAt": "2025-02-28T00:57:55.196Z", "updatedAt": "2025-02-28T00:57:55.196Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Placeholder", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aavRghx8kM7VyHEdqLj9t": {"description": "", "id": "aavRghx8kM7VyHEdqLj9t", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T05:16:48.917Z", "updatedAt": "2025-04-14T03:33:24.120Z"}, "name": "MULTIPLY - Table Values", "start": "rYcbUarhmeEfRhrMMlOZd", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "steps": {"azKaGP6U3smzaPP5zuanR": {"id": "azKaGP6U3smzaPP5zuanR", "metadata": {"createdAt": "2025-03-24T02:55:30.952Z", "updatedAt": "2025-03-24T02:55:30.952Z"}, "name": "Set form answer", "next": "SiigtW9Qo7X1QMds0SdgH", "properties": {"inputs": {"answer": "{{MULTIPLYCOL2}}", "columnId": "{{CYForm.Tt9L06N948.columns.IxrosLRLnX.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{CYForm.id}}", "operation": "setColumn", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "B8Bh87St9KCnOaa5LVWoB": {"id": "B8Bh87St9KCnOaa5LVWoB", "metadata": {"createdAt": "2025-04-23T02:01:15.013Z", "updatedAt": "2025-04-23T02:01:15.013Z"}, "name": "Set form answer", "next": "E5ahdaLxcbZ7aaaKqfbFb", "properties": {"inputs": {"answer": "{{MULTIPLY}}", "columnId": "{{CYForm.Tt9L06N948.columns.UbeUf70j1C.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{CYForm.id}}", "operation": "setColumn", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "E5ahdaLxcbZ7aaaKqfbFb": {"id": "E5ahdaLxcbZ7aaaKqfbFb", "metadata": {"createdAt": "2025-03-24T02:55:50.936Z", "updatedAt": "2025-03-24T02:55:50.936Z"}, "name": "COLUMN 1 * COLUMN 2", "next": "PHbCWORHFaJy641aEtKWR", "properties": {"variables": [{"identifier": "MULTIPLY_STRING", "type": "text", "value": "$string({{CYForm.Tt9L06N948.columns.UbeUf70j1C.answer}})"}, {"identifier": "MULTIPLY_STRING2", "type": "text", "value": "$SUBSTITUTE($SUBSTITUTE({{MULTIPLY_STRING}},\"\\\\\",\"\"),\"\\\"\",\"\")"}]}, "variant": "setVariables"}, "PHbCWORHFaJy641aEtKWR": {"id": "PHbCWORHFaJy641aEtKWR", "metadata": {"createdAt": "2025-03-24T02:56:19.750Z", "updatedAt": "2025-03-24T02:56:19.750Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{MULTIPLY_STRING2}}", "columnId": "{{form.pB7BSlhmKW.columns.bf4Ma7DWGF.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowIndex": "2", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "SiigtW9Qo7X1QMds0SdgH": {"id": "SiigtW9Qo7X1QMds0SdgH", "metadata": {"createdAt": "2025-04-23T02:00:49.003Z", "updatedAt": "2025-04-23T02:00:49.003Z"}, "name": "Set variable(s)", "next": "B8Bh87St9KCnOaa5LVWoB", "properties": {"variables": [{"identifier": "MULTIPLY", "properties": {}, "type": "json", "value": "$MULTIPLY({{CYForm.Tt9L06N948.columns.ROMCsqfEVl.answer}},{{CYForm.Tt9L06N948.columns.IxrosLRLnX.answer}})"}]}, "variant": "setVariables"}, "YZMEdMCtxzeY9q1SKBGpW": {"id": "YZMEdMCtxzeY9q1SKBGpW", "metadata": {"createdAt": "2025-03-24T02:54:03.202Z", "updatedAt": "2025-03-24T02:54:03.202Z"}, "name": "Select form", "next": "aO3taAOjBikfv9Ui5sgZ8", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "CYForm", "foundationId": "{{Assignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "aO3taAOjBikfv9Ui5sgZ8": {"id": "aO3taAOjBikfv9Ui5sgZ8", "metadata": {"createdAt": "2025-03-24T03:01:20.076Z", "updatedAt": "2025-03-24T03:01:20.076Z"}, "name": "Set variable(s)", "next": "uizVaalr7sLjQnKEpPaAP", "properties": {"variables": [{"identifier": "MULTIPLYCOL1", "type": "json", "value": "[\"100\",\"200\"]"}, {"identifier": "MULTIPLYCOL2", "type": "json", "value": "[\"10\",\"20\"]"}]}, "variant": "setVariables"}, "rYcbUarhmeEfRhrMMlOZd": {"id": "rYcbUarhmeEfRhrMMlOZd", "metadata": {"createdAt": "2025-03-24T02:53:13.916Z", "updatedAt": "2025-03-24T02:53:13.916Z"}, "name": "Set variable(s)", "next": "xAHkq98hzQWeuZ1aaLm6R", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"ASGN\",{{form.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "uizVaalr7sLjQnKEpPaAP": {"id": "uizVaalr7sLjQnKEpPaAP", "metadata": {"createdAt": "2025-03-24T02:54:34.779Z", "updatedAt": "2025-03-24T02:54:34.780Z"}, "name": "Set form answer", "next": "azKaGP6U3smzaPP5zuanR", "properties": {"inputs": {"answer": "{{MULTIPLYCOL1}}", "columnId": "{{CYForm.Tt9L06N948.columns.ROMCsqfEVl.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{CYForm.id}}", "operation": "setColumn", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "xAHkq98hzQWeuZ1aaLm6R": {"id": "xAHkq98hzQWeuZ1aaLm6R", "metadata": {"createdAt": "2025-03-24T02:53:40.947Z", "updatedAt": "2025-03-24T02:53:40.947Z"}, "name": "Select Foundation", "next": "YZMEdMCtxzeY9q1SKBGpW", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}}, "triggers": {"rqBhQaBA7w0ZWa8gKK1e9": {"id": "rqBhQaBA7w0ZWa8gKK1e9", "metadata": {"createdAt": "2025-03-05T05:16:51.927Z", "updatedAt": "2025-03-05T05:16:51.927Z"}, "name": "When an answer is changed", "next": "", "properties": {"inputs": {"formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form", "questionId": "bjvMa39Mww", "questionIds": "[\"waNU3AzjNL\"]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "a3DJIUaljyVYKVQC7XvTS": {"description": "", "id": "a3DJIUaljyVYKVQC7XvTS", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-12T02:12:10.908Z", "updatedAt": "2025-04-14T03:32:00.105Z"}, "name": "5. Update authorisation list when assignment information changes", "start": "g3hqzZ7kskybMuL1OgfJZ", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.gqqgE6trl8d5GQsL4e8b4"}], "steps": {"g3hqzZ7kskybMuL1OgfJZ": {"id": "g3hqzZ7kskybMuL1OgfJZ", "metadata": {"createdAt": "2025-03-12T02:12:47.720Z", "updatedAt": "2025-03-12T02:12:47.720Z"}, "name": "Get authorisation list", "next": "ko6JA26rQ9rlLkUiWV9jO", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "U3mqHJmaz9kEdWsGISca6", "formVariableName": "AuthList", "foundationId": "{{form.foundation.parentId}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "ko6JA26rQ9rlLkUiWV9jO": {"id": "ko6JA26rQ9rlLkUiWV9jO", "metadata": {"createdAt": "2025-03-12T02:14:08.607Z", "updatedAt": "2025-03-12T02:14:08.607Z"}, "name": "Go through assignments and update auth list", "next": "", "properties": {"configuration": {"start": "NaoabgwgvC5kxdaifeFfB", "steps": {"a2yMn3f7S8iif0ayiZnch": {"id": "a2yMn3f7S8iif0ayiZnch", "metadata": {"createdAt": "2025-03-12T11:53:43.749Z", "updatedAt": "2025-03-12T11:53:43.749Z"}, "name": "Set variable(s)", "next": "RzWua1odaceABXHQmawhv", "properties": {"variables": [{"identifier": "YearsAuthorised", "type": "text", "value": "$YEAR({{StartDate}})"}, {"identifier": "AuthorisationInfo", "type": "json", "value": "{ {{AuthList.Qg1l7LvjeN.columns.TroPGwZZrc.id}}:{{AssigneeId}},{{AuthList.Qg1l7LvjeN.columns.I1tiDtrnAi.id}}:{{AssigneeName}},{{AuthList.Qg1l7LvjeN.columns.on6bf5hai4.id}}:{{AssignmentPolicy}},{{AuthList.Qg1l7LvjeN.columns.nndTff2sKk.id}}:{{PolicyType}},{{AuthList.Qg1l7LvjeN.columns.ixLE34c666.id}}: true,{{AuthList.Qg1l7LvjeN.columns.jYqWsMZXO5.id}}: true,{{AuthList.Qg1l7LvjeN.columns.RhfF2TFKeJ.id}}: \"First year on assignment\",{{AuthList.Qg1l7LvjeN.columns.utYgarXaKa.id}}: false,{{AuthList.Qg1l7LvjeN.columns.a2qwFnLFof.id}}:{{YearsAuthorised}}}"}]}, "variant": "setVariables"}, "LDeyFoI1aqs4SDCMKU06G": {"id": "LDeyFoI1aqs4SDCMKU06G", "metadata": {"createdAt": "2025-03-12T11:51:41.211Z", "updatedAt": "2025-03-12T11:51:41.211Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{AuthorisationInfo}}", "formConfigurationId": "U3mqHJmaz9kEdWsGISca6", "formId": "{{AuthList.id}}", "operation": "setRow", "questionId": "Qg1l7LvjeN", "questionTypeWithOperation": "table", "rowIndex": "{{<PERSON><PERSON><PERSON><PERSON>signee}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "NaoabgwgvC5kxdaifeFfB": {"id": "NaoabgwgvC5kxdaifeFfB", "metadata": {"createdAt": "2025-03-12T02:15:17.762Z", "updatedAt": "2025-03-12T02:15:17.762Z"}, "name": "Set variable(s)", "next": "RxoQQMGZWWgXEMmmvRvud", "properties": {"variables": [{"identifier": "Assignee<PERSON><PERSON>", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.NygKq36atr.answer}},{{Assignee_index}})"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT($INDEX({{form.aqAohrlqQ2.columns.exI36T75Gq.answer}},{{Assignee_index}}), \" \",$INDEX({{form.aqAohrlqQ2.columns.aiCtHRlS00.answer}},{{Assignee_index}}))"}, {"identifier": "AssignmentPolicy", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.avKORamTMA.answer}},{{Assignee_index}})"}, {"identifier": "HomeCountry", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.oR3hExeYiL.answer}},{{Assignee_index}})"}, {"identifier": "HostCountry", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.VmlDFbLJKZ.answer}},{{Assignee_index}})"}, {"identifier": "StartDate", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.WSHCcFlExG.answer}},{{Assignee_index}})"}, {"identifier": "EndDate", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.waeNppHU1u.answer}},{{Assignee_index}})"}, {"identifier": "AuthorisationPolicy", "type": "text", "value": "$XLOOKUP({{AssignmentPolicy}},{{form.XfgXa6ooAm.columns.Pl2VZbKell.answer}},{{form.XfgXa6ooAm.columns.awsbsW5vEf.answer}})"}, {"identifier": "SpouseAuthorisation", "type": "text", "value": "$XLOOKUP({{AssignmentPolicy}},{{form.XfgXa6ooAm.columns.Pl2VZbKell.answer}},{{form.XfgXa6ooAm.columns.FIB0l1GDQv.answer}})"}, {"identifier": "PolicyType", "type": "text", "value": "$XLOOKUP({{AssignmentPolicy}},{{form.XfgXa6ooAm.columns.Pl2VZbKell.answer}},{{form.XfgXa6ooAm.columns.ogLzuKJAFY.answer}})"}]}, "variant": "setVariables"}, "RIevPrEd8XaVzHM5ekiGY": {"id": "RIevPrEd8XaVzHM5ekiGY", "metadata": {"createdAt": "2025-03-12T11:43:11.887Z", "updatedAt": "2025-03-12T11:43:11.887Z"}, "name": "Check if assignee already in auth list", "next": "qWE94NDJH0PD2Uplo5gW0", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "number", "value": "$COUNTIF({{AuthList.Qg1l7LvjeN.columns.TroPGwZZrc.answer}},{{AssigneeId}})"}]}, "variant": "setVariables"}, "RxoQQMGZWWgXEMmmvRvud": {"id": "RxoQQMGZWWgXEMmmvRvud", "metadata": {"createdAt": "2025-03-12T11:41:45.446Z", "updatedAt": "2025-03-12T11:41:45.446Z"}, "name": "Authorisation Policy is first year only?", "next": "", "properties": {"branches": [{"condition": {"lhs": "{{AuthorisationPolicy}}", "operator": "=", "rhs": "\"First year only\""}, "name": "Yes", "next": "RIevPrEd8XaVzHM5ekiGY"}]}, "variant": "condition"}, "RzWua1odaceABXHQmawhv": {"id": "RzWua1odaceABXHQmawhv", "metadata": {"createdAt": "2025-03-12T11:54:19.940Z", "updatedAt": "2025-03-12T11:54:19.940Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{AuthorisationInfo}}", "formConfigurationId": "U3mqHJmaz9kEdWsGISca6", "formId": "{{AuthList.id}}", "operation": "setRow", "questionId": "Qg1l7LvjeN", "questionTypeWithOperation": "table", "rowSelection": "rowId"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "TbhY5U890N7vUisM9hEHl": {"id": "TbhY5U890N7vUisM9hEHl", "metadata": {"createdAt": "2025-03-12T11:45:17.136Z", "updatedAt": "2025-03-12T11:45:17.136Z"}, "name": "Set variable(s)", "next": "LDeyFoI1aqs4SDCMKU06G", "properties": {"variables": [{"identifier": "IndexOfAssignee", "type": "number", "value": "$MATCH({{AssigneeId}},{{AuthList.Qg1l7LvjeN.columns.TroPGwZZrc.answer}},0)"}, {"identifier": "YearsAuthorised", "type": "text", "value": "$YEAR({{StartDate}})"}, {"identifier": "AuthorisationInfo", "type": "json", "value": "{ {{AuthList.Qg1l7LvjeN.columns.TroPGwZZrc.id}}:{{AssigneeId}},{{AuthList.Qg1l7LvjeN.columns.I1tiDtrnAi.id}}:{{AssigneeName}},{{AuthList.Qg1l7LvjeN.columns.on6bf5hai4.id}}:{{AssignmentPolicy}},{{AuthList.Qg1l7LvjeN.columns.nndTff2sKk.id}}:{{PolicyType}},{{AuthList.Qg1l7LvjeN.columns.ixLE34c666.id}}: true,{{AuthList.Qg1l7LvjeN.columns.jYqWsMZXO5.id}}: true,{{AuthList.Qg1l7LvjeN.columns.RhfF2TFKeJ.id}}: \"First year on assignment\",{{AuthList.Qg1l7LvjeN.columns.utYgarXaKa.id}}: false,{{AuthList.Qg1l7LvjeN.columns.a2qwFnLFof.id}}:{{YearsAuthorised}}}"}]}, "variant": "setVariables"}, "qWE94NDJH0PD2Uplo5gW0": {"id": "qWE94NDJH0PD2Uplo5gW0", "metadata": {"createdAt": "2025-03-12T11:44:41.285Z", "updatedAt": "2025-03-12T11:44:41.285Z"}, "name": "Assignee in auth list?", "next": "a2yMn3f7S8iif0ayiZnch", "properties": {"branches": [{"condition": {"lhs": "{{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "operator": ">", "rhs": "0"}, "name": "If", "next": "TbhY5U890N7vUisM9hEHl"}]}, "variant": "condition"}}}, "inputs": {"isReturn": "false", "itemVariableName": "Assignee", "list": "{{form.aqAohrlqQ2.columns.NygKq36atr.answer}}", "resultVariableName": "output__step_ko6JA26rQ9rlLkUiWV9jO", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"aYqAj2zrzvik3y7MbkRAe": {"id": "aYqAj2zrzvik3y7MbkRAe", "metadata": {"createdAt": "2025-03-12T02:12:15.170Z", "updatedAt": "2025-03-12T02:12:15.170Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Placeholder", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aKE2XzeRgf2YtI71aVKYU": {"description": "", "id": "aKE2XzeRgf2YtI71aVKYU", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-07T06:53:43.562Z", "updatedAt": "2025-04-14T03:35:22.200Z"}, "name": "INDEX", "start": "zqGGXaSmRlc84I5Qqp2lh", "steps": {"IkFEcyJHPak9aaSWA5L6g": {"id": "IkFEcyJHPak9aaSWA5L6g", "metadata": {"createdAt": "2025-03-07T06:54:45.571Z", "updatedAt": "2025-03-07T06:54:45.571Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{INDEX}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "questionId": "t60PjuSTaB", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "zqGGXaSmRlc84I5Qqp2lh": {"id": "zqGGXaSmRlc84I5Qqp2lh", "metadata": {"createdAt": "2025-03-07T07:09:10.367Z", "updatedAt": "2025-03-07T07:09:10.367Z"}, "name": "Set variable(s)", "next": "IkFEcyJHPak9aaSWA5L6g", "properties": {"variables": [{"identifier": "INDEX", "type": "text", "value": "$INDEX({{form.Tt9L06N948.columns.wMthkQKZXy.answer}},1)"}]}, "variant": "setVariables"}}, "triggers": {"ba1jms3w1t9ty0QclqUvN": {"id": "ba1jms3w1t9ty0QclqUvN", "metadata": {"createdAt": "2025-03-07T06:53:48.121Z", "updatedAt": "2025-03-07T06:53:48.121Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "INDEX", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a3gIiof7HoIQyUGTsUUML": {"description": "", "id": "a3gIiof7HoIQyUGTsUUML", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-12T09:50:44.771Z", "updatedAt": "2025-04-14T03:35:27.184Z"}, "name": "Set column 1 to column 2", "start": "aEqMu45tgxwh6vaU6Na1L", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.oXUOfkMKayra9lBi5NOvj"}], "steps": {"aEqMu45tgxwh6vaU6Na1L": {"id": "aEqMu45tgxwh6vaU6Na1L", "metadata": {"createdAt": "2025-03-12T09:53:14.344Z", "updatedAt": "2025-03-12T09:53:14.344Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{form.Tt9L06N948.columns.ROMCsqfEVl.answer}}", "columnId": "{{form.Tt9L06N948.columns.UbeUf70j1C.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"nFD19c5FNPaZd04nuaQTi": {"id": "nFD19c5FNPaZd04nuaQTi", "metadata": {"createdAt": "2025-03-12T09:50:49.638Z", "updatedAt": "2025-03-12T09:50:49.638Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "SYNC COLUMN 1 TO 2", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aqL0scQZ1FAEHFrcixhlm": {"description": "", "endingVariables": [], "id": "aqL0scQZ1FAEHFrcixhlm", "labels": ["f4V7hhKY0WrW7BexzpwSQ"], "metadata": {"createdAt": "2025-04-30T04:19:48.560Z", "updatedAt": "2025-04-30T04:19:48.560Z"}, "name": "Add Info To Test Runner", "start": "rJO2BkFCRbgDFQN5AYazZ", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "status": "inactive", "steps": {"aO95e1sk0BO1QibONaFqS": {"id": "aO95e1sk0BO1QibONaFqS", "metadata": {"createdAt": "2025-04-30T04:21:10.321Z", "updatedAt": "2025-04-30T04:21:10.321Z"}, "name": "Set form alert", "next": "", "properties": {"inputs": {"formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "groupIdentifier": "Info", "message": "Run \"Run Demo Sc<PERSON>rios\" to test the demo scenario", "operation": "add", "questionId": "CLE7acMs4f", "variant": "info"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}, "rJO2BkFCRbgDFQN5AYazZ": {"id": "rJO2BkFCRbgDFQN5AYazZ", "metadata": {"createdAt": "2025-04-30T04:20:01.920Z", "updatedAt": "2025-04-30T04:20:01.920Z"}, "name": "Set form alert", "next": "aO95e1sk0BO1QibONaFqS", "properties": {"inputs": {"formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "groupIdentifier": "Info", "message": "Run \"Set Up Tests\", run \"Start Testing\" to run flow testing", "operation": "add", "questionId": "B1AYblG3LF", "variant": "info"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}}, "triggers": {"zhXWK1XlUmG6i7tJ1uyam": {"id": "zhXWK1XlUmG6i7tJ1uyam", "metadata": {"createdAt": "2025-04-30T04:19:53.723Z", "updatedAt": "2025-04-30T04:19:53.723Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Add Info", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aRXEsAN6USxUdyK71Z1HK": {"description": "", "endingVariables": [{"identifier": "generatedFileName", "type": "text"}], "id": "aRXEsAN6USxUdyK71Z1HK", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-05-01T00:55:40.495Z", "updatedAt": "2025-05-01T00:55:40.495Z"}, "name": "Generate a document", "start": "WqMfHwsW71Ipry7O1RKOW", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.oXUOfkMKayra9lBi5NOvj"}], "status": "active", "steps": {"GjU63Axzn5L4BiJZxNJfw": {"id": "GjU63Axzn5L4BiJZxNJfw", "metadata": {"createdAt": "2025-05-01T00:57:12.436Z", "updatedAt": "2025-05-01T00:57:12.436Z"}, "name": "extract url from Question's First File", "next": "RNoY18uTmlQv24dlqCSQD", "properties": {"variables": [{"identifier": "urlFromFirstFileQuestion", "type": "text", "value": "($INDEX({{form.wUWCpxqIJ4.answer}},1)).path"}, {"identifier": "fileName", "type": "text", "value": "$CONCAT(\"generatedFromFile - \",$NOW(),\".docx\")"}]}, "variant": "setVariables"}, "LaLT2OpJaqUibYBFN3Sg6": {"id": "LaLT2OpJaqUibYBFN3Sg6", "metadata": {"createdAt": "2025-05-01T01:02:24.977Z", "updatedAt": "2025-05-01T01:02:24.977Z"}, "name": "Select a form", "next": "rA1E4TxBU04SeKPhZeL8h", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "TemplateForm", "foundationId": "{{TemplateAssignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "RNoY18uTmlQv24dlqCSQD": {"id": "RNoY18uTmlQv24dlqCSQD", "metadata": {"createdAt": "2025-05-01T00:58:47.614Z", "updatedAt": "2025-05-01T00:58:47.615Z"}, "name": "Generate Docx from template", "next": "bUw6FE6CrffPNfSPzdbve", "properties": {"inputs": {"documentVariableName": "document", "outputFilename": "generatedDocument", "replacements": "{{TemplateForm}}", "templateUrl": "{{urlFromFirstFileQuestion}}"}, "typePrimaryIdentifier": "generateDocxFromTemplate"}, "variant": "action"}, "WqMfHwsW71Ipry7O1RKOW": {"id": "WqMfHwsW71Ipry7O1RKOW", "metadata": {"createdAt": "2025-05-01T01:02:36.557Z", "updatedAt": "2025-05-01T01:02:36.557Z"}, "name": "Select or create a foundation", "next": "LaLT2OpJaqUibYBFN3Sg6", "properties": {"inputs": {"foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Temp<PERSON><PERSON><PERSON><PERSON>", "key": "TEMPLATEASSIGNEE", "parentId": "{{form.foundation.id}}"}, "typePrimaryIdentifier": "selectOrCreateFoundation"}, "variant": "action"}, "bUw6FE6CrffPNfSPzdbve": {"id": "bUw6FE6CrffPNfSPzdbve", "metadata": {"createdAt": "2025-05-01T00:58:57.085Z", "updatedAt": "2025-05-01T00:58:57.085Z"}, "name": "Set form answer", "next": "cINNDAoaa3EN0osVQIu0K", "properties": {"inputs": {"answer": "{{document}}", "fileOperation": "setFile", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "operation": "", "questionId": "iaHhwo7Cgh", "questionTypeWithOperation": "file", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "cINNDAoaa3EN0osVQIu0K": {"id": "cINNDAoaa3EN0osVQIu0K", "metadata": {"createdAt": "2025-05-01T01:14:48.270Z", "updatedAt": "2025-05-01T01:14:48.270Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "generatedFileName", "type": "text", "value": "($INDEX({{form.iaHhwo7Cgh.answer}},1)).name"}]}, "variant": "setVariables"}, "rA1E4TxBU04SeKPhZeL8h": {"id": "rA1E4TxBU04SeKPhZeL8h", "metadata": {"createdAt": "2025-05-01T01:02:19.909Z", "updatedAt": "2025-05-01T01:02:19.909Z"}, "name": "Set form answer", "next": "GjU63Axzn5L4BiJZxNJfw", "properties": {"inputs": {"answer": "{{TemplateForm.wUWCpxqIJ4.answer}}", "fileOperation": "setFile", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "questionId": "wUWCpxqIJ4", "questionTypeWithOperation": "file"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"hE4NxnSGJ7FnS7EFZnzmF": {"id": "hE4NxnSGJ7FnS7EFZnzmF", "metadata": {"createdAt": "2025-05-01T00:55:43.228Z", "updatedAt": "2025-05-01T00:55:43.228Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Generate Document", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "au9IDqSVdX8tBmRawaq8H": {"description": "", "id": "au9IDqSVdX8tBmRawaq8H", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-19T04:20:14.026Z", "updatedAt": "2025-04-14T03:32:09.805Z"}, "name": "11. Perform Sourcing", "start": "voBbDIF43DauhMywA3O18", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.SG14waZgbati8MISSWyVN"}], "steps": {"aSaaVCrFlt95mofB6gGrT": {"id": "aSaaVCrFlt95mofB6gGrT", "metadata": {"createdAt": "2025-03-19T05:26:13.497Z", "updatedAt": "2025-03-19T05:26:13.497Z"}, "name": "Update PayrollData Columns", "next": "zt9axVAPUulJpL7nW0Vhy", "properties": {"variables": [{"identifier": "PayrollData", "properties": {"columnIdentifier": "{{form.hDaERkazBA.columns.Ah3EuTk1bR.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.qO3xge5etd.columns.MRcRCBayfx.answer}}"}]}, "variant": "setVariables"}, "QgLu5Lvh5CJuxvf3ISSld": {"id": "QgLu5Lvh5CJuxvf3ISSld", "metadata": {"createdAt": "2025-03-20T09:29:19.625Z", "updatedAt": "2025-03-20T09:29:19.625Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{PayrollData}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setTable", "questionId": "hDaERkazBA", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "SPxlpFbgp4c015sQaGNfj": {"id": "SPxlpFbgp4c015sQaGNfj", "metadata": {"createdAt": "2025-03-20T07:24:58.140Z", "updatedAt": "2025-03-20T07:24:58.140Z"}, "name": "Update tax borne by ER", "next": "VLn2NQRdMxgfR4mOQWEoq", "properties": {"variables": [{"identifier": "PayrollData", "properties": {"columnIdentifier": "{{form.hDaERkazBA.columns.fQVjfa2xHt.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.qO3xge5etd.columns.aNomVlFYS4.answer}}"}]}, "variant": "setVariables"}, "VLn2NQRdMxgfR4mOQWEoq": {"id": "VLn2NQRdMxgfR4mOQWEoq", "metadata": {"createdAt": "2025-03-19T05:33:26.769Z", "updatedAt": "2025-03-19T05:33:26.769Z"}, "name": "Update amount", "next": "jeeF4SRtYx3QiJ1oHuW7Q", "properties": {"variables": [{"identifier": "PayrollData", "properties": {"columnIdentifier": "{{form.hDaERkazBA.columns.OOK8GIkJVg.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.qO3xge5etd.columns.aKi6gOawF3.answer}}"}]}, "variant": "setVariables"}, "YHJ1t2TkzyFvWxOa1yEmM": {"id": "YHJ1t2TkzyFvWxOa1yEmM", "metadata": {"createdAt": "2025-03-20T09:26:43.327Z", "updatedAt": "2025-03-20T09:26:43.327Z"}, "name": "Get client form", "next": "vmvbkvhpNUEdwLlHrQEIC", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formVariableName": "clientPositions", "foundationId": "{{form.foundation.parentId}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "jeeF4SRtYx3QiJ1oHuW7Q": {"id": "jeeF4SRtYx3QiJ1oHuW7Q", "metadata": {"createdAt": "2025-03-19T05:47:17.095Z", "updatedAt": "2025-03-19T05:47:17.095Z"}, "name": "For each", "next": "QgLu5Lvh5CJuxvf3ISSld", "properties": {"configuration": {"start": "a891i2azBDQyF2ANUpbGo", "steps": {"a891i2azBDQyF2ANUpbGo": {"id": "a891i2azBDQyF2ANUpbGo", "metadata": {"createdAt": "2025-03-19T05:48:34.607Z", "updatedAt": "2025-03-19T05:48:34.607Z"}, "name": "Set variable(s)", "next": "K9YFJCe2P2KWuNzSD3gay", "properties": {"variables": [{"identifier": "SourcingPeriodStart", "type": "date", "value": "$DATE(2025,1,1)"}, {"identifier": "SourcingPeriodEnd", "type": "date", "value": "$DATE(2025,12,31)"}, {"identifier": "DateDif", "type": "number", "value": "$DATEDIF({{SourcingPeriodStart}},{{SourcingPeriodEnd}},\"D\")"}, {"identifier": "TotalDays", "properties": {}, "type": "number", "value": "$SUM({{DateDif}},1)"}]}, "variant": "setVariables"}, "amZOJ67jfuFFgQIlopeHT": {"id": "amZOJ67jfuFFgQIlopeHT", "metadata": {"createdAt": "2025-03-20T09:31:23.041Z", "updatedAt": "2025-03-20T09:31:23.041Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "PayrollData", "properties": {"columnIdentifier": "{{form.hDaERkazBA.columns.K8J5lh76BL.id}}", "operation": "setCell", "rowIndex": "{{PayrollItem_index}}"}, "type": "table", "value": "{{TotalDays}}"}]}, "variant": "setVariables"}, "K9YFJCe2P2KWuNzSD3gay": {"id": "K9YFJCe2P2KWuNzSD3gay", "metadata": {"createdAt": "2025-03-20T09:30:04.226Z", "updatedAt": "2025-03-20T09:30:04.227Z"}, "name": "Set variable(s)", "next": "ieP38TAxJM7Vr1kuansWH", "properties": {"variables": [{"identifier": "PayrollData", "properties": {"columnIdentifier": "{{form.hDaERkazBA.columns.ac1pNirTze.id}}", "operation": "setCell", "rowIdentifier": "", "rowIndex": "{{PayrollItem_index}}"}, "type": "table", "value": "{{SourcingPeriodStart}}"}]}, "variant": "setVariables"}, "ieP38TAxJM7Vr1kuansWH": {"id": "ieP38TAxJM7Vr1kuansWH", "metadata": {"createdAt": "2025-03-20T09:31:00.274Z", "updatedAt": "2025-03-20T09:31:00.274Z"}, "name": "Set variable(s)", "next": "amZOJ67jfuFFgQIlopeHT", "properties": {"variables": [{"identifier": "PayrollData", "properties": {"columnIdentifier": "{{form.hDaERkazBA.columns.jBqasTxyl2.id}}", "operation": "setCell", "rowIndex": "{{PayrollItem_index}}"}, "type": "table", "value": "{{SourcingPeriodEnd}}"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "false", "itemVariableName": "PayrollItem", "list": "{{PayrollData}}", "resultVariableName": "output__step_jeeF4SRtYx3QiJ1oHuW7Q", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}, "vmvbkvhpNUEdwLlHrQEIC": {"id": "vmvbkvhpNUEdwLlHrQEIC", "metadata": {"createdAt": "2025-03-19T05:22:09.924Z", "updatedAt": "2025-03-19T05:22:09.924Z"}, "name": "Initiate table variable", "next": "aSaaVCrFlt95mofB6gGrT", "properties": {"variables": [{"identifier": "PayrollData", "properties": {"operation": "setTable"}, "type": "table", "value": "{{form.hDaERkazBA.answer}}"}]}, "variant": "setVariables"}, "voBbDIF43DauhMywA3O18": {"id": "voBbDIF43DauhMywA3O18", "metadata": {"createdAt": "2025-03-20T09:03:17.952Z", "updatedAt": "2025-03-20T09:03:17.952Z"}, "name": "Condition", "next": "", "properties": {"branches": [{"condition": {"lhs": "{{form.vRNGl6Xxxb.answer}}", "operator": "=", "rhs": "\"RUN\""}, "name": "If", "next": "YHJ1t2TkzyFvWxOa1yEmM"}]}, "variant": "condition"}, "zt9axVAPUulJpL7nW0Vhy": {"id": "zt9axVAPUulJpL7nW0Vhy", "metadata": {"createdAt": "2025-03-19T05:32:46.606Z", "updatedAt": "2025-03-19T05:32:46.606Z"}, "name": "Update standard mapping", "next": "SPxlpFbgp4c015sQaGNfj", "properties": {"variables": [{"identifier": "PayrollData", "properties": {"columnIdentifier": "{{form.hDaERkazBA.columns.iOLWzdaCOj.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.qO3xge5etd.columns.DYnqlhI7UM.answer}}"}]}, "variant": "setVariables"}}, "triggers": {"aDnaruT8lasBFGD106v2J": {"id": "aDnaruT8lasBFGD106v2J", "metadata": {"createdAt": "2025-03-19T04:20:36.225Z", "updatedAt": "2025-03-19T04:20:36.225Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Placeholder", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "BUafhWaQZiOzM7ljhCRbe": {"description": "", "id": "BUafhWaQZiOzM7ljhCRbe", "labels": ["f4V7hhKY0WrW7BexzpwSQ"], "metadata": {"createdAt": "2025-03-18T14:55:48.862Z", "updatedAt": "2025-04-14T03:36:15.534Z"}, "name": "Test Runner <PERSON><PERSON><PERSON>", "start": "aKstZIMCEaqq4h7cdA790", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "status": "inactive", "steps": {"aKstZIMCEaqq4h7cdA790": {"id": "aKstZIMCEaqq4h7cdA790", "metadata": {"createdAt": "2025-03-18T14:56:25.714Z", "updatedAt": "2025-03-18T14:56:25.714Z"}, "name": "For each", "next": "", "properties": {"configuration": {"start": "KMEAhFpRrfaoLMoFBi9Od", "startingVariables": [{"identifier": "Results", "type": "json"}, {"identifier": "Results_index", "type": "number"}, {"identifier": "Results_output", "type": "unknown"}], "steps": {"KMEAhFpRrfaoLMoFBi9Od": {"id": "KMEAhFpRrfaoLMoFBi9Od", "metadata": {"createdAt": "2025-03-26T22:12:07.478Z", "updatedAt": "2025-03-26T22:12:07.478Z"}, "name": "Set variable(s)", "next": "qcxSHqMBMDkJBMAxKaoPg", "properties": {"variables": [{"identifier": "Result", "type": "text", "value": "$INDEX({{form.pB7BSlhmKW.columns.bf4Ma7DWGF.answer}},{{Results_index}})"}, {"identifier": "Expected<PERSON><PERSON>ult", "type": "text", "value": "$INDEX({{form.pB7BSlhmKW.columns.awxQA8AtGn.answer}},{{Results_index}})"}]}, "variant": "setVariables"}, "TsoUpClw4Eebpciyosykl": {"id": "TsoUpClw4Eebpciyosykl", "metadata": {"createdAt": "2025-03-18T14:58:57.954Z", "updatedAt": "2025-03-18T14:58:57.954Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "PASS", "columnId": "{{form.pB7BSlhmKW.columns.awtfyzxoLZ.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowIndex": "{{Results_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "iogEcYbS6knROz9tJL57o": {"id": "iogEcYbS6knROz9tJL57o", "metadata": {"createdAt": "2025-03-18T22:36:01.744Z", "updatedAt": "2025-03-18T22:36:01.744Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "NO RESULT", "columnId": "{{form.pB7BSlhmKW.columns.awtfyzxoLZ.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowIndex": "{{Results_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "qcxSHqMBMDkJBMAxKaoPg": {"id": "qcxSHqMBMDkJBMAxKaoPg", "metadata": {"createdAt": "2025-03-18T14:58:15.039Z", "updatedAt": "2025-03-18T14:58:15.039Z"}, "name": "Result is not empty", "next": "iogEcYbS6knROz9tJL57o", "properties": {"branches": [{"condition": {"lhs": "{{Result}}", "operator": "isNotEmpty"}, "name": "If", "next": "t2E5Naj3GEALtV0GyeYTN"}]}, "variant": "condition"}, "ry71AfaJL2vLuO1Co4mLZ": {"id": "ry71AfaJL2vLuO1Co4mLZ", "metadata": {"createdAt": "2025-03-18T14:59:33.653Z", "updatedAt": "2025-03-18T14:59:33.653Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "FAIL", "columnId": "{{form.pB7BSlhmKW.columns.awtfyzxoLZ.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "{{Results_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "t2E5Naj3GEALtV0GyeYTN": {"id": "t2E5Naj3GEALtV0GyeYTN", "metadata": {"createdAt": "2025-03-18T14:58:34.284Z", "updatedAt": "2025-03-18T14:58:34.284Z"}, "name": "Evaluate", "next": "ry71AfaJL2vLuO1Co4mLZ", "properties": {"branches": [{"condition": {"lhs": "{{Result}}", "operator": "=", "rhs": "{{Expected<PERSON><PERSON><PERSON>}}"}, "name": "If", "next": "TsoUpClw4Eebpciyosykl"}]}, "variant": "condition"}}}, "inputs": {"isReturn": "false", "itemVariableName": "Results", "list": "{{form.pB7BSlhmKW.answer}}", "resultVariableName": "filtered__step_aKstZIMCEaqq4h7cdA790", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"ZoOOUQtzT8TMJ5iRgaLx1": {"id": "ZoOOUQtzT8TMJ5iRgaLx1", "metadata": {"createdAt": "2025-03-18T14:55:52.080Z", "updatedAt": "2025-03-18T14:55:52.080Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Old Evaluate", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "F4C7QxjXDITjch1FaNXwG": {"description": "When a client group is created, create the client group form", "endingVariables": [{"identifier": "ClientGroupForm", "type": "form"}], "id": "F4C7QxjXDITjch1FaNXwG", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-02-27T22:58:46.258Z", "updatedAt": "2025-04-14T03:30:48.233Z"}, "name": "1. Create client group form", "start": "ZRUsA5ucNMgjYnlawdKiC", "startingVariables": [{"identifier": "foundation", "properties": {"required": true}, "type": "foundation.daYJS2xq9fkLHcaY1YG7H"}], "steps": {"ZRUsA5ucNMgjYnlawdKiC": {"id": "ZRUsA5ucNMgjYnlawdKiC", "metadata": {"createdAt": "2025-03-20T23:55:19.094Z", "updatedAt": "2025-03-20T23:55:19.094Z"}, "name": "Select or create Form", "next": "", "properties": {"inputs": {"formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formVariableName": "ClientGroupForm", "foundationId": "{{foundation.id}}"}, "typePrimaryIdentifier": "selectOrCreateForm"}, "variant": "action"}}, "triggers": {"uk6vh3fldPVYyT7XQwX83": {"id": "uk6vh3fldPVYyT7XQwX83", "metadata": {"createdAt": "2025-02-27T22:58:51.805Z", "updatedAt": "2025-02-27T22:58:51.805Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "Placeholder", "foundationConfigurationId": "daYJS2xq9fkLHcaY1YG7H", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "FGKFPa0tAGXb2GXNZzMgC": {"description": "", "id": "FGKFPa0tAGXb2GXNZzMgC", "labels": ["f4V7hhKY0WrW7BexzpwSQ"], "metadata": {"createdAt": "2025-04-18T05:39:15.127Z", "updatedAt": "2025-04-18T05:39:15.127Z"}, "name": "Demo Flow Validation", "start": "ag9E9kL7M8Pt7uwnmmkMV", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "status": "active", "steps": {"avJV6OxvCWYbGpCswyMe7": {"id": "avJV6OxvCWYbGpCswyMe7", "metadata": {"createdAt": "2025-04-18T05:39:45.832Z", "updatedAt": "2025-04-18T05:39:45.832Z"}, "name": "Set up results scenarios", "next": "c2vLr7LwKh89mRIacpamN", "properties": {"variables": [{"identifier": "DemoResults", "properties": {"columnIdentifier": "{{form.FfKlGILPWz.columns.DWlkI9DVzT.id}}", "operation": "setColumn"}, "type": "table", "value": "[\n  \"Client Information - Assignment Policy\",\n  \"Client Information - Assignment End Date\",\n  \"Assignee Profile - Assignment End Date\",\n  \"Authorisation List - Assignment End Date\",\n  \"Client Information - COUNT of payroll mappings\",\n  \"CY Payroll Data - SUM of Total\",\n  \"Comp Analysis - SUM of SG Source Amount\",\n  \"Comp Analysis - SUM of Foreign Source Amount\",\n  \"Comp Summary - Salary Amount\",\n  \"Comp Summary - Bonus Amount\",\n  \"Comp Summary - Director Fe<PERSON> Amount\",\n  \"Comp Summary - Allowances Amount\",\n  \"Comp Summary - Total Income Amount\",\n  \"Comp Summary Table - SUM of SG Source\",\n  \"Comp Summary Table - SUM of Foreign Source\",\n  \"Comp Summary Table - SUM of ER Amount\",\n  \"Comp Summary Table - SUM of EE Amount\"\n]"}]}, "variant": "setVariables"}, "ablBVBhdhpWLKSCiO5aOn": {"id": "ablBVBhdhpWLKSCiO5aOn", "metadata": {"createdAt": "2025-04-18T06:09:08.736Z", "updatedAt": "2025-04-18T06:09:08.736Z"}, "name": "Set table to form", "next": "yPSUuOmaeiynjUkhDTcP8", "properties": {"inputs": {"answer": "{{Demo<PERSON><PERSON><PERSON><PERSON>}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setTable", "questionId": "FfKlGILPWz", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "aFmxaa3xa3NJssM8HZlXK": {"id": "aFmxaa3xa3NJssM8HZlXK", "metadata": {"createdAt": "2025-04-18T05:50:51.193Z", "updatedAt": "2025-04-18T05:50:51.193Z"}, "name": "Get assignee profile form", "next": "sNmDKD7VaMINokqA7Ija9", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "TaxReturnForm", "foundationId": "{{Assignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "ag9E9kL7M8Pt7uwnmmkMV": {"id": "ag9E9kL7M8Pt7uwnmmkMV", "metadata": {"createdAt": "2025-04-18T05:45:45.421Z", "updatedAt": "2025-04-18T05:45:45.421Z"}, "name": "Define keys", "next": "RqZ0jSpFPT0YrJgLnxlm4", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"CLI\",{{form.B1AYblG3LF.answer}})"}, {"identifier": "ClientGroupKey", "type": "text", "value": "$CONCAT(\"CG\",{{form.B1AYblG3LF.answer}})"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"DEMO\",{{form.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "B5buTrDmmmJRBNBgG0ug2": {"id": "B5buTrDmmmJRBNBgG0ug2", "metadata": {"createdAt": "2025-04-23T03:18:38.405Z", "updatedAt": "2025-04-23T03:18:38.405Z"}, "name": "Write pass/fail to form", "next": "", "properties": {"inputs": {"answer": "{{PassFail}}", "columnId": "{{form.FfKlGILPWz.columns.PEORVarfSN.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "FfKlGILPWz", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "RqZ0jSpFPT0YrJgLnxlm4": {"id": "RqZ0jSpFPT0YrJgLnxlm4", "metadata": {"createdAt": "2025-04-18T05:45:33.888Z", "updatedAt": "2025-04-18T05:45:33.888Z"}, "name": "Get client foundation", "next": "baZF1h0pXCxq5Vry3CGOB", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "D1axfoA5lvjOQ0uezN05G", "foundationVariableName": "Client", "key": "{{<PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "Sxg6bsrhxqiMG5WChBFoi": {"id": "Sxg6bsrhxqiMG5WChBFoi", "metadata": {"createdAt": "2025-04-18T05:50:02.128Z", "updatedAt": "2025-04-18T05:50:02.128Z"}, "name": "Get assignee profile form", "next": "aFmxaa3xa3NJssM8HZlXK", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "V0UCzZ49ccFOYy39zOPOh", "formVariableName": "Assign<PERSON>P<PERSON><PERSON><PERSON>", "foundationId": "{{Assignee.id}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "Ziarz40S1Wt86FT7nvvyD": {"id": "Ziarz40S1Wt86FT7nvvyD", "metadata": {"createdAt": "2025-04-23T03:17:06.522Z", "updatedAt": "2025-04-23T03:17:06.522Z"}, "name": "Write pass/fail", "next": "B5buTrDmmmJRBNBgG0ug2", "properties": {"variables": [{"identifier": "PassFail", "properties": {"listOperation": "setList"}, "type": "list", "value": "$map({{form.FfKlGILPWz.columns.InOWD9ZV6z.answer}}, function($v, $i) {\n  $IF($v={{form.FfKlGILPWz.columns.MLaUbsxjF1.answer}}[$i], \"PASS\", $IF($v=\"\", \"No Result\", \"FAIL\"))\n})"}]}, "variant": "setVariables"}, "aNxHqZFVPvH2a2GZdLaE4": {"id": "aNxHqZFVPvH2a2GZdLaE4", "metadata": {"createdAt": "2025-04-18T05:48:31.445Z", "updatedAt": "2025-04-18T05:48:31.445Z"}, "name": "Get auth list", "next": "e07iNvLS0rJ6F44v2cu1T", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "U3mqHJmaz9kEdWsGISca6", "formVariableName": "AuthList", "foundationId": "{{Client.id}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "baZF1h0pXCxq5Vry3CGOB": {"id": "baZF1h0pXCxq5Vry3CGOB", "metadata": {"createdAt": "2025-04-18T05:46:58.868Z", "updatedAt": "2025-04-18T05:46:58.868Z"}, "name": "Get client group foundation", "next": "oZLPQrMTpVucuKVXzb6Ku", "properties": {"inputs": {"continueFlowIfNotFound": "false", "foundationConfigurationId": "daYJS2xq9fkLHcaY1YG7H", "foundationVariableName": "ClientGroup", "key": "{{Client<PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "c2vLr7LwKh89mRIacpamN": {"id": "c2vLr7LwKh89mRIacpamN", "metadata": {"createdAt": "2025-04-18T05:39:52.468Z", "updatedAt": "2025-04-18T05:39:52.468Z"}, "name": "Set up results expected", "next": "ablBVBhdhpWLKSCiO5aOn", "properties": {"variables": [{"identifier": "DemoResults", "properties": {"columnIdentifier": "{{form.FfKlGILPWz.columns.MLaUbsxjF1.id}}", "operation": "setColumn"}, "type": "table", "value": "[\n  \"Transfer\",\n  \"2026-12-31\",\n  \"2026-12-31\",\n  \"2026-12-31\",\n  \"32\",\n  \"241732.24\",\n  \"241732.24\",\n  \"0\",\n  \"60000\",\n  \"157500\",\n  \"0\",\n  \"0\",\n  \"217500\",\n  \"435000\",\n  \"0\",\n  \"0\",\n  \"435000\"\n]"}]}, "variant": "setVariables"}, "e07iNvLS0rJ6F44v2cu1T": {"id": "e07iNvLS0rJ6F44v2cu1T", "metadata": {"createdAt": "2025-04-18T05:49:02.574Z", "updatedAt": "2025-04-18T05:49:02.574Z"}, "name": "Get client information form", "next": "Sxg6bsrhxqiMG5WChBFoi", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formVariableName": "ClientForm", "foundationId": "{{ClientGroup.id}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "jtlNkJSa2SJb31KvaeBaN": {"id": "jtlNkJSa2SJb31KvaeBaN", "metadata": {"createdAt": "2025-04-18T06:18:04.961Z", "updatedAt": "2025-04-18T06:18:04.961Z"}, "name": "Set table to form", "next": "Ziarz40S1Wt86FT7nvvyD", "properties": {"inputs": {"answer": "{{Demo<PERSON><PERSON><PERSON><PERSON>}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setTable", "questionId": "FfKlGILPWz", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "kag45RkdrIppdRBKefLEa": {"id": "kag45RkdrIppdRBKefLEa", "metadata": {"createdAt": "2025-04-18T05:51:49.949Z", "updatedAt": "2025-04-18T05:51:49.949Z"}, "name": "Write results", "next": "jtlNkJSa2SJb31KvaeBaN", "properties": {"variables": [{"identifier": "DemoResults", "properties": {"columnIdentifier": "{{form.FfKlGILPWz.columns.InOWD9ZV6z.id}}", "operation": "setColumn"}, "type": "table", "value": "[\n  {{AssignmentPolicy}},\n  {{AssignmentEndCI}},\n  {{AssignmentEndAP}},\n  {{AssignmentEndAuth}},\n  {{CountOfPayrollMappings}},\n  {{SumPayrollTotal}},\n  {{SumAnalysisSG}},\n  {{SumAnalysisForeign}},\n  {{TaxReturnForm.A9PZ0cxvav.answer}},\n  {{TaxReturnForm.NgX8CIqfBq.answer}},\n  {{TaxReturnForm.a3RbMg5asL.answer}},\n  {{TaxReturnForm.z1BbXlIjFh.answer}},\n  {{TaxReturnForm.NodtWWRePe.answer}},\n  {{SumSummarySG}},\n  {{SumSummaryForeign}},\n  {{SumSummaryER}},\n  {{SumSummaryEE}}\n]"}]}, "variant": "setVariables"}, "oZLPQrMTpVucuKVXzb6Ku": {"id": "oZLPQrMTpVucuKVXzb6Ku", "metadata": {"createdAt": "2025-04-18T05:48:10.623Z", "updatedAt": "2025-04-18T05:48:10.623Z"}, "name": "Get assignee foundation", "next": "aNxHqZFVPvH2a2GZdLaE4", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "sNmDKD7VaMINokqA7Ija9": {"id": "sNmDKD7VaMINokqA7Ija9", "metadata": {"createdAt": "2025-04-18T05:39:38.106Z", "updatedAt": "2025-04-18T05:39:38.106Z"}, "name": "Initiate results table", "next": "avJV6OxvCWYbGpCswyMe7", "properties": {"variables": [{"identifier": "DemoResults", "properties": {"operation": "setTable"}, "type": "table", "value": "[]"}]}, "variant": "setVariables"}, "yPSUuOmaeiynjUkhDTcP8": {"id": "yPSUuOmaeiynjUkhDTcP8", "metadata": {"createdAt": "2025-04-18T05:52:31.862Z", "updatedAt": "2025-04-18T05:52:31.862Z"}, "name": "Get data points", "next": "kag45RkdrIppdRBKefLEa", "properties": {"variables": [{"identifier": "AssignmentPolicy", "type": "text", "value": "$INDEX({{ClientForm.XfgXa6ooAm.columns.Pl2VZbKell.answer}},1)"}, {"identifier": "AssignmentEndCI", "type": "text", "value": "$INDEX({{ClientForm.aqAohrlqQ2.columns.waeNppHU1u.answer}},1)"}, {"identifier": "AssignmentEndAP", "type": "text", "value": "$INDEX({{AssigneeProfile.T7IoTXkA0l.columns.VuaSF5PWY9.answer}},1)"}, {"identifier": "AssignmentEndAuth", "type": "text", "value": "$INDEX({{AssigneeProfile.T7IoTXkA0l.columns.VuaSF5PWY9.answer}},1)"}, {"identifier": "CountOfPayrollMappings", "type": "text", "value": "$COUNT({{ClientForm.SoBlBkh5ZM.columns.XaADpaR1Sf.answer}})"}, {"identifier": "SumPayrollTotal", "type": "text", "value": "$SUM({{TaxReturnForm.qO3xge5etd.columns.aKi6gOawF3.answer}})"}, {"identifier": "SumAnalysisSG", "type": "text", "value": "$SUM({{TaxReturnForm.hDaERkazBA.columns.nKTz3jqg7E.answer}})"}, {"identifier": "SumAnalysisForeign", "type": "text", "value": "$SUM({{TaxReturnForm.hDaERkazBA.columns.WAvMl6VKwl.answer}})"}, {"identifier": "SumSummarySG", "type": "text", "value": "$SUM({{TaxReturnForm.hAJuHuyjwx.columns.acGhopiihu.answer}})"}, {"identifier": "SumSummaryForeign", "type": "text", "value": "$SUM({{TaxReturnForm.hAJuHuyjwx.columns.d4oMDINbmM.answer}})"}, {"identifier": "SumSummaryER", "type": "text", "value": "$SUM({{TaxReturnForm.hAJuHuyjwx.columns.TWpGz9Vmmx.answer}})"}, {"identifier": "SumSummaryEE", "type": "text", "value": "$SUM({{TaxReturnForm.hAJuHuyjwx.columns.jKpaBjVFaG.answer}})"}]}, "variant": "setVariables"}}, "triggers": {"aLxRjBjaukSBelygYGelq": {"id": "aLxRjBjaukSBelygYGelq", "metadata": {"createdAt": "2025-04-18T05:39:18.565Z", "updatedAt": "2025-04-18T05:39:18.565Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Validate Demo Flow Data", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "FUp3ETxZ9AuX6X8yk4ajQ": {"description": "", "endingVariables": [{"identifier": "AssigneeFormId", "type": "text"}], "id": "FUp3ETxZ9AuX6X8yk4ajQ", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-03T22:51:54.122Z", "updatedAt": "2025-04-14T03:31:36.787Z"}, "name": "3. Create assignee profiles from client form", "start": "KIaGJRrPNxZGcaim33baF", "startingVariables": [{"identifier": "form", "type": "form.gqqgE6trl8d5GQsL4e8b4"}], "status": "active", "steps": {"Gc923oMziva2gYKm4UPgL": {"id": "Gc923oMziva2gYKm4UPgL", "metadata": {"createdAt": "2025-03-07T05:06:12.667Z", "updatedAt": "2025-03-07T05:06:12.667Z"}, "name": "For each", "next": "", "properties": {"configuration": {"start": "VSa8jNc7ee2nlyjbpiE0A", "steps": {"aXiavw07sVlk1d9MoLi87": {"id": "aXiavw07sVlk1d9MoLi87", "metadata": {"createdAt": "2025-04-18T01:59:17.462Z", "updatedAt": "2025-04-18T01:59:17.462Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "AssigneeFormId", "properties": {}, "type": "text", "value": "{{AssigneeProfile.id}}"}]}, "variant": "setVariables"}, "aTU439LfGgGOoenDSaySe": {"id": "aTU439LfGgGOoenDSaySe", "metadata": {"createdAt": "2025-03-10T22:51:05.797Z", "updatedAt": "2025-03-10T22:51:05.797Z"}, "name": "Set form answer", "next": "njYsjecO55wWqJ0a1Lssa", "properties": {"inputs": {"answer": "{{FirstName}}", "formConfigurationId": "V0UCzZ49ccFOYy39zOPOh", "formId": "{{AssigneeProfile.id}}", "questionId": "M7aZ2r5d5O", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "VSa8jNc7ee2nlyjbpiE0A": {"id": "VSa8jNc7ee2nlyjbpiE0A", "metadata": {"createdAt": "2025-03-07T05:07:08.279Z", "updatedAt": "2025-03-07T05:07:08.279Z"}, "name": "Get assignee information", "next": "hnMDPngOSaGhZprLjq99e", "properties": {"variables": [{"identifier": "LastName", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.aiCtHRlS00.answer}},{{AssigneeFirstName_index}})"}, {"identifier": "HomeCountry", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.oR3hExeYiL.answer}},{{AssigneeFirstName_index}})"}, {"identifier": "HostCountry", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.VmlDFbLJKZ.answer}},{{AssigneeFirstName_index}})"}, {"identifier": "AssignmentStart", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.WSHCcFlExG.answer}},{{AssigneeFirstName_index}})"}, {"identifier": "AssignmentEnd", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.waeNppHU1u.answer}},{{AssigneeFirstName_index}})"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT({{Assignee<PERSON><PERSON>tName}}, \" \", {{LastName}})"}, {"identifier": "Assignee<PERSON><PERSON>", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.NygKq36atr.answer}},{{AssigneeFirstName_index}})"}, {"identifier": "FirstName", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.exI36T75Gq.answer}},{{AssigneeFirstName_index}})"}, {"identifier": "AssignmentPolicy", "type": "text", "value": "$INDEX({{form.aqAohrlqQ2.columns.avKORamTMA.answer}},{{AssigneeFirstName_index}})"}, {"identifier": "AssignmentInfo", "type": "json", "value": "[{\"aXgaVheZhu\":{{AssignmentPolicy}},\"MGJXlvwkBj\":{{HomeCountry}},\"apRPubvsrG\":{{HostCountry}},\"av7k23SmGg\":{{AssignmentStart}},\"VuaSF5PWY9\":{{AssignmentEnd}}}]"}]}, "variant": "setVariables"}, "WSgod7vMuivem2mogeskj": {"id": "WSgod7vMuivem2mogeskj", "metadata": {"createdAt": "2025-03-12T12:44:21.542Z", "updatedAt": "2025-03-12T12:44:21.542Z"}, "name": "Set form answer", "next": "aXiavw07sVlk1d9MoLi87", "properties": {"inputs": {"answer": "{{AssignmentInfo}}", "formConfigurationId": "V0UCzZ49ccFOYy39zOPOh", "formId": "{{AssigneeProfile.id}}", "operation": "setTable", "questionId": "T7IoTXkA0l", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "hnMDPngOSaGhZprLjq99e": {"id": "hnMDPngOSaGhZprLjq99e", "metadata": {"createdAt": "2025-04-18T02:24:11.869Z", "updatedAt": "2025-04-18T02:24:11.869Z"}, "name": "Create assignee", "next": "py5SwRl2aPbG2YDazGS5a", "properties": {"inputs": {"foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "AssigneeObject", "key": "{{AssigneeId}}", "name": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "parentId": "{{form.foundation.id}}"}, "typePrimaryIdentifier": "selectOrCreateFoundation"}, "variant": "action"}, "njYsjecO55wWqJ0a1Lssa": {"id": "njYsjecO55wWqJ0a1Lssa", "metadata": {"createdAt": "2025-03-10T22:51:55.329Z", "updatedAt": "2025-03-10T22:51:55.329Z"}, "name": "Set form answer", "next": "WSgod7vMuivem2mogeskj", "properties": {"inputs": {"answer": "{{LastName}}", "formConfigurationId": "V0UCzZ49ccFOYy39zOPOh", "formId": "{{AssigneeProfile.id}}", "questionId": "jc2EKPHZeO", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "py5SwRl2aPbG2YDazGS5a": {"id": "py5SwRl2aPbG2YDazGS5a", "metadata": {"createdAt": "2025-04-18T02:03:17.650Z", "updatedAt": "2025-04-18T02:03:17.650Z"}, "name": "Create assignee profile", "next": "aTU439LfGgGOoenDSaySe", "properties": {"inputs": {"formConfigurationId": "V0UCzZ49ccFOYy39zOPOh", "formVariableName": "Assign<PERSON>P<PERSON><PERSON><PERSON>", "foundationId": "{{AssigneeObject.id}}"}, "typePrimaryIdentifier": "selectOrCreateForm"}, "variant": "action"}}}, "inputs": {"isReturn": "false", "itemVariableName": "Assign<PERSON><PERSON><PERSON><PERSON><PERSON>ame", "list": "{{form.aqAohrlqQ2.columns.exI36T75Gq.answer}}", "resultVariableName": "Assignees", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}, "KIaGJRrPNxZGcaim33baF": {"id": "KIaGJRrPNxZGcaim33baF", "metadata": {"createdAt": "2025-04-18T01:58:48.450Z", "updatedAt": "2025-04-18T01:58:48.450Z"}, "name": "Set variable(s)", "next": "Gc923oMziva2gYKm4UPgL", "properties": {"variables": [{"identifier": "AssigneeFormId", "type": "text", "value": ""}]}, "variant": "setVariables"}}, "triggers": {"laTNCTah2Wr5myyBwe8VD": {"id": "laTNCTah2Wr5myyBwe8VD", "metadata": {"createdAt": "2025-03-03T22:51:56.485Z", "updatedAt": "2025-03-03T22:51:56.485Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Create Assignee Profiles", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "FjoMUqOoDabtqBivljsO6": {"description": "", "id": "FjoMUqOoDabtqBivljsO6", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T13:28:58.700Z", "updatedAt": "2025-04-14T03:34:35.023Z"}, "name": "Conditional - Options", "start": "aXNh7P9vIqKNXbgtp24mB", "steps": {"aXNh7P9vIqKNXbgtp24mB": {"id": "aXNh7P9vIqKNXbgtp24mB", "metadata": {"createdAt": "2025-03-05T13:29:47.666Z", "updatedAt": "2025-03-05T13:29:47.666Z"}, "name": "Options Check", "next": "P99IVB4pFg5gkVqq5DeRN", "properties": {"branches": [{"condition": {"lhs": "{{form.X2T3j281Sy.answer}}", "operator": "=", "rhs": "Option 1"}, "name": "Option 1", "next": "W5u2DarbDuwXHkN8GUevF"}, {"condition": {"lhs": "{{form.X2T3j281Sy.answer}}", "operator": "=", "rhs": "Option 2"}, "name": "Option 2", "next": "cvYxuMcZpAgodNlIlCA0R"}]}, "variant": "condition"}, "P99IVB4pFg5gkVqq5DeRN": {"id": "P99IVB4pFg5gkVqq5DeRN", "metadata": {"createdAt": "2025-03-05T13:32:13.016Z", "updatedAt": "2025-03-05T13:32:13.016Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "No Option", "formConfigurationId": "", "formId": "{{form.id}}", "questionId": "{{form.cqJsH01A6G.id}}", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "W5u2DarbDuwXHkN8GUevF": {"id": "W5u2DarbDuwXHkN8GUevF", "metadata": {"createdAt": "2025-03-05T13:31:24.604Z", "updatedAt": "2025-03-05T13:31:24.604Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "Option 1", "formId": "{{form.id}}", "questionId": "{{form.cqJsH01A6G.id}}", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "cvYxuMcZpAgodNlIlCA0R": {"id": "cvYxuMcZpAgodNlIlCA0R", "metadata": {"createdAt": "2025-03-05T13:31:57.634Z", "updatedAt": "2025-03-05T13:31:57.634Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "Option 2", "formId": "{{form.id}}", "questionId": "{{form.cqJsH01A6G.id}}", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"zjQYSimJWsaUrQuo0ea4a": {"id": "zjQYSimJWsaUrQuo0ea4a", "metadata": {"createdAt": "2025-03-05T13:29:05.533Z", "updatedAt": "2025-03-05T13:29:05.533Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "RUN CONDITIONAL", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "HciG4QaRykhNJq7pg1Y4D": {"description": "", "id": "HciG4QaRykhNJq7pg1Y4D", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T06:06:31.690Z", "updatedAt": "2025-04-14T03:34:06.400Z"}, "name": "Set answer from prior year", "start": "nrSA9bkkiG0lzNyazaaSp", "startingVariables": [{"identifier": "TestForm", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "status": "active", "steps": {"XEQ3bjFV38BbZRTt03RFX": {"id": "XEQ3bjFV38BbZRTt03RFX", "metadata": {"createdAt": "2025-03-28T00:04:08.186Z", "updatedAt": "2025-03-28T00:04:08.186Z"}, "name": "Select form", "next": "Zta58D03laaScZO7D4nh4", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "PYForm", "foundationId": "{{Assignee.id}}", "intervalId": "nfwPMnwqpUvfaLCUwMqe6"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "Zta58D03laaScZO7D4nh4": {"id": "Zta58D03laaScZO7D4nh4", "metadata": {"createdAt": "2025-03-28T00:05:30.390Z", "updatedAt": "2025-03-28T00:05:30.390Z"}, "name": "Set form answer", "next": "c4Em8Qz0QsJB7Z8mQmwXu", "properties": {"inputs": {"answer": "This is an answer from PY", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{PYForm.id}}", "questionId": "t60PjuSTaB", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "c4Em8Qz0QsJB7Z8mQmwXu": {"id": "c4Em8Qz0QsJB7Z8mQmwXu", "metadata": {"createdAt": "2025-04-17T02:44:19.213Z", "updatedAt": "2025-04-17T02:44:19.213Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{PYForm.t60PjuSTaB.answer}}", "columnId": "{{TestForm.pB7BSlhmKW.columns.bf4Ma7DWGF.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{TestForm.id}}", "operation": "setCell", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "5", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "iguxSE4YguKn0BaJ7cKFT": {"id": "iguxSE4YguKn0BaJ7cKFT", "metadata": {"createdAt": "2025-03-28T00:04:24.120Z", "updatedAt": "2025-03-28T00:04:24.120Z"}, "name": "Select Foundation", "next": "XEQ3bjFV38BbZRTt03RFX", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "nrSA9bkkiG0lzNyazaaSp": {"id": "nrSA9bkkiG0lzNyazaaSp", "metadata": {"createdAt": "2025-03-28T00:03:45.638Z", "updatedAt": "2025-03-28T00:03:45.638Z"}, "name": "Set variable(s)", "next": "iguxSE4YguKn0BaJ7cKFT", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"ASGN\",{{TestForm.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}}, "triggers": {"kbtGWwxayy2TaO5o94i6q": {"id": "kbtGWwxayy2TaO5o94i6q", "metadata": {"createdAt": "2025-03-05T06:06:33.985Z", "updatedAt": "2025-03-05T06:06:33.985Z"}, "name": "When an answer is changed", "next": "", "properties": {"inputs": {"formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "TestForm", "questionId": "rxcs6pDarL", "questionIds": "[\"waNU3AzjNL\"]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "LaaJ5JrTngjEfJop763AO": {"description": "", "id": "LaaJ5JrTngjEfJop763AO", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T13:01:28.868Z", "updatedAt": "2025-04-14T03:34:25.800Z"}, "name": "Set alert - Single Value", "start": "NDFKdBOWhJ3CAqbhcanap", "steps": {"NDFKdBOWhJ3CAqbhcanap": {"id": "NDFKdBOWhJ3CAqbhcanap", "metadata": {"createdAt": "2025-03-05T13:01:49.381Z", "updatedAt": "2025-03-05T13:01:49.381Z"}, "name": "Set form alert", "next": "", "properties": {"inputs": {"formId": "{{form.id}}", "groupIdentifier": "Alert_1", "message": "<PERSON><PERSON>!", "operation": "add", "questionId": "{{form.t60PjuSTaB.id}}", "variant": "blocker"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}}, "triggers": {"RjTP78aT40gQMfahgjy3S": {"id": "RjTP78aT40gQMfahgjy3S", "metadata": {"createdAt": "2025-03-05T13:01:32.950Z", "updatedAt": "2025-03-05T13:01:32.950Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "SET ALERT - SINGLE", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "LfGhaaZrFZzlcbVsccQrW": {"description": "", "id": "LfGhaaZrFZzlcbVsccQrW", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-06T00:46:54.045Z", "updatedAt": "2025-04-14T03:35:00.233Z"}, "name": "LEFT/MID - Split text by space", "start": "MhvtA1IqKFNmqjxJKamaI", "steps": {"MhvtA1IqKFNmqjxJKamaI": {"id": "MhvtA1IqKFNmqjxJKamaI", "metadata": {"createdAt": "2025-03-06T00:47:12.715Z", "updatedAt": "2025-03-06T00:47:12.715Z"}, "name": "Set variable(s)", "next": "QrabU2aSHpCF1akD0ARBk", "properties": {"variables": [{"identifier": "FirstName", "type": "text", "value": "$LEFT({{form.n96ZMA9IwA.answer}},$FIND(\" \",{{form.n96ZMA9IwA.answer}},1)-1)"}, {"identifier": "LastName", "type": "text", "value": "$MID({{form.n96ZMA9IwA.answer}},$FIND(\" \",{{form.n96ZMA9IwA.answer}},1)+1,100)"}, {"identifier": "CONCAT", "type": "text", "value": "$CONCAT({{FirstName}}, \" \", {{LastName}})"}]}, "variant": "setVariables"}, "QrabU2aSHpCF1akD0ARBk": {"id": "QrabU2aSHpCF1akD0ARBk", "metadata": {"createdAt": "2025-03-06T00:48:42.648Z", "updatedAt": "2025-03-06T00:48:42.648Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{CONCAT}}", "formId": "{{form.id}}", "questionId": "{{form.t60PjuSTaB.id}}", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"OCihOPVfl8QB6aqk2hcn0": {"id": "OCihOPVfl8QB6aqk2hcn0", "metadata": {"createdAt": "2025-03-06T00:46:56.449Z", "updatedAt": "2025-03-06T00:46:56.449Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "LEFT/MID - SINGLE", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "ObWGDhTKIfuMEeXj274hQ": {"description": "", "id": "ObWGDhTKIfuMEeXj274hQ", "labels": ["f4V7hhKY0WrW7BexzpwSQ"], "metadata": {"createdAt": "2025-04-10T04:13:53.727Z", "updatedAt": "2025-04-14T03:36:30.420Z"}, "name": "<PERSON> <PERSON><PERSON>", "start": "unbzjeZBP24BwZauenOuq", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "status": "active", "steps": {"aQ4v1Sfh6aIXpGmRKVn7K": {"id": "aQ4v1Sfh6aIXpGmRKVn7K", "metadata": {"createdAt": "2025-04-18T04:44:46.940Z", "updatedAt": "2025-04-18T04:44:46.940Z"}, "name": "Set form answer", "next": "AQuyDr3cOmAj0eAjIQK2Q", "properties": {"inputs": {"answer": "{{TemplateClientForm.SoBlBkh5ZM.answer}}", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formId": "{{ClientGroupForm.id}}", "operation": "setTable", "questionId": "SoBlBkh5ZM", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "a2z8khzu2CbAC2nsS4hZY": {"id": "a2z8khzu2CbAC2nsS4hZY", "metadata": {"createdAt": "2025-04-17T08:10:06.128Z", "updatedAt": "2025-04-17T08:10:06.128Z"}, "name": "Select Foundation", "next": "jC1mZXsJm8uQcvKNIsPxa", "properties": {"inputs": {"continueFlowIfNotFound": "false", "foundationConfigurationId": "D1axfoA5lvjOQ0uezN05G", "foundationVariableName": "Client", "key": "{{<PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "at2aiCZSUpSfZMizfyrau": {"id": "at2aiCZSUpSfZMizfyrau", "metadata": {"createdAt": "2025-04-18T04:55:56.220Z", "updatedAt": "2025-04-18T04:55:56.220Z"}, "name": "7. Analyse Comp Data", "next": "knEI1qFRt37A0LVWNEdyu", "properties": {"inputs": {"flowConfigurationId": "zwXBlUpcsNiCC3AgaKTGR", "form": "{{TaxReturnForm.id}}"}}, "variant": "flow"}, "aX98qtzYS4ci42LEWAG0B": {"id": "aX98qtzYS4ci42LEWAG0B", "metadata": {"createdAt": "2025-04-18T02:51:13.378Z", "updatedAt": "2025-04-18T02:51:13.378Z"}, "name": "Add complete to log", "next": "q843WMSuUzB5tWqarJM6V", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "5", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "agYsviGx2k2KhWmZjjfwN": {"id": "agYsviGx2k2KhWmZjjfwN", "metadata": {"createdAt": "2025-04-18T01:49:36.004Z", "updatedAt": "2025-04-18T01:49:36.004Z"}, "name": "3. Create assignee profiles from client form", "next": "Lp9ZQGx831kiQn7Rq7vpa", "properties": {"inputs": {"flowConfigurationId": "FUp3ETxZ9AuX6X8yk4ajQ", "form": "{{ClientGroupForm.id}}"}}, "variant": "flow"}, "aLgDZVehntReTyzqOOGpo": {"id": "aLgDZVehntReTyzqOOGpo", "metadata": {"createdAt": "2025-04-18T04:39:41.662Z", "updatedAt": "2025-04-18T04:39:41.662Z"}, "name": "10. Missing Bonus Warning", "next": "lPtryeSq8v9luNRfWPa5Y", "properties": {"inputs": {"flowConfigurationId": "rjqfSI2di2j5UUedWIloG", "form": "{{TaxReturnForm.id}}"}}, "variant": "flow"}, "axMrTh9QGFj2isvhorraB": {"id": "axMrTh9QGFj2isvhorraB", "metadata": {"createdAt": "2025-04-17T08:08:38.647Z", "updatedAt": "2025-04-17T08:08:38.647Z"}, "name": "2. Create authorisation list when client is created", "next": "DI1tpRotSpefgaBLWSAOy", "properties": {"inputs": {"flowConfigurationId": "fNJ3ONFMx41eY8j4JK1RT", "foundation": "{{Client.id}}"}}, "variant": "flow"}, "azgWoQ6abFFapnv0vI6sY": {"id": "azgWoQ6abFFapnv0vI6sY", "metadata": {"createdAt": "2025-04-17T07:59:02.220Z", "updatedAt": "2025-04-17T07:59:02.220Z"}, "name": "Get client key", "next": "XoeQuytkz3S05yFtYJjeB", "properties": {"variables": [{"identifier": "CGKey", "type": "text", "value": "$CONCAT(\"CG\",{{form.B1AYblG3LF.answer}})"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"CLI\",{{form.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "AQuyDr3cOmAj0eAjIQK2Q": {"id": "AQuyDr3cOmAj0eAjIQK2Q", "metadata": {"createdAt": "2025-04-18T01:48:04.244Z", "updatedAt": "2025-04-18T01:48:04.244Z"}, "name": "Add complete to log", "next": "RXMLDaBNJULxtNCH9eTSW", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "3", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "ClVaJREQNkMWZ6Fiv5HSU": {"id": "ClVaJREQNkMWZ6Fiv5HSU", "metadata": {"createdAt": "2025-04-18T04:57:55.478Z", "updatedAt": "2025-04-18T04:57:55.478Z"}, "name": "Add complete to log", "next": "EKKPbH1KBpBGYa5kBeFd8", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "15", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "Da6cJczqbwFj7302hPnMM": {"id": "Da6cJczqbwFj7302hPnMM", "metadata": {"createdAt": "2025-04-18T01:42:06.925Z", "updatedAt": "2025-04-18T01:42:06.925Z"}, "name": "Get Template Client Group", "next": "PtXhpbYs3MDCVTc4TBoP3", "properties": {"inputs": {"continueFlowIfNotFound": "false", "foundationConfigurationId": "daYJS2xq9fkLHcaY1YG7H", "foundationVariableName": "TemplateClient", "key": "TEMPLATEGROUP"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "DI1tpRotSpefgaBLWSAOy": {"id": "DI1tpRotSpefgaBLWSAOy", "metadata": {"createdAt": "2025-04-18T01:19:56.579Z", "updatedAt": "2025-04-18T01:19:56.579Z"}, "name": "Set variable(s)", "next": "eiIR5YDKaSEDFegTXmpId", "properties": {"variables": [{"identifier": "Step2Complete", "type": "text", "value": "$CONCAT(\"Success - Form ID:\", {{AuthorisationList.id}})"}]}, "variant": "setVariables"}, "EKKPbH1KBpBGYa5kBeFd8": {"id": "EKKPbH1KBpBGYa5kBeFd8", "metadata": {"createdAt": "2025-04-18T04:59:32.341Z", "updatedAt": "2025-04-18T04:59:32.341Z"}, "name": "13. Calculated foreign and local sourced amounts", "next": "EkEyIb3learJkVwDqeOax", "properties": {"inputs": {"flowConfigurationId": "g74c8tbdIBKQXnCW32FVE", "form": "{{TaxReturnForm.id}}"}}, "variant": "flow"}, "EkEyIb3learJkVwDqeOax": {"id": "EkEyIb3learJkVwDqeOax", "metadata": {"createdAt": "2025-04-23T01:49:00.568Z", "updatedAt": "2025-04-23T01:49:00.568Z"}, "name": "9. <PERSON>mmarise Comp Data (Table)", "next": "IyAakvvbXX7GaLaSRaCIE", "properties": {"inputs": {"flowConfigurationId": "e6Q09UzxR1taN88FG1Nok", "form": "{{TaxReturnForm.id}}"}}, "variant": "flow"}, "EtYuPkRfLaPAyWVKTIM8o": {"id": "EtYuPkRfLaPAyWVKTIM8o", "metadata": {"createdAt": "2025-04-18T02:21:41.376Z", "updatedAt": "2025-04-18T02:21:41.376Z"}, "name": "Select form", "next": "rP9R2Y8zj1ajGWUufautv", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "V0UCzZ49ccFOYy39zOPOh", "formVariableName": "As<PERSON>eeForm", "foundationId": "{{Assignee.id}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "Fg6cIagkaX8TZfCrloFMX": {"id": "Fg6cIagkaX8TZfCrloFMX", "metadata": {"createdAt": "2025-04-18T02:04:02.966Z", "updatedAt": "2025-04-18T02:04:02.966Z"}, "name": "Add complete to log", "next": "mFS9hFBqnOvkFy3RVaEoV", "properties": {"inputs": {"answer": "{{Step3Complete}}", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "4", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "G81KpVTe4aVT06DZ1LffE": {"id": "G81KpVTe4aVT06DZ1LffE", "metadata": {"createdAt": "2025-04-18T04:42:10.076Z", "updatedAt": "2025-04-18T04:42:10.076Z"}, "name": "Update bonus mapping", "next": "VTEiN3YYCmapVEaDdYaQd", "properties": {"inputs": {"answer": "Bonus", "columnId": "{{TemplateClientForm.SoBlBkh5ZM.columns.XaADpaR1Sf.id}}", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formId": "{{ClientGroupForm.id}}", "operation": "setCell", "questionId": "SoBlBkh5ZM", "questionTypeWithOperation": "table", "rowIndex": "32", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "IyAakvvbXX7GaLaSRaCIE": {"id": "IyAakvvbXX7GaLaSRaCIE", "metadata": {"createdAt": "2025-04-18T04:59:49.973Z", "updatedAt": "2025-04-18T04:59:49.973Z"}, "name": "Add complete to log", "next": "XNa2aIZqe6sOPp0n49u7Z", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "16", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "KzU5wx8p2TdRvm5m3EJaF": {"id": "KzU5wx8p2TdRvm5m3EJaF", "metadata": {"createdAt": "2025-04-18T02:52:58.209Z", "updatedAt": "2025-04-18T02:52:58.209Z"}, "name": "Add complete to log", "next": "ZcMzQyzE3bRXYtkafll7a", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "7", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "L061TxX6ODyVA19nAkEZE": {"id": "L061TxX6ODyVA19nAkEZE", "metadata": {"createdAt": "2025-04-18T02:52:30.824Z", "updatedAt": "2025-04-18T02:52:30.824Z"}, "name": "Add complete to log", "next": "wiPbTbVGe7zRrul4DXRTy", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "6", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "Lp9ZQGx831kiQn7Rq7vpa": {"id": "Lp9ZQGx831kiQn7Rq7vpa", "metadata": {"createdAt": "2025-04-18T02:04:46.809Z", "updatedAt": "2025-04-18T02:04:46.809Z"}, "name": "Set variable(s)", "next": "Fg6cIagkaX8TZfCrloFMX", "properties": {"variables": [{"identifier": "Step3Complete", "type": "text", "value": "$CONCAT(\"Success - Form ID: \",{{AssigneeFormId}})"}, {"identifier": "NewEndDate", "type": "date", "value": "$DATE(2026,12,31)"}]}, "variant": "setVariables"}, "PtXhpbYs3MDCVTc4TBoP3": {"id": "PtXhpbYs3MDCVTc4TBoP3", "metadata": {"createdAt": "2025-04-18T01:41:53.858Z", "updatedAt": "2025-04-18T01:41:53.858Z"}, "name": "Get Template Form", "next": "VsadgGhaeXDhX9N0J6Tta", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formVariableName": "TemplateClientForm", "foundationId": "{{TemplateClient.id}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "RMee9aCVrnFEFawOvKtad": {"id": "RMee9aCVrnFEFawOvKtad", "metadata": {"createdAt": "2025-04-18T01:18:59.432Z", "updatedAt": "2025-04-18T01:18:59.432Z"}, "name": "Set variable(s)", "next": "ZyaR9aPbTKePSy0wk2nZM", "properties": {"variables": [{"identifier": "Step1Complete", "type": "text", "value": "$CONCAT(\"Success - Form ID:\",{{ClientGroupForm.id}})"}]}, "variant": "setVariables"}, "RXMLDaBNJULxtNCH9eTSW": {"id": "RXMLDaBNJULxtNCH9eTSW", "metadata": {"createdAt": "2025-04-18T01:46:41.256Z", "updatedAt": "2025-04-18T01:46:41.257Z"}, "name": "Set assignee ID", "next": "XJamBYIYx8iRnRSnLdHCp", "properties": {"variables": [{"identifier": "Assignee<PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"DEMO\",{{form.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "VTEiN3YYCmapVEaDdYaQd": {"id": "VTEiN3YYCmapVEaDdYaQd", "metadata": {"createdAt": "2025-04-18T04:58:59.739Z", "updatedAt": "2025-04-18T04:58:59.739Z"}, "name": "Update bonus mapping", "next": "gzA3Y4vBFZjeHC1dapgh5", "properties": {"inputs": {"answer": "false", "columnId": "{{TemplateClientForm.SoBlBkh5ZM.columns.X4KTqYVmaV.id}}", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formId": "{{ClientGroupForm.id}}", "operation": "setCell", "questionId": "SoBlBkh5ZM", "questionTypeWithOperation": "table", "rowIndex": "32", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "VsadgGhaeXDhX9N0J6Tta": {"id": "VsadgGhaeXDhX9N0J6Tta", "metadata": {"createdAt": "2025-04-18T01:44:03.106Z", "updatedAt": "2025-04-18T01:44:03.106Z"}, "name": "Set template authorisation policy", "next": "kFPfFnZViJpqvxV73haTt", "properties": {"inputs": {"answer": "{{TemplateClientForm.XfgXa6ooAm.answer}}", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formId": "{{ClientGroupForm.id}}", "operation": "setTable", "questionId": "XfgXa6ooAm", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "false"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "XJamBYIYx8iRnRSnLdHCp": {"id": "XJamBYIYx8iRnRSnLdHCp", "metadata": {"createdAt": "2025-04-18T01:45:41.510Z", "updatedAt": "2025-04-18T01:45:41.510Z"}, "name": "Set assignee id", "next": "agYsviGx2k2KhWmZjjfwN", "properties": {"inputs": {"answer": "{{AssigneeId}}", "columnId": "{{TemplateClientForm.aqAohrlqQ2.columns.NygKq36atr.id}}", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formId": "{{ClientGroupForm.id}}", "operation": "setCell", "questionId": "aqAohrlqQ2", "questionTypeWithOperation": "table", "rowIndex": "1", "rowSelection": "rowIndex", "valueRowIdShouldUpdate": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "XNa2aIZqe6sOPp0n49u7Z": {"id": "XNa2aIZqe6sOPp0n49u7Z", "metadata": {"createdAt": "2025-04-18T06:20:41.712Z", "updatedAt": "2025-04-18T06:20:41.712Z"}, "name": "Demo Flow Validation", "next": "n9QMosa2NePQh4GdJrD9Z", "properties": {"inputs": {"flowConfigurationId": "FGKFPa0tAGXb2GXNZzMgC", "form": "{{form.id}}"}}, "variant": "flow"}, "Xg5dvpqBkOx11vQxg1MQl": {"id": "Xg5dvpqBkOx11vQxg1MQl", "metadata": {"createdAt": "2025-04-18T03:15:51.229Z", "updatedAt": "2025-04-18T03:15:51.229Z"}, "name": "Add complete to log", "next": "wJxqgKo6DGiaoDnmCqkGg", "properties": {"inputs": {"answer": "{{Step6Complete}}", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "8", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "XoeQuytkz3S05yFtYJjeB": {"id": "XoeQuytkz3S05yFtYJjeB", "metadata": {"createdAt": "2025-04-17T08:09:39.697Z", "updatedAt": "2025-04-17T08:09:39.697Z"}, "name": "Select Foundation", "next": "a2z8khzu2CbAC2nsS4hZY", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "daYJS2xq9fkLHcaY1YG7H", "foundationVariableName": "ClientGroup", "key": "{{<PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "ZcMzQyzE3bRXYtkafll7a": {"id": "ZcMzQyzE3bRXYtkafll7a", "metadata": {"createdAt": "2025-04-18T03:14:04.293Z", "updatedAt": "2025-04-18T03:14:04.293Z"}, "name": "6. Approve authorisation list and create tax return forms", "next": "tkTsL6jmlDNpNvoIdCniM", "properties": {"inputs": {"flowConfigurationId": "aA6A7kQGadU9zCG0YEdF3", "form": "{{AuthorisationList.id}}"}}, "variant": "flow"}, "ZyaR9aPbTKePSy0wk2nZM": {"id": "ZyaR9aPbTKePSy0wk2nZM", "metadata": {"createdAt": "2025-04-18T00:52:26.254Z", "updatedAt": "2025-04-18T00:52:26.254Z"}, "name": "Add complete to log", "next": "axMrTh9QGFj2isvhorraB", "properties": {"inputs": {"answer": "{{Step1Complete}}", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "1", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "aNLnzawtC66xNTaohqEv9": {"id": "aNLnzawtC66xNTaohqEv9", "metadata": {"createdAt": "2025-04-18T04:36:16.956Z", "updatedAt": "2025-04-18T04:36:16.956Z"}, "name": "7. Analyse Comp Data", "next": "ij3X8kUOYbhKQM2vI1il0", "properties": {"inputs": {"flowConfigurationId": "zwXBlUpcsNiCC3AgaKTGR", "form": "{{TaxReturnForm.id}}"}}, "variant": "flow"}, "b2s16PaIyXBlvz2GUcN4h": {"id": "b2s16PaIyXBlvz2GUcN4h", "metadata": {"createdAt": "2025-04-18T00:47:32.004Z", "updatedAt": "2025-04-18T00:47:32.004Z"}, "name": "Set log scenarios", "next": "pmagaMCuI3AhrI5R0HVGr", "properties": {"variables": [{"identifier": "Log", "properties": {"columnIdentifier": "{{form.CLE7acMs4f.columns.gpewhsGTMO.id}}", "operation": "setColumn"}, "type": "table", "value": "[\"Create client group form\", \"Create authorisation list\", \"Write answers to client form from template\",\"Create assignee profile\", \"Update assignee assignment dates\", \"Sync to client group form\", \"Sync to authorisation list\", \"Create assignee tax return\", \"Load current year payroll data\", \"Run payroll analysis\", \"Verify bonus check\", \"Update mappings\", \"Re-run payroll analysis\", \"Verify remove bonus alert\", \"Run sourcing analysis\", \"Summarise comp table\",\"Evaluating Test Results\"]"}]}, "variant": "setVariables"}, "caiwe9n93sDOSIC6vGkaC": {"id": "caiwe9n93sDOSIC6vGkaC", "metadata": {"createdAt": "2025-04-18T04:32:43.083Z", "updatedAt": "2025-04-18T04:32:43.083Z"}, "name": "Set template payroll data", "next": "j9nQSI7EcXxgISYvl09rv", "properties": {"inputs": {"answer": "{{TemplateTaxReturnForm.qO3xge5etd.answer}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{TaxReturnForm.id}}", "operation": "setTable", "questionId": "qO3xge5etd", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "eiIR5YDKaSEDFegTXmpId": {"id": "eiIR5YDKaSEDFegTXmpId", "metadata": {"createdAt": "2025-04-18T00:54:05.808Z", "updatedAt": "2025-04-18T00:54:05.808Z"}, "name": "Add complete to log", "next": "Da6cJczqbwFj7302hPnMM", "properties": {"inputs": {"answer": "{{Step2Complete}}", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "2", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "gzA3Y4vBFZjeHC1dapgh5": {"id": "gzA3Y4vBFZjeHC1dapgh5", "metadata": {"createdAt": "2025-04-18T04:55:04.188Z", "updatedAt": "2025-04-18T04:55:04.188Z"}, "name": "Add complete to log", "next": "at2aiCZSUpSfZMizfyrau", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "12", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "iXgJaVFD25Npsdvh8Aiwa": {"id": "iXgJaVFD25Npsdvh8Aiwa", "metadata": {"createdAt": "2025-04-18T04:29:58.128Z", "updatedAt": "2025-04-18T04:29:58.128Z"}, "name": "Get template tax return", "next": "caiwe9n93sDOSIC6vGkaC", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "TemplateTaxReturnForm", "foundationId": "{{TemplateAssignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "ij3X8kUOYbhKQM2vI1il0": {"id": "ij3X8kUOYbhKQM2vI1il0", "metadata": {"createdAt": "2025-04-18T04:36:47.212Z", "updatedAt": "2025-04-18T04:36:47.212Z"}, "name": "Add complete to log", "next": "aLgDZVehntReTyzqOOGpo", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "10", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "j9nQSI7EcXxgISYvl09rv": {"id": "j9nQSI7EcXxgISYvl09rv", "metadata": {"createdAt": "2025-04-18T04:34:00.735Z", "updatedAt": "2025-04-18T04:34:00.735Z"}, "name": "Add complete to log", "next": "aNLnzawtC66xNTaohqEv9", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "9", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "jC1mZXsJm8uQcvKNIsPxa": {"id": "jC1mZXsJm8uQcvKNIsPxa", "metadata": {"createdAt": "2025-04-17T07:58:34.051Z", "updatedAt": "2025-04-17T07:58:34.051Z"}, "name": "1. Create client group form", "next": "RMee9aCVrnFEFawOvKtad", "properties": {"inputs": {"flowConfigurationId": "F4C7QxjXDITjch1FaNXwG", "foundation": "{{ClientGroup.id}}"}}, "variant": "flow"}, "jLMyXJAdefwML3MTGqbIc": {"id": "jLMyXJAdefwML3MTGqbIc", "metadata": {"createdAt": "2025-04-18T04:58:02.940Z", "updatedAt": "2025-04-18T04:58:02.940Z"}, "name": "12. Calculate days in SG during sourcing period", "next": "ClVaJREQNkMWZ6Fiv5HSU", "properties": {"inputs": {"flowConfigurationId": "aTPYchZaa77BEaguqh3YP", "form": "{{TaxReturnForm.id}}"}}, "variant": "flow"}, "kFPfFnZViJpqvxV73haTt": {"id": "kFPfFnZViJpqvxV73haTt", "metadata": {"createdAt": "2025-04-18T01:45:07.260Z", "updatedAt": "2025-04-18T01:45:07.260Z"}, "name": "Set template assignees", "next": "aQ4v1Sfh6aIXpGmRKVn7K", "properties": {"inputs": {"answer": "{{TemplateClientForm.aqAohrlqQ2.answer}}", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formId": "{{ClientGroupForm.id}}", "operation": "setTable", "questionId": "aqAohrlqQ2", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "false"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "knEI1qFRt37A0LVWNEdyu": {"id": "knEI1qFRt37A0LVWNEdyu", "metadata": {"createdAt": "2025-04-18T04:56:15.974Z", "updatedAt": "2025-04-18T04:56:15.974Z"}, "name": "Add complete to log", "next": "ulvKcp1eANq5Kj6UqLTsg", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "13", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "lPtryeSq8v9luNRfWPa5Y": {"id": "lPtryeSq8v9luNRfWPa5Y", "metadata": {"createdAt": "2025-04-18T04:40:40.276Z", "updatedAt": "2025-04-18T04:40:40.276Z"}, "name": "Add complete to log", "next": "G81KpVTe4aVT06DZ1LffE", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "11", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "mFS9hFBqnOvkFy3RVaEoV": {"id": "mFS9hFBqnOvkFy3RVaEoV", "metadata": {"createdAt": "2025-04-18T02:28:15.851Z", "updatedAt": "2025-04-18T02:28:15.851Z"}, "name": "Select Foundation", "next": "EtYuPkRfLaPAyWVKTIM8o", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{AssigneeId}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "n9QMosa2NePQh4GdJrD9Z": {"id": "n9QMosa2NePQh4GdJrD9Z", "metadata": {"createdAt": "2025-04-18T06:21:11.965Z", "updatedAt": "2025-04-18T06:21:11.965Z"}, "name": "Add complete to log", "next": "", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "17", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "pmagaMCuI3AhrI5R0HVGr": {"id": "pmagaMCuI3AhrI5R0HVGr", "metadata": {"createdAt": "2025-04-18T00:48:39.305Z", "updatedAt": "2025-04-18T00:48:39.305Z"}, "name": "Set log table to form", "next": "azgWoQ6abFFapnv0vI6sY", "properties": {"inputs": {"answer": "{{Log}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setTable", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "false"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "q2jv7DHfOUhiaea4apfIN": {"id": "q2jv7DHfOUhiaea4apfIN", "metadata": {"createdAt": "2025-04-18T04:56:20.636Z", "updatedAt": "2025-04-18T04:56:20.636Z"}, "name": "Add complete to log", "next": "zUOuGY15tJ6b3QiaxUNTP", "properties": {"inputs": {"answer": "Success", "columnId": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CLE7acMs4f", "questionTypeWithOperation": "table", "rowIndex": "14", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "q5CaxaQqPbYNsS4OlGdo4": {"id": "q5CaxaQqPbYNsS4OlGdo4", "metadata": {"createdAt": "2025-04-18T04:30:39.048Z", "updatedAt": "2025-04-18T04:30:39.049Z"}, "name": "Get template assignee", "next": "iXgJaVFD25Npsdvh8Aiwa", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Temp<PERSON><PERSON><PERSON><PERSON>", "key": "TEMPLATEASSIGNEE"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "q843WMSuUzB5tWqarJM6V": {"id": "q843WMSuUzB5tWqarJM6V", "metadata": {"createdAt": "2025-04-18T02:52:07.241Z", "updatedAt": "2025-04-18T02:52:07.241Z"}, "name": "4. Update assignment information in client form when assignee profile changes", "next": "L061TxX6ODyVA19nAkEZE", "properties": {"inputs": {"flowConfigurationId": "edG3ZL0GtqLBDn6wSMt9D", "form": "{{AssigneeForm.id}}"}}, "variant": "flow"}, "rP9R2Y8zj1ajGWUufautv": {"id": "rP9R2Y8zj1ajGWUufautv", "metadata": {"createdAt": "2025-04-18T02:20:49.925Z", "updatedAt": "2025-04-18T02:20:49.925Z"}, "name": "Update assignee form", "next": "aX98qtzYS4ci42LEWAG0B", "properties": {"inputs": {"answer": "{{NewEndDate}}", "columnId": "{{AssigneeForm.T7IoTXkA0l.columns.VuaSF5PWY9.id}}", "formConfigurationId": "V0UCzZ49ccFOYy39zOPOh", "formId": "{{AssigneeFormId}}", "operation": "setCell", "questionId": "T7IoTXkA0l", "questionTypeWithOperation": "table", "rowIndex": "1", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "tkTsL6jmlDNpNvoIdCniM": {"id": "tkTsL6jmlDNpNvoIdCniM", "metadata": {"createdAt": "2025-04-18T03:15:19.940Z", "updatedAt": "2025-04-18T03:15:19.940Z"}, "name": "Set variable(s)", "next": "Xg5dvpqBkOx11vQxg1MQl", "properties": {"variables": [{"identifier": "Step6Complete", "type": "text", "value": "$CONCAT(\"Success - Form ID: \",{{AssigneeTaxReturnId}})"}]}, "variant": "setVariables"}, "ulvKcp1eANq5Kj6UqLTsg": {"id": "ulvKcp1eANq5Kj6UqLTsg", "metadata": {"createdAt": "2025-04-18T04:55:17.388Z", "updatedAt": "2025-04-18T04:55:17.388Z"}, "name": "8. Remove alert on PIT", "next": "q2jv7DHfOUhiaea4apfIN", "properties": {"inputs": {"flowConfigurationId": "dEYkVA0hFRoJizwo9bu5e", "form": "{{TaxReturnForm.id}}"}}, "variant": "flow"}, "unbzjeZBP24BwZauenOuq": {"id": "unbzjeZBP24BwZauenOuq", "metadata": {"createdAt": "2025-04-18T00:46:55.754Z", "updatedAt": "2025-04-18T00:46:55.754Z"}, "name": "Initiate log table", "next": "b2s16PaIyXBlvz2GUcN4h", "properties": {"variables": [{"identifier": "Log", "properties": {"operation": "setTable"}, "type": "table", "value": "[]"}]}, "variant": "setVariables"}, "wJxqgKo6DGiaoDnmCqkGg": {"id": "wJxqgKo6DGiaoDnmCqkGg", "metadata": {"createdAt": "2025-04-18T04:28:25.027Z", "updatedAt": "2025-04-18T04:28:25.027Z"}, "name": "Get tax return form", "next": "q5CaxaQqPbYNsS4OlGdo4", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "TaxReturnForm", "foundationId": "{{Assignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "wiPbTbVGe7zRrul4DXRTy": {"id": "wiPbTbVGe7zRrul4DXRTy", "metadata": {"createdAt": "2025-04-18T02:52:39.962Z", "updatedAt": "2025-04-18T02:52:39.962Z"}, "name": "5. Update authorisation list when assignment information changes", "next": "KzU5wx8p2TdRvm5m3EJaF", "properties": {"inputs": {"flowConfigurationId": "a3DJIUaljyVYKVQC7XvTS", "form": "{{ClientGroupForm.id}}"}}, "variant": "flow"}, "zUOuGY15tJ6b3QiaxUNTP": {"id": "zUOuGY15tJ6b3QiaxUNTP", "metadata": {"createdAt": "2025-04-18T04:57:33.204Z", "updatedAt": "2025-04-18T04:57:33.204Z"}, "name": "11. Perform Sourcing", "next": "jLMyXJAdefwML3MTGqbIc", "properties": {"inputs": {"flowConfigurationId": "au9IDqSVdX8tBmRawaq8H", "form": "{{TaxReturnForm.id}}"}}, "variant": "flow"}}, "triggers": {"au83JMIvB53B8RhAVwwVK": {"id": "au83JMIvB53B8RhAVwwVK", "metadata": {"createdAt": "2025-04-10T04:14:02.658Z", "updatedAt": "2025-04-10T04:14:02.658Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "<PERSON> <PERSON>", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "Orcq5ABUgofevSZNkfFCO": {"description": "", "id": "Orcq5ABUgofevSZNkfFCO", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-14T02:58:27.778Z", "updatedAt": "2025-04-14T03:35:35.434Z"}, "name": "Remove Duplicates", "start": "gr16dJ0x3JL55QkQPDylm", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.oXUOfkMKayra9lBi5NOvj"}], "steps": {"gr16dJ0x3JL55QkQPDylm": {"id": "gr16dJ0x3JL55QkQPDylm", "metadata": {"createdAt": "2025-03-14T02:58:48.755Z", "updatedAt": "2025-03-14T02:58:48.755Z"}, "name": "Set variable(s)", "next": "hXR4eIG2xaQgfgZXVEsRN", "properties": {"variables": [{"identifier": "REMOVEDUPLICATES", "type": "text", "value": "$REMOVEDUPLICATES({{form.Tt9L06N948.columns.wMthkQKZXy.answer}},{{form.Tt9L06N948.columns.wMthkQKZXy.answer}})"}]}, "variant": "setVariables"}, "hXR4eIG2xaQgfgZXVEsRN": {"id": "hXR4eIG2xaQgfgZXVEsRN", "metadata": {"createdAt": "2025-03-14T02:59:53.974Z", "updatedAt": "2025-03-14T02:59:53.974Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{REMOVEDUPLICATES}}", "columnId": "{{form.r05KtVT7D6.columns.pEi5LKU1ak.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "r05KtVT7D6", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"goBDHGrQVOkTqzADIbWEg": {"id": "goBDHGrQVOkTqzADIbWEg", "metadata": {"createdAt": "2025-03-14T02:58:33.974Z", "updatedAt": "2025-03-14T02:58:33.974Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "REMOVE DUPLICATES", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "PUGIvuoviwZK6lzQCw4wZ": {"description": "", "id": "PUGIvuoviwZK6lzQCw4wZ", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T23:26:12.221Z", "updatedAt": "2025-04-14T03:34:55.527Z"}, "name": "XLOOKUP - Single Value", "start": "lyUbtzDKPM1dj6GIkLw0a", "steps": {"atYFpt4Haxu6xSK7yOJUz": {"id": "atYFpt4Haxu6xSK7yOJUz", "metadata": {"createdAt": "2025-03-05T23:27:41.638Z", "updatedAt": "2025-03-05T23:27:41.639Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{XLOOKUP}}", "formId": "{{form.id}}", "questionId": "{{form.t60PjuSTaB.id}}", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "lyUbtzDKPM1dj6GIkLw0a": {"id": "lyUbtzDKPM1dj6GIkLw0a", "metadata": {"createdAt": "2025-03-05T23:26:31.155Z", "updatedAt": "2025-03-05T23:26:31.155Z"}, "name": "Set variable(s)", "next": "atYFpt4Haxu6xSK7yOJUz", "properties": {"variables": [{"identifier": "XLOOKUP", "type": "text", "value": "$XLOOKUP({{form.n96ZMA9IwA.answer}},{{form.Tt9L06N948.columns.wMthkQKZXy.answer}},{{form.Tt9L06N948.columns.wHhQcqKPvo.answer}})"}]}, "variant": "setVariables"}}, "triggers": {"uwg7a2QxaHocI6e8sUGUz": {"id": "uwg7a2QxaHocI6e8sUGUz", "metadata": {"createdAt": "2025-03-05T23:26:19.971Z", "updatedAt": "2025-03-05T23:26:19.971Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "XLOOKUP - <PERSON><PERSON><PERSON>", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "QjExeOqabLLerYaNtLSQE": {"description": "", "id": "QjExeOqabLLerYaNtLSQE", "labels": ["f4V7hhKY0WrW7BexzpwSQ"], "metadata": {"createdAt": "2025-03-23T23:00:27.914Z", "updatedAt": "2025-04-14T03:36:20.620Z"}, "name": "Publish Results", "start": "a58glL34LOUFaId1BPdDG", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "steps": {"a58glL34LOUFaId1BPdDG": {"id": "a58glL34LOUFaId1BPdDG", "metadata": {"createdAt": "2025-04-17T04:48:56.017Z", "updatedAt": "2025-04-17T04:48:56.017Z"}, "name": "Set variable(s)", "next": "w8kMqtmk9IFFWlgD2JzBg", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"CLI\",{{form.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "aOGHZ5h9wxEYmPvSi21bI": {"id": "aOGHZ5h9wxEYmPvSi21bI", "metadata": {"createdAt": "2025-04-17T04:51:28.071Z", "updatedAt": "2025-04-17T04:51:28.071Z"}, "name": "Set result column", "next": "m7BiJ8lFwzOdVFz1EsStz", "properties": {"variables": [{"identifier": "Results", "properties": {"columnIdentifier": "{{TestReport.YznzdrFg1a.columns.kehO4VyB1W.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.pB7BSlhmKW.columns.bf4Ma7DWGF.answer}}"}, {"identifier": "DemoResults", "properties": {"columnIdentifier": "{{TestReport.WSAnWbMCKB.columns.DLyahR7WrA.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.FfKlGILPWz.columns.InOWD9ZV6z.answer}}"}]}, "variant": "setVariables"}, "aLJbvrIz5i5TaIOLXeSmm": {"id": "aLJbvrIz5i5TaIOLXeSmm", "metadata": {"createdAt": "2025-05-01T03:54:58.290Z", "updatedAt": "2025-05-01T03:54:58.290Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{Demo<PERSON><PERSON><PERSON><PERSON>}}", "formConfigurationId": "z7690ou099uWbug54HSBU", "formId": "{{TestReport.id}}", "operation": "setTable", "questionId": "WSAnWbMCKB", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "aA36dNT7w1uSwl8qWoXg5": {"id": "aA36dNT7w1uSwl8qWoXg5", "metadata": {"createdAt": "2025-03-23T23:00:53.341Z", "updatedAt": "2025-03-23T23:00:53.341Z"}, "name": "Initiate results table", "next": "pMzFItDVlPjLwUsCm1vyo", "properties": {"variables": [{"identifier": "Results", "properties": {"operation": "setTable"}, "type": "table", "value": "[]"}, {"identifier": "DemoLog", "properties": {"operation": "setTable"}, "type": "table", "value": "[]"}, {"identifier": "DemoResults", "properties": {"operation": "setTable"}, "type": "table", "value": "[]"}]}, "variant": "setVariables"}, "VDTLUZ3aVuQmYrmfrgCGu": {"id": "VDTLUZ3aVuQmYrmfrgCGu", "metadata": {"createdAt": "2025-04-17T04:48:42.831Z", "updatedAt": "2025-04-17T04:48:42.831Z"}, "name": "Select form", "next": "aA36dNT7w1uSwl8qWoXg5", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "z7690ou099uWbug54HSBU", "formVariableName": "TestReport", "foundationId": "{{Client.id}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "m7BiJ8lFwzOdVFz1EsStz": {"id": "m7BiJ8lFwzOdVFz1EsStz", "metadata": {"createdAt": "2025-04-17T04:51:34.770Z", "updatedAt": "2025-04-17T04:51:34.770Z"}, "name": "Set pass/fail column", "next": "mMrBWfglaUyXa9Q7cDOmg", "properties": {"variables": [{"identifier": "Results", "properties": {"columnIdentifier": "{{TestReport.YznzdrFg1a.columns.Kg2EJvwFYm.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.pB7BSlhmKW.columns.awtfyzxoLZ.answer}}"}, {"identifier": "DemoResults", "properties": {"columnIdentifier": "{{TestReport.WSAnWbMCKB.columns.aO9VZWmBfL.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.FfKlGILPWz.columns.PEORVarfSN.answer}}"}]}, "variant": "setVariables"}, "mMrBWfglaUyXa9Q7cDOmg": {"id": "mMrBWfglaUyXa9Q7cDOmg", "metadata": {"createdAt": "2025-03-23T23:02:26.537Z", "updatedAt": "2025-03-23T23:02:26.537Z"}, "name": "Set form answer", "next": "t8qca9earK2SwOZAtu4fa", "properties": {"inputs": {"answer": "{{Results}}", "formConfigurationId": "z7690ou099uWbug54HSBU", "formId": "{{TestReport.id}}", "operation": "setTable", "questionId": "YznzdrFg1a", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "pMzFItDVlPjLwUsCm1vyo": {"id": "pMzFItDVlPjLwUsCm1vyo", "metadata": {"createdAt": "2025-03-23T23:02:00.824Z", "updatedAt": "2025-03-23T23:02:00.824Z"}, "name": "Set scenario column", "next": "tL1l9NQ0OjopGLpfAvFMQ", "properties": {"variables": [{"identifier": "Results", "properties": {"columnIdentifier": "{{TestReport.YznzdrFg1a.columns.iGsDLQIZwu.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.pB7BSlhmKW.columns.avyyvIN7EH.answer}}"}, {"identifier": "DemoResults", "properties": {"columnIdentifier": "{{TestReport.WSAnWbMCKB.columns.sZB6FObFGc.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.FfKlGILPWz.columns.DWlkI9DVzT.answer}}"}, {"identifier": "DemoLog", "properties": {"columnIdentifier": "{{TestReport.XcrkuM1qZx.columns.aGSrahCW6l.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.CLE7acMs4f.columns.gpewhsGTMO.answer}}"}]}, "variant": "setVariables"}, "t8qca9earK2SwOZAtu4fa": {"id": "t8qca9earK2SwOZAtu4fa", "metadata": {"createdAt": "2025-05-01T03:54:42.230Z", "updatedAt": "2025-05-01T03:54:42.230Z"}, "name": "Set form answer", "next": "aLJbvrIz5i5TaIOLXeSmm", "properties": {"inputs": {"answer": "{{<PERSON><PERSON><PERSON><PERSON>}}", "formConfigurationId": "z7690ou099uWbug54HSBU", "formId": "{{TestReport.id}}", "operation": "setTable", "questionId": "XcrkuM1qZx", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "tL1l9NQ0OjopGLpfAvFMQ": {"id": "tL1l9NQ0OjopGLpfAvFMQ", "metadata": {"createdAt": "2025-04-17T04:51:17.222Z", "updatedAt": "2025-04-17T04:51:17.222Z"}, "name": "Set expected result column", "next": "aOGHZ5h9wxEYmPvSi21bI", "properties": {"variables": [{"identifier": "Results", "properties": {"columnIdentifier": "{{TestReport.YznzdrFg1a.columns.CNr8EpqHcC.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.pB7BSlhmKW.columns.awxQA8AtGn.answer}}"}, {"identifier": "DemoResults", "properties": {"columnIdentifier": "{{TestReport.WSAnWbMCKB.columns.fTDXL3TLRW.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.FfKlGILPWz.columns.MLaUbsxjF1.answer}}"}, {"identifier": "DemoLog", "properties": {"columnIdentifier": "{{TestReport.XcrkuM1qZx.columns.zCajNN5fdF.id}}", "operation": "setColumn"}, "type": "table", "value": "{{form.CLE7acMs4f.columns.Q1ESau3aVM.answer}}"}]}, "variant": "setVariables"}, "w8kMqtmk9IFFWlgD2JzBg": {"id": "w8kMqtmk9IFFWlgD2JzBg", "metadata": {"createdAt": "2025-04-17T04:48:39.013Z", "updatedAt": "2025-04-17T04:48:39.013Z"}, "name": "Select Client", "next": "VDTLUZ3aVuQmYrmfrgCGu", "properties": {"inputs": {"continueFlowIfNotFound": "false", "foundationConfigurationId": "D1axfoA5lvjOQ0uezN05G", "foundationVariableName": "Client", "key": "{{<PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}}, "triggers": {"SpCpUAaxPQGIdMPaBKgOa": {"id": "SpCpUAaxPQGIdMPaBKgOa", "metadata": {"createdAt": "2025-03-23T23:00:32.355Z", "updatedAt": "2025-03-23T23:00:32.355Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Publish Results", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "Xx3FDs6isPccDlvhgPT6C": {"description": "", "id": "Xx3FDs6isPccDlvhgPT6C", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T05:39:10.934Z", "updatedAt": "2025-04-14T03:33:35.746Z"}, "name": "Create form when foundation is created", "start": "GV7827azWAvFUdDRPwvu6", "startingVariables": [{"identifier": "foundation", "properties": {"required": true}, "type": "foundation.x00VHaApOAVpOa8VKFkBC"}], "steps": {"GV7827azWAvFUdDRPwvu6": {"id": "GV7827azWAvFUdDRPwvu6", "metadata": {"createdAt": "2025-03-05T05:39:24.046Z", "updatedAt": "2025-03-05T05:39:24.046Z"}, "name": "Create a form", "next": "", "properties": {"inputs": {"formConfigurationId": "eatFwcRAAByzHwV92fmEf", "formVariableName": "form__step_GV7827azWAvFUdDRPwvu6", "foundationId": "{{foundation.id}}"}, "typePrimaryIdentifier": "createForm"}, "variant": "action"}}, "triggers": {"RBu8TI1BwAn6bOiuIFYN8": {"id": "RBu8TI1BwAn6bOiuIFYN8", "metadata": {"createdAt": "2025-03-05T05:39:13.360Z", "updatedAt": "2025-03-05T05:39:13.360Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "DO NOT USE", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "ZjUi6K0WaHjU2aokT7bto": {"description": "", "id": "ZjUi6K0WaHjU2aokT7bto", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T05:43:25.752Z", "updatedAt": "2025-04-14T03:33:40.879Z"}, "name": "CONCAT - Single Values", "start": "a1vOZTuRYwwrHZwSmA4Fo", "steps": {"a1vOZTuRYwwrHZwSmA4Fo": {"id": "a1vOZTuRYwwrHZwSmA4Fo", "metadata": {"createdAt": "2025-03-05T05:43:47.814Z", "updatedAt": "2025-03-05T05:43:47.814Z"}, "name": "Set variable(s)", "next": "JzLz53tZVOa86BTQa2fUJ", "properties": {"variables": [{"identifier": "CONCAT", "type": "text", "value": "$CONCAT({{form.n96ZMA9IwA.answer}}, \" \",{{form.x39DSHJnGO.answer}})"}]}, "variant": "setVariables"}, "JzLz53tZVOa86BTQa2fUJ": {"id": "JzLz53tZVOa86BTQa2fUJ", "metadata": {"createdAt": "2025-03-05T05:44:20.961Z", "updatedAt": "2025-03-05T05:44:20.961Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{CONCAT}}", "formId": "{{form.id}}", "questionId": "{{form.t60PjuSTaB.id}}", "questionTypeWithOperation": "no", "rowId": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"BRwyj4GkS3WOhw8Z0wqFF": {"id": "BRwyj4GkS3WOhw8Z0wqFF", "metadata": {"createdAt": "2025-03-05T05:43:28.763Z", "updatedAt": "2025-03-05T05:43:28.763Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "CONCAT - SINGLE", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aAw1vtdZkjM3jsCaiE5Sa": {"description": "", "id": "aAw1vtdZkjM3jsCaiE5Sa", "labels": ["f4V7hhKY0WrW7BexzpwSQ"], "metadata": {"createdAt": "2025-04-23T00:47:09.734Z", "updatedAt": "2025-04-23T00:47:09.734Z"}, "name": "Test Results Evaluator v2", "start": "aoPB5IZTQdeDxebXODGKk", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "status": "active", "steps": {"aoPB5IZTQdeDxebXODGKk": {"id": "aoPB5IZTQdeDxebXODGKk", "metadata": {"createdAt": "2025-04-23T00:47:29.806Z", "updatedAt": "2025-04-23T00:47:29.806Z"}, "name": "Set variable(s)", "next": "f4SYAmq9AWw5MaMsIoT9H", "properties": {"variables": [{"identifier": "PassFail", "properties": {"listOperation": "setList"}, "type": "list", "value": "$map({{form.pB7BSlhmKW.columns.bf4Ma7DWGF.answer}}, function($v, $i) {\n  $IF($v={{form.pB7BSlhmKW.columns.awxQA8AtGn.answer}}[$i], \"PASS\", $IF($v=\"\", \"No Result\", \"FAIL\"))\n})"}]}, "variant": "setVariables"}, "f4SYAmq9AWw5MaMsIoT9H": {"id": "f4SYAmq9AWw5MaMsIoT9H", "metadata": {"createdAt": "2025-04-23T00:47:46.422Z", "updatedAt": "2025-04-23T00:47:46.422Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{PassFail}}", "columnId": "{{form.pB7BSlhmKW.columns.awtfyzxoLZ.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"kHPL4bME4CNbuHEQOKqGN": {"id": "kHPL4bME4CNbuHEQOKqGN", "metadata": {"createdAt": "2025-04-23T00:47:16.508Z", "updatedAt": "2025-04-23T00:47:16.508Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Evaluate Tests V2", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aZtJgCmAA7fSuXBgh4iZN": {"description": "", "id": "aZtJgCmAA7fSuXBgh4iZN", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T13:49:55.061Z", "updatedAt": "2025-04-14T03:34:41.935Z"}, "name": "ADD DAYS - Single Value", "start": "Z3aNcoe3J9SZfMTBnccpi", "steps": {"XsVTJm3KHbX5FmjnVlxN9": {"id": "XsVTJm3KHbX5FmjnVlxN9", "metadata": {"createdAt": "2025-03-05T13:50:39.769Z", "updatedAt": "2025-03-05T13:50:39.769Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{ADDDATE}}", "formId": "{{form.id}}", "questionId": "{{form.wmh80dsYYl.id}}", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "Z3aNcoe3J9SZfMTBnccpi": {"id": "Z3aNcoe3J9SZfMTBnccpi", "metadata": {"createdAt": "2025-03-05T13:52:56.675Z", "updatedAt": "2025-03-05T13:52:56.675Z"}, "name": "Set variable(s)", "next": "XsVTJm3KHbX5FmjnVlxN9", "properties": {"variables": [{"identifier": "ADDDATE", "type": "date", "value": "$ADDDAYS({{form.eXoUzbPlns.answer}},1)"}]}, "variant": "setVariables"}}, "triggers": {"TC42RmCpz3zYOanZaRBF9": {"id": "TC42RmCpz3zYOanZaRBF9", "metadata": {"createdAt": "2025-03-05T13:49:58.056Z", "updatedAt": "2025-03-05T13:49:58.056Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "ADD DAYS - SINGLE", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "dEYkVA0hFRoJizwo9bu5e": {"description": "", "id": "dEYkVA0hFRoJizwo9bu5e", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-03T07:08:44.408Z", "updatedAt": "2025-04-14T03:31:11.944Z"}, "name": "8. Remove alert on PIT", "start": "h5KjZfLNVPaaM3FS0HZ8g", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.SG14waZgbati8MISSWyVN"}], "steps": {"h5KjZfLNVPaaM3FS0HZ8g": {"id": "h5KjZfLNVPaaM3FS0HZ8g", "metadata": {"createdAt": "2025-03-20T07:17:06.813Z", "updatedAt": "2025-03-20T07:17:06.813Z"}, "name": "For each", "next": "", "properties": {"configuration": {"start": "pfLt1H97y0bAk1Omlck8Q", "steps": {"A60OQKlbGTvMKCl1I9a7p": {"id": "A60OQKlbGTvMKCl1I9a7p", "metadata": {"createdAt": "2025-03-20T09:49:08.005Z", "updatedAt": "2025-03-20T09:49:08.005Z"}, "name": "Add alert", "next": "", "properties": {"inputs": {"formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "groupIdentifier": "MissingMapping", "message": "Payroll item is not mapped. Go to client information to map payroll item.", "operation": "add", "questionId": "qO3xge5etd", "rowId": "{{Item._rowId}}", "variant": "blocker"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}, "SFQ6GruFeVBl2AECq5yha": {"id": "SFQ6GruFeVBl2AECq5yha", "metadata": {"createdAt": "2025-03-20T07:18:05.844Z", "updatedAt": "2025-03-20T07:18:05.844Z"}, "name": "Condition", "next": "a3IlQynh56aMdLkRTZXOc", "properties": {"branches": [{"condition": {"lhs": "{{StandardMapping}}", "operator": "isEmpty"}, "name": "If", "next": "A60OQKlbGTvMKCl1I9a7p"}]}, "variant": "condition"}, "a3IlQynh56aMdLkRTZXOc": {"id": "a3IlQynh56aMdLkRTZXOc", "metadata": {"createdAt": "2025-03-20T07:18:34.859Z", "updatedAt": "2025-03-20T07:18:34.859Z"}, "name": "Set form alert", "next": "", "properties": {"inputs": {"formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "groupIdentifier": "MissingMapping", "operation": "remove", "questionId": "qO3xge5etd", "rowId": "{{Item._rowId}}", "variant": "blocker"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}, "pfLt1H97y0bAk1Omlck8Q": {"id": "pfLt1H97y0bAk1Omlck8Q", "metadata": {"createdAt": "2025-03-20T07:17:40.712Z", "updatedAt": "2025-03-20T07:17:40.712Z"}, "name": "Set variable(s)", "next": "SFQ6GruFeVBl2AECq5yha", "properties": {"variables": [{"identifier": "StandardMapping", "type": "text", "value": "$INDEX({{form.qO3xge5etd.columns.DYnqlhI7UM.answer}},{{Item_index}})"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "false", "itemVariableName": "<PERSON><PERSON>", "list": "{{form.qO3xge5etd.answer}}", "resultVariableName": "output__step_h5KjZfLNVPaaM3FS0HZ8g", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"Gob2CdN5sbOoxhsc1I9an": {"id": "Gob2CdN5sbOoxhsc1I9an", "metadata": {"createdAt": "2025-03-03T07:08:51.697Z", "updatedAt": "2025-03-03T07:08:51.697Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Refresh alerts", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "e6Q09UzxR1taN88FG1Nok": {"description": "", "id": "e6Q09UzxR1taN88FG1Nok", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-20T07:31:55.059Z", "updatedAt": "2025-04-14T03:32:28.288Z"}, "name": "9. <PERSON>mmarise Comp Data (Table)", "start": "aH9CmaGCttv6Y7vuBoEzX", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.SG14waZgbati8MISSWyVN"}], "steps": {"aH9CmaGCttv6Y7vuBoEzX": {"id": "aH9CmaGCttv6Y7vuBoEzX", "metadata": {"createdAt": "2025-03-20T10:28:44.303Z", "updatedAt": "2025-03-20T10:28:44.303Z"}, "name": "Set variable(s)", "next": "aHFYCGOXcJ6tK08zByaav", "properties": {"variables": [{"identifier": "TotalBaseSalary", "type": "number", "value": "$SUMIF({{form.qO3xge5etd.columns.DYnqlhI7UM.answer}},\"Salary\", {{form.qO3xge5etd.columns.aKi6gOawF3.answer}})"}, {"identifier": "TotalBonus", "type": "number", "value": "$SUMIF({{form.qO3xge5etd.columns.DYnqlhI7UM.answer}},\"Bonus\", {{form.qO3xge5etd.columns.aKi6gOawF3.answer}})"}, {"identifier": "TotalDirectorFees", "type": "number", "value": "$SUMIF({{form.qO3xge5etd.columns.DYnqlhI7UM.answer}},\"Director's Fees\", {{form.qO3xge5etd.columns.aKi6gOawF3.answer}})"}, {"identifier": "TotalAllowances", "type": "number", "value": "$SUMIF({{form.qO3xge5etd.columns.DYnqlhI7UM.answer}},\"Allowance\", {{form.qO3xge5etd.columns.aKi6gOawF3.answer}})"}, {"identifier": "TotalIncome", "type": "number", "value": "{{TotalBaseSalary}}+{{TotalBonus}}+{{TotalDirectorFees}}+{{TotalAllowances}}"}]}, "variant": "setVariables"}, "aeIea8DWqbrHEG3nhKcqI": {"id": "aeIea8DWqbrHEG3nhKcqI", "metadata": {"createdAt": "2025-03-20T07:32:05.319Z", "updatedAt": "2025-03-20T07:32:05.319Z"}, "name": "Initiate comp summary table", "next": "yOVIceWkGIVHUHsl6uhur", "properties": {"variables": [{"identifier": "CompSummary", "properties": {"operation": "setTable"}, "type": "table", "value": "{{form.hAJuHuyjwx.answer}}"}]}, "variant": "setVariables"}, "aYCdL0rdYiahlOhK2sOEM": {"id": "aYCdL0rdYiahlOhK2sOEM", "metadata": {"createdAt": "2025-03-20T07:33:29.851Z", "updatedAt": "2025-03-20T07:33:29.851Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{Comp<PERSON>ummary}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setTable", "questionId": "hAJuHuyjwx", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "aHFYCGOXcJ6tK08zByaav": {"id": "aHFYCGOXcJ6tK08zByaav", "metadata": {"createdAt": "2025-03-20T10:30:12.016Z", "updatedAt": "2025-03-20T10:30:12.016Z"}, "name": "Set form answer", "next": "OcVxMpcYmwb3NZVVY6eSW", "properties": {"inputs": {"answer": "{{TotalBaseSalary}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "questionId": "A9PZ0cxvav", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "DpOHzk63kxb9w3balBE9t": {"id": "DpOHzk63kxb9w3balBE9t", "metadata": {"createdAt": "2025-03-20T10:32:18.327Z", "updatedAt": "2025-03-20T10:32:18.327Z"}, "name": "Set form answer", "next": "aeIea8DWqbrHEG3nhKcqI", "properties": {"inputs": {"answer": "{{TotalIncome}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "questionId": "NodtWWRePe", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "GwfCbzSJrPy80dBFKhrPq": {"id": "GwfCbzSJrPy80dBFKhrPq", "metadata": {"createdAt": "2025-03-20T07:40:22.900Z", "updatedAt": "2025-03-20T07:40:22.900Z"}, "name": "For each", "next": "aYCdL0rdYiahlOhK2sOEM", "properties": {"configuration": {"start": "iOSTvRxicBe12ao0jdtJ1", "steps": {"algTseuOCvaz4wbntWm0i": {"id": "algTseuOCvaz4wbntWm0i", "metadata": {"createdAt": "2025-03-20T08:03:20.496Z", "updatedAt": "2025-03-20T08:03:20.496Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "CompSummary", "properties": {"columnIdentifier": "{{form.hAJuHuyjwx.columns.acGhopiihu.id}}", "operation": "setRow", "rowIndex": "{{Item_index}}"}, "type": "table", "value": "{\n  {{form.hAJuHuyjwx.columns.wyv5AiSveD.id}}:{{CompItem}},\n  {{form.hAJuHuyjwx.columns.acGhopiihu.id}}:{{SGBonus}},\n  {{form.hAJuHuyjwx.columns.d4oMDINbmM.id}}:{{ForeignBonus}},\n  {{form.hAJuHuyjwx.columns.TWpGz9Vmmx.id}}:0,\n  {{form.hAJuHuyjwx.columns.jKpaBjVFaG.id}}:{{ERBonus}}\n}"}]}, "variant": "setVariables"}, "aealUQYrayxZaQpa3b7G3": {"id": "aealUQYrayxZaQpa3b7G3", "metadata": {"createdAt": "2025-03-20T08:04:05.069Z", "updatedAt": "2025-03-20T08:04:05.069Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "CompSummary", "properties": {"columnIdentifier": "{{form.hAJuHuyjwx.columns.acGhopiihu.id}}", "operation": "setRow", "rowIndex": "{{Item_index}}"}, "type": "table", "value": "{\n  {{form.hAJuHuyjwx.columns.wyv5AiSveD.id}}:{{CompItem}},\n  {{form.hAJuHuyjwx.columns.acGhopiihu.id}}:{{SGDirectorFees}},\n  {{form.hAJuHuyjwx.columns.d4oMDINbmM.id}}:{{ForeignDirectorFees}},\n  {{form.hAJuHuyjwx.columns.TWpGz9Vmmx.id}}:0,\n  {{form.hAJuHuyjwx.columns.jKpaBjVFaG.id}}:{{ERDirectorFees}}\n}"}]}, "variant": "setVariables"}, "aoZDDELzwSC7WCnD7EF7T": {"id": "aoZDDELzwSC7WCnD7EF7T", "metadata": {"createdAt": "2025-03-20T08:38:09.206Z", "updatedAt": "2025-03-20T08:38:09.206Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "CompSummary", "properties": {"operation": "setRow", "rowIndex": "{{Item_index}}"}, "type": "table", "value": "{\n  {{form.hAJuHuyjwx.columns.wyv5AiSveD.id}}:{{CompItem}},\n  {{form.hAJuHuyjwx.columns.acGhopiihu.id}}:{{SGBaseSalary}},\n  {{form.hAJuHuyjwx.columns.d4oMDINbmM.id}}:{{ForeignBaseSalary}},\n  {{form.hAJuHuyjwx.columns.TWpGz9Vmmx.id}}:0,\n  {{form.hAJuHuyjwx.columns.jKpaBjVFaG.id}}:{{ERSalary}}\n}"}]}, "variant": "setVariables"}, "JMaGH2P84cxPFthyTz3ui": {"id": "JMaGH2P84cxPFthyTz3ui", "metadata": {"createdAt": "2025-03-20T08:04:32.057Z", "updatedAt": "2025-03-20T08:04:32.057Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "CompSummary", "properties": {"columnIdentifier": "{{form.hAJuHuyjwx.columns.acGhopiihu.id}}", "operation": "setRow", "rowIndex": "{{Item_index}}"}, "type": "table", "value": "{\n  {{form.hAJuHuyjwx.columns.wyv5AiSveD.id}}:{{CompItem}},\n  {{form.hAJuHuyjwx.columns.acGhopiihu.id}}:{{SGAllowances}},\n  {{form.hAJuHuyjwx.columns.d4oMDINbmM.id}}:{{ForeignAllowances}},\n  {{form.hAJuHuyjwx.columns.TWpGz9Vmmx.id}}:0,\n  {{form.hAJuHuyjwx.columns.jKpaBjVFaG.id}}:{{ERAllowances}}\n}"}]}, "variant": "setVariables"}, "M21LgtO2c3MoQrVlOgIva": {"id": "M21LgtO2c3MoQrVlOgIva", "metadata": {"createdAt": "2025-03-20T08:01:12.848Z", "updatedAt": "2025-03-20T08:01:12.848Z"}, "name": "Set variable(s)", "next": "RyF7JxgrHHqjtL60QtHPp", "properties": {"variables": [{"identifier": "SGBaseSalary", "type": "number", "value": "$SUMIF({{form.hDaERkazBA.columns.iOLWzdaCOj.answer}},\"Salary\", {{form.hDaERkazBA.columns.nKTz3jqg7E.answer}})"}, {"identifier": "SGBonus", "type": "number", "value": "$SUMIF({{form.hDaERkazBA.columns.iOLWzdaCOj.answer}},\"Bonus\",{{form.hDaERkazBA.columns.nKTz3jqg7E.answer}})"}, {"identifier": "SGDirectorFees", "type": "number", "value": "$SUMIF({{form.hDaERkazBA.columns.iOLWzdaCOj.answer}},\"Director's Fees\",{{form.hDaERkazBA.columns.nKTz3jqg7E.answer}})"}, {"identifier": "SGAllowances", "type": "number", "value": "$SUMIF({{form.hDaERkazBA.columns.iOLWzdaCOj.answer}},\"Allowance\",{{form.hDaERkazBA.columns.nKTz3jqg7E.answer}})"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "value": "{{SGBaseSalary}}+{{SGBonus}}+{{SGDirectorFees}}+{{SGAllowances}}"}, {"identifier": "ForeignBaseSalary", "type": "number", "value": "$SUMIF({{form.hDaERkazBA.columns.iOLWzdaCOj.answer}},\"Salary\", {{form.hDaERkazBA.columns.WAvMl6VKwl.answer}})"}, {"identifier": "ForeignBonus", "type": "number", "value": "$SUMIF({{form.hDaERkazBA.columns.iOLWzdaCOj.answer}},\"Bonus\",{{form.hDaERkazBA.columns.WAvMl6VKwl.answer}})"}, {"identifier": "ForeignDirectorFees", "type": "number", "value": "$SUMIF({{form.hDaERkazBA.columns.iOLWzdaCOj.answer}},\"Director's Fees\",{{form.hDaERkazBA.columns.WAvMl6VKwl.answer}})"}, {"identifier": "ForeignAllowances", "type": "number", "value": "$SUMIF({{form.hDaERkazBA.columns.iOLWzdaCOj.answer}},\"Allowance\",{{form.hDaERkazBA.columns.WAvMl6VKwl.answer}})"}, {"identifier": "ForeignIncome", "type": "number", "value": "{{ForeignBaseSalary}}+{{ForeignBonus}}+{{ForeignDirectorFees}}+{{ForeignAllowances}}"}]}, "variant": "setVariables"}, "RyF7JxgrHHqjtL60QtHPp": {"id": "RyF7JxgrHHqjtL60QtHPp", "metadata": {"createdAt": "2025-03-20T08:24:30.954Z", "updatedAt": "2025-03-20T08:24:30.954Z"}, "name": "Set variable(s)", "next": "fNNcG9QSwjBEwvue8QjbX", "properties": {"variables": [{"identifier": "ERSalary", "type": "number", "value": "$ROUNDUP($SUM({{ForeignBaseSalary}},{{SGBaseSalary}}),0)"}, {"identifier": "ERBonus", "type": "number", "value": "$ROUNDUP($SUM({{SGBonus}},{{ForeignBonus}}),0)"}, {"identifier": "ERDirectorFees", "type": "number", "value": "$ROUNDUP($SUM({{SGDirectorFees}},{{ForeignDirectorFees}}),0)"}, {"identifier": "ERAllowances", "type": "number", "value": "$ROUNDUP($SUM({{SGAllowances}},{{ForeignAllowances}}),0)"}, {"identifier": "ERIncome", "type": "number", "value": "$ROUNDUP($SUM({{SGIncome}},{{ForeignIncome}}),0)"}]}, "variant": "setVariables"}, "fNNcG9QSwjBEwvue8QjbX": {"id": "fNNcG9QSwjBEwvue8QjbX", "metadata": {"createdAt": "2025-03-20T07:58:51.885Z", "updatedAt": "2025-03-20T07:58:51.885Z"}, "name": "Condition", "next": "", "properties": {"branches": [{"condition": {"lhs": "{{CompItem}}", "operator": "contains", "rhs": "\"Gross Salary\""}, "name": "Salary", "next": "aoZDDELzwSC7WCnD7EF7T"}, {"condition": {"lhs": "{{CompItem}}", "operator": "=", "rhs": "\"Bonus\""}, "name": "Bonus", "next": "algTseuOCvaz4wbntWm0i"}, {"condition": {"lhs": "{{CompItem}}", "operator": "contains", "rhs": "\"Director\""}, "name": "Director's Fees", "next": "aealUQYrayxZaQpa3b7G3"}, {"condition": {"lhs": "{{CompItem}}", "operator": "=", "rhs": "\"Allowances\""}, "name": "Allowances", "next": "JMaGH2P84cxPFthyTz3ui"}, {"condition": {"lhs": "{{CompItem}}", "operator": "=", "rhs": "\"Total Income\""}, "name": "Total Income", "next": "tdvpNSq30o5blchQWnYlM"}]}, "variant": "condition"}, "iOSTvRxicBe12ao0jdtJ1": {"id": "iOSTvRxicBe12ao0jdtJ1", "metadata": {"createdAt": "2025-03-20T07:41:23.783Z", "updatedAt": "2025-03-20T07:41:23.783Z"}, "name": "Set variable(s)", "next": "M21LgtO2c3MoQrVlOgIva", "properties": {"variables": [{"identifier": "CompItem", "type": "text", "value": "{{Item.wyv5AiSveD}}"}]}, "variant": "setVariables"}, "tdvpNSq30o5blchQWnYlM": {"id": "tdvpNSq30o5blchQWnYlM", "metadata": {"createdAt": "2025-03-20T08:05:06.792Z", "updatedAt": "2025-03-20T08:05:06.792Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "CompSummary", "properties": {"columnIdentifier": "{{form.hAJuHuyjwx.columns.acGhopiihu.id}}", "operation": "setRow", "rowIndex": "{{Item_index}}"}, "type": "table", "value": "{\n  {{form.hAJuHuyjwx.columns.wyv5AiSveD.id}}:{{CompItem}},\n  {{form.hAJuHuyjwx.columns.acGhopiihu.id}}:{{SGIncome}},\n  {{form.hAJuHuyjwx.columns.d4oMDINbmM.id}}:{{ForeignIncome}},\n  {{form.hAJuHuyjwx.columns.TWpGz9Vmmx.id}}:0,\n  {{form.hAJuHuyjwx.columns.jKpaBjVFaG.id}}:{{ERIncome}}\n}"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "false", "itemVariableName": "<PERSON><PERSON>", "list": "{{Comp<PERSON>ummary}}", "resultVariableName": "output__step_GwfCbzSJrPy80dBFKhrPq", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}, "OcVxMpcYmwb3NZVVY6eSW": {"id": "OcVxMpcYmwb3NZVVY6eSW", "metadata": {"createdAt": "2025-03-20T10:31:00.968Z", "updatedAt": "2025-03-20T10:31:00.968Z"}, "name": "Set form answer", "next": "dSjEHLVfUMoaEZSxJcSpy", "properties": {"inputs": {"answer": "{{TotalBonus}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "questionId": "NgX8CIqfBq", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "dSjEHLVfUMoaEZSxJcSpy": {"id": "dSjEHLVfUMoaEZSxJcSpy", "metadata": {"createdAt": "2025-03-20T10:31:22.067Z", "updatedAt": "2025-03-20T10:31:22.067Z"}, "name": "Set form answer", "next": "rqQYG1F4X5SLav8KdyFw0", "properties": {"inputs": {"answer": "{{TotalDirectorFees}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "questionId": "a3RbMg5asL", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "rqQYG1F4X5SLav8KdyFw0": {"id": "rqQYG1F4X5SLav8KdyFw0", "metadata": {"createdAt": "2025-03-20T10:31:56.648Z", "updatedAt": "2025-03-20T10:31:56.648Z"}, "name": "Set form answer", "next": "DpOHzk63kxb9w3balBE9t", "properties": {"inputs": {"answer": "{{TotalAllowances}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "questionId": "z1BbXlIjFh", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "yOVIceWkGIVHUHsl6uhur": {"id": "yOVIceWkGIVHUHsl6uhur", "metadata": {"createdAt": "2025-03-20T07:33:06.086Z", "updatedAt": "2025-03-20T07:33:06.086Z"}, "name": "Set variable(s)", "next": "GwfCbzSJrPy80dBFKhrPq", "properties": {"variables": [{"identifier": "CompSummary", "properties": {"columnIdentifier": "{{form.hAJuHuyjwx.columns.wyv5AiSveD.id}}", "operation": "setColumn"}, "type": "table", "value": "[\"Gross Salary, Fees, Leave Pay, Wages and Overtime Pay\",\"Bonus\",\"Director’s fees\",\"Allowances\",\"Total Income\"]"}]}, "variant": "setVariables"}}, "triggers": {"xLOIWbwjxS0BAQBQd0f35": {"id": "xLOIWbwjxS0BAQBQd0f35", "metadata": {"createdAt": "2025-03-20T07:31:57.401Z", "updatedAt": "2025-03-20T07:31:57.401Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Summarise Comp Table", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "edG3ZL0GtqLBDn6wSMt9D": {"description": "", "id": "edG3ZL0GtqLBDn6wSMt9D", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-06T00:40:50.029Z", "updatedAt": "2025-04-14T03:31:46.234Z"}, "name": "4. Update assignment information in client form when assignee profile changes", "start": "agzcz0u5dGamZRMgqfZzl", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.V0UCzZ49ccFOYy39zOPOh"}], "steps": {"agzcz0u5dGamZRMgqfZzl": {"id": "agzcz0u5dGamZRMgqfZzl", "metadata": {"createdAt": "2025-03-11T06:09:45.108Z", "updatedAt": "2025-03-11T06:09:45.108Z"}, "name": "Get client group form", "next": "ZLQaRwQyW8HJJPxIaiLwa", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formVariableName": "ClientGroupForm", "foundationId": "{{form.foundation.parentId}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "ZLQaRwQyW8HJJPxIaiLwa": {"id": "ZLQaRwQyW8HJJPxIaiLwa", "metadata": {"createdAt": "2025-03-11T07:04:44.055Z", "updatedAt": "2025-03-11T07:04:44.055Z"}, "name": "Update client group form", "next": "", "properties": {"configuration": {"start": "h0VNBcoE7oaDewdwhfCek", "steps": {"Fx730YnMWQfbW7j6RcV8r": {"id": "Fx730YnMWQfbW7j6RcV8r", "metadata": {"createdAt": "2025-03-12T01:14:36.885Z", "updatedAt": "2025-03-12T01:14:36.885Z"}, "name": "Set variable(s)", "next": "FxtcnaYavFHzGmp2TOfWG", "properties": {"variables": [{"identifier": "IndexOfAssignee", "type": "text", "value": "$MATCH({{form.foundation.key}},{{ClientGroupForm.aqAohrlqQ2.columns.NygKq36atr.answer}},0)"}, {"identifier": "AssigneeInfo", "type": "json", "value": "{ {{ClientGroupForm.aqAohrlqQ2.columns.NygKq36atr.id}}: {{form.foundation.key}},{{ClientGroupForm.aqAohrlqQ2.columns.exI36T75Gq.id}}: {{form.M7aZ2r5d5O.answer}},{{ClientGroupForm.aqAohrlqQ2.columns.aiCtHRlS00.id}}:{{form.jc2EKPHZeO.answer}},{{ClientGroupForm.aqAohrlqQ2.columns.oR3hExeYiL.id}}:{{HomeCountry}},{{ClientGroupForm.aqAohrlqQ2.columns.VmlDFbLJKZ.id}}:{{HostCountry}},{{ClientGroupForm.aqAohrlqQ2.columns.WSHCcFlExG.id}}:{{AssignmentStart}},{{ClientGroupForm.aqAohrlqQ2.columns.waeNppHU1u.id}}:{{AssignmentEnd}},{{ClientGroupForm.aqAohrlqQ2.columns.avKORamTMA.id}}:{{AssignmentPolicy}}}"}]}, "variant": "setVariables"}, "FxtcnaYavFHzGmp2TOfWG": {"id": "FxtcnaYavFHzGmp2TOfWG", "metadata": {"createdAt": "2025-03-12T01:15:13.868Z", "updatedAt": "2025-03-12T01:15:13.868Z"}, "name": "Update assignee information", "next": "", "properties": {"inputs": {"answer": "{{AssigneeInfo}}", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formId": "{{ClientGroupForm.id}}", "operation": "setRow", "questionId": "aqAohrlqQ2", "questionTypeWithOperation": "table", "rowIndex": "{{<PERSON><PERSON><PERSON><PERSON>signee}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "KekaI2CJS53wJ2Lxmna9X": {"id": "KekaI2CJS53wJ2Lxmna9X", "metadata": {"createdAt": "2025-03-12T02:07:42.580Z", "updatedAt": "2025-03-12T02:07:42.580Z"}, "name": "Add new assignee information", "next": "", "properties": {"inputs": {"answer": "{{<PERSON><PERSON><PERSON><PERSON>}}", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formId": "{{ClientGroupForm.id}}", "operation": "setRow", "questionId": "aqAohrlqQ2", "questionTypeWithOperation": "table", "rowSelection": "rowId"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "h0VNBcoE7oaDewdwhfCek": {"id": "h0VNBcoE7oaDewdwhfCek", "metadata": {"createdAt": "2025-03-12T01:03:23.618Z", "updatedAt": "2025-03-12T01:03:23.618Z"}, "name": "Set variable(s)", "next": "osaApj6oaIeUF1jPU4DBf", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "number", "value": "$COUNTIF({{ClientGroupForm.aqAohrlqQ2.columns.NygKq36atr.answer}},{{form.foundation.key}})"}, {"identifier": "HomeCountry", "type": "text", "value": "$INDEX({{form.T7IoTXkA0l.columns.MGJXlvwkBj.answer}},{{AssignmentDetails_index}})"}, {"identifier": "HostCountry", "type": "text", "value": "$INDEX({{form.T7IoTXkA0l.columns.apRPubvsrG.answer}},{{AssignmentDetails_index}})"}, {"identifier": "AssignmentStart", "type": "text", "value": "$INDEX({{form.T7IoTXkA0l.columns.av7k23SmGg.answer}},{{AssignmentDetails_index}})"}, {"identifier": "AssignmentEnd", "type": "text", "value": "$INDEX({{form.T7IoTXkA0l.columns.VuaSF5PWY9.answer}},{{AssignmentDetails_index}})"}, {"identifier": "AssignmentPolicy", "type": "text", "value": "$INDEX({{form.T7IoTXkA0l.columns.aXgaVheZhu.answer}},{{AssignmentDetails_index}})"}]}, "variant": "setVariables"}, "osaApj6oaIeUF1jPU4DBf": {"id": "osaApj6oaIeUF1jPU4DBf", "metadata": {"createdAt": "2025-03-12T01:13:34.037Z", "updatedAt": "2025-03-12T01:13:34.037Z"}, "name": "Assignee exists in client form?", "next": "zLg0bCL3DrarKeKUx2LC4", "properties": {"branches": [{"condition": {"lhs": "{{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "operator": ">", "rhs": "0"}, "name": "If", "next": "Fx730YnMWQfbW7j6RcV8r"}]}, "variant": "condition"}, "zLg0bCL3DrarKeKUx2LC4": {"id": "zLg0bCL3DrarKeKUx2LC4", "metadata": {"createdAt": "2025-03-12T02:07:06.404Z", "updatedAt": "2025-03-12T02:07:06.404Z"}, "name": "Set variable(s)", "next": "KekaI2CJS53wJ2Lxmna9X", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON>", "type": "json", "value": "{ {{ClientGroupForm.aqAohrlqQ2.columns.NygKq36atr.id}}: {{form.foundation.key}},{{ClientGroupForm.aqAohrlqQ2.columns.exI36T75Gq.id}}: {{form.M7aZ2r5d5O.answer}},{{ClientGroupForm.aqAohrlqQ2.columns.aiCtHRlS00.id}}:{{form.jc2EKPHZeO.answer}},{{ClientGroupForm.aqAohrlqQ2.columns.oR3hExeYiL.id}}:{{HomeCountry}},{{ClientGroupForm.aqAohrlqQ2.columns.VmlDFbLJKZ.id}}:{{HostCountry}},{{ClientGroupForm.aqAohrlqQ2.columns.WSHCcFlExG.id}}:{{AssignmentStart}},{{ClientGroupForm.aqAohrlqQ2.columns.waeNppHU1u.id}}:{{AssignmentEnd}},{{ClientGroupForm.aqAohrlqQ2.columns.avKORamTMA.id}}:{{AssignmentPolicy}}}"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "false", "itemVariableName": "AssignmentDetails", "list": "{{form.T7IoTXkA0l.columns.MGJXlvwkBj.answer}}", "resultVariableName": "filtered__step_ZLQaRwQyW8HJJPxIaiLwa", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"bqVOVgbfwvUtboOSlf0Vg": {"id": "bqVOVgbfwvUtboOSlf0Vg", "metadata": {"createdAt": "2025-03-06T00:40:56.027Z", "updatedAt": "2025-03-06T00:40:56.027Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Placeholder", "formConfigurationId": "V0UCzZ49ccFOYy39zOPOh", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "fNJ3ONFMx41eY8j4JK1RT": {"description": "", "endingVariables": [{"identifier": "AuthorisationList", "type": "form"}], "id": "fNJ3ONFMx41eY8j4JK1RT", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-10T23:22:14.972Z", "updatedAt": "2025-04-14T03:31:52.685Z"}, "name": "2. Create authorisation list when client is created", "start": "ffXSAdXOw3xqKE0PsWa16", "startingVariables": [{"identifier": "foundation", "properties": {"required": true}, "type": "foundation.D1axfoA5lvjOQ0uezN05G"}], "steps": {"OGj9iQi6Ql8fxLGWOq5LS": {"id": "OGj9iQi6Ql8fxLGWOq5LS", "metadata": {"createdAt": "2025-03-12T11:58:12.732Z", "updatedAt": "2025-03-12T11:58:12.732Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{Year}}", "formConfigurationId": "U3mqHJmaz9kEdWsGISca6", "formId": "{{AuthorisationList.id}}", "questionId": "a2Kh0a6jFL", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "UL2Ydr3v5W5fhD4JvSJUp": {"id": "UL2Ydr3v5W5fhD4JvSJUp", "metadata": {"createdAt": "2025-03-12T11:57:40.334Z", "updatedAt": "2025-03-12T11:57:40.334Z"}, "name": "Set variable(s)", "next": "OGj9iQi6Ql8fxLGWOq5LS", "properties": {"variables": [{"identifier": "Year", "type": "text", "value": "$YEAR($TODAY())"}]}, "variant": "setVariables"}, "ffXSAdXOw3xqKE0PsWa16": {"id": "ffXSAdXOw3xqKE0PsWa16", "metadata": {"createdAt": "2025-03-11T06:12:00.795Z", "updatedAt": "2025-03-11T06:12:00.795Z"}, "name": "Select or create Form", "next": "UL2Ydr3v5W5fhD4JvSJUp", "properties": {"inputs": {"formConfigurationId": "U3mqHJmaz9kEdWsGISca6", "formVariableName": "AuthorisationList", "foundationId": "{{foundation.id}}"}, "typePrimaryIdentifier": "selectOrCreateForm"}, "variant": "action"}}, "triggers": {"tJKxtvW3unScgOJccSQYh": {"id": "tJKxtvW3unScgOJccSQYh", "metadata": {"createdAt": "2025-03-10T23:22:19.826Z", "updatedAt": "2025-03-10T23:22:19.827Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "Placeholder", "foundationConfigurationId": "D1axfoA5lvjOQ0uezN05G", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "g4JFJsrJaOvm3cwx1OLzF": {"description": "", "id": "g4JFJsrJaOvm3cwx1OLzF", "labels": ["f4V7hhKY0WrW7BexzpwSQ"], "metadata": {"createdAt": "2025-03-18T13:54:43.559Z", "updatedAt": "2025-04-14T03:36:02.500Z"}, "name": "Test Runner", "start": "a6XCuQGkcHOy6DohRuhYT", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "steps": {"aQ4a7kJlJajo3y3yqE74K": {"id": "aQ4a7kJlJajo3y3yqE74K", "metadata": {"createdAt": "2025-05-01T01:09:17.505Z", "updatedAt": "2025-05-01T01:09:17.505Z"}, "name": "Generate a document", "next": "OS8ffv9bCQal37Z85sB02", "properties": {"inputs": {"flowConfigurationId": "aRXEsAN6USxUdyK71Z1HK", "form": "{{TestForm.id}}"}}, "variant": "flow"}, "apnzah7qxwRSaV5I0BNZE": {"id": "apnzah7qxwRSaV5I0BNZE", "metadata": {"createdAt": "2025-04-17T07:48:53.546Z", "updatedAt": "2025-04-17T07:48:53.546Z"}, "name": "COUNT - ARRAY", "next": "nPTitCUeaQpFnEZFELaa9", "properties": {"inputs": {"TestForm": "{{form.id}}", "flowConfigurationId": "xKs57oJMw2YIQiBS9vwaP"}}, "variant": "flow"}, "a6XCuQGkcHOy6DohRuhYT": {"id": "a6XCuQGkcHOy6DohRuhYT", "metadata": {"createdAt": "2025-05-01T01:10:23.598Z", "updatedAt": "2025-05-01T01:10:23.598Z"}, "name": "Set variable(s)", "next": "qbNScHmLaYjhPa51VE8VN", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"ASGN\",{{form.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "OS8ffv9bCQal37Z85sB02": {"id": "OS8ffv9bCQal37Z85sB02", "metadata": {"createdAt": "2025-05-01T01:40:24.955Z", "updatedAt": "2025-05-01T01:40:24.955Z"}, "name": "Set form answer", "next": "OyaUaNnaa5apJvaVaa9Te", "properties": {"inputs": {"answer": "{{generatedFileName}}", "columnId": "{{form.pB7BSlhmKW.columns.bf4Ma7DWGF.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "7", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "OyaUaNnaa5apJvaVaa9Te": {"id": "OyaUaNnaa5apJvaVaa9Te", "metadata": {"createdAt": "2025-04-18T00:03:34.492Z", "updatedAt": "2025-04-18T00:03:34.492Z"}, "name": "Test Results Evaluator v2", "next": "d4l8jqKx9sxaVEn7ywoQu", "properties": {"inputs": {"flowConfigurationId": "aAw1vtdZkjM3jsCaiE5Sa", "form": "{{form.id}}"}}, "variant": "flow"}, "PSPzQAPUKFg0u6ooAaLuN": {"id": "PSPzQAPUKFg0u6ooAaLuN", "metadata": {"createdAt": "2025-04-17T07:48:24.520Z", "updatedAt": "2025-04-17T07:48:24.520Z"}, "name": "MULTIPLY - Table Values", "next": "oZR6V6ZOMSeSoiqVsG6Ta", "properties": {"inputs": {"flowConfigurationId": "aavRghx8kM7VyHEdqLj9t", "form": "{{form.id}}"}}, "variant": "flow"}, "TAByO8QP0QFS2xHXQIjeb": {"id": "TAByO8QP0QFS2xHXQIjeb", "metadata": {"createdAt": "2025-04-17T07:49:24.732Z", "updatedAt": "2025-04-17T07:49:24.732Z"}, "name": "SUMIF - Single Value", "next": "aQ4a7kJlJajo3y3yqE74K", "properties": {"inputs": {"TestForm": "{{form.id}}", "flowConfigurationId": "ta9tAaMvSBUla13vobuil"}}, "variant": "flow"}, "d4l8jqKx9sxaVEn7ywoQu": {"id": "d4l8jqKx9sxaVEn7ywoQu", "metadata": {"createdAt": "2025-04-18T00:03:53.690Z", "updatedAt": "2025-04-18T00:03:53.690Z"}, "name": "Publish Results", "next": "", "properties": {"inputs": {"flowConfigurationId": "QjExeOqabLLerYaNtLSQE", "form": "{{form.id}}"}}, "variant": "flow"}, "nPTitCUeaQpFnEZFELaa9": {"id": "nPTitCUeaQpFnEZFELaa9", "metadata": {"createdAt": "2025-04-17T07:49:12.168Z", "updatedAt": "2025-04-17T07:49:12.168Z"}, "name": "Set answer from prior year", "next": "TAByO8QP0QFS2xHXQIjeb", "properties": {"inputs": {"TestForm": "{{form.id}}", "flowConfigurationId": "HciG4QaRykhNJq7pg1Y4D"}}, "variant": "flow"}, "oZR6V6ZOMSeSoiqVsG6Ta": {"id": "oZR6V6ZOMSeSoiqVsG6Ta", "metadata": {"createdAt": "2025-04-17T07:48:41.871Z", "updatedAt": "2025-04-17T07:48:41.871Z"}, "name": "MULTIPLY - Iterator Table Values", "next": "apnzah7qxwRSaV5I0BNZE", "properties": {"inputs": {"TestForm": "{{form.id}}", "flowConfigurationId": "ldDyEsOALrBjlHDR7YyXr"}}, "variant": "flow"}, "otfIhRLbh7IatqYKZT9aK": {"id": "otfIhRLbh7IatqYKZT9aK", "metadata": {"createdAt": "2025-05-01T01:10:13.724Z", "updatedAt": "2025-05-01T01:10:13.724Z"}, "name": "Select or create a form", "next": "tzHKmaLlHe3nAAy498FXi", "properties": {"inputs": {"formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "TestForm", "foundationId": "{{Assignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectOrCreateForm"}, "variant": "action"}, "qbNScHmLaYjhPa51VE8VN": {"id": "qbNScHmLaYjhPa51VE8VN", "metadata": {"createdAt": "2025-05-01T01:10:06.393Z", "updatedAt": "2025-05-01T01:10:06.393Z"}, "name": "Select a foundation", "next": "otfIhRLbh7IatqYKZT9aK", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "tzHKmaLlHe3nAAy498FXi": {"id": "tzHKmaLlHe3nAAy498FXi", "metadata": {"createdAt": "2025-04-17T07:48:05.688Z", "updatedAt": "2025-04-17T07:48:05.688Z"}, "name": "MULTIPLY - Single Values", "next": "PSPzQAPUKFg0u6ooAaLuN", "properties": {"inputs": {"flowConfigurationId": "oTZe8Wnnm0xGkfP51amMF", "form": "{{form.id}}"}}, "variant": "flow"}}, "triggers": {"d8aa8O7M91tvsidoAtFPQ": {"id": "d8aa8O7M91tvsidoAtFPQ", "metadata": {"createdAt": "2025-03-18T13:54:46.020Z", "updatedAt": "2025-03-18T13:54:46.020Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Start Testing", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "g74c8tbdIBKQXnCW32FVE": {"description": "", "id": "g74c8tbdIBKQXnCW32FVE", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-20T06:23:19.352Z", "updatedAt": "2025-04-14T03:32:23.502Z"}, "name": "13. Calculated foreign and local sourced amounts", "start": "zi2dshPLMjwpjzMS91tf1", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.SG14waZgbati8MISSWyVN"}], "steps": {"zi2dshPLMjwpjzMS91tf1": {"id": "zi2dshPLMjwpjzMS91tf1", "metadata": {"createdAt": "2025-03-20T06:24:42.196Z", "updatedAt": "2025-03-20T06:24:42.196Z"}, "name": "For each", "next": "", "properties": {"configuration": {"start": "jaTmxORLPOWGT3QCDY9pq", "steps": {"atfPXTTcY8ksUMksMwvUo": {"id": "atfPXTTcY8ksUMksMwvUo", "metadata": {"createdAt": "2025-03-20T06:28:31.878Z", "updatedAt": "2025-03-20T06:28:31.878Z"}, "name": "Set form answer", "next": "apacCW7OsilaFyaVHVXEN", "properties": {"inputs": {"answer": "{{SGA<PERSON>}}", "columnId": "{{form.hDaERkazBA.columns.nKTz3jqg7E.id}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setCell", "questionId": "hDaERkazBA", "questionTypeWithOperation": "table", "rowIndex": "{{PayrollItem_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "apacCW7OsilaFyaVHVXEN": {"id": "apacCW7OsilaFyaVHVXEN", "metadata": {"createdAt": "2025-03-20T06:29:13.528Z", "updatedAt": "2025-03-20T06:29:13.528Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{ForeignAmount}}", "columnId": "{{form.hDaERkazBA.columns.WAvMl6VKwl.id}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setCell", "questionId": "hDaERkazBA", "questionTypeWithOperation": "table", "rowIndex": "{{PayrollItem_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "jaTmxORLPOWGT3QCDY9pq": {"id": "jaTmxORLPOWGT3QCDY9pq", "metadata": {"createdAt": "2025-03-20T06:25:03.872Z", "updatedAt": "2025-03-20T06:25:03.872Z"}, "name": "Set variable(s)", "next": "atfPXTTcY8ksUMksMwvUo", "properties": {"variables": [{"identifier": "DaysInSG", "type": "number", "value": "$INDEX({{form.hDaERkazBA.columns.pGsLVC62OH.answer}},{{PayrollItem_index}})"}, {"identifier": "TotalDays", "type": "number", "value": "$INDEX({{form.hDaERkazBA.columns.K8J5lh76BL.answer}},{{PayrollItem_index}})"}, {"identifier": "SGPercentage", "type": "number", "value": "{{DaysInSG}}/{{TotalDays}}"}, {"identifier": "TotalAmount", "type": "number", "value": "$INDEX({{form.hDaERkazBA.columns.OOK8GIkJVg.answer}},{{PayrollItem_index}})"}, {"identifier": "SGAmount", "type": "number", "value": "$ROUND($MULTIPLY({{TotalAmount}},{{SGPercentage}}),2)"}, {"identifier": "ForeignAmount", "type": "number", "value": "$ROUND({{TotalAmount}}-{{SGAmount}},2)"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "false", "itemVariableName": "PayrollItem", "list": "{{form.hDaERkazBA.answer}}", "resultVariableName": "output__step_zi2dshPLMjwpjzMS91tf1", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"mYulOD2jlRvmt8NqtkCrH": {"id": "mYulOD2jlRvmt8NqtkCrH", "metadata": {"createdAt": "2025-03-20T06:23:53.534Z", "updatedAt": "2025-03-20T06:23:53.534Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Placeholder", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "kxW7iB4mGka2ICJaVLgKe": {"description": "", "id": "kxW7iB4mGka2ICJaVLgKe", "labels": ["f4V7hhKY0WrW7BexzpwSQ"], "metadata": {"createdAt": "2025-03-18T13:59:41.523Z", "updatedAt": "2025-04-14T03:36:10.285Z"}, "name": "Setup Test Runner Form", "start": "IbuvAA94yoqynwTp2Nr1W", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "steps": {"aSZ8Fw5tXveKcvrpoW3V9": {"id": "aSZ8Fw5tXveKcvrpoW3V9", "metadata": {"createdAt": "2025-03-18T14:23:26.439Z", "updatedAt": "2025-03-18T14:23:26.439Z"}, "name": "Set form answer", "next": "aY4WgFTosiEVjy0Gb8BLx", "properties": {"inputs": {"answer": "Complete", "columnId": "{{form.CenareaD0c.columns.yalXVBoM49.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CenareaD0c", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "1", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "aY4WgFTosiEVjy0Gb8BLx": {"id": "aY4WgFTosiEVjy0Gb8BLx", "metadata": {"createdAt": "2025-03-18T14:24:20.936Z", "updatedAt": "2025-03-18T14:24:20.936Z"}, "name": "Create a form", "next": "BB4psmQJ7PSfTvpYpJJp0", "properties": {"inputs": {"formConfigurationId": "eatFwcRAAByzHwV92fmEf", "formVariableName": "ClientGroupForm", "foundationId": "{{ClientGroup.id}}"}, "typePrimaryIdentifier": "createForm"}, "variant": "action"}, "aVPvEvo5hgE20HUZcw81C": {"id": "aVPvEvo5hgE20HUZcw81C", "metadata": {"createdAt": "2025-03-18T14:49:47.424Z", "updatedAt": "2025-03-18T14:49:47.424Z"}, "name": "Set form answer", "next": "all5hvGauoapWj0nVZoAg", "properties": {"inputs": {"answer": "{{SetUpFlows}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setTable", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "arMEaTZAbramjIKqC1EBG": {"id": "arMEaTZAbramjIKqC1EBG", "metadata": {"createdAt": "2025-03-18T14:03:08.995Z", "updatedAt": "2025-03-18T14:03:08.995Z"}, "name": "Set form answer", "next": "jb55vhPj0ssetwZ1U6kH1", "properties": {"inputs": {"answer": "{{NOW}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "questionId": "B1AYblG3LF", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "all5hvGauoapWj0nVZoAg": {"id": "all5hvGauoapWj0nVZoAg", "metadata": {"createdAt": "2025-03-24T01:02:43.237Z", "updatedAt": "2025-03-24T01:02:43.237Z"}, "name": "Create a foundation", "next": "WAiCB9kareBjmYaOp317g", "properties": {"inputs": {"foundationConfigurationId": "D1axfoA5lvjOQ0uezN05G", "foundationVariableName": "Client", "key": "{{<PERSON><PERSON><PERSON><PERSON>}}", "name": "{{<PERSON><PERSON><PERSON><PERSON>}}", "parentId": "{{form.foundation.id}}"}, "typePrimaryIdentifier": "createFoundation"}, "variant": "action"}, "AGMdPIEtdsir9EkTZfLae": {"id": "AGMdPIEtdsir9EkTZfLae", "metadata": {"createdAt": "2025-03-24T03:53:21.658Z", "updatedAt": "2025-03-24T03:53:21.658Z"}, "name": "Set variable(s)", "next": "DkgPgM20cLkO089lYLaHF", "properties": {"variables": [{"identifier": "SetUpFlows", "properties": {"columnIdentifier": "{{form.pB7BSlhmKW.columns.avyyvIN7EH.id}}", "operation": "setColumn"}, "type": "table", "value": "{{ResultScenarios}}"}]}, "variant": "setVariables"}, "BB4psmQJ7PSfTvpYpJJp0": {"id": "BB4psmQJ7PSfTvpYpJJp0", "metadata": {"createdAt": "2025-03-18T14:24:13.389Z", "updatedAt": "2025-03-18T14:24:13.389Z"}, "name": "Set form answer", "next": "YpnkzUfQrwAVZBJjhrWx1", "properties": {"inputs": {"answer": "Complete", "columnId": "{{form.CenareaD0c.columns.yalXVBoM49.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CenareaD0c", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "2", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "DkgPgM20cLkO089lYLaHF": {"id": "DkgPgM20cLkO089lYLaHF", "metadata": {"createdAt": "2025-03-24T03:54:57.658Z", "updatedAt": "2025-03-24T03:54:57.658Z"}, "name": "Set variable(s)", "next": "arMEaTZAbramjIKqC1EBG", "properties": {"variables": [{"identifier": "SetUpFlows", "properties": {"columnIdentifier": "{{form.pB7BSlhmKW.columns.awxQA8AtGn.id}}", "operation": "setColumn"}, "type": "table", "value": "{{ExpectedResults}}"}]}, "variant": "setVariables"}, "EuwTwwL4qKMRxYIaIsyuo": {"id": "EuwTwwL4qKMRxYIaIsyuo", "metadata": {"createdAt": "2025-04-10T04:09:51.606Z", "updatedAt": "2025-04-10T04:09:51.606Z"}, "name": "Set variable(s)", "next": "AGMdPIEtdsir9EkTZfLae", "properties": {"variables": [{"identifier": "SetupDemoScenarios", "properties": {"columnIdentifier": "{{form.FfKlGILPWz.columns.DWlkI9DVzT.id}}", "operation": "setColumn"}, "type": "table", "value": "{{DemoS<PERSON><PERSON><PERSON>}}"}]}, "variant": "setVariables"}, "FMSWzJjO0XojYu0jIQ1BA": {"id": "FMSWzJjO0XojYu0jIQ1BA", "metadata": {"createdAt": "2025-03-18T14:27:57.825Z", "updatedAt": "2025-03-18T14:27:57.825Z"}, "name": "Create a form", "next": "THNrtTpNocOOW848H8l4g", "properties": {"inputs": {"formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "CYForm", "foundationId": "{{Assignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "createForm"}, "variant": "action"}, "IbuvAA94yoqynwTp2Nr1W": {"id": "IbuvAA94yoqynwTp2Nr1W", "metadata": {"createdAt": "2025-03-18T13:59:54.763Z", "updatedAt": "2025-03-18T13:59:54.763Z"}, "name": "Set variable(s)", "next": "OQfbXS9R9LrYQ1PN2vpaP", "properties": {"variables": [{"identifier": "NOW", "type": "text", "value": "$NOW()"}, {"identifier": "SetupScenarios", "properties": {"operation": "setTable"}, "type": "table", "value": "[]"}, {"identifier": "ClientName", "type": "text", "value": "$CONCAT(\"CLI\", {{NOW}})"}, {"identifier": "ClientGroupName", "type": "text", "value": "$CONCAT(\"CG\",{{NOW}})"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"ASGN\",{{NOW}})"}, {"identifier": "SetUpFlows", "properties": {"operation": "setTable"}, "type": "table", "value": "[]"}, {"identifier": "ResultScenarios", "type": "json", "value": "[\"Multiply Single Values\", \"Multiply Table Values\", \"Multiply Iterator\", \"Count Array\", \"Get answer from PY\", \"SUMIF Single Values\", \"Generate a document\"]"}, {"identifier": "ExpectedResults", "type": "json", "value": "[\"200.0\",\"[1000,4000]\",\"[800,600]\", \"2\", \"This is an answer from PY\",\"100.0\",\"generatedDocument.docx\"]"}, {"identifier": "Scenarios", "type": "json", "value": "[\"Created Client Group\", \"Created Client Group Form\", \"Created Assignee\", \"Created Assignee Form - PY\", \"Created Assignee Form - CY\"]"}, {"identifier": "DemoScenarios", "type": "json", "value": "[\n  \"1. Create client group form\",\n  \"2. Create authorisation list when client is created\",\n  \"3. Create assignee profiles from client form\",\n  \"4. Update assignment information in client form when assignee profile changes\",\n  \"5. Update authorisation list when assignment information changes\",\n  \"6. Approve authorisation list and create tax return forms\",\n  \"7. Analyse Comp Data\",\n  \"8. Remove alert on PIT\",\n  \"9. Summarise Comp Data\",\n  \"10. Missing Bonus Warning\",\n  \"11. Perform Sourcing\",\n  \"12. Calculate days in SG during sourcing period\",\n  \"13. Calculated foreign and local sourced amounts\"\n]"}, {"identifier": "SetUpDemoScenarios", "properties": {"operation": "setTable"}, "type": "table", "value": "{{form.FfKlGILPWz.answer}}"}]}, "variant": "setVariables"}, "OQfbXS9R9LrYQ1PN2vpaP": {"id": "OQfbXS9R9LrYQ1PN2vpaP", "metadata": {"createdAt": "2025-03-24T04:02:28.232Z", "updatedAt": "2025-03-24T04:02:28.232Z"}, "name": "Set variable(s)", "next": "EuwTwwL4qKMRxYIaIsyuo", "properties": {"variables": [{"identifier": "SetupScenarios", "properties": {"columnIdentifier": "{{form.CenareaD0c.columns.DfIers83Wd.id}}", "operation": "setColumn"}, "type": "table", "value": "{{<PERSON><PERSON><PERSON><PERSON>}}"}]}, "variant": "setVariables"}, "THNrtTpNocOOW848H8l4g": {"id": "THNrtTpNocOOW848H8l4g", "metadata": {"createdAt": "2025-03-18T14:26:35.140Z", "updatedAt": "2025-03-18T14:26:35.140Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "Complete", "columnId": "{{form.CenareaD0c.columns.yalXVBoM49.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CenareaD0c", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "5", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "WAiCB9kareBjmYaOp317g": {"id": "WAiCB9kareBjmYaOp317g", "metadata": {"createdAt": "2025-03-24T02:37:47.867Z", "updatedAt": "2025-03-24T02:37:47.867Z"}, "name": "Create a form", "next": "fjjNpb5LenoJPB3Muaomr", "properties": {"inputs": {"formConfigurationId": "z7690ou099uWbug54HSBU", "formVariableName": "TestReport", "foundationId": "{{Client.id}}"}, "typePrimaryIdentifier": "createForm"}, "variant": "action"}, "XegROl52G3ae5kKkNF5vd": {"id": "XegROl52G3ae5kKkNF5vd", "metadata": {"createdAt": "2025-03-18T14:24:45.071Z", "updatedAt": "2025-03-18T14:24:45.071Z"}, "name": "Set form answer", "next": "vfTvIiXgHJORtrpJkW2ay", "properties": {"inputs": {"answer": "Complete", "columnId": "{{form.CenareaD0c.columns.yalXVBoM49.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CenareaD0c", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "3", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "YpnkzUfQrwAVZBJjhrWx1": {"id": "YpnkzUfQrwAVZBJjhrWx1", "metadata": {"createdAt": "2025-03-18T14:24:52.902Z", "updatedAt": "2025-03-18T14:24:52.902Z"}, "name": "Create a foundation", "next": "XegROl52G3ae5kKkNF5vd", "properties": {"inputs": {"foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "name": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "parentId": "{{ClientGroup.id}}"}, "typePrimaryIdentifier": "createFoundation"}, "variant": "action"}, "fjjNpb5LenoJPB3Muaomr": {"id": "fjjNpb5LenoJPB3Muaomr", "metadata": {"createdAt": "2025-03-18T14:21:40.235Z", "updatedAt": "2025-03-18T14:21:40.235Z"}, "name": "Create client group", "next": "aSZ8Fw5tXveKcvrpoW3V9", "properties": {"inputs": {"foundationConfigurationId": "daYJS2xq9fkLHcaY1YG7H", "foundationVariableName": "ClientGroup", "key": "{{ClientGroupName}}", "name": "{{ClientGroupName}}", "parentId": "{{Client.id}}"}, "typePrimaryIdentifier": "createFoundation"}, "variant": "action"}, "gvvMzJCxqBMF5WclBiXBM": {"id": "gvvMzJCxqBMF5WclBiXBM", "metadata": {"createdAt": "2025-03-18T14:26:33.056Z", "updatedAt": "2025-03-18T14:26:33.056Z"}, "name": "Set form answer", "next": "FMSWzJjO0XojYu0jIQ1BA", "properties": {"inputs": {"answer": "Complete", "columnId": "{{form.CenareaD0c.columns.yalXVBoM49.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setCell", "questionId": "CenareaD0c", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "4", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "jb55vhPj0ssetwZ1U6kH1": {"id": "jb55vhPj0ssetwZ1U6kH1", "metadata": {"createdAt": "2025-03-18T14:07:17.702Z", "updatedAt": "2025-03-18T14:07:17.702Z"}, "name": "Set form answer", "next": "aVPvEvo5hgE20HUZcw81C", "properties": {"inputs": {"answer": "{{SetupScenarios}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{form.id}}", "operation": "setTable", "questionId": "CenareaD0c", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "false"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "vfTvIiXgHJORtrpJkW2ay": {"id": "vfTvIiXgHJORtrpJkW2ay", "metadata": {"createdAt": "2025-03-18T14:26:44.590Z", "updatedAt": "2025-03-18T14:26:44.590Z"}, "name": "Create a form", "next": "gvvMzJCxqBMF5WclBiXBM", "properties": {"inputs": {"formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "PYForm", "foundationId": "{{Assignee.id}}", "intervalId": "nfwPMnwqpUvfaLCUwMqe6"}, "typePrimaryIdentifier": "createForm"}, "variant": "action"}}, "triggers": {"dIXSaezwAvFRZNtupFXXT": {"id": "dIXSaezwAvFRZNtupFXXT", "metadata": {"createdAt": "2025-03-18T13:59:45.776Z", "updatedAt": "2025-03-18T13:59:45.776Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Set up tests", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "ldDyEsOALrBjlHDR7YyXr": {"description": "", "id": "ldDyEsOALrBjlHDR7YyXr", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T05:25:07.326Z", "updatedAt": "2025-04-14T03:33:28.720Z"}, "name": "MULTIPLY - Iterator Table Values", "start": "chkxONHLlatNmihRsnczn", "startingVariables": [{"identifier": "TestForm", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "steps": {"FgaQav5hBglnLRQkXofAC": {"id": "FgaQav5hBglnLRQkXofAC", "metadata": {"createdAt": "2025-03-26T04:48:13.513Z", "updatedAt": "2025-03-26T04:48:13.513Z"}, "name": "Set variable(s)", "next": "Wp62IaikmMzGs0PK3HD6S", "properties": {"variables": [{"identifier": "MULTIPLY_COL1", "type": "json", "value": "[\"20\",\"30\"]"}, {"identifier": "MULTIPLY_COL2", "type": "json", "value": "[\"40\",\"20\"]"}]}, "variant": "setVariables"}, "GPnuUDFUAK674aq0a129P": {"id": "GPnuUDFUAK674aq0a129P", "metadata": {"createdAt": "2025-03-26T03:32:07.252Z", "updatedAt": "2025-03-26T03:32:07.252Z"}, "name": "Set variable(s)", "next": "IfuCD0szHtMs1rKKspb0K", "properties": {"variables": [{"identifier": "variable_1", "type": "text", "value": "$string({{form.Tt9L06N948.columns.UbeUf70j1C.answer}})"}, {"identifier": "variable_2", "type": "text", "value": "$SUBSTITUTE($SUBSTITUTE({{variable_1}},\"\\\\\",\"\"),\"\\\"\",\"\")"}]}, "variant": "setVariables"}, "IfuCD0szHtMs1rKKspb0K": {"id": "IfuCD0szHtMs1rKKspb0K", "metadata": {"createdAt": "2025-03-26T03:31:24.135Z", "updatedAt": "2025-03-26T03:31:24.135Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{variable_2}}", "columnId": "{{TestForm.pB7BSlhmKW.columns.bf4Ma7DWGF.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{TestForm.id}}", "operation": "setCell", "questionId": "bf4Ma7DWGF", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "3", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "PiSdJk8xD6XycT1THYgZB": {"id": "PiSdJk8xD6XycT1THYgZB", "metadata": {"createdAt": "2025-03-05T05:25:23.762Z", "updatedAt": "2025-03-05T05:25:23.762Z"}, "name": "For each", "next": "GPnuUDFUAK674aq0a129P", "properties": {"configuration": {"start": "iCz7kmM2Sz5ltomyQszTF", "steps": {"iCz7kmM2Sz5ltomyQszTF": {"id": "iCz7kmM2Sz5ltomyQszTF", "metadata": {"createdAt": "2025-03-05T05:26:57.038Z", "updatedAt": "2025-03-05T05:26:57.038Z"}, "name": "Set variable(s)", "next": "qWcGt6Vgp6rYYICaeDC8a", "properties": {"variables": [{"identifier": "Number1", "type": "number", "value": "$INDEX({{form.Tt9L06N948.columns.ROMCsqfEVl.answer}},{{tableItem_index}})"}, {"identifier": "Number2", "type": "number", "value": "$INDEX({{form.Tt9L06N948.columns.IxrosLRLnX.answer}},{{tableItem_index}})"}, {"identifier": "MULTIPLY", "type": "text", "value": "$MULTIPLY({{Number1}}, {{Number2}})"}]}, "variant": "setVariables"}, "qWcGt6Vgp6rYYICaeDC8a": {"id": "qWcGt6Vgp6rYYICaeDC8a", "metadata": {"createdAt": "2025-03-05T05:28:16.562Z", "updatedAt": "2025-03-05T05:28:16.562Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{MULTIPLY}}", "columnId": "{{form.Tt9L06N948.columns.UbeUf70j1C.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "operation": "setCell", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "{{tableItem_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}}, "inputs": {"isReturn": "false", "itemVariableName": "tableItem", "list": "{{form.Tt9L06N948.answer}}", "resultVariableName": "output__step_PiSdJk8xD6XycT1THYgZB", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}, "Wp62IaikmMzGs0PK3HD6S": {"id": "Wp62IaikmMzGs0PK3HD6S", "metadata": {"createdAt": "2025-03-26T03:37:44.644Z", "updatedAt": "2025-03-26T03:37:44.644Z"}, "name": "Set form answer", "next": "hGyazIwulVTHctCnaIya8", "properties": {"inputs": {"answer": "{{MULTIPLY_COL1}}", "columnId": "{{form.Tt9L06N948.columns.ROMCsqfEVl.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "byzZUjOKRCOmlbFPDlIAj": {"id": "byzZUjOKRCOmlbFPDlIAj", "metadata": {"createdAt": "2025-03-26T03:29:51.682Z", "updatedAt": "2025-03-26T03:29:51.682Z"}, "name": "Select Foundation", "next": "hS83hrG90Ahvmpg5EBavP", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "name": ""}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "chkxONHLlatNmihRsnczn": {"id": "chkxONHLlatNmihRsnczn", "metadata": {"createdAt": "2025-03-26T03:30:08.045Z", "updatedAt": "2025-03-26T03:30:08.045Z"}, "name": "Set variable(s)", "next": "byzZUjOKRCOmlbFPDlIAj", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"ASGN\",{{TestForm.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "hGyazIwulVTHctCnaIya8": {"id": "hGyazIwulVTHctCnaIya8", "metadata": {"createdAt": "2025-03-26T03:37:48.699Z", "updatedAt": "2025-03-26T03:37:48.699Z"}, "name": "Set form answer", "next": "PiSdJk8xD6XycT1THYgZB", "properties": {"inputs": {"answer": "{{MULTIPLY_COL2}}", "columnId": "{{form.Tt9L06N948.columns.IxrosLRLnX.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "hS83hrG90Ahvmpg5EBavP": {"id": "hS83hrG90Ahvmpg5EBavP", "metadata": {"createdAt": "2025-03-26T03:29:11.109Z", "updatedAt": "2025-03-26T03:29:11.109Z"}, "name": "Select form", "next": "FgaQav5hBglnLRQkXofAC", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form", "foundationId": "{{Assignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}}, "triggers": {"EiVWYEXGIGM3PPn6u2bS1": {"id": "EiVWYEXGIGM3PPn6u2bS1", "metadata": {"createdAt": "2025-03-05T05:25:10.679Z", "updatedAt": "2025-03-05T05:25:10.679Z"}, "name": "When an answer is changed", "next": "", "properties": {"inputs": {"formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "TestForm", "questionId": "arIQUhZ3V7", "questionIds": "[\"waNU3AzjNL\"]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "n3CwdVuL1TdyzlwhHCAAn": {"description": "", "id": "n3CwdVuL1TdyzlwhHCAAn", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-07T06:37:17.292Z", "updatedAt": "2025-04-14T03:35:14.904Z"}, "name": "COUNTIF - Single Value", "start": "OvB235SavmwXQKThYIfbQ", "steps": {"ab8piOGVifyVmY5FO8T0x": {"id": "ab8piOGVifyVmY5FO8T0x", "metadata": {"createdAt": "2025-03-07T06:37:59.434Z", "updatedAt": "2025-03-07T06:37:59.434Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{COUNTIF}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "questionId": "Ig6hhxmKmf", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "OvB235SavmwXQKThYIfbQ": {"id": "OvB235SavmwXQKThYIfbQ", "metadata": {"createdAt": "2025-03-07T06:37:33.984Z", "updatedAt": "2025-03-07T06:37:33.984Z"}, "name": "Set variable(s)", "next": "ab8piOGVifyVmY5FO8T0x", "properties": {"variables": [{"identifier": "COUNTIF", "type": "text", "value": "$COUNTIF({{form.Tt9L06N948.columns.wMthkQKZXy.answer}},\"Hello\")"}]}, "variant": "setVariables"}}, "triggers": {"JXj94d3OlajMAybQ1tfLM": {"id": "JXj94d3OlajMAybQ1tfLM", "metadata": {"createdAt": "2025-03-07T06:37:27.035Z", "updatedAt": "2025-03-07T06:37:27.035Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "COUNTIF - SINGLE", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "oTZe8Wnnm0xGkfP51amMF": {"description": "", "endingVariables": [{"identifier": "MULTIPLY", "type": "number"}], "id": "oTZe8Wnnm0xGkfP51amMF", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T04:57:36.225Z", "updatedAt": "2025-04-14T03:33:01.283Z"}, "name": "MULTIPLY - Single Values", "start": "a90dVtlhPGyiAG4rUZDKx", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "steps": {"a90dVtlhPGyiAG4rUZDKx": {"id": "a90dVtlhPGyiAG4rUZDKx", "metadata": {"createdAt": "2025-03-18T14:44:30.179Z", "updatedAt": "2025-03-18T14:44:30.179Z"}, "name": "Set variable(s)", "next": "aT5ZHODfoQr5JHfNmfr5v", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"ASGN\",{{form.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "aFXaK8Wvfc3hCpaJSan6P": {"id": "aFXaK8Wvfc3hCpaJSan6P", "metadata": {"createdAt": "2025-03-18T14:46:13.769Z", "updatedAt": "2025-03-18T14:46:13.769Z"}, "name": "Set form answer", "next": "HoMrdeyrcu9MDzgN8Tk5i", "properties": {"inputs": {"answer": "20", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{CYForm.id}}", "questionId": "aAjV4Ryb0x", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "aT5ZHODfoQr5JHfNmfr5v": {"id": "aT5ZHODfoQr5JHfNmfr5v", "metadata": {"createdAt": "2025-03-18T14:44:22.366Z", "updatedAt": "2025-03-18T14:44:22.366Z"}, "name": "Select Foundation", "next": "mSfbfcmhlWx3OQaLMYkBU", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "HoMrdeyrcu9MDzgN8Tk5i": {"id": "HoMrdeyrcu9MDzgN8Tk5i", "metadata": {"createdAt": "2025-03-05T04:57:52.667Z", "updatedAt": "2025-03-05T04:57:52.667Z"}, "name": "Number 1 * Number 2", "next": "KudJrqof1hvHgPAjTNiWZ", "properties": {"variables": [{"identifier": "MULTIPLY", "type": "number", "value": "$MULTIPLY({{CYForm.w1FWFfddba.answer}}, {{CYForm.aAjV4Ryb0x.answer}})"}]}, "variant": "setVariables"}, "KudJrqof1hvHgPAjTNiWZ": {"id": "KudJrqof1hvHgPAjTNiWZ", "metadata": {"createdAt": "2025-03-05T05:01:18.567Z", "updatedAt": "2025-03-05T05:01:18.567Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{MULTIPLY}}", "columnId": "{{form.pB7BSlhmKW.columns.bf4Ma7DWGF.id}}", "formId": "{{form.id}}", "operation": "setCell", "questionId": "{{form.pB7BSlhmKW.id}}", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "1", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "gRxbeJma6weX9UZMUV4iB": {"id": "gRxbeJma6weX9UZMUV4iB", "metadata": {"createdAt": "2025-03-18T14:45:55.569Z", "updatedAt": "2025-03-18T14:45:55.569Z"}, "name": "Set form answer", "next": "aFXaK8Wvfc3hCpaJSan6P", "properties": {"inputs": {"answer": "10", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{CYForm.id}}", "questionId": "w1FWFfddba", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "mSfbfcmhlWx3OQaLMYkBU": {"id": "mSfbfcmhlWx3OQaLMYkBU", "metadata": {"createdAt": "2025-03-18T14:44:00.685Z", "updatedAt": "2025-03-18T14:44:00.685Z"}, "name": "Select form", "next": "gRxbeJma6weX9UZMUV4iB", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "CYForm", "foundationId": "{{Assignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}}, "triggers": {"jh82Xwia8Yau8vNGCDMsG": {"id": "jh82Xwia8Yau8vNGCDMsG", "metadata": {"createdAt": "2025-03-05T04:57:38.850Z", "updatedAt": "2025-03-05T04:57:38.850Z"}, "name": "When an answer is changed", "next": "", "properties": {"inputs": {"formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "form", "questionId": "waNU3AzjNL", "questionIds": "[\"waNU3AzjNL\"]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "q2v1U6480oC26iqw4tYEJ": {"description": "", "id": "q2v1U6480oC26iqw4tYEJ", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T13:57:58.444Z", "updatedAt": "2025-04-14T03:34:50.435Z"}, "name": "Get date values", "start": "YAwfkSswrkPao3R9lINOC", "steps": {"YAwfkSswrkPao3R9lINOC": {"id": "YAwfkSswrkPao3R9lINOC", "metadata": {"createdAt": "2025-03-05T13:58:14.488Z", "updatedAt": "2025-03-05T13:58:14.488Z"}, "name": "Set variable(s)", "next": "mmfWDvI85REC7MeL6C8Nj", "properties": {"variables": [{"identifier": "CONCAT", "type": "text", "value": "$CONCAT(\"The date is \", $DAY({{form.eXoUzbPlns.answer}}), \"/\", $MONTH({{form.eXoUzbPlns.answer}}), \"/\", $YEAR({{form.eXoUzbPlns.answer}}))"}]}, "variant": "setVariables"}, "mmfWDvI85REC7MeL6C8Nj": {"id": "mmfWDvI85REC7MeL6C8Nj", "metadata": {"createdAt": "2025-03-05T13:58:38.788Z", "updatedAt": "2025-03-05T13:58:38.788Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{CONCAT}}", "formId": "{{form.id}}", "questionId": "{{form.t60PjuSTaB.id}}", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"a3moWKSMU0ZdEHGtZ8TQ6": {"id": "a3moWKSMU0ZdEHGtZ8TQ6", "metadata": {"createdAt": "2025-03-05T13:58:04.304Z", "updatedAt": "2025-03-05T13:58:04.304Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "GET DATE VALUES", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "rjqfSI2di2j5UUedWIloG": {"description": "", "id": "rjqfSI2di2j5UUedWIloG", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-02T22:58:06.856Z", "updatedAt": "2025-04-14T03:31:07.518Z"}, "name": "10. Missing Bonus Warning", "start": "PdVqN8ufEsJhwf4amP0H9", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.SG14waZgbati8MISSWyVN"}], "steps": {"axyQ9SrEuv0FWTlyf1afp": {"id": "axyQ9SrEuv0FWTlyf1afp", "metadata": {"createdAt": "2025-03-02T23:05:30.929Z", "updatedAt": "2025-03-02T23:05:30.929Z"}, "name": "Raise warning for missing bonus", "next": "", "properties": {"inputs": {"formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "groupIdentifier": "BonusWarning", "message": "Assignee had a bonus last year but did not have a bonus this year", "operation": "add", "questionId": "NgX8CIqfBq", "variant": "warning"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}, "aSMESOyJKGMfk5bnubBrQ": {"id": "aSMESOyJKGMfk5bnubBrQ", "metadata": {"createdAt": "2025-03-02T23:04:07.323Z", "updatedAt": "2025-03-02T23:04:07.323Z"}, "name": "Check if PY payroll contains bonus", "next": "nVtaOBecN8AuUPLvZZK6x", "properties": {"variables": [{"identifier": "PYBonusCheck", "type": "number", "value": "$COUNTIF({{form.a3a8xLFwNd.columns.aAPrhNEtXT.answer}}, \"Bonus\")"}]}, "variant": "setVariables"}, "PdVqN8ufEsJhwf4amP0H9": {"id": "PdVqN8ufEsJhwf4amP0H9", "metadata": {"createdAt": "2025-03-02T22:59:26.128Z", "updatedAt": "2025-03-02T22:59:26.129Z"}, "name": "Check if CY payroll contains bonus", "next": "ZPTTPa99PsisNH5TNguFl", "properties": {"variables": [{"identifier": "Bonus<PERSON>heck", "type": "number", "value": "$COUNTIF({{form.qO3xge5etd.columns.DYnqlhI7UM.answer}}, \"Bonus\")"}]}, "variant": "setVariables"}, "ZPTTPa99PsisNH5TNguFl": {"id": "ZPTTPa99PsisNH5TNguFl", "metadata": {"createdAt": "2025-03-02T23:00:54.864Z", "updatedAt": "2025-03-02T23:00:54.864Z"}, "name": "Current year compensation data contains bonus", "next": "aSMESOyJKGMfk5bnubBrQ", "properties": {"branches": [{"condition": {"lhs": "{{<PERSON><PERSON><PERSON><PERSON>}}", "operator": ">", "rhs": "0"}, "name": "Yes", "next": "s3mFoZFKHULrsiJLOP7fx"}]}, "variant": "condition"}, "kl43KIk7o3IYTO1f8F58V": {"id": "kl43KIk7o3IYTO1f8F58V", "metadata": {"createdAt": "2025-03-12T23:18:55.034Z", "updatedAt": "2025-03-12T23:18:55.034Z"}, "name": "Remove existing alerts", "next": "axyQ9SrEuv0FWTlyf1afp", "properties": {"inputs": {"formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "groupIdentifier": "BonusWarning", "message": "", "operation": "remove", "questionId": "NgX8CIqfBq", "variant": "warning"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}, "nVtaOBecN8AuUPLvZZK6x": {"id": "nVtaOBecN8AuUPLvZZK6x", "metadata": {"createdAt": "2025-03-02T23:04:43.854Z", "updatedAt": "2025-03-02T23:04:43.854Z"}, "name": "Previous year compensation data contains bonus", "next": "", "properties": {"branches": [{"condition": {"lhs": "{{PYBonusCheck}}", "operator": ">", "rhs": "0"}, "name": "Yes", "next": "kl43KIk7o3IYTO1f8F58V"}]}, "variant": "condition"}, "s3mFoZFKHULrsiJLOP7fx": {"id": "s3mFoZFKHULrsiJLOP7fx", "metadata": {"createdAt": "2025-03-20T14:12:05.047Z", "updatedAt": "2025-03-20T14:12:05.047Z"}, "name": "Set form alert", "next": "", "properties": {"inputs": {"formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "groupIdentifier": "BonusWarning", "operation": "remove", "questionId": "NgX8CIqfBq", "variant": "warning"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}}, "triggers": {"jZc0eOukOIxGXsq2Yhhok": {"id": "jZc0eOukOIxGXsq2Yhhok", "metadata": {"createdAt": "2025-03-02T22:58:13.183Z", "updatedAt": "2025-03-02T22:58:13.183Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Placeholder", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "rkAwUlduOHIDjQM7aQvEl": {"description": "", "id": "rkAwUlduOHIDjQM7aQvEl", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T05:54:10.399Z", "updatedAt": "2025-04-14T03:33:52.080Z"}, "name": "SUM - Single Values", "start": "Jnva56c5UAzHIaTChhqYQ", "steps": {"a1005XerqdqlITamdSvtd": {"id": "a1005XerqdqlITamdSvtd", "metadata": {"createdAt": "2025-03-05T05:54:48.324Z", "updatedAt": "2025-03-05T05:54:48.324Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{SUM}}", "formId": "{{form.id}}", "questionId": "{{form.Ig6hhxmKmf.id}}", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "Jnva56c5UAzHIaTChhqYQ": {"id": "Jnva56c5UAzHIaTChhqYQ", "metadata": {"createdAt": "2025-03-05T05:54:19.242Z", "updatedAt": "2025-03-05T05:54:19.242Z"}, "name": "Set variable(s)", "next": "a1005XerqdqlITamdSvtd", "properties": {"variables": [{"identifier": "SUM", "type": "number", "value": "$SUM({{form.w1FWFfddba.answer}},{{form.aAjV4Ryb0x.answer}})"}]}, "variant": "setVariables"}}, "triggers": {"HRpoaXd52BAuLyjaDqPio": {"id": "HRpoaXd52BAuLyjaDqPio", "metadata": {"createdAt": "2025-03-05T05:54:12.709Z", "updatedAt": "2025-03-05T05:54:12.709Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "SUM - SINGLE", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "ta9tAaMvSBUla13vobuil": {"description": "", "id": "ta9tAaMvSBUla13vobuil", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T12:55:02.668Z", "updatedAt": "2025-04-14T03:34:18.737Z"}, "name": "SUMIF - Single Value", "start": "gm0iBqBmjnnLkPofaNnp5", "startingVariables": [{"identifier": "TestForm", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "steps": {"a0wG3C1oYEOanYWDUN0Uj": {"id": "a0wG3C1oYEOanYWDUN0Uj", "metadata": {"createdAt": "2025-04-10T03:25:11.501Z", "updatedAt": "2025-04-10T03:25:11.501Z"}, "name": "Select form", "next": "BOh7QE9qGqfaOUiH2l4jR", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form", "foundationId": "{{Assignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "aVUANJAx1jUH8hzMf9q5T": {"id": "aVUANJAx1jUH8hzMf9q5T", "metadata": {"createdAt": "2025-03-05T12:55:14.506Z", "updatedAt": "2025-03-05T12:55:14.506Z"}, "name": "SUMIF Hello", "next": "aBLTsLbbay5iGPepdxtnE", "properties": {"variables": [{"identifier": "SUMIF", "type": "number", "value": "$SUMIF({{form.Tt9L06N948.columns.wMthkQKZXy.answer}},\"Hello\", {{form.Tt9L06N948.columns.ROMCsqfEVl.answer}})"}]}, "variant": "setVariables"}, "arlkiFvHYFhB159lS5whf": {"id": "arlkiFvHYFhB159lS5whf", "metadata": {"createdAt": "2025-04-10T03:25:30.532Z", "updatedAt": "2025-04-10T03:25:30.532Z"}, "name": "Set form answer", "next": "P3VLNnZw8AqV62NfzJeMT", "properties": {"inputs": {"answer": "{{TEXTCOLUMN}}", "columnId": "{{form.Tt9L06N948.columns.wMthkQKZXy.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "aBLTsLbbay5iGPepdxtnE": {"id": "aBLTsLbbay5iGPepdxtnE", "metadata": {"createdAt": "2025-04-10T03:26:41.531Z", "updatedAt": "2025-04-10T03:26:41.531Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{SUMIF}}", "columnId": "{{TestForm.pB7BSlhmKW.columns.bf4Ma7DWGF.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{TestForm.id}}", "operation": "setCell", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "6", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "BOh7QE9qGqfaOUiH2l4jR": {"id": "BOh7QE9qGqfaOUiH2l4jR", "metadata": {"createdAt": "2025-04-10T03:25:21.585Z", "updatedAt": "2025-04-10T03:25:21.585Z"}, "name": "Set variable(s)", "next": "arlkiFvHYFhB159lS5whf", "properties": {"variables": [{"identifier": "TEXTCOLUMN", "type": "json", "value": "[\"Hello\",\"World\"]"}, {"identifier": "NUMCOLUMN", "type": "json", "value": "[100,200]"}]}, "variant": "setVariables"}, "P3VLNnZw8AqV62NfzJeMT": {"id": "P3VLNnZw8AqV62NfzJeMT", "metadata": {"createdAt": "2025-04-10T03:25:53.184Z", "updatedAt": "2025-04-10T03:25:53.184Z"}, "name": "Set form answer", "next": "aVUANJAx1jUH8hzMf9q5T", "properties": {"inputs": {"answer": "{{NUMCOLUMN}}", "columnId": "{{form.Tt9L06N948.columns.ROMCsqfEVl.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "gm0iBqBmjnnLkPofaNnp5": {"id": "gm0iBqBmjnnLkPofaNnp5", "metadata": {"createdAt": "2025-04-10T03:25:03.931Z", "updatedAt": "2025-04-10T03:25:03.931Z"}, "name": "Set variable(s)", "next": "kfFg11d1wfGGah0GJn7cU", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"ASGN\",{{TestForm.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "kfFg11d1wfGGah0GJn7cU": {"id": "kfFg11d1wfGGah0GJn7cU", "metadata": {"createdAt": "2025-04-17T02:48:19.255Z", "updatedAt": "2025-04-17T02:48:19.255Z"}, "name": "Select Foundation", "next": "a0wG3C1oYEOanYWDUN0Uj", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}}, "triggers": {"aRppceI1BqA8f9CabWNp1": {"id": "aRppceI1BqA8f9CabWNp1", "metadata": {"createdAt": "2025-03-05T12:55:05.806Z", "updatedAt": "2025-03-05T12:55:05.806Z"}, "name": "When an answer is changed", "next": "", "properties": {"inputs": {"formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "TestForm", "questionId": "qVHxJx2vCv", "questionIds": "[\"waNU3AzjNL\"]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "v9VoW8SFRuovXbM5tavTq": {"description": "", "id": "v9VoW8SFRuovXbM5tavTq", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T13:03:38.741Z", "updatedAt": "2025-04-14T03:34:31.150Z"}, "name": "Set alert - Table Row", "start": "a8awLArDluVhwUbzDoH7g", "steps": {"a8awLArDluVhwUbzDoH7g": {"id": "a8awLArDluVhwUbzDoH7g", "metadata": {"createdAt": "2025-03-05T13:04:07.651Z", "updatedAt": "2025-03-05T13:04:07.651Z"}, "name": "For each", "next": "", "properties": {"configuration": {"start": "a1TLPk2LXT63OzzU8xDlo", "steps": {"a1TLPk2LXT63OzzU8xDlo": {"id": "a1TLPk2LXT63OzzU8xDlo", "metadata": {"createdAt": "2025-03-05T13:11:04.747Z", "updatedAt": "2025-03-05T13:11:04.747Z"}, "name": "= Knock knock?", "next": "", "properties": {"branches": [{"condition": {"lhs": "{{iterator_a8awLArDluVhwUbzDoH7g_item}}", "operator": "=", "rhs": "Knock knock"}, "name": "If", "next": "vz2RWqyHCrHbeNR2ucQjR"}]}, "variant": "condition"}, "vz2RWqyHCrHbeNR2ucQjR": {"id": "vz2RWqyHCrHbeNR2ucQjR", "metadata": {"createdAt": "2025-03-05T13:13:09.408Z", "updatedAt": "2025-03-05T13:13:09.408Z"}, "name": "Set form alert", "next": "", "properties": {"inputs": {"formId": "{{form.id}}", "message": "Who's there?", "operation": "add", "questionId": "{{form.Tt9L06N948.*.wMthkQKZXy.id}}", "rowId": "{{iterator_a8awLArDluVhwUbzDoH7g_item._rowId}}", "variant": "blocker"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}}}, "inputs": {"isReturn": "false", "itemVariableName": "item_a8awLArDluVhwUbzDoH7g", "list": "{{form.Tt9L06N948.columns.wMthkQKZXy.answer}}", "resultVariableName": "output__step_a8awLArDluVhwUbzDoH7g", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"DuaqZ8Xx1lRYb9la9IhiV": {"id": "DuaqZ8Xx1lRYb9la9IhiV", "metadata": {"createdAt": "2025-03-05T13:03:43.631Z", "updatedAt": "2025-03-05T13:03:43.631Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "SET ALERT - TABLE ROW", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "vjO4qtl0CltSj7v0N32Ff": {"description": "", "id": "vjO4qtl0CltSj7v0N32Ff", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-07T02:16:08.614Z", "updatedAt": "2025-04-14T03:35:07.318Z"}, "name": "Remove alert - Single Value", "start": "rcJM7XraurrP6ACMBuv1u", "steps": {"rcJM7XraurrP6ACMBuv1u": {"id": "rcJM7XraurrP6ACMBuv1u", "metadata": {"createdAt": "2025-03-07T02:16:34.576Z", "updatedAt": "2025-03-07T02:16:34.576Z"}, "name": "Set form alert", "next": "", "properties": {"inputs": {"formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "groupIdentifier": "Alert_1", "message": "<PERSON><PERSON>!", "operation": "remove", "questionId": "t60PjuSTaB", "variant": "warning"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}}, "triggers": {"UXWR4dydbNUVhs7JydmFW": {"id": "UXWR4dydbNUVhs7JydmFW", "metadata": {"createdAt": "2025-03-07T02:16:17.215Z", "updatedAt": "2025-03-07T02:16:17.216Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "REMOVE ALERTS - <PERSON>ING<PERSON>", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "xKs57oJMw2YIQiBS9vwaP": {"description": "", "id": "xKs57oJMw2YIQiBS9vwaP", "labels": ["IckoggTcyJCMaHkbb4sI5"], "metadata": {"createdAt": "2025-03-05T05:50:22.906Z", "updatedAt": "2025-04-14T03:33:45.436Z"}, "name": "COUNT - ARRAY", "start": "AvQVJJalFawaQVva7sGCU", "startingVariables": [{"identifier": "TestForm", "properties": {"required": true}, "type": "form.EISiO632dEeNfgsIyqykP"}], "steps": {"AvQVJJalFawaQVva7sGCU": {"id": "AvQVJJalFawaQVva7sGCU", "metadata": {"createdAt": "2025-03-27T22:53:36.026Z", "updatedAt": "2025-03-27T22:53:36.026Z"}, "name": "Set variable(s)", "next": "R9SuWob6871nvOy5oFBzm", "properties": {"variables": [{"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "value": "$CONCAT(\"ASGN\",{{TestForm.B1AYblG3LF.answer}})"}]}, "variant": "setVariables"}, "R9SuWob6871nvOy5oFBzm": {"id": "R9SuWob6871nvOy5oFBzm", "metadata": {"createdAt": "2025-04-17T02:37:30.463Z", "updatedAt": "2025-04-17T02:37:30.463Z"}, "name": "Select Foundation", "next": "qfH6OV0mSXCtx1GgjzC9s", "properties": {"inputs": {"continueFlowIfNotFound": "true", "foundationConfigurationId": "x00VHaApOAVpOa8VKFkBC", "foundationVariableName": "Assignee", "key": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}"}, "typePrimaryIdentifier": "selectFoundation"}, "variant": "action"}, "SqDjQUscn6AkB2O1EabUf": {"id": "SqDjQUscn6AkB2O1EabUf", "metadata": {"createdAt": "2025-03-28T00:01:58.603Z", "updatedAt": "2025-03-28T00:01:58.603Z"}, "name": "Set variable(s)", "next": "iaUsvT2nY7yXEnHMCLBTa", "properties": {"variables": [{"identifier": "TEXTCOLUMN", "type": "json", "value": "[\"Hello\",\"World\"]"}]}, "variant": "setVariables"}, "iaUsvT2nY7yXEnHMCLBTa": {"id": "iaUsvT2nY7yXEnHMCLBTa", "metadata": {"createdAt": "2025-03-28T00:02:20.416Z", "updatedAt": "2025-03-28T00:02:20.416Z"}, "name": "Set form answer", "next": "tUCvSEaqZZI1DBtjVKckA", "properties": {"inputs": {"answer": "{{TEXTCOLUMN}}", "columnId": "{{form.Tt9L06N948.columns.wMthkQKZXy.id}}", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "Tt9L06N948", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "kXjWHD2duvuAw3CVRPQrO": {"id": "kXjWHD2duvuAw3CVRPQrO", "metadata": {"createdAt": "2025-03-05T05:51:13.413Z", "updatedAt": "2025-03-05T05:51:13.413Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{COUNT}}", "columnId": "{{TestForm.pB7BSlhmKW.columns.bf4Ma7DWGF.id}}", "formConfigurationId": "EISiO632dEeNfgsIyqykP", "formId": "{{TestForm.id}}", "operation": "setCell", "questionId": "pB7BSlhmKW", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "4", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "qfH6OV0mSXCtx1GgjzC9s": {"id": "qfH6OV0mSXCtx1GgjzC9s", "metadata": {"createdAt": "2025-03-27T22:53:40.996Z", "updatedAt": "2025-03-27T22:53:40.996Z"}, "name": "Select form", "next": "SqDjQUscn6AkB2O1EabUf", "properties": {"inputs": {"continueFlowIfNotFound": "false", "formConfigurationId": "oXUOfkMKayra9lBi5NOvj", "formVariableName": "form", "foundationId": "{{Assignee.id}}", "intervalId": "ZJWjjm2p6EaqyEG1esaY4"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "tUCvSEaqZZI1DBtjVKckA": {"id": "tUCvSEaqZZI1DBtjVKckA", "metadata": {"createdAt": "2025-03-05T05:50:52.966Z", "updatedAt": "2025-03-05T05:50:52.966Z"}, "name": "Set variable(s)", "next": "kXjWHD2duvuAw3CVRPQrO", "properties": {"variables": [{"identifier": "COUNT", "type": "number", "value": "$COUNT({{form.Tt9L06N948.columns.wMthkQKZXy.answer}})"}]}, "variant": "setVariables"}}, "triggers": {"QzULkcau62YG7pINn3ywL": {"id": "QzULkcau62YG7pINn3ywL", "metadata": {"createdAt": "2025-03-05T05:50:41.649Z", "updatedAt": "2025-03-05T05:50:41.649Z"}, "name": "When an answer is changed", "next": "", "properties": {"inputs": {"formConfigurationId": "EISiO632dEeNfgsIyqykP", "formVariableName": "TestForm", "questionId": "FIGzzv8LYR", "questionIds": "[\"waNU3AzjNL\"]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "zwXBlUpcsNiCC3AgaKTGR": {"description": "", "id": "zwXBlUpcsNiCC3AgaKTGR", "labels": ["ow2DwQbCNv3oC6LJKS4cE"], "metadata": {"createdAt": "2025-03-04T05:51:11.202Z", "updatedAt": "2025-04-14T03:31:41.337Z"}, "name": "7. Analyse Comp Data", "start": "hsJgZBNa9sxsyTi4j60bt", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.SG14waZgbati8MISSWyVN"}], "steps": {"aFWOaTyhkyoXZ96L47TUS": {"id": "aFWOaTyhkyoXZ96L47TUS", "metadata": {"createdAt": "2025-03-12T13:00:46.190Z", "updatedAt": "2025-03-12T13:00:46.190Z"}, "name": "Map payroll data", "next": "d5JTYwftMdTanaxISzbis", "properties": {"configuration": {"start": "ZKsAJDDQSBLgadJd9lpP4", "startingVariables": [{"identifier": "PayrollItem", "properties": null, "type": "json"}, {"identifier": "PayrollItem_index", "properties": null, "type": "number"}, {"identifier": "PayrollItem_output", "properties": null, "type": "unknown"}], "steps": {"aS6Hh3z3wgQ4V6g726jGK": {"id": "aS6Hh3z3wgQ4V6g726jGK", "metadata": {"createdAt": "2025-03-13T01:54:47.134Z", "updatedAt": "2025-03-13T01:54:47.134Z"}, "name": "Remove any alerts", "next": "", "properties": {"inputs": {"formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "groupIdentifier": "MissingMapping", "message": "", "operation": "remove", "questionId": "qO3xge5etd", "rowId": "{{PayrollItem._rowId}}", "variant": "blocker"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}, "OfeBSWGivqNanjaJovA7p": {"id": "OfeBSWGivqNanjaJovA7p", "metadata": {"createdAt": "2025-03-13T01:23:19.454Z", "updatedAt": "2025-03-13T01:23:19.455Z"}, "name": "Remove alerts", "next": "ngXU5GKLKa1w8oCQCoZYY", "properties": {"inputs": {"formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "groupIdentifier": "MissingMapping", "message": "", "operation": "remove", "questionId": "qO3xge5etd", "rowId": "{{PayrollItem._rowId}}", "variant": "blocker"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}, "UPaNOZBIXMZ4ToPMkaVER": {"id": "UPaNOZBIXMZ4ToPMkaVER", "metadata": {"createdAt": "2025-03-13T01:48:01.406Z", "updatedAt": "2025-03-13T01:48:01.406Z"}, "name": "Add new row to client information payroll mappings", "next": "", "properties": {"inputs": {"answer": "{{PayrollItem}}", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formId": "{{clientPositions.id}}", "operation": "setRow", "questionId": "SoBlBkh5ZM", "questionTypeWithOperation": "table", "rowSelection": "rowId"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "ZKsAJDDQSBLgadJd9lpP4": {"id": "ZKsAJDDQSBLgadJd9lpP4", "metadata": {"createdAt": "2025-04-23T04:19:51.720Z", "updatedAt": "2025-04-23T04:19:51.720Z"}, "name": "Set variable(s)", "next": "tDUkZ4hAN6tCuZtqdWbv7", "properties": {"variables": [{"identifier": "Description", "type": "text", "value": "$INDEX({{form.qO3xge5etd.columns.MRcRCBayfx.answer}},{{PayrollItem_index}})"}, {"identifier": "StandardMappingValue", "type": "text", "value": "$XLOOKUP({{Description}},{{clientPositions.SoBlBkh5ZM.columns.aVg591B27j.answer}},{{clientPositions.SoBlBkh5ZM.columns.XaADpaR1Sf.answer}})"}]}, "variant": "setVariables"}, "ngXU5GKLKa1w8oCQCoZYY": {"id": "ngXU5GKLKa1w8oCQCoZYY", "metadata": {"createdAt": "2025-03-13T01:27:32.771Z", "updatedAt": "2025-03-13T01:27:32.772Z"}, "name": "Add alerts for missing mappings", "next": "riJLRCSaWXqtI5RZ0lLdR", "properties": {"inputs": {"formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "groupIdentifier": "MissingMapping", "message": "Payroll item is not mapped. Go to client information to map payroll item.", "operation": "add", "questionId": "qO3xge5etd", "rowId": "{{PayrollItem._rowId}}", "variant": "blocker"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}, "riJLRCSaWXqtI5RZ0lLdR": {"id": "riJLRCSaWXqtI5RZ0lLdR", "metadata": {"createdAt": "2025-03-13T01:43:21.384Z", "updatedAt": "2025-03-13T01:43:21.384Z"}, "name": "Create new payroll item in client information", "next": "UPaNOZBIXMZ4ToPMkaVER", "properties": {"variables": [{"identifier": "PayrollItem", "type": "json", "value": "{ {{clientPositions.SoBlBkh5ZM.columns.aVg591B27j.id}}:{{Description}}}"}]}, "variant": "setVariables"}, "tDUkZ4hAN6tCuZtqdWbv7": {"id": "tDUkZ4hAN6tCuZtqdWbv7", "metadata": {"createdAt": "2025-03-13T01:22:24.166Z", "updatedAt": "2025-03-13T01:22:24.166Z"}, "name": "Standard mapping not found", "next": "aS6Hh3z3wgQ4V6g726jGK", "properties": {"branches": [{"condition": {"OR": [{"lhs": "{{StandardMappingValue}}", "operator": "=", "rhs": "\"#N/A\""}, {"lhs": "{{StandardMappingValue}}", "operator": "=", "rhs": "\"\""}]}, "name": "If", "next": "OfeBSWGivqNanjaJovA7p"}]}, "variant": "condition"}}}, "inputs": {"isReturn": "false", "itemVariableName": "PayrollItem", "list": "{{form.qO3xge5etd.answer}}", "resultVariableName": "output__step_aFWOaTyhkyoXZ96L47TUS", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}, "M2BIizU7LvxA3BSHavXDE": {"id": "M2BIizU7LvxA3BSHavXDE", "metadata": {"createdAt": "2025-04-23T04:11:08.835Z", "updatedAt": "2025-04-23T04:11:08.835Z"}, "name": "Set variable(s)", "next": "atG6rbMAhYah6ghPq9vNw", "properties": {"variables": [{"identifier": "StandardMapping", "properties": {"listOperation": "setList"}, "type": "list", "value": "$map({{form.qO3xge5etd.columns.MRcRCBayfx.answer}}, function($v, $i){\n  $IFNA($XLOOKUP($v, {{clientPositions.SoBlBkh5ZM.columns.aVg591B27j.answer}}, {{clientPositions.SoBlBkh5ZM.columns.XaADpaR1Sf.answer}}),\"\")\n})"}, {"identifier": "TaxBorneBy", "properties": {"listOperation": "setList"}, "type": "list", "value": "$map({{form.qO3xge5etd.columns.MRcRCBayfx.answer}}, function($v, $i){\n  $IFNA($XLOOKUP($v, {{clientPositions.SoBlBkh5ZM.columns.aVg591B27j.answer}}, {{clientPositions.SoBlBkh5ZM.columns.X4KTqYVmaV.answer}}),\"\")\n})"}, {"identifier": "CYPayroll", "properties": {"columnIdentifier": "{{form.qO3xge5etd.columns.DYnqlhI7UM.id}}", "operation": "setColumn"}, "type": "table", "value": "{{StandardMapping}}"}]}, "variant": "setVariables"}, "VJ4SCtZU8bMKNQD20kLWY": {"id": "VJ4SCtZU8bMKNQD20kLWY", "metadata": {"createdAt": "2025-04-23T04:18:00.989Z", "updatedAt": "2025-04-23T04:18:00.989Z"}, "name": "Set form answer", "next": "aFWOaTyhkyoXZ96L47TUS", "properties": {"inputs": {"answer": "{{<PERSON><PERSON><PERSON><PERSON>}}", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "operation": "setTable", "questionId": "qO3xge5etd", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "atG6rbMAhYah6ghPq9vNw": {"id": "atG6rbMAhYah6ghPq9vNw", "metadata": {"createdAt": "2025-04-23T04:17:21.597Z", "updatedAt": "2025-04-23T04:17:21.597Z"}, "name": "Set Tax Borne By to CYPayroll", "next": "VJ4SCtZU8bMKNQD20kLWY", "properties": {"variables": [{"identifier": "CYPayroll", "properties": {"columnIdentifier": "{{form.qO3xge5etd.columns.aNomVlFYS4.id}}", "operation": "setColumn"}, "type": "table", "value": "{{Tax<PERSON>orneBy}}"}]}, "variant": "setVariables"}, "d5JTYwftMdTanaxISzbis": {"id": "d5JTYwftMdTanaxISzbis", "metadata": {"createdAt": "2025-03-20T09:05:12.206Z", "updatedAt": "2025-03-20T09:05:12.206Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "RUN", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formId": "{{form.id}}", "questionId": "vRNGl6Xxxb", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "hsJgZBNa9sxsyTi4j60bt": {"id": "hsJgZBNa9sxsyTi4j60bt", "metadata": {"createdAt": "2025-03-04T05:53:11.086Z", "updatedAt": "2025-03-04T05:53:11.086Z"}, "name": "Get client information form", "next": "lTSXvFEs7ZZ6DRDWOmFU7", "properties": {"inputs": {"continueFlowIfNotFound": "true", "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4", "formVariableName": "clientPositions", "foundationId": "{{form.foundation.parentId}}"}, "typePrimaryIdentifier": "selectForm"}, "variant": "action"}, "lTSXvFEs7ZZ6DRDWOmFU7": {"id": "lTSXvFEs7ZZ6DRDWOmFU7", "metadata": {"createdAt": "2025-03-13T23:46:12.954Z", "updatedAt": "2025-03-13T23:46:12.954Z"}, "name": "Create CY Payroll Table", "next": "M2BIizU7LvxA3BSHavXDE", "properties": {"variables": [{"identifier": "CYPayroll", "properties": {"operation": "setTable"}, "type": "table", "value": "{{form.qO3xge5etd.answer}}"}]}, "variant": "setVariables"}}, "triggers": {"Vmr6SmGt5ctUUmWDaHaKW": {"id": "Vmr6SmGt5ctUUmWDaHaKW", "metadata": {"createdAt": "2025-03-04T05:51:15.218Z", "updatedAt": "2025-03-04T05:51:15.218Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Analyse Comp Data", "formConfigurationId": "SG14waZgbati8MISSWyVN", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}}, "order": ["F4C7QxjXDITjch1FaNXwG", "aTPYchZaa77BEaguqh3YP", "rjqfSI2di2j5UUedWIloG", "dEYkVA0hFRoJizwo9bu5e", "FUp3ETxZ9AuX6X8yk4ajQ", "zwXBlUpcsNiCC3AgaKTGR", "edG3ZL0GtqLBDn6wSMt9D", "fNJ3ONFMx41eY8j4JK1RT", "a3DJIUaljyVYKVQC7XvTS", "aA6A7kQGadU9zCG0YEdF3", "au9IDqSVdX8tBmRawaq8H", "g74c8tbdIBKQXnCW32FVE", "e6Q09UzxR1taN88FG1Nok", "oTZe8Wnnm0xGkfP51amMF", "aavRghx8kM7VyHEdqLj9t", "ldDyEsOALrBjlHDR7YyXr", "Xx3FDs6isPccDlvhgPT6C", "ZjUi6K0WaHjU2aokT7bto", "xKs57oJMw2YIQiBS9vwaP", "rkAwUlduOHIDjQM7aQvEl", "HciG4QaRykhNJq7pg1Y4D", "asSPezA0cnfSzSC83ZnjZ", "ta9tAaMvSBUla13vobuil", "LaaJ5JrTngjEfJop763AO", "v9VoW8SFRuovXbM5tavTq", "FjoMUqOoDabtqBivljsO6", "aZtJgCmAA7fSuXBgh4iZN", "q2v1U6480oC26iqw4tYEJ", "PUGIvuoviwZK6lzQCw4wZ", "LfGhaaZrFZzlcbVsccQrW", "vjO4qtl0CltSj7v0N32Ff", "n3CwdVuL1TdyzlwhHCAAn", "aKE2XzeRgf2YtI71aVKYU", "a3gIiof7HoIQyUGTsUUML", "Orcq5ABUgofevSZNkfFCO", "g4JFJsrJaOvm3cwx1OLzF", "kxW7iB4mGka2ICJaVLgKe", "BUafhWaQZiOzM7ljhCRbe", "QjExeOqabLLerYaNtLSQE", "ObWGDhTKIfuMEeXj274hQ", "FGKFPa0tAGXb2GXNZzMgC", "aAw1vtdZkjM3jsCaiE5Sa", "aqL0scQZ1FAEHFrcixhlm", "aRXEsAN6USxUdyK71Z1HK"]}, "forms": {"EISiO632dEeNfgsIyqykP": {"content": [{"content": [{"content": [{"description": "", "id": "B1AYblG3LF", "identifier": "BaseKey", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Base Key", "type": "text"}, {"description": "", "id": "CenareaD0c", "identifier": "SetupResults", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "DfIers83Wd", "identifier": "<PERSON><PERSON><PERSON>", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "<PERSON><PERSON><PERSON>", "type": "text"}, {"description": "", "id": "yalXVBoM49", "identifier": "Result", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Result", "type": "text"}], "required": false}, "text": "Setup results", "type": "table"}, {"description": "", "id": "waNU3AzjNL", "identifier": "MULTIPLYSINGLEVALUES", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "hidden": true, "placeholder": "", "required": false}, "text": "MULTIPLY - SINGLE VALUES", "type": "text"}, {"description": "", "id": "bjvMa39Mww", "identifier": "MUTLIPLYTABLEVALUES", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "hidden": true, "placeholder": "", "required": false}, "text": "MUTLIPLY - TABLE VALUES", "type": "text"}, {"description": "", "id": "arIQUhZ3V7", "identifier": "MULTIPLYITERATOR", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "hidden": true, "placeholder": "", "required": false}, "text": "MULTIPLY - ITERATOR", "type": "text"}, {"description": "", "id": "FIGzzv8LYR", "identifier": "COUNTARRAY", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "hidden": true, "placeholder": "", "required": false}, "text": "COUNT - ARRAY", "type": "text"}, {"description": "", "id": "rxcs6pDarL", "identifier": "GETANSWERFROMPY", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "hidden": true, "placeholder": "", "required": false}, "text": "GET ANSWER FROM PY", "type": "text"}, {"description": "", "id": "qVHxJx2vCv", "identifier": "SUMIFSingleValue", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "hidden": true, "placeholder": "", "required": false}, "text": "SUMIF - Single Value", "type": "text"}, {"description": "", "id": "nUqJ82nwNl", "identifier": "DemoFlow", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "disabled": false, "hidden": true, "placeholder": "", "required": false}, "text": "Demo Flow", "type": "text"}], "id": "azm24Y7Dgk", "level": 2, "name": "General"}], "id": "jVH96DL8jW", "level": 1, "name": "Setup"}, {"content": [{"content": [{"description": "", "id": "pB7BSlhmKW", "identifier": "Results", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "avyyvIN7EH", "identifier": "<PERSON><PERSON><PERSON>", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "<PERSON><PERSON><PERSON>", "type": "text"}, {"description": "", "id": "awxQA8AtGn", "identifier": "Expected<PERSON><PERSON>ult", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Expected Result", "type": "text"}, {"description": "", "id": "bf4Ma7DWGF", "identifier": "Result", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Result", "type": "text"}, {"description": "", "id": "awtfyzxoLZ", "identifier": "PassFail", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "PASS"}, {"label": "", "value": "FAIL"}, {"label": "", "value": "NO RESULT"}], "placeholder": "", "required": false}, "text": "Pass/Fail", "type": "select"}], "required": false}, "text": "Results", "type": "table"}], "id": "y0Wsck1qjl", "level": 2, "name": "General"}], "id": "ASbysAz8xJ", "level": 1, "name": "Flows Testing - Results"}, {"content": [{"content": [{"description": "", "id": "CLE7acMs4f", "identifier": "DemoFlowLog", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "gpewhsGTMO", "identifier": "Log", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Log", "type": "text"}, {"description": "", "id": "Q1ESau3aVM", "identifier": "Result", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Result", "type": "text"}], "required": false}, "text": "DemoFlowLog", "type": "table"}], "id": "BCW42aukyL", "level": 2, "name": "General"}], "id": "zGJmLKuFv9", "level": 1, "name": "Demo Flow Log"}, {"content": [{"content": [{"description": "", "id": "FfKlGILPWz", "identifier": "DemoFlowResults", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "DWlkI9DVzT", "identifier": "<PERSON><PERSON><PERSON>", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "<PERSON><PERSON><PERSON>", "type": "text"}, {"description": "", "id": "MLaUbsxjF1", "identifier": "Expected<PERSON><PERSON>ult", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Expected Result", "type": "text"}, {"description": "", "id": "InOWD9ZV6z", "identifier": "Result", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Result", "type": "text"}, {"description": "", "id": "PEORVarfSN", "identifier": "PassFail", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "PASS"}, {"label": "", "value": "FAIL"}, {"label": "", "value": "NO RESULT"}], "placeholder": "", "required": false}, "text": "Pass/Fail", "type": "select"}], "required": false}, "text": "Demo Flow Results", "type": "table"}], "id": "ap0uZUHWAx", "level": 2, "name": "General"}], "id": "d2xRXWIM5o", "level": 1, "name": "Demo Flow Testing - Results"}], "description": "", "foundationId": "a3290a72ad744a4d84ab3b6a12e5461c4eb8", "id": "EISiO632dEeNfgsIyqykP", "key": "TR", "level": 0, "metadata": {"createdAt": "2025-03-18T04:07:04.793Z", "updatedAt": "2025-03-18T04:07:04.793Z"}, "name": "Test Runner", "seriesId": ""}, "SG14waZgbati8MISSWyVN": {"content": [{"content": [{"content": [{"description": "", "id": "tSDpCur4RF", "identifier": "NameOfEmployer", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Name of Employer", "type": "text"}, {"description": "", "id": "wrO3i1AQaR", "identifier": "AddressOfEmployer", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Address of Employer", "type": "text"}], "id": "zEnVgF5NsG", "level": 2, "name": "General"}], "id": "koM1EWu4Au", "level": 1, "name": "Employer Details"}, {"content": [{"content": [{"description": "", "id": "a3a8xLFwNd", "identifier": "PYPayrollData", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "PoU9TXoyju", "identifier": "Description", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Description", "type": "text"}, {"description": "", "id": "xIqaqEHuYe", "identifier": "TaxBorneByEmployer", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Tax Borne By Employer", "type": "boolean"}, {"description": "", "id": "aAPrhNEtXT", "identifier": "StandardMapping", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Standard Mapping", "type": "text"}, {"description": "", "id": "Nz9xzmAQSu", "identifier": "January", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "January", "type": "number"}, {"description": "", "id": "H2uCjaacUW", "identifier": "February", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "February", "type": "number"}, {"description": "", "id": "al8QaVaqkb", "identifier": "March", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "March", "type": "number"}, {"description": "", "id": "iU4Z45DFda", "identifier": "April", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "April", "type": "number"}, {"description": "", "id": "iNtZaM43z5", "identifier": "May", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "May", "type": "number"}, {"description": "", "id": "uuPaqKdFG2", "identifier": "June", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "June", "type": "number"}, {"description": "", "id": "btwjTzkRrK", "identifier": "July", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "July", "type": "number"}, {"description": "", "id": "H4a57idQib", "identifier": "August", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "August", "type": "number"}, {"description": "", "id": "aVk1CR6CXP", "identifier": "September", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "September", "type": "number"}, {"description": "", "id": "b8ya2VYiS2", "identifier": "October", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "October", "type": "number"}, {"description": "", "id": "bjqGcJFejl", "identifier": "November", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "November", "type": "number"}, {"description": "", "id": "HVJgjjhrAC", "identifier": "December", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false}, "text": "December", "type": "number"}, {"description": "", "id": "aDXsPqthxj", "identifier": "Total", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Total", "type": "number"}], "required": false}, "text": "PY Payroll data", "type": "table"}], "id": "eb5WUxbMdo", "level": 2, "name": "General"}], "id": "sThdDrrkAb", "level": 1, "name": "Prior Year Payroll Data"}, {"content": [{"content": [{"description": "", "id": "qO3xge5etd", "identifier": "CYPayrollData", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "MRcRCBayfx", "identifier": "Description", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Description", "type": "text"}, {"description": "", "id": "DYnqlhI7UM", "identifier": "StandardMapping", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Salary"}, {"label": "", "value": "Bonus"}, {"label": "", "value": "Equity"}, {"label": "", "value": "Director's fees"}, {"label": "", "value": "Gratuity"}, {"label": "", "value": "Pay in lieu of notice"}, {"label": "", "value": "Allowance"}, {"label": "", "value": "Benefits in kind"}, {"label": "", "value": "Employment Expense"}, {"label": "", "value": "Donations"}, {"label": "", "value": "Other"}], "placeholder": "", "required": false}, "text": "Standard Mapping", "type": "select"}, {"description": "", "id": "aNomVlFYS4", "identifier": "TaxBorneByEmployer", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Tax Borne By Employer", "type": "boolean"}, {"description": "", "id": "ZO21H6q7dI", "identifier": "January", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "January", "type": "number"}, {"description": "", "id": "adSD7Z9rBJ", "identifier": "February", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "February", "type": "number"}, {"description": "", "id": "dkZ4vfnj94", "identifier": "March", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "March", "type": "number"}, {"description": "", "id": "jfwcMLms9j", "identifier": "April", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "April", "type": "number"}, {"description": "", "id": "ztgLvL9Kb3", "identifier": "May", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "May", "type": "number"}, {"description": "", "id": "aAdqR24NVE", "identifier": "June", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "June", "type": "number"}, {"description": "", "id": "a05bQeaYc3", "identifier": "July", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "July", "type": "number"}, {"description": "", "id": "zGdeUMjLnS", "identifier": "August", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "August", "type": "number"}, {"description": "", "id": "QUMyeNUXyb", "identifier": "September", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "September", "type": "number"}, {"description": "", "id": "aquo4nxzFk", "identifier": "October", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "October", "type": "number"}, {"description": "", "id": "LrMvAqOKah", "identifier": "November", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "November", "type": "number"}, {"description": "", "id": "d8dbOdOm9h", "identifier": "December", "properties": {"allowReuseAcrossForms": false, "min": 0, "placeholder": "", "required": false, "type": "accounting"}, "text": "December", "type": "number"}, {"description": "", "id": "aKi6gOawF3", "identifier": "Total", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false, "type": "accounting"}, "text": "Total", "type": "number"}], "required": false}, "text": "CY Payroll Data", "type": "table"}], "id": "Z45RONalkg", "level": 2, "name": "General"}], "id": "XbinjU4ab7", "level": 1, "name": "Current Year Payroll Data"}, {"content": [{"content": [{"description": "", "id": "hDaERkazBA", "identifier": "CurrentYearCompensationAnalysis", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "Ah3EuTk1bR", "identifier": "PayrollDescription", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Payroll Description", "type": "text"}, {"description": "", "id": "iOLWzdaCOj", "identifier": "StandardMapping", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Salary"}, {"label": "", "value": "Bonus"}, {"label": "", "value": "Equity"}, {"label": "", "value": "Director's fees"}, {"label": "", "value": "Gratuity"}, {"label": "", "value": "Pay in lieu of notice"}, {"label": "", "value": "Allowance"}, {"label": "", "value": "Benefits in kind"}, {"label": "", "value": "Employment Expense"}, {"label": "", "value": "Donations"}, {"label": "", "value": "Other"}], "placeholder": "", "required": false}, "text": "Standard Mapping", "type": "select"}, {"description": "", "id": "fQVjfa2xHt", "identifier": "TaxBorneByER", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Tax Borne By ER", "type": "boolean"}, {"description": "", "id": "ac1pNirTze", "identifier": "SourcingPeriodStart", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Sourcing Period Start", "type": "date"}, {"description": "", "id": "jBqasTxyl2", "identifier": "SourcingPeriodEnd", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Sourcing Period End", "type": "date"}, {"description": "", "id": "OOK8GIkJVg", "identifier": "Amount", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Amount", "type": "number"}, {"description": "", "id": "pGsLVC62OH", "identifier": "DaysInSGDuringSourcingPeriod", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Days in SG during Sourcing Period", "type": "number"}, {"description": "", "id": "K8J5lh76BL", "identifier": "TotalDaysInSourcingPeriod", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Total Days in Sourcing Period", "type": "number"}, {"description": "", "id": "nKTz3jqg7E", "identifier": "SGSourceAmount", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false, "type": "accounting"}, "text": "SG Source Amount", "type": "number"}, {"description": "", "id": "WAvMl6VKwl", "identifier": "ForeignSourceAmount", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false, "type": "accounting"}, "text": "Foreign Source Amount", "type": "number"}], "required": false}, "text": "Current Year Compensation Analysis", "type": "table"}, {"description": "", "id": "vRNGl6Xxxb", "identifier": "RunCompAnalysisFlow", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "hidden": true, "placeholder": "", "required": false}, "text": "Run Comp Analysis Flow", "type": "text"}], "id": "DxjcO0XJ4a", "level": 2, "name": "General"}], "id": "acGcyn8dTl", "level": 1, "name": "Compensation Analysis"}, {"content": [{"content": [{"description": "", "id": "A9PZ0cxvav", "identifier": "GrossSalary", "properties": {"allowReuseAcrossForms": false, "hidden": false, "placeholder": "", "required": false}, "text": "Gross Salary, Fees, Leave Pay, Wages and Overtime Pay ", "type": "number"}, {"description": "", "id": "NgX8CIqfBq", "identifier": "Bonus", "properties": {"allowReuseAcrossForms": false, "hidden": false, "placeholder": "", "required": false}, "text": "Bonus ", "type": "number"}, {"description": "", "id": "a3RbMg5asL", "identifier": "DirectorsFees", "properties": {"allowReuseAcrossForms": false, "hidden": false, "placeholder": "", "required": false}, "text": "Director’s fees ", "type": "number"}, {"description": "", "id": "z1BbXlIjFh", "identifier": "Allowances", "properties": {"allowReuseAcrossForms": false, "hidden": false, "placeholder": "", "required": false}, "text": "Allowances", "type": "number"}, {"description": "", "id": "NodtWWRePe", "identifier": "TotalIncome", "properties": {"allowReuseAcrossForms": false, "hidden": false, "placeholder": "", "required": false, "type": "accounting"}, "text": "Total Income", "type": "number"}, {"description": "", "id": "hAJuHuyjwx", "identifier": "CompensationSummary", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "wyv5AiSveD", "identifier": "IncomeItem", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Income Item", "type": "text"}, {"description": "", "id": "acGhopiihu", "identifier": "SGSource", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "SG Source", "type": "number"}, {"description": "", "id": "d4oMDINbmM", "identifier": "ForeignSource", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Foreign Source", "type": "number"}, {"description": "", "id": "TWpGz9Vmmx", "identifier": "AmountBorneByER", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Amount borne by ER", "type": "number"}, {"description": "", "id": "jKpaBjVFaG", "identifier": "AmountBorneByEE", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Amount borne by EE", "type": "number"}], "required": false}, "text": "Compensation Summary", "type": "table"}], "id": "RU45bYgHjf", "level": 2, "name": "General"}], "id": "fN5BI2aha2", "level": 1, "name": "Compensation Summary"}, {"content": [{"content": [{"description": "", "id": "W2DQN8KGqK", "identifier": "CompulsoryCPFContribution", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Employee's contribution to CPF/Designated Pension or Provident Fund", "type": "number"}, {"description": "", "id": "tGLcQ9In2c", "identifier": "NameOfFund", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Name of Fund", "type": "text"}, {"description": "", "id": "fBtznPQJ73", "identifier": "Donations", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Donations", "type": "number"}, {"description": "", "id": "g7j2EdHA1Y", "identifier": "ContributionsToMosqueBuildingFund", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Contributions to Mosque Building Fund", "type": "number"}, {"description": "", "id": "FwJJmea5bF", "identifier": "LifeInsurancePremiumsDeductedFromSalaries", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Life Insurance premiums deducted from salaries", "type": "number"}], "id": "xI2XLN8XtP", "level": 2, "name": "General"}], "id": "v65lQqaVss", "level": 1, "name": "Deductions"}, {"content": [{"content": [{"description": "", "id": "fChLYph93m", "identifier": "TotalIncomeBorneByEmployer", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Total Income Borne By Employer", "type": "number"}, {"description": "", "id": "EXpJVykejE", "identifier": "GrossUp", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false, "type": "accounting"}, "text": "Gross Up", "type": "number"}], "id": "H3TyYv0mnj", "level": 2, "name": "General"}], "id": "JxIMZXQIlh", "level": 1, "name": "Tax  Payment"}], "description": "", "foundationId": "x00VHaApOAVpOa8VKFkBC", "id": "SG14waZgbati8MISSWyVN", "key": "PIT", "level": 0, "metadata": {"createdAt": "2025-02-28T00:59:46.170Z", "updatedAt": "2025-02-28T00:59:46.170Z"}, "name": "Personal Income Tax Return", "seriesId": "aYRZznoOYF37z5UIuYfr1"}, "U3mqHJmaz9kEdWsGISca6": {"content": [{"content": [{"content": [{"description": "", "id": "d6Bj8jC6bF", "identifier": "AssignmentInformation", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "GLIi2p53sD", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Assignee Name", "type": "text"}, {"description": "", "id": "N9Vtm6yxrO", "identifier": "AssignmentPolicy", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Assignment Policy", "type": "text"}, {"description": "", "id": "afZ5brTCaW", "identifier": "HomeCountry", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Home Country", "type": "text"}, {"description": "", "id": "C2Ti5Bb0S7", "identifier": "HostCountry", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Host Country", "type": "text"}, {"description": "", "id": "aiennauvcx", "identifier": "AssignmentStart", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Assignment Start", "type": "date"}, {"description": "", "id": "Uc867T509C", "identifier": "AssignmentEnd", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Assignment End", "type": "date"}], "required": false}, "text": "Assignment Information", "type": "table"}], "id": "adDGHz9NcO", "level": 2, "name": "General"}], "id": "t0lDuOTdLa", "level": 1, "name": "Assignment Information"}, {"content": [{"content": [{"description": "", "id": "Qg1l7LvjeN", "identifier": "AuthorisationList", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "a2qwFnLFof", "identifier": "Years", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Year", "type": "text"}, {"description": "", "id": "TroPGwZZrc", "identifier": "AssigneeID", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Assignee ID", "type": "text"}, {"description": "", "id": "I1tiDtrnAi", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Assignee Name", "type": "text"}, {"description": "", "id": "on6bf5hai4", "identifier": "AssignmentPolicy", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Assignment Policy", "type": "text"}, {"description": "", "id": "nndTff2sKk", "identifier": "PolicyType", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Policy Type", "type": "text"}, {"description": "", "id": "ixLE34c666", "identifier": "AuthorisationTaxReturn", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Authorisation - Tax Return", "type": "boolean"}, {"description": "", "id": "jYqWsMZXO5", "identifier": "AuthorisationTaxBriefing", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Authorisation - Tax Briefing", "type": "boolean"}, {"description": "", "id": "RhfF2TFKeJ", "identifier": "AuthorisationReason", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Authorisation Reason", "type": "text"}, {"description": "", "id": "utYgarXaKa", "identifier": "AuthorisationSpouseTaxReturn", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Authorisation - Spouse Tax Return", "type": "boolean"}], "required": false}, "text": "Authorisation List", "type": "table"}, {"description": "", "id": "a2Kh0a6jFL", "identifier": "YearForA<PERSON>roval", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "2024"}, {"label": "", "value": "2025"}, {"label": "", "value": "2026"}], "placeholder": "", "required": false}, "text": "Year for Approval", "type": "select"}], "id": "GaV0ZfWvz4", "level": 2, "name": "General"}], "id": "GY5XZD5YD6", "level": 1, "name": "Authorisation"}], "description": "", "foundationId": "D1axfoA5lvjOQ0uezN05G", "id": "U3mqHJmaz9kEdWsGISca6", "key": "AUTHLIST", "level": 0, "metadata": {"createdAt": "2025-03-10T03:49:17.142Z", "updatedAt": "2025-03-10T03:49:17.142Z"}, "name": "Authorisation List", "seriesId": ""}, "V0UCzZ49ccFOYy39zOPOh": {"content": [{"content": [{"content": [{"description": "", "id": "M7aZ2r5d5O", "identifier": "FirstName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "First Name", "type": "text"}, {"description": "", "id": "jc2EKPHZeO", "identifier": "LastName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Last Name", "type": "text"}, {"description": "", "id": "mGsSaXhlko", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Email Address", "type": "text"}, {"description": "", "id": "UC7zkaoKJf", "identifier": "PhoneNumber", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Phone Number", "type": "text"}, {"description": "", "id": "N9r0FCEuXG", "identifier": "DateOfBirth", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Date of Birth", "type": "date"}], "id": "xqZC7r5JOa", "level": 2, "name": "General"}], "id": "adXEbGKF3I", "level": 1, "name": "Personal Information"}, {"content": [{"content": [{"description": "", "id": "pNamzZFMPs", "identifier": "SpouseFirstName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Spouse - First Name", "type": "text"}, {"description": "", "id": "M5J1jq50rt", "identifier": "SpouseLastName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Spouse - Last Name", "type": "text"}, {"description": "", "id": "QPiOmtWq42", "identifier": "SpouseEmailAddress", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Spouse - Email address", "type": "text"}, {"description": "", "id": "azW2Leq1uV", "identifier": "SpousePhoneNumber", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Spouse - Phone Number", "type": "text"}, {"description": "", "id": "aAmINOqdCh", "identifier": "SpouseDateOfBirth", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Spouse - Date of Birth", "type": "date"}], "id": "dufsKxg0J4", "level": 2, "name": "General"}], "id": "L5jw0IckLh", "level": 1, "name": "Spouse Name"}, {"content": [{"content": [{"description": "", "id": "HCHjzXNeYG", "identifier": "Children", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "eUOLT8y1Vv", "identifier": "FirstName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "First Name", "type": "text"}, {"description": "", "id": "jxlBeW2ha0", "identifier": "LastName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Last Name", "type": "text"}, {"description": "", "id": "FtiSm4DQvQ", "identifier": "DateOfBirth", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Date of Birth", "type": "date"}, {"description": "", "id": "qHaWuvPDXZ", "identifier": "Age", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Age", "type": "number"}, {"description": "", "id": "aTKM1vuyGN", "identifier": "Disabled", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Disabled?", "type": "boolean"}], "required": false}, "text": "Children", "type": "table"}], "id": "pdf6B7Bg2p", "level": 2, "name": "General"}], "id": "Ibdr8xRava", "level": 1, "name": "Children"}, {"content": [{"content": [{"description": "", "id": "OmnbNebAJk", "identifier": "EmployerDetails", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "KZlEQd0HwB", "identifier": "EmployerName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Employer Name", "type": "text"}, {"description": "", "id": "jUEUBHX4vW", "identifier": "EmploymentStartDate", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Employment Start Date", "type": "date"}, {"description": "", "id": "aazPbaVr9U", "identifier": "EmploymentEndDate", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Employment End Date", "type": "date"}], "required": false}, "text": "Employers", "type": "table"}], "id": "rCEuHQAjaL", "level": 2, "name": "General"}], "id": "zKa06ppl5F", "level": 1, "name": "Employer Details"}, {"content": [{"content": [{"description": "", "id": "T7IoTXkA0l", "identifier": "AssignmentDetails", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "aXgaVheZhu", "identifier": "AssignmentPolicy", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Assignment Policy", "type": "text"}, {"description": "", "id": "MGJXlvwkBj", "identifier": "HomeCountry", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Australia"}, {"label": "", "value": "Singapore"}], "placeholder": "", "required": false}, "text": "Home Country", "type": "select"}, {"description": "", "id": "apRPubvsrG", "identifier": "HostCountry", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Australia"}, {"label": "", "value": "Singapore"}], "placeholder": "", "required": false}, "text": "Host Country", "type": "select"}, {"description": "", "id": "av7k23SmGg", "identifier": "AssignmentStart", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Assignment Start", "type": "date"}, {"description": "", "id": "VuaSF5PWY9", "identifier": "AssignmentEnd", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Assignment End", "type": "date"}], "required": false}, "text": "Assignment Details", "type": "table"}], "id": "whoPgKi7s6", "level": 2, "name": "General"}], "id": "SYa9adD7wa", "level": 1, "name": "Assignment Details"}], "description": "", "foundationId": "x00VHaApOAVpOa8VKFkBC", "id": "V0UCzZ49ccFOYy39zOPOh", "key": "AP", "level": 0, "metadata": {"createdAt": "2025-03-06T00:35:17.215Z", "updatedAt": "2025-03-06T00:35:17.215Z"}, "name": "Assignee Profile", "seriesId": ""}, "eatFwcRAAByzHwV92fmEf": {"content": [{"content": [{"content": [{"description": "", "id": "Dal4SiZ9kT", "identifier": "Untitled", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Untitled", "type": "text"}], "id": "NKzja81jxT", "level": 2, "name": "General"}], "id": "AIC0jMdnrd", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "daYJS2xq9fkLHcaY1YG7H", "id": "eatFwcRAAByzHwV92fmEf", "key": "CLF", "level": 0, "metadata": {"createdAt": "2025-03-05T04:50:54.724Z", "updatedAt": "2025-03-05T04:50:54.724Z"}, "name": "Client Group Level Form", "seriesId": ""}, "gqqgE6trl8d5GQsL4e8b4": {"content": [{"content": [{"content": [{"description": "", "id": "lZxjZXNqEx", "identifier": "EmployerName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": true}, "text": "Employer Name", "type": "text"}, {"description": "", "id": "X2wC83iZtS", "identifier": "UEN", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": true}, "text": "UEN", "type": "text"}, {"description": "", "id": "PnlzKKywTT", "identifier": "AddressOfEmployer", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isTextArea": true, "placeholder": "", "required": true}, "text": "Address of Employer", "type": "text"}, {"description": "", "id": "OHANAySUV6", "identifier": "Contacts", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "akNUS4Sc77", "identifier": "Name", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Name", "type": "text"}, {"description": "", "id": "HEIDghwbyL", "identifier": "Designation", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Designation", "type": "text"}, {"description": "", "id": "IRdf93igDx", "identifier": "Email", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Email", "type": "text"}], "required": false}, "text": "Contacts", "type": "table"}], "id": "fNaKdzYY3f", "level": 2, "name": "General"}], "id": "hB4mMo3XkY", "level": 1, "name": "Basic Information"}, {"content": [{"content": [{"description": "", "id": "XfgXa6ooAm", "identifier": "AuthorisationPolicy", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "Pl2VZbKell", "identifier": "AssignmentPolicy", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Assignment Policy ", "type": "text"}, {"description": "", "id": "ogLzuKJAFY", "identifier": "PolicyType", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Short Term Assignment"}, {"label": "", "value": "Long Term Assignment"}, {"label": "", "value": "Permanent Transfer"}, {"label": "", "value": "Rotational Assignment"}], "placeholder": "", "required": false}, "text": "Policy Type", "type": "select"}, {"description": "", "id": "KMaXT0b7aW", "identifier": "TaxEqualised", "properties": {"allowReuseAcrossForms": false, "falseText": "No", "required": false, "trueText": "Yes"}, "text": "Tax Equalised?", "type": "boolean"}, {"description": "", "id": "awsbsW5vEf", "identifier": "Authorisation", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"key": 0, "label": "", "value": "All years on assignment"}, {"key": 3, "label": "", "value": "All years on assignment plus trailing income"}, {"key": 1, "label": "", "value": "First year only"}, {"key": 2, "label": "", "value": "First and last year only"}], "placeholder": "", "required": false}, "text": "Authorisation", "type": "select"}, {"description": "", "id": "FIB0l1GDQv", "identifier": "SpouseAuthorisation", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Not authorised"}, {"label": "", "value": "First year only"}, {"label": "", "value": "Same as assignee"}], "placeholder": "", "required": false}, "text": "Spouse Authorisation", "type": "select"}], "required": false}, "text": "Authorisation Policy", "type": "table"}, {"description": "", "id": "aqAohrlqQ2", "identifier": "Assignees", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "NygKq36atr", "identifier": "AssigneeID", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Assignee ID", "type": "text"}, {"description": "", "id": "exI36T75Gq", "identifier": "FirstName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "First Name", "type": "text"}, {"description": "", "id": "aiCtHRlS00", "identifier": "LastName", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Last Name", "type": "text"}, {"description": "", "id": "avKORamTMA", "identifier": "AssignmentPolicy", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Assignment Policy", "type": "text"}, {"description": "", "id": "oR3hExeYiL", "identifier": "HomeCountry", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Australia"}, {"label": "", "value": "Singapore"}], "placeholder": "", "required": false}, "text": "Home Country", "type": "select"}, {"description": "", "id": "VmlDFbLJKZ", "identifier": "HostCountry", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Australia"}, {"label": "", "value": "Singapore"}], "placeholder": "", "required": false}, "text": "Host Country", "type": "select"}, {"description": "", "id": "WSHCcFlExG", "identifier": "AssignmentStartDate", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Assignment Start Date", "type": "date"}, {"description": "", "id": "waeNppHU1u", "identifier": "AssignmentEndDate", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Assignment End Date", "type": "date"}], "required": false}, "text": "Assignees", "type": "table"}], "id": "vKPf0a2WX9", "level": 2, "name": "General"}], "id": "h4dgVVbGYr", "level": 1, "name": "Authorisation"}, {"content": [{"content": [{"description": "", "id": "SoBlBkh5ZM", "identifier": "CompensationDataMapping", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "aVg591B27j", "identifier": "PayrollDescription", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Payroll Description", "type": "text"}, {"description": "", "id": "XaADpaR1Sf", "identifier": "StandardMapping", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"key": 0, "label": "", "value": "Salary"}, {"key": 1, "label": "", "value": "Bonus"}, {"key": 10, "label": "", "value": "Equity"}, {"key": 2, "label": "", "value": "Director's fees"}, {"key": 3, "label": "", "value": "Gratuity"}, {"key": 4, "label": "", "value": "Pay in lieu of notice"}, {"key": 5, "label": "", "value": "Allowance"}, {"key": 6, "label": "", "value": "Benefits in kind"}, {"key": 7, "label": "", "value": "Employment Expense"}, {"key": 8, "label": "", "value": "Donations"}, {"key": 9, "label": "", "value": "Other"}], "placeholder": "", "required": false}, "text": "Standard Mapping", "type": "select"}, {"description": "", "id": "X4KTqYVmaV", "identifier": "TaxBorneByER", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Tax Borne By ER", "type": "boolean"}, {"description": "", "id": "qUpy6BD0Yk", "identifier": "GrossUp", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Gross Up", "type": "boolean"}], "required": false}, "text": "Compensation Data Mapping", "type": "table"}], "id": "hoC85wfOIX", "level": 2, "name": "General"}], "id": "cntNvFsPOL", "level": 1, "name": "Compensation Data Mapping"}], "description": "", "foundationId": "daYJS2xq9fkLHcaY1YG7H", "id": "gqqgE6trl8d5GQsL4e8b4", "key": "CLIENTPOSITIONS", "level": 0, "metadata": {"createdAt": "2025-02-27T05:30:45.060Z", "updatedAt": "2025-02-27T05:30:45.060Z"}, "name": "Client Information", "seriesId": ""}, "oXUOfkMKayra9lBi5NOvj": {"content": [{"content": [{"content": [{"description": "", "id": "Tt9L06N948", "identifier": "Table", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "wMthkQKZXy", "identifier": "TextColumn1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text Column 1", "type": "text"}, {"description": "", "id": "wHhQcqKPvo", "identifier": "TextColumn2", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text Column 2", "type": "text"}, {"description": "", "id": "X60WRatO9M", "identifier": "TextColumn3", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text Column 3", "type": "text"}, {"description": "", "id": "ROMCsqfEVl", "identifier": "NumberColumn1", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number Column 1", "type": "number"}, {"description": "", "id": "IxrosLRLnX", "identifier": "NumberColumn2", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number Column 2", "type": "number"}, {"description": "", "id": "UbeUf70j1C", "identifier": "NumberColumn3", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number Column 3", "type": "number"}], "required": false}, "text": "Table", "type": "table"}, {"description": "", "id": "n96ZMA9IwA", "identifier": "Text1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text 1", "type": "text"}, {"description": "", "id": "x39DSHJnGO", "identifier": "Text2", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text 2", "type": "text"}, {"description": "", "id": "w1FWFfddba", "identifier": "Number1", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number 1", "type": "number"}, {"description": "", "id": "aAjV4Ryb0x", "identifier": "Number2", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number 2", "type": "number"}, {"description": "", "id": "eXoUzbPlns", "identifier": "Date1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Date 1", "type": "date"}, {"description": "", "id": "aFtatmzZHA", "identifier": "Date2", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Date 2", "type": "text"}, {"description": "", "id": "X2T3j281Sy", "identifier": "Select1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Option 1"}, {"label": "", "value": "Option 2"}], "placeholder": "", "required": false}, "text": "Select 1", "type": "select"}, {"description": "", "id": "nzVkykwsa8", "identifier": "Select2", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Option 1"}, {"label": "", "value": "Option 2"}], "placeholder": "", "required": false}, "text": "Select 2", "type": "select"}, {"description": "", "id": "a9kaeBskvg", "identifier": "File1", "properties": {"allowReuseAcrossForms": false, "max": 10, "maxFileSizeMB": 100, "required": false}, "text": "File 1", "type": "files"}, {"description": "", "id": "T58Lm7QOCg", "identifier": "File2", "properties": {"allowReuseAcrossForms": false, "max": 10, "maxFileSizeMB": 100, "required": false}, "text": "File 2", "type": "files"}, {"description": "", "id": "wUWCpxqIJ4", "identifier": "TemplateFile", "properties": {"allowReuseAcrossForms": false, "max": 10, "maxFileSizeMB": 100, "required": false}, "text": "Template File", "type": "files"}], "id": "HPMUI8qtks", "level": 2, "name": "General"}], "id": "a0qvzzUS9g", "level": 1, "name": "Inputs"}, {"content": [{"content": [{"description": "", "id": "r05KtVT7D6", "identifier": "Table2", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "pEi5LKU1ak", "identifier": "TextColumn1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text Column 1", "type": "text"}, {"description": "", "id": "i9a8yB3AF1", "identifier": "TextColumn2", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Text Column 2", "type": "number"}, {"description": "", "id": "uKeRmnKYar", "identifier": "TextColumn3", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text Column 3", "type": "text"}, {"description": "", "id": "ZU0D8QbAYM", "identifier": "NumberColumn1", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number Column 1", "type": "number"}, {"description": "", "id": "UQVAlHfWce", "identifier": "NumberColumn2", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Number Column 2", "type": "text"}, {"description": "", "id": "hwwdKbcezv", "identifier": "NumberColumn3", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number Column 3", "type": "number"}], "required": false}, "text": "Table 2", "type": "table"}, {"description": "", "id": "t60PjuSTaB", "identifier": "Text1_1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Text 1", "type": "text"}, {"description": "", "id": "Ig6hhxmKmf", "identifier": "Number1_1", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "required": false}, "text": "Number 1", "type": "number"}, {"description": "", "id": "wmh80dsYYl", "identifier": "Date1_1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "required": false}, "text": "Date 1", "type": "date"}, {"description": "", "id": "cqJsH01A6G", "identifier": "Options1_1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "Option 1"}, {"label": "", "value": "Option 2"}, {"label": "", "value": "No Option"}], "placeholder": "", "required": false}, "text": "Options 1", "type": "select"}, {"description": "", "id": "u1jmatoaMO", "identifier": "YesNo1_1", "properties": {"allowReuseAcrossForms": false, "required": false}, "text": "Yes No 1", "type": "boolean"}, {"description": "", "id": "iaHhwo7Cgh", "identifier": "File1_1", "properties": {"allowReuseAcrossForms": false, "max": 10, "maxFileSizeMB": 100, "required": false}, "text": "File 1", "type": "files"}], "id": "pKQGjLrFaV", "level": 2, "name": "General"}], "id": "rMAP1eqxEt", "level": 1, "name": "Results"}], "description": "", "foundationId": "x00VHaApOAVpOa8VKFkBC", "id": "oXUOfkMKayra9lBi5NOvj", "key": "ALF", "level": 0, "metadata": {"createdAt": "2025-03-05T04:51:30.553Z", "updatedAt": "2025-03-05T04:51:30.553Z"}, "name": "Assignee Level Form", "seriesId": "aYRZznoOYF37z5UIuYfr1"}, "z7690ou099uWbug54HSBU": {"content": [{"content": [{"content": [{"description": "", "id": "YznzdrFg1a", "identifier": "Results", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "iGsDLQIZwu", "identifier": "<PERSON><PERSON><PERSON>", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "<PERSON><PERSON><PERSON>", "type": "text"}, {"description": "", "id": "CNr8EpqHcC", "identifier": "Expected<PERSON><PERSON>ult", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Expected Result", "type": "text"}, {"description": "", "id": "kehO4VyB1W", "identifier": "Result", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Result", "type": "text"}, {"description": "", "id": "Kg2EJvwFYm", "identifier": "PassFail", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "PASS"}, {"label": "", "value": "FAIL"}, {"label": "", "value": "NO RESULT"}], "placeholder": "", "required": false}, "text": "Pass / Fail", "type": "select"}], "required": false}, "text": "Results", "type": "table"}], "id": "cKOvnxYts8", "level": 2, "name": "General"}], "id": "lSg8DXAAgO", "level": 1, "name": "Test Results"}, {"content": [{"content": [{"description": "", "id": "XcrkuM1qZx", "identifier": "DemoFlowLog", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "aGSrahCW6l", "identifier": "Log", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Log", "type": "text"}, {"description": "", "id": "zCajNN5fdF", "identifier": "Result", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Result", "type": "text"}], "required": false}, "text": "Demo Flow Log", "type": "table"}], "id": "mcSDsyd1T8", "level": 2, "name": "General"}], "id": "rAGONk9xro", "level": 1, "name": "Demo Flow Log"}, {"content": [{"content": [{"description": "", "id": "WSAnWbMCKB", "identifier": "DemoFlowResults", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "sZB6FObFGc", "identifier": "<PERSON><PERSON><PERSON>", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "<PERSON><PERSON><PERSON>", "type": "text"}, {"description": "", "id": "fTDXL3TLRW", "identifier": "Expected<PERSON><PERSON>ult", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Expected Result", "type": "text"}, {"description": "", "id": "DLyahR7WrA", "identifier": "Result", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Result", "type": "text"}, {"description": "", "id": "aO9VZWmBfL", "identifier": "PassFail", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "PASS"}, {"label": "", "value": "FAIL"}, {"label": "", "value": "NO RESULT"}], "placeholder": "", "required": false}, "text": "Pass/Fail", "type": "select"}], "required": false}, "text": "Demo Flow Results", "type": "table"}], "id": "aySTFpRuWE", "level": 2, "name": "General"}], "id": "X5VTccEg7o", "level": 1, "name": "Demo Flow Results"}], "description": "", "foundationId": "D1axfoA5lvjOQ0uezN05G", "id": "z7690ou099uWbug54HSBU", "key": "TESTREPORT", "level": 0, "metadata": {"createdAt": "2025-03-24T02:35:42.909Z", "updatedAt": "2025-03-24T02:35:42.909Z"}, "name": "Test Report", "seriesId": ""}}, "foundations": {"entities": {"a3290a72ad744a4d84ab3b6a12e5461c4eb8": {"id": "a3290a72ad744a4d84ab3b6a12e5461c4eb8", "metadata": {"createdAt": "2025-03-05T04:49:59.915196092Z", "updatedAt": "2025-03-05T04:49:59.915196092Z"}, "name": "Workspace", "relationship": "OneToMany"}, "D1axfoA5lvjOQ0uezN05G": {"description": "", "id": "D1axfoA5lvjOQ0uezN05G", "metadata": {"createdAt": "2025-03-05T04:50:04.581Z", "updatedAt": "2025-03-05T04:50:04.581Z"}, "name": "Client", "relationship": "OneToMany"}, "daYJS2xq9fkLHcaY1YG7H": {"description": "", "id": "daYJS2xq9fkLHcaY1YG7H", "metadata": {"createdAt": "2025-03-05T04:50:08.446Z", "updatedAt": "2025-03-05T04:50:08.446Z"}, "name": "Client Group", "relationship": "OneToMany"}, "x00VHaApOAVpOa8VKFkBC": {"description": "", "id": "x00VHaApOAVpOa8VKFkBC", "metadata": {"createdAt": "2025-03-05T04:50:17.532Z", "updatedAt": "2025-03-05T04:50:17.532Z"}, "name": "Assignee", "relationship": "OneToMany"}}, "order": ["a3290a72ad744a4d84ab3b6a12e5461c4eb8", "D1axfoA5lvjOQ0uezN05G", "daYJS2xq9fkLHcaY1YG7H", "x00VHaApOAVpOa8VKFkBC"]}, "id": 3, "key": "TR", "labels": {"IckoggTcyJCMaHkbb4sI5": {"availableTo": ["formConfiguration", "flowConfiguration"], "color": "3", "description": "", "id": "IckoggTcyJCMaHkbb4sI5", "metadata": {"createdAt": "2025-04-14T03:30:39.826Z", "updatedAt": "2025-04-14T03:30:39.826Z"}, "name": "Flows Testing"}, "f4V7hhKY0WrW7BexzpwSQ": {"availableTo": ["formConfiguration", "flowConfiguration"], "color": "4", "description": "", "id": "f4V7hhKY0WrW7BexzpwSQ", "metadata": {"createdAt": "2025-04-14T03:33:14.742Z", "updatedAt": "2025-04-14T03:33:14.742Z"}, "name": "Test Runner"}, "ow2DwQbCNv3oC6LJKS4cE": {"availableTo": ["flowConfiguration", "formConfiguration"], "color": "2", "description": "", "id": "ow2DwQbCNv3oC6LJKS4cE", "metadata": {"createdAt": "2025-04-14T03:30:33.515Z", "updatedAt": "2025-04-14T03:32:42.852Z"}, "name": "<PERSON><PERSON>"}}, "metadata": {"createdAt": "2025-03-05T04:49:59.915274492Z", "updatedAt": "2025-03-05T04:49:59.915275092Z"}, "name": "Test Runner", "series": {"aYRZznoOYF37z5UIuYfr1": {"description": "", "id": "aYRZznoOYF37z5UIuYfr1", "intervals": {"entities": {"ZJWjjm2p6EaqyEG1esaY4": {"id": "ZJWjjm2p6EaqyEG1esaY4", "name": "2025"}, "nfwPMnwqpUvfaLCUwMqe6": {"id": "nfwPMnwqpUvfaLCUwMqe6", "name": "2024"}}, "order": ["nfwPMnwqpUvfaLCUwMqe6", "ZJWjjm2p6EaqyEG1esaY4"]}, "metadata": {"createdAt": "2025-03-05T04:51:06.467Z", "updatedAt": "2025-03-05T04:51:06.467Z"}, "name": "Year"}}, "type": "WORKSPACE_CONFIGURATION"}