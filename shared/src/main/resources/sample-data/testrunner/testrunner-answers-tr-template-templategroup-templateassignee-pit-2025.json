{"answers": {"a3a8xLFwNd": {"questionId": "a3a8xLFwNd", "type": "table", "value": {"entities": {"aUUMmaB76hsaI1j5vXj7G": {"columns": {}, "id": "aUUMmaB76hsaI1j5vXj7G"}}, "order": ["aUUMmaB76hsaI1j5vXj7G"]}}, "aqAohrlqQ2": {"questionId": "aqAohrlqQ2", "type": "table", "value": {"entities": {"yqalFRGSKydst4sww6Nq6": {"columns": {}, "id": "yqalFRGSKydst4sww6Nq6"}}, "order": ["yqalFRGSKydst4sww6Nq6"]}}, "HCHjzXNeYG": {"questionId": "HCHjzXNeYG", "type": "table", "value": {"entities": {"Cc5blja1lBao0rSqyda8v": {"columns": {}, "id": "Cc5blja1lBao0rSqyda8v"}}, "order": ["Cc5blja1lBao0rSqyda8v"]}}, "OHANAySUV6": {"questionId": "OHANAySUV6", "type": "table", "value": {"entities": {"agb1ZX8fHw7TaF1q2FSEu": {"columns": {}, "id": "agb1ZX8fHw7TaF1q2FSEu"}}, "order": ["agb1ZX8fHw7TaF1q2FSEu"]}}, "OmnbNebAJk": {"questionId": "OmnbNebAJk", "type": "table", "value": {"entities": {"ejAi7i1lyCp8IaPyLFaC5": {"columns": {}, "id": "ejAi7i1lyCp8IaPyLFaC5"}}, "order": ["ejAi7i1lyCp8IaPyLFaC5"]}}, "Qg1l7LvjeN": {"questionId": "Qg1l7LvjeN", "type": "table", "value": {"entities": {"EdYLc86WxIDtcg0kFVqr2": {"columns": {}, "id": "EdYLc86WxIDtcg0kFVqr2"}}, "order": ["EdYLc86WxIDtcg0kFVqr2"]}}, "SoBlBkh5ZM": {"questionId": "SoBlBkh5ZM", "type": "table", "value": {"entities": {"a8qeqmWbfJbaKDItnsSfr": {"columns": {}, "id": "a8qeqmWbfJbaKDItnsSfr"}}, "order": ["a8qeqmWbfJbaKDItnsSfr"]}}, "T7IoTXkA0l": {"questionId": "T7IoTXkA0l", "type": "table", "value": {"entities": {"HRgpiXGlpAnN2stQ8zMSV": {"columns": {}, "id": "HRgpiXGlpAnN2stQ8zMSV"}}, "order": ["HRgpiXGlpAnN2stQ8zMSV"]}}, "XfgXa6ooAm": {"questionId": "XfgXa6ooAm", "type": "table", "value": {"entities": {"aD0EYHzPphOKLJMiLRacD": {"columns": {}, "id": "aD0EYHzPphOKLJMiLRacD"}}, "order": ["aD0EYHzPphOKLJMiLRacD"]}}, "d6Bj8jC6bF": {"questionId": "d6Bj8jC6bF", "type": "table", "value": {"entities": {"d3fF4g3LST5afrZxsQD1E": {"columns": {}, "id": "d3fF4g3LST5afrZxsQD1E"}}, "order": ["d3fF4g3LST5afrZxsQD1E"]}}, "hAJuHuyjwx": {"questionId": "hAJuHuyjwx", "type": "table", "value": {"entities": {"ZyvdoVPLpq7TNVTbsqglY": {"columns": {}, "id": "ZyvdoVPLpq7TNVTbsqglY"}}, "order": ["ZyvdoVPLpq7TNVTbsqglY"]}}, "hDaERkazBA": {"questionId": "hDaERkazBA", "type": "table", "value": {"entities": {"aiJqphQomrNH0fxM4HZRg": {"columns": {}, "id": "aiJqphQomrNH0fxM4HZRg"}}, "order": ["aiJqphQomrNH0fxM4HZRg"]}}, "qO3xge5etd": {"questionId": "qO3xge5etd", "type": "table", "value": {"entities": {"aki7R81uZPc5KedwsCqs8": {"columns": {"a05bQeaYc3": {"questionId": "a05bQeaYc3", "type": "number", "value": "0.0"}, "aAdqR24NVE": {"questionId": "aAdqR24NVE", "type": "number", "value": "0.0"}, "aNomVlFYS4": {"questionId": "aNomVlFYS4", "type": "boolean"}, "adSD7Z9rBJ": {"questionId": "adSD7Z9rBJ", "type": "number", "value": "0.0"}, "aKi6gOawF3": {"questionId": "aKi6gOawF3", "type": "number", "value": "24232.24"}, "aquo4nxzFk": {"questionId": "aquo4nxzFk", "type": "number", "value": "13561.6"}, "DYnqlhI7UM": {"questionId": "DYnqlhI7UM", "type": "select"}, "LrMvAqOKah": {"questionId": "LrMvAqOKah", "type": "number", "value": "0.0"}, "MRcRCBayfx": {"questionId": "MRcRCBayfx", "type": "text", "value": "DIVIDEND EQUIVALENT (US)"}, "QUMyeNUXyb": {"questionId": "QUMyeNUXyb", "type": "number", "value": "0.0"}, "ZO21H6q7dI": {"questionId": "ZO21H6q7dI", "type": "number", "value": "0.0"}, "d8dbOdOm9h": {"questionId": "d8dbOdOm9h", "type": "number", "value": "0.0"}, "dkZ4vfnj94": {"questionId": "dkZ4vfnj94", "type": "number", "value": "0.0"}, "jfwcMLms9j": {"questionId": "jfwcMLms9j", "type": "number", "value": "10670.64"}, "zGdeUMjLnS": {"questionId": "zGdeUMjLnS", "type": "number", "value": "0.0"}, "ztgLvL9Kb3": {"questionId": "ztgLvL9Kb3", "type": "number", "value": "0.0"}}, "id": "aki7R81uZPc5KedwsCqs8"}, "CjGWARCmCkmQneYhwZIyz": {"columns": {"a05bQeaYc3": {"questionId": "a05bQeaYc3", "type": "number", "value": "5000.0"}, "aAdqR24NVE": {"questionId": "aAdqR24NVE", "type": "number", "value": "5000.0"}, "aNomVlFYS4": {"questionId": "aNomVlFYS4", "type": "boolean"}, "adSD7Z9rBJ": {"questionId": "adSD7Z9rBJ", "type": "number", "value": "5000.0"}, "aKi6gOawF3": {"questionId": "aKi6gOawF3", "type": "number", "value": "60000.0"}, "aquo4nxzFk": {"questionId": "aquo4nxzFk", "type": "number", "value": "5000.0"}, "DYnqlhI7UM": {"questionId": "DYnqlhI7UM", "type": "select"}, "LrMvAqOKah": {"questionId": "LrMvAqOKah", "type": "number", "value": "5000.0"}, "MRcRCBayfx": {"questionId": "MRcRCBayfx", "type": "text", "value": "Base Salary for SDL (SG)"}, "QUMyeNUXyb": {"questionId": "QUMyeNUXyb", "type": "number", "value": "5000.0"}, "ZO21H6q7dI": {"questionId": "ZO21H6q7dI", "type": "number", "value": "5000.0"}, "d8dbOdOm9h": {"questionId": "d8dbOdOm9h", "type": "number", "value": "5000.0"}, "dkZ4vfnj94": {"questionId": "dkZ4vfnj94", "type": "number", "value": "5000.0"}, "jfwcMLms9j": {"questionId": "jfwcMLms9j", "type": "number", "value": "5000.0"}, "zGdeUMjLnS": {"questionId": "zGdeUMjLnS", "type": "number", "value": "5000.0"}, "ztgLvL9Kb3": {"questionId": "ztgLvL9Kb3", "type": "number", "value": "5000.0"}}, "id": "CjGWARCmCkmQneYhwZIyz"}, "hiazfS1njryF3YLLmF8OV": {"columns": {"a05bQeaYc3": {"questionId": "a05bQeaYc3", "type": "number", "value": "0.0"}, "aAdqR24NVE": {"questionId": "aAdqR24NVE", "type": "number", "value": "0.0"}, "aNomVlFYS4": {"questionId": "aNomVlFYS4", "type": "boolean"}, "adSD7Z9rBJ": {"questionId": "adSD7Z9rBJ", "type": "number", "value": "39375.0"}, "aKi6gOawF3": {"questionId": "aKi6gOawF3", "type": "number", "value": "157500.0"}, "aquo4nxzFk": {"questionId": "aquo4nxzFk", "type": "number", "value": "0.0"}, "DYnqlhI7UM": {"questionId": "DYnqlhI7UM", "type": "select"}, "LrMvAqOKah": {"questionId": "LrMvAqOKah", "type": "number", "value": "39375.0"}, "MRcRCBayfx": {"questionId": "MRcRCBayfx", "type": "text", "value": "VP DIR BONUS"}, "QUMyeNUXyb": {"questionId": "QUMyeNUXyb", "type": "number", "value": "0.0"}, "ZO21H6q7dI": {"questionId": "ZO21H6q7dI", "type": "number", "value": "0.0"}, "d8dbOdOm9h": {"questionId": "d8dbOdOm9h", "type": "number", "value": "0.0"}, "dkZ4vfnj94": {"questionId": "dkZ4vfnj94", "type": "number", "value": "0.0"}, "jfwcMLms9j": {"questionId": "jfwcMLms9j", "type": "number", "value": "0.0"}, "zGdeUMjLnS": {"questionId": "zGdeUMjLnS", "type": "number", "value": "39375.0"}, "ztgLvL9Kb3": {"questionId": "ztgLvL9Kb3", "type": "number", "value": "39375.0"}}, "id": "hiazfS1njryF3YLLmF8OV"}}, "order": ["CjGWARCmCkmQneYhwZIyz", "aki7R81uZPc5KedwsCqs8", "hiazfS1njryF3YLLmF8OV"]}}}, "id": 15}