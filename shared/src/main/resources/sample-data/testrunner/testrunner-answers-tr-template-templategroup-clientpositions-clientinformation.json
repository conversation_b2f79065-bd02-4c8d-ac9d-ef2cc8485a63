{"answers": {"aqAohrlqQ2": {"questionId": "aqAohrlqQ2", "type": "table", "value": {"entities": {"ainklsaS47Iha3PaW7Gka": {"columns": {"avKORamTMA": {"questionId": "avKORamTMA", "type": "text", "value": "Transfer"}, "aiCtHRlS00": {"questionId": "aiCtHRlS00", "type": "text", "value": "Runner"}, "NygKq36atr": {"questionId": "NygKq36atr", "type": "text", "value": ""}, "VmlDFbLJKZ": {"questionId": "VmlDFbLJKZ", "type": "select", "value": "Singapore"}, "WSHCcFlExG": {"questionId": "WSHCcFlExG", "type": "date", "value": "2025-01-01"}, "exI36T75Gq": {"questionId": "exI36T75Gq", "type": "text", "value": "Test"}, "oR3hExeYiL": {"questionId": "oR3hExeYiL", "type": "select", "value": "Australia"}, "waeNppHU1u": {"questionId": "waeNppHU1u", "type": "date", "value": "2027-12-31"}}, "id": "ainklsaS47Iha3PaW7Gka"}}, "order": ["ainklsaS47Iha3PaW7Gka"]}}, "CLE7acMs4f": {"questionId": "CLE7acMs4f", "type": "table", "value": {"entities": {"ws8a31gP0aanf83ec3JRH": {"columns": {}, "id": "ws8a31gP0aanf83ec3JRH"}}, "order": ["ws8a31gP0aanf83ec3JRH"]}}, "CenareaD0c": {"questionId": "CenareaD0c", "type": "table", "value": {"entities": {"aRlmmwTxRawT3WHlakGe7": {"columns": {}, "id": "aRlmmwTxRawT3WHlakGe7"}}, "order": ["aRlmmwTxRawT3WHlakGe7"]}}, "FfKlGILPWz": {"questionId": "FfKlGILPWz", "type": "table", "value": {"entities": {"aOFxkxDlUuOLFOyDVtOQP": {"columns": {}, "id": "aOFxkxDlUuOLFOyDVtOQP"}}, "order": ["aOFxkxDlUuOLFOyDVtOQP"]}}, "OHANAySUV6": {"questionId": "OHANAySUV6", "type": "table", "value": {"entities": {"GaXRDx5ig4yx5OYfTKgpN": {"columns": {}, "id": "GaXRDx5ig4yx5OYfTKgpN"}}, "order": ["GaXRDx5ig4yx5OYfTKgpN"]}}, "Qg1l7LvjeN": {"questionId": "Qg1l7LvjeN", "type": "table", "value": {"entities": {"UmkKaaefqiUEOseFlbTOm": {"columns": {}, "id": "UmkKaaefqiUEOseFlbTOm"}}, "order": ["UmkKaaefqiUEOseFlbTOm"]}}, "SoBlBkh5ZM": {"questionId": "SoBlBkh5ZM", "type": "table", "value": {"entities": {"aRtM5yaSGAQwakGvbhNOH": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "RSU FED No FICA"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Equity"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "aRtM5yaSGAQwakGvbhNOH"}, "ak0uS1375iaQEEwxi8j5g": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Host Location Misc. Housing Expenses - m"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "ak0uS1375iaQEEwxi8j5g"}, "a2g1NmaNPDFJNooKPpO9E": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "CIGNA Premiums"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "a2g1NmaNPDFJNooKPpO9E"}, "axX399jEcb2N0yl0KU0HI": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Employer Skill Development Fund"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Employment Expense"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "axX399jEcb2N0yl0KU0HI"}, "BwUjg28KPdasZdWsGUDnp": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Bonus"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Bonus"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "BwUjg28KPdasZdWsGUDnp"}, "DlNRGa880xMmxF3EWPRxq": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "2020 Host Tax Preparation Fees-SG"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Employment Expense"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "DlNRGa880xMmxF3EWPRxq"}, "F4BfDkxMawfXa6Yr5BaoK": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "DIVIDEND EQUIVALENT (US)"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Equity"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "F4BfDkxMawfXa6Yr5BaoK"}, "IF7uzpFaJUqMna6t3vSRR": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "2020 Home Tax Preparation Fees-US"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Employment Expense"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "IF7uzpFaJUqMna6t3vSRR"}, "LDrR86osIawfDz20c5JKQ": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Stock Purchase"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Equity"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "LDrR86osIawfDz20c5JKQ"}, "Lc46a96M7lIkjbzx2ziF1": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "TAXABLE RELOCATION"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "Lc46a96M7lIkjbzx2ziF1"}, "NSUvtlbD5Dygr63lRwPsl": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Home Leave Airfare"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "NSUvtlbD5Dygr63lRwPsl"}, "SmGSdPt33wCZ6a9Ib6yNy": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Eyer SPF monthly contr"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Employment Expense"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "SmGSdPt33wCZ6a9Ib6yNy"}, "TG1SslCBdQKrU81Jrqz27": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "RSU Tax Offset"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Equity"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "TG1SslCBdQKrU81Jrqz27"}, "TGlvw4Y1a4TZ06qJ3COEN": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Base Salary for SDL (SG)"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Salary"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "TGlvw4Y1a4TZ06qJ3COEN"}, "aev1bTJXiev2PaaJ6bMSB": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Housing/Utility Allowance - more than 1"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "aev1bTJXiev2PaaJ6bMSB"}, "axUfk2DBEhsN1uneJaq5G": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "High Cost Payment/COLA - more than 1 yea"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "axUfk2DBEhsN1uneJaq5G"}, "anOWZj8ha8STF5oGQDy4a": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "2020 Home Tax Return Extension Payment"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "anOWZj8ha8STF5oGQDy4a"}, "ca5bemhAsWcfiReqTNyLg": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "EYER Sum Addtional funds"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Employment Expense"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "ca5bemhAsWcfiReqTNyLg"}, "eWG8eH51mfGHdpvIzyj5a": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Destination Services (3)"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "eWG8eH51mfGHdpvIzyj5a"}, "eyVEUVSEJaEoLTzaNRRE9": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Transportation Allowance (SIRVA)"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "eyVEUVSEJaEoLTzaNRRE9"}, "fCiua4agm3zbZVHS7m1Ed": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Host Location Housing - more than 1 year"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "fCiua4agm3zbZVHS7m1Ed"}, "gW5gtl5nsIWKlne22Vw7S": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "SICK"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Salary"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "gW5gtl5nsIWKlne22Vw7S"}, "hGrXWglyFXI4yWyb1oeqL": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "2020 TEQ Repayment"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "hGrXWglyFXI4yWyb1oeqL"}, "lHJlRS8rpOSAZGHvO6fP3": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "VACATION"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Salary"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "lHJlRS8rpOSAZGHvO6fP3"}, "qRrPlXCUOZXEccKCvF9qX": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Host Location Housing Security Deposit-"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "qRrPlXCUOZXEccKCvF9qX"}, "qn6M52xSXpwBdQglFnk0E": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Salary Exempt"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Salary"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "qn6M52xSXpwBdQglFnk0E"}, "sW2HygU4PSJUmr38Ar4DK": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "2020 Host Tax Return Payment"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "sW2HygU4PSJUmr38Ar4DK"}, "txS2BfWiFWpcov6aHixlu": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Assignee Visa/Passport"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Employment Expense"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "txS2BfWiFWpcov6aHixlu"}, "u2mlxp3ifGD137eh9tF9m": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Net Taxable income-GRSUP (SG)"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "u2mlxp3ifGD137eh9tF9m"}, "ufq1nkEa1I4ww0R6UKpHW": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "Property Mgmt  Maintenance"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "ufq1nkEa1I4ww0R6UKpHW"}, "x5doxEs0PiRpnT2EM36CE": {"columns": {"aVg591B27j": {"questionId": "aVg591B27j", "type": "text", "value": "TNTD Withholding Tax - FED"}, "X4KTqYVmaV": {"questionId": "X4KTqYVmaV", "type": "boolean", "value": false}, "XaADpaR1Sf": {"questionId": "XaADpaR1Sf", "type": "select", "value": "Allowance"}, "qUpy6BD0Yk": {"questionId": "qUpy6BD0Yk", "type": "boolean", "value": false}}, "id": "x5doxEs0PiRpnT2EM36CE"}}, "order": ["TGlvw4Y1a4TZ06qJ3COEN", "axX399jEcb2N0yl0KU0HI", "SmGSdPt33wCZ6a9Ib6yNy", "ca5bemhAsWcfiReqTNyLg", "u2mlxp3ifGD137eh9tF9m", "sW2HygU4PSJUmr38Ar4DK", "DlNRGa880xMmxF3EWPRxq", "a2g1NmaNPDFJNooKPpO9E", "txS2BfWiFWpcov6aHixlu", "eWG8eH51mfGHdpvIzyj5a", "axUfk2DBEhsN1uneJaq5G", "NSUvtlbD5Dygr63lRwPsl", "fCiua4agm3zbZVHS7m1Ed", "qRrPlXCUOZXEccKCvF9qX", "ak0uS1375iaQEEwxi8j5g", "aev1bTJXiev2PaaJ6bMSB", "ufq1nkEa1I4ww0R6UKpHW", "eyVEUVSEJaEoLTzaNRRE9", "anOWZj8ha8STF5oGQDy4a", "IF7uzpFaJUqMna6t3vSRR", "hGrXWglyFXI4yWyb1oeqL", "F4BfDkxMawfXa6Yr5BaoK", "aRtM5yaSGAQwakGvbhNOH", "TG1SslCBdQKrU81Jrqz27", "qn6M52xSXpwBdQglFnk0E", "gW5gtl5nsIWKlne22Vw7S", "LDrR86osIawfDz20c5JKQ", "Lc46a96M7lIkjbzx2ziF1", "x5doxEs0PiRpnT2EM36CE", "lHJlRS8rpOSAZGHvO6fP3", "BwUjg28KPdasZdWsGUDnp"]}}, "XfgXa6ooAm": {"questionId": "XfgXa6ooAm", "type": "table", "value": {"entities": {"hWWTCyHBfdAu97RH2QPjw": {"columns": {"awsbsW5vEf": {"questionId": "awsbsW5vEf", "type": "select", "value": "First year only"}, "FIB0l1GDQv": {"questionId": "FIB0l1GDQv", "type": "select", "value": "Not authorised"}, "KMaXT0b7aW": {"questionId": "KMaXT0b7aW", "type": "boolean"}, "Pl2VZbKell": {"questionId": "Pl2VZbKell", "type": "text", "value": "Transfer"}, "ogLzuKJAFY": {"questionId": "ogLzuKJAFY", "type": "select", "value": "Permanent Transfer"}}, "id": "hWWTCyHBfdAu97RH2QPjw"}}, "order": ["hWWTCyHBfdAu97RH2QPjw"]}}, "d6Bj8jC6bF": {"questionId": "d6Bj8jC6bF", "type": "table", "value": {"entities": {"XzNbUvzqQsa7YyZecWw8b": {"columns": {}, "id": "XzNbUvzqQsa7YyZecWw8b"}}, "order": ["XzNbUvzqQsa7YyZecWw8b"]}}, "pB7BSlhmKW": {"questionId": "pB7BSlhmKW", "type": "table", "value": {"entities": {"zZaVmUsIYqBTRZcBRxY7F": {"columns": {}, "id": "zZaVmUsIYqBTRZcBRxY7F"}}, "order": ["zZaVmUsIYqBTRZcBRxY7F"]}}}, "id": 14}