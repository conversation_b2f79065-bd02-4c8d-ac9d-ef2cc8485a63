package services.oneteam.ai.shared.domains.workspace

import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationSearchCriteria
import services.oneteam.ai.shared.helpers.CustomNanoId
import java.time.Instant
import java.util.*

class FoundationConfigurationService(
    val check: Checks,
    private val dictionary: ResourceBundle,
    private val foundationRepository: FoundationRepository
) {

    fun createRootFoundationConfiguration(): FoundationConfiguration.ForApi {
        return create(
            FoundationConfiguration.ForCreate(
                name = FoundationConfiguration.Name(dictionary.getString("workspace")),
                description = null,
                relationship = FoundationConfiguration.Relationship.OneToMany
            )
        )
    }

    fun create(foundationConfiguration: FoundationConfiguration.ForCreate): FoundationConfiguration.ForApi {
        val now = Instant.now()
        return FoundationConfiguration.ForApi(
            id = FoundationConfiguration.Id(CustomNanoId.generate()),
            name = foundationConfiguration.name,
            description = foundationConfiguration.description,
            relationship = foundationConfiguration.relationship,
            metadata = EntityMetadata(
                createdAt = now,
                updatedAt = now
            )
        )
    }

    suspend fun canDeleteFoundationLevel(workspaceId: Workspace.Id, foundationConfigurationId: FoundationConfiguration.Id, foundationPageRequest: PageRequest): Boolean {
        val foundationSearchCriteria = FoundationSearchCriteria(
            workspaceId,
            foundationConfigurationIdList = listOf(foundationConfigurationId),
        )

        return DatabaseUtils.dbQueryWithTenant {
            val foundation = foundationRepository.search(foundationSearchCriteria, foundationPageRequest)
            return@dbQueryWithTenant foundation.items.isEmpty()
        }
    }
}