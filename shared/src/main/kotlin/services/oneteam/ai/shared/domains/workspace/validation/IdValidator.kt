package services.oneteam.ai.shared.domains.workspace.validation

private const val ERRORS_INVALID_ID = "errors.invalidId"

class IdValidator {
    companion object {
        fun <T> build(producer: (T) -> Long?): Constraint<T> {
            return NumberConstraint(
                "id",
                producer,
                ERRORS_INVALID_ID,
                NumberBounds(0, null)
            )
        }
    }
}