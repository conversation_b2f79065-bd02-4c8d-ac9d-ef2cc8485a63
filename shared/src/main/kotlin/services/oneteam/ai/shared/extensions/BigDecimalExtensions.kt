package services.oneteam.ai.shared.extensions

import java.math.BigDecimal
import java.math.RoundingMode

/**
 * Using a scale of 15 to avoid precision issues with BigDecimal
 *  eg 0.8 - 0.1 = 0.7000000000000001
 *  using a scale of 15 gives us
 *  0.700000000000000
 */
const val scale = 15

/**
 * Strips trailing zeros and converts to a plain string.
 * When we are using BigDecimal, we don't want numbers with many trailing zeros. So instead of 1.000 we want 1.
 *
 * By itself, stripTrailingZeros() removes even those trailing zeros BEFORE the decimal point.
 * So 10 becomes 1E+1.
 *
 * So the solution is to first strip the trailing zeros, then convert to a plain string. So 1E+1 becomes 10.
 * And 1.000 becomes 1.
 */
fun BigDecimal.convertToNiceBigDecimal(): BigDecimal {
    return BigDecimal(this.setScale(scale, RoundingMode.HALF_EVEN).stripTrailingZeros().toPlainString())
}
