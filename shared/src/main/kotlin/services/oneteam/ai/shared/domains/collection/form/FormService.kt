package services.oneteam.ai.shared.domains.collection.form

import kotlinx.datetime.LocalDateTime
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder
import org.jetbrains.exposed.sql.and
import org.jetbrains.kotlinx.dataframe.DataFrame
import org.jetbrains.kotlinx.dataframe.api.*
import org.jetbrains.kotlinx.dataframe.io.readCSV
import org.jetbrains.kotlinx.dataframe.io.readExcel
import org.jetbrains.kotlinx.dataframe.io.writeCSV
import org.jetbrains.kotlinx.dataframe.io.writeExcel
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.*
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.collection.foundation.getFoundationConfiguration
import services.oneteam.ai.shared.domains.event.EventKey
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.domains.workspace.document.create
import services.oneteam.ai.shared.withTenantTransactionScope
import java.io.File

class FormService(
    private val formRepository: FormRepository,
    private val workspaceRepository: WorkspaceRepository,
    private val documentService: IDocumentService,
    private val workspaceVersionRepository: WorkspaceVersionRepository,
    private val uploadService: BlobService,
    private val foundationService: FoundationService,
    private val workspaceVersionService: WorkspaceVersionService,
    val check: Checks
) {

    suspend fun select(params: ObtainFormParams): Form.ForApi? {
        return withTenantTransactionScope {
            val interval = getIntervalOrNull(params)

            val predicate: SqlExpressionBuilder.() -> Op<Boolean> = {
                (Forms.workspace eq EntityID(
                    params.workspaceId.value, Forms
                )) and (Forms.foundation eq EntityID(
                    params.foundationId.value, Forms
                )) and (Forms.formConfigurationId eq params.formConfigurationId.value) and (interval?.id?.value.takeIf { it?.isBlank() != true }
                    ?.let { Forms.intervalId eq it }
                    ?: Op.TRUE)
            }
            return@withTenantTransactionScope formRepository.select(predicate)
        }
    }

    suspend fun obtain(params: ObtainFormParams): ObtainFormResponse {
        return withTenantTransactionScope {
            val isAllowNull = params.allowNull;
            val createIfNotExists = params.createIfNotExists;
            val form = select(params)
            if (form != null) {
                return@withTenantTransactionScope ObtainFormResponse(form, true)
            }
            if (createIfNotExists == true) {
                val interval = getIntervalOrNull(params)
                val formForCreate = Form.ForCreate(
                    foundationId = params.foundationId,
                    formConfigurationId = params.formConfigurationId,
                    intervalId = interval?.id,
                )
                val newForm = create(
                    params.workspaceId,
                    formForCreate
                )
                return@withTenantTransactionScope ObtainFormResponse(newForm, false)
            } else if (isAllowNull == true) {
                return@withTenantTransactionScope ObtainFormResponse(null, false)
            } else {
                throw BadRequestException("Form not found")
            }
        }
    }

    private suspend fun getIntervalOrNull(params: ObtainFormParams): Interval? {
        val workspaceConfiguration = workspaceVersionRepository.searchLatestVersion(
            params.workspaceId
        )?.toDTO()?.configuration

        val formConfiguration = workspaceConfiguration?.findForm(params.formConfigurationId)
            ?: throw BadRequestException("Form configuration not found when selecting form")

        if (formConfiguration.seriesId.isNullOrBlank()) {
            return null
        }
        val seriesConfiguration =
            workspaceConfiguration.findSeries(SeriesConfiguration.Id(formConfiguration.seriesId))

        val interval = if (!params.intervalIdOrName.isNullOrBlank()) {
            seriesConfiguration.findIntervalByIdOrName(params.intervalIdOrName)
        } else {
            null
        }
        return interval
    }


    suspend fun search(
        pageRequest: PageRequest, formSearchCriteria: FormSearchCriteria
    ): Page<Form.ForApi> {
        return withTenantTransactionScope {
            // check workspace exists and throw 404 if not
            workspaceRepository.findOne(formSearchCriteria.workspaceId)

            val searchResult = formRepository.search(
                pageRequest, formSearchCriteria
            )
            return@withTenantTransactionScope Page(
                searchResult.page, searchResult.total, searchResult.items.map { it.toDTO() })
        }
    }

    suspend fun searchByConfigId(
        workspaceId: Workspace.Id,
        pageRequest: PageRequest,
        configIds: List<FormConfiguration.Id>,
        intervalIds: List<IntervalId>,
        keywords: String?
    ): Page<Form.ForApi> {
        val searchCriteria = FormSearchCriteria(
            workspaceId,
            formConfigurationIdList = configIds,
            intervalIdList = intervalIds,
            searchTerm = keywords
        )

        return withTenantTransactionScope {
            val searchResult = formRepository.search(
                pageRequest,
                searchCriteria
            )
            return@withTenantTransactionScope Page(
                searchResult.page, searchResult.total, searchResult.items.map { it.toDTO() })
        }
    }

    suspend fun create(workspaceId: Workspace.Id, form: Form.ForCreate): Form.ForApi {
        return withTenantTransactionScope {
            val formAnswerForJson = createDefaultFormAnswerDocument()
            val formAnnotationForJson = createDefaultFormAnnotationDocument()
            return@withTenantTransactionScope create(workspaceId, form, formAnswerForJson, formAnnotationForJson)
        }
    }

    suspend fun create(
        workspaceId: Workspace.Id,
        form: Form.ForCreate,
        formAnswerForJson: FormAnswer.ForJson,
        formAnnotationForJson: FormAnnotation.ForJson
    ): Form.ForApi {
        return withTenantTransactionScope { tenant ->
            val workspaceEntity = workspaceRepository.findOne(workspaceId) // check workspace exists
            validateForm(workspaceId, form.formConfigurationId, form.foundationId, form.intervalId)

            val createdForm = formRepository.create(tenant, workspaceEntity, form)
            migrate(tenant, createdForm, formAnswerForJson, formAnnotationForJson)
            return@withTenantTransactionScope findOneOrThrow(createdForm.id)
        }
    }

    suspend fun update(formId: Form.Id, form: Form.ForUpdate): Form.ForApi = withTenantTransactionScope {
        val formEntity = formRepository.getById(formId.value) ?: throw BadRequestException("Form not found")
        validateForm(formEntity.workspaceId, form.formConfigurationId, form.foundationId, form.intervalId)
        return@withTenantTransactionScope formRepository.update(formId, form)
    }

    suspend fun updateProperties(formId: Form.Id, properties: JsonObject, workspaceId: Workspace.Id? = null): Form.ForApi = withTenantTransactionScope {
        findOneOrThrow(formId, workspaceId)
        return@withTenantTransactionScope formRepository.updateProperties(formId, properties)
    }

    suspend fun delete(formId: Form.Id) = withTenantTransactionScope {
        formRepository.delete(findOneOrThrow(formId).id)
    }

    suspend fun get(id: Form.Id): Form.ForApi = withTenantTransactionScope { tenant ->
        return@withTenantTransactionScope migrate(tenant, findOneOrThrow(id))
    }

    private suspend fun migrate(
        tenant: Tenant,
        form: Form.ForApi,
        formAnswerForJson: FormAnswer.ForJson? = null,
        formAnnotationForJson: FormAnnotation.ForJson? = null
    ): Form.ForApi {

        if (form.documentId != null && form.annotationDocumentId != null) {
            return form
        }

        var updatedForm = form
        if (form.documentId == null) {
            val answerDocumentId = documentService.create(
                tenant.id,
                formAnswerForJson?.copy(id = form.id)
                    ?: createDefaultFormAnswerDocument().copy(id = form.id)
            )
            updatedForm = updatedForm.copy(documentId = Form.DocumentId(answerDocumentId))
        }
        if (form.annotationDocumentId == null) {
            val annotationDocumentId = documentService.create(
                tenant.id,
                formAnnotationForJson?.copy(id = form.id)
                    ?: createDefaultFormAnnotationDocument().copy(id = form.id)
            )
            updatedForm = updatedForm.copy(annotationDocumentId = Form.AnnotationDocumentId(annotationDocumentId))
        }
        return formRepository.updatePrivateFields(updatedForm)
    }

    private suspend fun validateForm(
        workspaceId: Workspace.Id,
        formConfigurationId: FormConfiguration.Id,
        foundationId: Foundation.Id,
        intervalId: IntervalId?
    ) {
        // business validation
        val workspaceConfiguration = workspaceVersionRepository.searchLatestVersion(workspaceId)?.toDTO()?.configuration
            ?: throw BadRequestException("Workspace version not found")
        val formConfiguration = workspaceConfiguration.findForm(formConfigurationId)
        val foundation = foundationService.get(foundationId)

        // foundation configuration for the form must match the foundation configuration on the form
        foundation.foundationConfigurationId.value == formConfiguration.foundationId || throw BadRequestException("Foundation configuration mismatch")

        // if a form configuration specifies a series then the given request must match series and interval
        if (formConfiguration.seriesId?.isNotBlank() == true) {
            // interval must be specified
            intervalId != null || throw BadRequestException("Interval not specified")
            // get the series associated with the form configuration
            val series = workspaceConfiguration.findSeries(SeriesConfiguration.Id(formConfiguration.seriesId))
            // interval must exist in series
            series.findInterval(intervalId)
        } else {
            // interval must not be specified
            intervalId?.value.isNullOrEmpty() || throw BadRequestException("Interval specified for non-series form")
        }
    }

    /**
     * Find a form by id, workspaceId or throw a not found exception.
     */
    private fun findOneOrThrow(formId: Form.Id, workspaceId: Workspace.Id? = null): Form.ForApi {
        val formEntity = if (workspaceId != null) {
            formRepository.getByIdAndWorkspaceId(formId.value, workspaceId.value)
        } else {
            formRepository.getById(formId.value)
        }
        check.exists(formEntity) { "Unknown form" }
        return formEntity!!
    }

    suspend fun checkDuplicatedForm(workspaceId: Workspace.Id, formForCreate: Form.ForCreate) =
        withTenantTransactionScope {
            val predicate: SqlExpressionBuilder.() -> Op<Boolean> = {
                (Forms.workspace eq EntityID(workspaceId.value, Forms)) and
                        (Forms.foundation eq EntityID(formForCreate.foundationId.value, Forms)) and
                        (Forms.formConfigurationId eq formForCreate.formConfigurationId.value) and
                        (formForCreate.intervalId?.value?.let { Forms.intervalId eq it } ?: Op.TRUE)
            }

            val duplicatedForm = formRepository.select(predicate)
            if (duplicatedForm !== null) {
                val foundation = foundationService.get(formForCreate.foundationId)
                val foundationConfiguration = getFoundationConfiguration(workspaceVersionService, foundation)
                throw BadRequestException("Cannot create ${foundationConfiguration?.name?.value} form because it already exists.")
            }
        }

    fun createDefaultFormAnswerDocument(): FormAnswer.ForJson {
        return FormAnswer.ForJson(Form.Id(0), emptyMap())
    }

    fun createDefaultFormAnnotationDocument(): FormAnnotation.ForJson {
        return FormAnnotation.ForJson(Form.Id(0), emptyMap(), emptyMap())
    }

    suspend fun getSignedUrl(
        formId: Form.Id, questionId: String, rowId: String?, existingFilename: String? = null
    ): String = withTenantTransactionScope { tenant ->
        val (workspace, formEntity) = getCommonConfig(formId)
        return@withTenantTransactionScope buildSignedUrl(
            tenant.id,
            workspace,
            formEntity,
            questionId,
            rowId,
            existingFilename
        )
    }

    private fun getCommonConfig(formId: Form.Id): Pair<Workspace.ForJson, Form.ForApi> {
        val formEntity = findOneOrThrow(formId)
        val workspaces = workspaceVersionRepository.search(
            PageRequest(0, 1, "", Sort(listOf(SortField("id", Sort.DESC)))),
            formEntity.workspaceId,
            null
        )
        val workspace = workspaces.items.firstOrNull() ?: throw IllegalArgumentException("Workspace not found")
        return Pair(workspace.configuration, formEntity)
    }

    fun buildSignedUrl(
        tenantId: Long,
        workspace: Workspace.ForJson,
        formEntity: Form.ForApi,
        questionId: String,
        rowId: String?,
        existingFilename: String? = null
    ): String {
        val formConfiguration = workspace.forms[formEntity.formConfigurationId]
            ?: throw IllegalArgumentException("Form configuration not found")
        val question = findQuestionById(formConfiguration.content, questionId)
            ?: throw IllegalArgumentException("Question not found")
        if (rowId != null) {
            question is BaseSection.TableQuestion || throw IllegalArgumentException("Question is not table question")
        }
        val formId = formEntity.id
        return if (existingFilename == null) {
            val filepath = generateFilePath(
                tenantId,
                workspace.id.value.toString(),
                formId.value.toString(),
                questionId,
                rowId
            )
            uploadService.generateSasToken(filepath, true)
        } else {
            uploadService.generateSasToken(existingFilename, false)
        }
    }

    fun readFile(url: String, isCsvFile: Boolean): DataFrame<*> {
        return if (isCsvFile) {
            DataFrame.readCSV(url)
        } else {
            DataFrame.readExcel(url)
        }
    }

    suspend fun matchFileDataWithForm(formId: Form.Id, questionId: String, df: DataFrame<*>): List<Any>? =
        withTenantTransactionScope { tenant ->
            val formattedDf = df.convert { colsOf<LocalDateTime>() }
                .with { it.toString().substringBefore("T") }

            val (workspace, formEntity) = getCommonConfig(formId)
            val formConfiguration = workspace.forms[formEntity.formConfigurationId]
            val question = findQuestionById(formConfiguration?.content, questionId) as BaseSection.TableQuestion
            val columns = question.properties?.columns ?: return@withTenantTransactionScope null
            val dfColumns = formattedDf.columnNames().toSet()
            val matchedColumns = mutableMapOf<String, String>()

            for (col in columns) {
                if (col.properties?.disabled == true || col.properties?.hidden == true) {
                    continue
                }
                when {
                    col.identifier in dfColumns -> matchedColumns[col.id.value] = col.identifier  // Prefer identifier
                    col.text in dfColumns -> matchedColumns[col.id.value] = col.text // Fallback to text
                }
            }

            val filteredDf = formattedDf.select(*matchedColumns.values.toTypedArray())
            val result = filteredDf.rows().map { row ->
                matchedColumns.mapValues { (_, actualColumnName) -> row[actualColumnName] }
            }
            return@withTenantTransactionScope result
        }

    suspend fun downloadTableData(formId: Form.Id, questionId: BaseSection.Id, fileType: String): String =
        withTenantTransactionScope { tenant ->
            val (form, question) = getFormAndQuestion(formId, questionId)
            val answersDocumentId = form.documentId?.value ?: throw IllegalArgumentException("Invalid form")
            val answers = documentService.show(answersDocumentId, cookie = null, FormAnswer.ForJson::class, true)
            val answer: FormAnswer<*>? = answers.getAnswer(question.id)
            if (answer !is FormAnswer.TableAnswer || question !is BaseSection.TableQuestion) {
                throw IllegalArgumentException("question ID: $questionId is not a table question")
            }

            val rows = answer.value.toList()
            val columnMapping = question.properties?.columns?.associate { it.id.value to it.identifier } ?: emptyMap()
            val allColumnQuestionIds = columnMapping.keys.toList()

            val rowValues = rows.flatMap { row ->
                allColumnQuestionIds.map { colId ->
                    row.columns[BaseSection.Id(colId)]?.value ?: ""
                }
            }

            val df = dataFrameOf(*allColumnQuestionIds.toTypedArray())(*rowValues.toTypedArray())
                .rename(*allColumnQuestionIds.mapNotNull { colId ->
                    columnMapping[colId]?.let { colId to it }
                }.toTypedArray())

            val file = File.createTempFile("temp", null)
            when (fileType) {
                "csv" -> df.writeCSV(file)
                else -> df.writeExcel(file)
            }

            val (workspace) = getCommonConfig(formId)
            val filepath = generateFilePath(
                tenant.id,
                workspace.id.value.toString(),
                formId.value.toString(),
                questionId.value
            )
            return@withTenantTransactionScope uploadService.uploadFile(filepath, file)
        }

    // TODO remove this function because we have a better way to find questions
    private fun findQuestionById(
        sections: List<BaseSection>?, id: String
    ): BaseSection? {
        sections?.forEach {
            when (it) {
                is BaseSection.Section -> {
                    val found = findQuestionById(it.content, id)
                    if (found != null) {
                        return found
                    }
                }

                is BaseSection.BaseQuestion -> {
                    if (it.id.value == id) {
                        return it
                    }
                }
            }
        }
        return null
    }

    // Form question is the question attached to the form not through any parent (table or json) question
    private suspend fun getFormAndQuestion(
        formId: Form.Id, questionId: BaseSection.Id, workspaceId: Workspace.Id? = null
    ): Pair<Form.ForApi, BaseSection.BaseQuestion> {
        val formEntity = findOneOrThrow(formId, workspaceId)

        val workspaceVersion = workspaceVersionRepository.searchLatestVersion(formEntity.workspaceId)
            ?: throw NotFoundException("Workspace version for ${formEntity.workspaceId} not found")
        val formConfigurations = workspaceVersion.configuration.findForm(formEntity.formConfigurationId)

        val question = findQuestionInSections(questionId, formConfigurations.content) ?: throw IllegalArgumentException(
            "Question not found"
        )

        return Pair(formEntity, question)
    }

    suspend fun setAnswer(formId: Form.Id, cookie: String? = null, formAnswer: FormAnswerRequestBody, workspaceId: Workspace.Id? = null
    ): String =
        withTenantTransactionScope { tenant ->
            val (formEntity, question) = getFormAndQuestion(formId, formAnswer.questionId, workspaceId)
            val (workspace) = getCommonConfig(formId)

            val answersDocumentId = formEntity.documentId?.value ?: throw IllegalArgumentException("Invalid form")

            val formAnswers = validateAndGenerateAnswer(
                question,
                formAnswer,
                answersDocumentId,
                documentService,
                tenant.id,
                workspace,
                formId,
                uploadService,
                this
            )
                ?: throw IllegalArgumentException("Invalid value for question ID: ${formAnswer.questionId.value}")

            return@withTenantTransactionScope documentService.answerQuestion(answersDocumentId, cookie, formAnswers)
        }

    suspend fun setAlert(formId: Form.Id, cookie: String? = null, formAlert: FormAlertRequestBody): String =
        withTenantTransactionScope {
            val (formEntity, question) = getFormAndQuestion(formId, formAlert.questionId, formAlert.workspaceId)

            val answersDocumentId = formEntity.documentId?.value ?: throw IllegalArgumentException("Invalid form")
            val annotationDocumentId =
                formEntity.annotationDocumentId?.value ?: throw IllegalArgumentException("Invalid form")

            val formAlerts = validateAndGenerateAlert(documentService, answersDocumentId, question, formAlert)

            return@withTenantTransactionScope documentService.alertAnnotation(annotationDocumentId, cookie, formAlerts)
        }
}

@Serializable
data class FormAnswerRequestBody(
    val questionId: BaseSection.Id,
    val value: JsonElement? = null,
    val operation: SetAnswerOperation? = null,
    val rowId: String? = "",
    val rowIndex: Int? = null,
    val columnId: String? = null,
    val valueRowIdShouldUpdate: String? = "",
    val listOperation: SetListAnswerOperation? = null,
    val itemId: String? = "",
    val itemIndex: Int? = null,
    val fileOperation: SetFileAnswerOperation? = null,
    val fileIndex: Int? = null,
    val workspaceId: Workspace.Id? = null,
)

@Serializable
data class FormAnswersRequestBody(
    val answers: List<FormAnswerRequestBody>,
    val workspaceId: Workspace.Id
)

@Serializable
enum class SetAnswerOperation {
    @SerialName("setTable")
    SET_TABLE,

    @SerialName("setRow")
    SET_ROW,

    @SerialName("setColumn")
    SET_COLUMN,

    @SerialName("setCell")
    SET_CELL

}

@Serializable
enum class SetListAnswerOperation {
    @SerialName("setList")
    SET_LIST,

    @SerialName("setItem")
    SET_ITEM,

    @SerialName("removeItem")
    REMOVE_ITEM,

}

@Serializable
enum class SetFileAnswerOperation {
    @SerialName("setFile")
    SET_FILE,

    @SerialName("addFile")
    ADD_FILE,

    @SerialName("removeFile")
    REMOVE_FILE,

}

@Serializable
enum class FileInputType {
    @SerialName("url")
    URL,

    @SerialName("binary")
    BINARY,
}

@Serializable
data class SetAnswerParams(
    val path: List<String>, val formAnswer: FormAnswer<*>
)

@Serializable
data class FormAlertRequestBody(
    val questionId: BaseSection.Id,
    val operation: AlertOperation? = AlertOperation.ADD,
    val rowId: RowId? = null,
    val columnId: BaseSection.Id? = null,
    val type: AlertType? = null,
    val groupIdentifier: String? = null,
    val message: String? = null,
    val workspaceId: Workspace.Id
)

@Serializable
data class FormAlertRequestSyncServerBody(
    val path: List<String>, val alerts: FormAlert
)

@Serializable
data class FormAnswerChangedRequestSyncServerBody(
    val documentId: String,
    val formId: Form.Id,
    val eventType: EventKey,
    val formAnswer: FormAnswerRequestBody,
)

@Serializable
data class ObtainFormParams(
    val workspaceId: Workspace.Id,
    val foundationId: Foundation.Id,
    val intervalIdOrName: String? = null,
    val formConfigurationId: FormConfiguration.Id,
    val allowNull: Boolean? = null,
    val createIfNotExists: Boolean? = false
)

@Serializable
data class ObtainFormResponse(
    val form: Form.ForApi?,
    val alreadyExists: Boolean = false,
)


@Serializable
data class PrefillRequestBody(val path: String, val isCsvFile: Boolean)

@Serializable
data class SetAnswerResponse(
    val documentId: String?,
    val message: String?
)

@Serializable
data class FormPropertiesRequestBody(
    val properties: JsonObject,
    val workspaceId: Workspace.Id
)