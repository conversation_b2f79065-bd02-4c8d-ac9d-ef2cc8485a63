package services.oneteam.ai.shared.domains.flow.variables

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.otTypeDescrim

@Serializable
@OptIn(ExperimentalSerializationApi::class)
@JsonClassDiscriminator(otTypeDescrim)
sealed class Variable {
    abstract val identifier: VariableIdentifier
    abstract val properties: VariableProperties?
    abstract val type: VariableDataType
}

typealias VariableIdentifier = String
