package services.oneteam.ai.shared.domains.workspace.validation

import services.oneteam.ai.shared.domains.workspace.BaseSection
import services.oneteam.ai.shared.domains.workspace.FormConfiguration

class QuestionDuplicateValidator(private val form: FormConfiguration.ForJson) {
    private val formQuestions = mutableListOf<BaseSection.BaseQuestion>()
    private val childToParentMap = mutableMapOf<BaseSection.BaseQuestion, BaseSection.BaseQuestion>()

    init {
        form.content?.forEach { section ->
            collectFormQuestions(section)
        }
    }

    private fun collectFormQuestions(item: BaseSection) {
        if (item is BaseSection.Section) {
            item.content?.forEach { section ->
                collectFormQuestions(section)
            }
        } else {
            formQuestions.add(item as BaseSection.BaseQuestion)
            mapChildToParent(item)
        }
    }

    private fun mapChildToParent(question: BaseSection.BaseQuestion) {
        if (question is BaseSection.TableQuestion) {
            question.properties.columns?.forEach { columnQuestion ->
                childToParentMap[columnQuestion] = question
                mapChildToParent(columnQuestion)
            }
        } else if (question is BaseSection.JsonQuestion) {
            question.properties.items?.forEach { itemQuestion ->
                childToParentMap[itemQuestion] = question
                mapChildToParent(itemQuestion)
            }
        }
    }

    fun validate(path: String, errors: Errors) {
         form.content?.forEachIndexed { index, section ->
            visitChild(section, "$path.content.$index", errors, this::validateQuestion)
        }
    }

    private fun validateQuestion(question: BaseSection.BaseQuestion, path: String, errors: Errors) {
        if (formQuestions.contains(question)) {
            val isIdentifierDuplicated =
                formQuestions.any { it.identifier == question.identifier && it !== question }
            if (isIdentifierDuplicated) {
                errors.add(ConstraintError.buildDuplicateIdentifierError(path))
            }
        }

        if (childToParentMap[question] != null) {
            val parent = childToParentMap[question]
            val siblingQuestions = (when (parent) {
                is BaseSection.TableQuestion -> {
                    parent.properties.columns
                }

                is BaseSection.JsonQuestion -> {
                    parent.properties.items
                }

                else -> {
                    null
                }
            })?.filter { it !== question }
            val duplicatedIdentifier = siblingQuestions?.any { it.identifier == question.identifier }
            if (duplicatedIdentifier == true) {
                errors.add(ConstraintError.buildDuplicateIdentifierError(path))
            }
        }
    }

    private fun visitChild(item: BaseSection, path: String, errors: Errors, visitor: (BaseSection.BaseQuestion, String, Errors) -> Unit) {
        if (item is BaseSection.Section) {
            item.content?.forEachIndexed { index, section ->
                visitChild(section, "$path.content.$index", errors, visitor)
            }
        } else {
            val question = item as BaseSection.BaseQuestion
            visitor(question, path, errors)
            if (question is BaseSection.TableQuestion) {
                question.properties.columns?.forEach { columnQuestion ->
                    visitChild(columnQuestion, "$path.properties.columns.${columnQuestion.id.value}", errors, visitor)
                }
            } else if (question is BaseSection.JsonQuestion) {
                question.properties.items?.forEach { itemQuestion ->
                    visitChild(itemQuestion, "$path.properties.items.${itemQuestion.id.value}", errors, visitor)
                }
            }
        }
    }

    companion object {
        fun build(form: FormConfiguration.ForJson): QuestionDuplicateValidator {
            return QuestionDuplicateValidator(form)
        }
    }
}