package services.oneteam.ai.shared.domains.flow.configuration

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.workspace.validation.*

class FlowStepConditionValidator(
    private val id: FlowConfiguration.Step.Id,
    private val properties: FlowConfiguration.Step.Properties,
    private val path: String,
    private val errors: Errors
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun validate() {
        minBranches()
        minBranchNames()
        warnings()
    }

    private fun warnings() {
        if (!properties.typePrimaryIdentifier.isNullOrEmpty()) {
            logger.warn("Step type primary identifier should be null at $path for step ${id.value}")
        }
    }

    fun minBranches() {
        if (properties.branches.isNullOrEmpty()) {
            errors.add(
                ConstraintError(
                    Field("branches"),
                    Type("required"),
                    Path("$path.steps.${id.value}.properties.branches"),
                    ConstraintDetail("Branches must not be empty"),
                    Message("Branches must not be empty")
                )
            )
        }
    }

    fun minBranchNames() {
        properties.branches?.forEachIndexed { index, branch ->
            if (branch.name.isEmpty()) {
                errors.add(
                    ConstraintError(
                        Field("name"),
                        Type("required"),
                        Path("$path.steps.${id.value}.properties.branches.$index.name"),
                        ConstraintDetail("Branch name is required"),
                        Message("Branch name is required")
                    )
                )
            }
        }
    }
}