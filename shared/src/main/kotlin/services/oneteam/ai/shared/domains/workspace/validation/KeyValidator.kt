package services.oneteam.ai.shared.domains.workspace.validation

private val ERRORS_INVALID_KEY = mapOf(
    "required" to "errors.workspace.key.required",
    "length" to "errors.workspace.key.length",
    "alphanumeric" to "errors.workspace.key.alphanumeric",
)

class KeyValidator {
    companion object {
        fun <T> build(producer: (T) -> String?): Constraint<T> {
            return Validator<T>(
                listOf(
                    StringRequiredConstraint(
                        "key",
                        producer,
                        ERRORS_INVALID_KEY.getValue("required")
                    ),
                    StringLengthConstraint(
                        "key",
                        producer,
                        ERRORS_INVALID_KEY.getValue("length"),
                        StringBounds(2, 20)
                    ),
                    StringAlphaNumericConstraint(
                        "key",
                        producer,
                        ERRORS_INVALID_KEY.getValue("alphanumeric")
                    )
                )
            )
        }
    }
}