package services.oneteam.ai.shared.domains.collection.form

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.JsonValue.valueOrNull
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.domains.*
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationEntity
import services.oneteam.ai.shared.domains.collection.foundation.Foundations
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.validation.Validator
import services.oneteam.ai.shared.extensions.paginate

object Forms : BaseLongIdTable("forms") {
    val formConfigurationId = text("form_configuration_id")
    val seriesId = text("series_id").nullable()
    val intervalId = text("interval_id").nullable()
    val foundation = reference("foundation_id", Foundations)

    val documentId = text("document_id").nullable()
    val annotationDocumentId = text("annotation_document_id").nullable()

    val workspace = reference("workspace_id", Workspaces)
    val tenantId = long("tenant_id").references(id)
}

data class FormSearchCriteria(
    val workspaceId: Workspace.Id,
    val foundationId: Foundation.Id? = null,
    val foundationParentId: Foundation.Id? = null,
    val foundationConfigurationId: FoundationConfiguration.Id? = null,
    val formConfigurationId: FormConfiguration.Id? = null,
    val formConfigurationIdList: List<FormConfiguration.Id>? = null,
    val intervalIdList: List<IntervalId>? = null,
    val searchTerm: String? = null
) {
    companion object {
        // empty companion object so we can add extension functions
    }
}

@Serializable
sealed class Form {
    abstract val formConfigurationId: FormConfiguration.Id
    abstract val foundationId: Foundation.Id
    abstract val seriesId: SeriesConfiguration.Id? // TODO remove - we can derive this from formConfiguration
    abstract val intervalId: IntervalId?

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    @JvmInline
    value class DocumentId(val value: String)

    @Serializable
    @JvmInline
    value class AnnotationDocumentId(val value: String)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    data class ForCreate(
        override val formConfigurationId: FormConfiguration.Id,
        override val foundationId: Foundation.Id,
        override val seriesId: SeriesConfiguration.Id? = null, // TODO remove
        override val intervalId: IntervalId? = null,
    ) : Form()

    @Serializable
    data class ForUpdate(
        override val formConfigurationId: FormConfiguration.Id,
        override val foundationId: Foundation.Id,
        override val seriesId: SeriesConfiguration.Id? = null, // TODO remove
        override val intervalId: IntervalId? = null,
    ) : Form()

    @Serializable
    data class ForApi(
        val id: Id,
        override val formConfigurationId: FormConfiguration.Id,
        override val foundationId: Foundation.Id,
        override val seriesId: SeriesConfiguration.Id? = null,
        override val intervalId: IntervalId? = null,
        val documentId: DocumentId? = null,
        val annotationDocumentId: AnnotationDocumentId? = null,
        val workspaceId: Workspace.Id,
        val metadata: EntityMetadata,
        val foundation: Foundation.ForApi
    ) : Form() {
        fun toMap(foundationConfiguration: FoundationConfiguration.ForApi): JsonObject {
            return JsonObject(
                mapOf(
                    "id" to valueOrNull(id.value),
                    "formConfigurationId" to valueOrNull(formConfigurationId.value),
                    "foundationId" to valueOrNull(foundationId.value),
                    "foundation" to foundation.toMap(foundationConfiguration),
                    "seriesId" to valueOrNull(seriesId?.value),
                    "intervalId" to valueOrNull(intervalId?.value),
                    "documentId" to valueOrNull(documentId?.value),
                    "annotationDocumentId" to valueOrNull(annotationDocumentId?.value),
                )
            )
        }
    }

    fun validate(): ValidationErrors {
        val errors = Validator<Form>(
            listOf(

            )
        ).validate(this)

        return ValidationErrors.from(errors)
    }

}


class FormEntity(id: EntityID<Long>) : BaseLongEntity(id, Forms) {
    companion object : BaseLongEntityClass<FormEntity>(Forms)

    var formConfigurationId by Forms.formConfigurationId
    var seriesId by Forms.seriesId
    var intervalId by Forms.intervalId
    var foundation by FoundationEntity referencedOn Forms.foundation
    var documentId by Forms.documentId
    var annotationDocumentId by Forms.annotationDocumentId
    var workspace by WorkspaceEntity referencedOn Forms.workspace
    var tenantId by Forms.tenantId

    fun toDTO(): Form.ForApi {
        return Form.ForApi(
            id = Form.Id(this.id.value),
            formConfigurationId = FormConfiguration.Id(this.formConfigurationId),
            foundationId = Foundation.Id(this.foundation.id.value),
            intervalId = if (this.intervalId != null) IntervalId(this.intervalId!!) else null,
            seriesId = if (this.seriesId != null) SeriesConfiguration.Id(this.seriesId!!) else null,
            documentId = if (this.documentId != null) Form.DocumentId(this.documentId!!) else null,
            annotationDocumentId = if (this.annotationDocumentId != null) Form.AnnotationDocumentId(this.annotationDocumentId!!) else null,
            workspaceId = Workspace.Id(this.workspace.id.value),
            metadata = EntityMetadata.from(this),
            foundation = this.foundation.toDTO()
        )
    }

}

class FormRepository(val check: Checks) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "name" to Foundations.name, "metadata.updatedAt" to Forms.updatedAt
            )
        )
    }

    suspend fun select(predicate: SqlExpressionBuilder.() -> Op<Boolean>): Form.ForApi? {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = FormEntity.find { SqlExpressionBuilder.predicate() }.firstOrNull()
            return@dbQueryWithTenant dao?.toDTO()
        }
    }

    suspend fun getById(id: Long): Form.ForApi? {
        return DatabaseUtils.dbQueryWithTenant {
            // join with foundation
            val dao = Forms.innerJoin(Foundations).selectAll().andWhere { Forms.id eq id }.toList()
            val result = dao.map(FormEntity::wrapRow).firstOrNull()
            return@dbQueryWithTenant result?.toDTO()
        }
    }

    suspend fun search(
        pageRequest: PageRequest, formSearchCriteria: FormSearchCriteria
    ): Page<FormEntity> {
        return DatabaseUtils.dbQueryWithTenant {

            return@dbQueryWithTenant formSearchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
                FormEntity.wrapRow(
                    it
                )
            }
        }
    }

    suspend fun delete(id: Form.Id) {
        return DatabaseUtils.dbQueryWithTenant {
            FormEntity.findById(id.value)?.delete()
        }
    }

    suspend fun create(
        workspaceEntity: WorkspaceEntity, form: Form.ForCreate
    ): Form.ForApi {
        val formEntity = DatabaseUtils.dbQueryWithTenant { tenant ->
            val foundationEntity =
                check.exists(FoundationEntity.findById(form.foundationId.value)) { "Unknown foundation" }

            val dao = FormEntity.new {
                formConfigurationId = form.formConfigurationId.value
                foundation = foundationEntity
                intervalId = form.intervalId?.value

                tenantId = tenant.id
                workspace = workspaceEntity
            }
            return@dbQueryWithTenant dao
        }
        return getById(formEntity.id.value)!!
    }

    suspend fun update(formId: Form.Id, form: Form.ForUpdate): Form.ForApi {
        return DatabaseUtils.dbQueryWithTenant {
            val foundationEntity =
                check.exists(FoundationEntity.findById(form.foundationId.value)) { "Unknown foundation" }

            val dao = FormEntity.findByIdAndUpdate(formId.value) {
                it.formConfigurationId = form.formConfigurationId.value
                it.foundation = foundationEntity
                it.intervalId = form.intervalId?.value
            }
            return@dbQueryWithTenant getById(dao!!.id.value)!!
        }
    }

    /**
     * This allows us to update fields after creation. Used to migrate objects when we add new capability or if we
     * have fields that are system controlled (not user controlled) and need to be lazily set.
     */
    suspend fun updatePrivateFields(
        form: Form.ForApi,
    ): Form.ForApi {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = FormEntity.findByIdAndUpdate(form.id.value) {
                it.documentId = form.documentId?.value
                it.annotationDocumentId = form.annotationDocumentId?.value
            }
            return@dbQueryWithTenant getById(dao!!.id.value)!!
        }
    }

}

fun FormSearchCriteria.toQuery(): Query {
    val query = Forms.innerJoin(Foundations).selectAll().andWhere { Forms.workspace eq workspaceId.value }
    if (foundationId?.value != null) {
        query.andWhere { Forms.foundation eq foundationId.value }
    }
    if (foundationParentId != null) {
        query.andWhere { Foundations.parentId eq foundationParentId.value }
    }
    if (foundationConfigurationId?.value?.isNotBlank() == true) {
        query.andWhere { Foundations.foundationConfigurationId eq foundationConfigurationId.value }
    }
    if (formConfigurationId?.value?.isNotBlank() == true) {
        query.andWhere { Forms.formConfigurationId eq formConfigurationId.value }
    }

    val intervalIds = intervalIdList?.map(IntervalId::value).orEmpty()
    val formConfigurationIds = formConfigurationIdList?.map(FormConfiguration.Id::value).orEmpty()
    query.andWhere {
        (Forms.intervalId inList intervalIds) or
                (Forms.formConfigurationId inList formConfigurationIds) or
                (Foundations.name.lowerCase() like "%${searchTerm?.lowercase()}%")
    }
    return query
}