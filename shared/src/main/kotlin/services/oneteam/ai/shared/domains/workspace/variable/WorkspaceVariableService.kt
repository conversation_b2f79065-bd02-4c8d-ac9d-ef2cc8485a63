package services.oneteam.ai.shared.domains.workspace.variable

import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.variable.secured.WorkspaceSecuredValue
import services.oneteam.ai.shared.domains.workspace.variable.secured.WorkspaceSecuredValueService
import services.oneteam.ai.shared.helpers.CustomNanoId
import services.oneteam.ai.shared.withTenantTransactionScope

class WorkspaceVariableService(
    val workspaceSecuredValueService: WorkspaceSecuredValueService,
    val workspaceVariableRepository: WorkspaceVariableRepository
) {
    suspend fun create(workspaceId: Workspace.Id, createParams: WorkspaceVariable.ForCreate): WorkspaceVariable.ForApi = withTenantTransactionScope { tenant ->
        val (value, securedValueId) = if (createParams.isSecured) {
            val securedValueId = workspaceSecuredValueService.create(createParams.value.revealedValue, workspaceId)
            Pair<WorkspaceVariable.RevealedValue?, WorkspaceSecuredValue.Id?>(null, securedValueId)
        } else {
            Pair<WorkspaceVariable.RevealedValue?, WorkspaceSecuredValue.Id?>(createParams.value, null)
        }

        val uniqueRefIdentifier = CustomNanoId.generate()
        return@withTenantTransactionScope workspaceVariableRepository.create(
            ref = uniqueRefIdentifier,
            value = value?.revealedValue,
            secured = createParams.isSecured,
            workspaceSecuredValueId = securedValueId,
            workspaceId = workspaceId,
            tenant = tenant
        )
    }

    suspend fun getAllWithoutVersion(workspaceId: Workspace.Id): List<WorkspaceVariable.ForApi> = withTenantTransactionScope {
        return@withTenantTransactionScope workspaceVariableRepository.getAll(
            workspaceId = workspaceId
        )
    }

    suspend fun getAllByVersion(workspaceId: Workspace.Id, workspaceVersionId: WorkspaceVersion.Id): List<WorkspaceVariable.ForApi> = withTenantTransactionScope {
        return@withTenantTransactionScope workspaceVariableRepository.getAll(
            workspaceId = workspaceId,
            workspaceVersionId = workspaceVersionId
        )
    }

    suspend fun delete(workspaceId: Workspace.Id, workspaceVariableId: WorkspaceVariable.Id) = withTenantTransactionScope {
        workspaceVariableRepository.delete(workspaceId, workspaceVariableId)
    }

    // would need to be called when a workspace version is created
    suspend fun createVersion(
        variableRefs: List<String>,
        workspaceVersion: WorkspaceVersion.ForApi
    ): List<WorkspaceVariable.ForApi> = withTenantTransactionScope { tenant ->
        val asSet = variableRefs.map { WorkspaceVariable.Ref(it) }.toSet()
        return@withTenantTransactionScope workspaceVariableRepository.createVersionWithIdentifiers(
            asSet, workspaceVersion.id, workspaceVersion.workspaceId, tenant
        )
    }

    suspend fun getAllByVersionAndReveal(workspaceId: Workspace.Id, workspaceVersionId: WorkspaceVersion.Id): List<WorkspaceVariable.ForServiceFullyRevealed> = withTenantTransactionScope {
        val variables = getAllByVersion(workspaceId, workspaceVersionId)

        val securedValuesToReveal = variables.mapNotNull { it.workspaceSecuredValueId }
        val revealedSecuredValues = workspaceSecuredValueService.getManyRevealed(securedValuesToReveal).associateBy { it.id }

        val fullyRevealedVariables = variables.map {
            val revealedValue = if (it.isSecured) {
                revealedSecuredValues[it.workspaceSecuredValueId!!]?.value?.revealedValue ?: throw IllegalStateException("Revealed value for secured variable ${it.id} not found")
            } else {
                it.value!!
            }

            WorkspaceVariable.ForServiceFullyRevealed(
                id = it.id,
                workspaceId = it.workspaceId,
                workspaceVersionId = it.workspaceVersionId,
                ref = it.ref,
                value = WorkspaceVariable.RevealedValue(revealedValue),
                isSecured = it.isSecured,
                workspaceSecuredValueId = it.workspaceSecuredValueId,
                metadata = it.metadata
            )
        }

        return@withTenantTransactionScope fullyRevealedVariables
    }
}
