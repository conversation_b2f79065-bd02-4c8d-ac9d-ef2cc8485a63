package services.oneteam.ai.shared.domains.flow.configuration

import kotlinx.coroutines.runBlocking
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration.Step.Variant
import services.oneteam.ai.shared.domains.workspace.HasId
import services.oneteam.ai.shared.domains.workspace.LabelConfiguration
import services.oneteam.ai.shared.domains.workspace.validation.Errors
import services.oneteam.ai.shared.domains.workspace.validation.WorkspaceValidationContext

typealias NextStepId = FlowConfiguration.Step.Id

@Serializable
enum class FlowConfigurationStatusType {
    @SerialName("active")
    ACTIVE,

    @SerialName("inactive")
    INACTIVE,
}

@Serializable
sealed class FlowConfiguration {
    abstract val labels: List<LabelConfiguration.Id>?

    @Serializable
    @JvmInline
    value class Id(val value: String? = "")

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Description(val value: String?)

    @Serializable
    val steps: Map<Step.Id, Step>? = null

    @Serializable
    val status: FlowConfigurationStatusType? = FlowConfigurationStatusType.ACTIVE

    @Serializable
    data class ForApi(val id: Id)

    @OptIn(ExperimentalSerializationApi::class)
    @Serializable
    data class ForJson(
        override val id: Id = Id(),
        val name: Name = Name(""),
        val description: Description? = null,
        val start: Step.Id? = null,
        val steps: MutableMap<Step.Id, Step>? = null,
        val status: FlowConfigurationStatusType? = FlowConfigurationStatusType.ACTIVE,
        @EncodeDefault
        val labels: List<LabelConfiguration.Id>? = listOf(),
        val metadata: EntityMetadata? = null,
        val triggers: Map<Step.Id, Trigger>? = null,
        val startingVariables: List<Content>? = null,
        val endingVariables: List<Content>? = null,
        val properties: Properties? = null,
    ) : HasId<Id> {
        fun validateConfiguration(path: String, context: WorkspaceValidationContext): Errors = runBlocking {
            val errors = Errors()
            steps?.forEach { (_, step) ->
                FlowStepValidator(context).validate(step.id, step.variant, step.properties, path, errors)
            }
            triggers?.forEach { (_, trigger) ->
                FlowStepValidator(context).validate(trigger.id, trigger.variant, trigger.properties, path, errors)
            }
            return@runBlocking errors

        }

        fun allSteps(): List<Step> {
            val allSteps = mutableListOf<Step>()
            steps?.forEach { (_, step) ->
                allSteps.add(step)
                // process nested flows
                if (step.properties.configuration != null) {
                    allSteps.addAll(step.properties.configuration.allSteps())
                }
            }
            return allSteps
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    @Serializable
    data class Trigger(
        override val id: Step.Id,
        val name: String,
        @EncodeDefault
        val variant: Variant = Variant.TRIGGER,
        val properties: Step.Properties,
        val next: NextStepId? = null,
        val metadata: EntityMetadata? = null,
    ) : HasId<Step.Id>

    /**
     * FlowConfiguration.Step
     *
     * Validation
     * - if variant is setVariables then `properties.variables` must not be empty
     * - if variant is condition then `properties.branches` must not be empty
     */
    @Serializable
    data class Step(
        override val id: Id,
        val variant: Variant,
        val name: String? = null,
        val properties: Properties,
        val next: NextStepId? = null,
        val metadata: EntityMetadata? = null,
    ) : HasId<Step.Id> {
        @Serializable
        @JvmInline
        value class Id(val value: String)

        @Serializable
        data class Properties(
            // Trigger, Action, Iterator
            val typePrimaryIdentifier: String? = "",
            val inputs: Map<String, JsonElement> = emptyMap(),

            // Condition
            val branches: List<ConditionBranch>? = null,

            // Set Variables
            val variables: List<Variable>? = null,

            // Iterator
            val configuration: ForJson? = null,
        ) {
            @Serializable
            data class ConditionBranch(
                val name: String,
                val condition: JsonElement,
                val next: NextStepId? = null,
            )

            @Serializable
            data class Variable(
                val type: String,
                val identifier: String,
                val value: JsonElement,
                val properties: JsonElement? = null,
            )
        }

        @Serializable
        enum class Variant {
            @SerialName("action")
            ACTION,

            @SerialName("setVariables")
            SET_VARIABLES,

            @SerialName("condition")
            CONDITION,

            @SerialName("iterator")
            ITERATOR,

            @SerialName("trigger")
            TRIGGER,

            @SerialName("flow")
            FLOW;
        }
    }

    @Serializable
    data class Content(
        val type: String,
        val identifier: String,
        val properties: Properties? = null,
    ) {
        @Serializable
        data class Properties(
            val type: String? = null,
            val required: Boolean? = null,
            val regex: String? = null,
            val options: List<Option>? = null,
            val dynamicOptions: DynamicOptions? = null,
            val properties: Properties? = null,
            val defaultValue: String? = null,
        ) {
            @Serializable
            data class Option(
                val label: String, val value: String
            )
        }

        @Serializable
        data class DynamicOptions(
            val tag: String? = null,
            val body: JsonElement? = null
        )
    }

    @Serializable
    data class Properties(
        val hidden: Boolean? = false,
    )
}
