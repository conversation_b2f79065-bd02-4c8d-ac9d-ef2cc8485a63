package services.oneteam.ai.shared.domains.collection.form

import kotlinx.serialization.json.jsonPrimitive
import services.oneteam.ai.shared.domains.collection.foundation.constructFoundationMinimalFromFoundation
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.workspace.BaseSection
import services.oneteam.ai.shared.domains.workspace.FormConfiguration
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService

suspend fun getFormConfiguration(
    workspaceVersionService: WorkspaceVersionService,
    targetForm: Form.ForApi
): FormConfiguration.ForJson? {
    val workspace = workspaceVersionService.findVersion(targetForm.workspaceId)
    return workspace.configuration.forms[targetForm.formConfigurationId]
}

fun constructFormMinimalFromForm(
    form: Form.ForApi,
    formConfiguration: FormConfiguration.ForJson,
    foundationConfiguration: FoundationConfiguration.ForApi
): Event.FormMinimal {
    return Event.FormMinimal(
        id = form.id.value,
        foundationId = form.foundationId.value,
        foundation = constructFoundationMinimalFromFoundation(
            foundationConfiguration = foundationConfiguration,
            foundation = form.foundation
        ),
        formConfiguration = constructFormConfiguration(formConfiguration),
        documentId = form.documentId?.value,
        intervalId = form.intervalId?.value,
    )
}

fun constructFormConfiguration(formConfiguration: FormConfiguration.ForJson): Event.FormConfigurationMinimal {
    return Event.FormConfigurationMinimal(
        id = formConfiguration.id,
        key = formConfiguration.key,
        name = formConfiguration.name,
        seriesId = formConfiguration.seriesId,
    )
}

fun constructQuestion(question: BaseSection.BaseQuestion, formAnswer: FormAnswerRequestBody): Event.Question {
    return Event.Question(
        id = question.id.value,
        type = question.type.toString(),
        text = question.text,
        identifier = question.identifier,
        properties = null, // unstructured data
        answer = formAnswer.value
    )
}