package services.oneteam.ai.shared

import kotlinx.serialization.Serializable

@Serializable
data class AuthenticatedUser(
    val id: Long,
    val firstName: String,
    val lastName: String,
    val email: String,
    val tenantId: Long,
    val hostId: Int,
    val autoLogoutIdleTimer: AutoLogout,
)

@Serializable
data class AutoLogout(val enabled: Boolean, val value: Int)

@Serializable
class UserPrincipal(val user: AuthenticatedUser)

@Serializable
data class UserSession(val user: AuthenticatedUser, var maxTime: Long)

