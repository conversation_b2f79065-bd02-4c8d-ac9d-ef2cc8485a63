package services.oneteam.ai.shared.domains.workspace.validation

import com.fasterxml.jackson.databind.JsonNode
import com.jayway.jsonpath.ParseContext

/**
 * This class is used to build a validator
 * to validate duplicate fields in json object when the path has more than 1 unknown (*)
 * @param path the path to the json object
 * @param field the field to validate
 * @param errorKey the error key to use
 * @param parseContext the parse context to use
 * @param caseSensitive whether the validation should be case-sensitive
 */
class JsonPathMultiLevelDuplicateValidator(
    private val path: String,
    private val field: String,
    private val errorKey: String,
    private val parseContext: ParseContext,
    private val caseSensitive: Boolean = false
) {

    /**
     * This function is used to build the validator
     * @param jsonDocument the json document to validate
     * example:
     * path = "$.series.*.intervals.entities.*.name"
     * This function will replace the first * with the actual id of the series
     * (e.g. $.series.seriesId1.intervals.entities.*.name,
     * $.series.seriesId2.intervals.entities.*.name)
     * And create a JsonPathDuplicateConstraint for each path with the replaced id
     * @return the validator built with the constraints above
     * End result is to validate that the name field is unique for each interval within each series
     * @see JsonPathDuplicateConstraint
     * @see services.oneteam.ai.shared.domains.workspace.document.WorkspaceDocumentValidator
     * Can be later extended to have more than 2 levels of unknowns (*) with loops
     */
    fun build(jsonDocument: JsonNode): Constraint<JsonNode> {

        var jsonConstraints: MutableList<JsonPathDuplicateConstraint> = mutableListOf()

        val firstIds: JsonNode = parseContext.parse(jsonDocument).read(replaceFirstWithId(path))
        firstIds.forEachIndexed { index, id ->
            val pathCurrent = path.replaceFirst("*", if (id != null) id.asText() else index.toString())
            jsonConstraints.add(
                JsonPathDuplicateConstraint(
                    pathCurrent,
                    field,
                    errorKey,
                    parseContext,
                    caseSensitive
                )
            )
        }

        return Validator<JsonNode>(jsonConstraints)
    }

    /**
     * This function is used to replace the string after the first * with .id
     * @param string the string to replace, eg: $.series.*.intervals.entities.*.name
     * @return the replaced string, eg: $.series.id
     */
    private fun replaceFirstWithId(string: String): String {
        val parts = string.split(".")
        val idIndex = parts.indexOfFirst { it == "*" }
        return if (idIndex > 0) {
            parts.subList(0, idIndex + 1).joinToString(".") + ".id"
        } else {
            string
        }
    }
}