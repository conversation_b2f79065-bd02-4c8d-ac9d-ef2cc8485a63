package services.oneteam.ai.shared.domains.passvault

import org.bouncycastle.crypto.generators.OpenBSDBCrypt
import org.bouncycastle.jce.provider.BouncyCastleProvider
import services.oneteam.ai.shared.domains.tenant.Tenant
import java.security.SecureRandom
import java.security.Security
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.SecretKeySpec

typealias Base64String = String
typealias OtaiEncryptedData = Base64String
typealias OtaiHashedData = String

/** Initialization vector length in bytes for AES-GCM */
const val ENCRYPTION_INITIALIZATION_VECTOR_LENGTH = 12
/** Authentication tag length in bits for AES-GCM */
const val ENCRYPTION_AUTHENTICATION_TAG_LENGTH = 128
/** Encryption algorithm, mode, and padding scheme */
const val ENCRYPTION_SCHEME = "AES/GCM/NoPadding"

/** Work factor for BCrypt hashing */
const val BCRYPT_WORK_FACTOR = 10
const val BCRYPT_SALT_LENGTH = 16

/**
 * Use this class to encrypt and decrypt sensitive data using a tenant-specific encryption key.
 */
class PassVault(val keyVaultService: KeyVaultService) {
    init {
        Security.addProvider(BouncyCastleProvider())
    }

    fun getTenantLocksmith(tenant: Tenant): PassVaultLocksmith {
        val encryptionKey = getEncryptionKey(tenant)
        return PassVaultLocksmith(encryptionKey)
    }

    private fun getEncryptionKey(tenant: Tenant): SecretKey {
        val secretDataEncryptionKeyKVSecretName = dataEncryptionKeyName(tenant.name)
        val symmetricKeyBase64 = keyVaultService.retrieveSecret(secretDataEncryptionKeyKVSecretName)
        val symmetricKey = Base64.getDecoder().decode(symmetricKeyBase64)
        val key = SecretKeySpec(symmetricKey, "AES")
        return key;
    }

    fun getHasher(): PassVaultHasher {
        return PassVaultHasher()
    }

    companion object {
        internal fun dataEncryptionKeyName(tenantName: String): String {
            return "${tenantName}-passVault-dataEncryptionKey"
        }
    }
}

/**
 * Don't instantiate this class directly, use [PassVault.getHasher] instead.
 */
class PassVaultHasher() {
    private val secureRandom = SecureRandom()

    /**
     * Hashes the given data
     * @return Hashed data in the form of a String
     */
    fun hash(data: String): OtaiHashedData {
        val contents = data.toCharArray()
        val salt = generateSalt()
        return OpenBSDBCrypt.generate(contents, salt, BCRYPT_WORK_FACTOR)
    }

    /**
     * Checks whether the data corresponds to the given hash.
     * @return true if the data matches the hash, false otherwise
     */
    fun compareWithHash(data: String, hash: OtaiHashedData): Boolean {
        val contents = data.toCharArray()
        return OpenBSDBCrypt.checkPassword(hash, contents)
    }

    private fun generateSalt(): ByteArray {
        val salt = ByteArray(BCRYPT_SALT_LENGTH)
        secureRandom.nextBytes(salt)
        return salt
    }
}

/**
 * Don't instantiate this class directly, use [PassVault.getTenantLocksmith] instead.
 */
class PassVaultLocksmith(val encryptionKey: SecretKey): PassVaultEncrypter, PassVaultDecrypter {
    private val secureRandom = SecureRandom()

    init {
        require(encryptionKey.algorithm == "AES") { "Only AES keys are supported" }
        require(encryptionKey.encoded.size == 32) { "Key size must be 256 bits" }
    }

    override fun encrypt(data: String): OtaiEncryptedData {
        val iv = ByteArray(ENCRYPTION_INITIALIZATION_VECTOR_LENGTH)
        secureRandom.nextBytes(iv)
        val gcmSpec = GCMParameterSpec(ENCRYPTION_AUTHENTICATION_TAG_LENGTH, iv)

        val cipher = Cipher.getInstance(ENCRYPTION_SCHEME)
        cipher.init(Cipher.ENCRYPT_MODE, encryptionKey, gcmSpec)
        val encryptedBytes = cipher.doFinal(data.toByteArray())

        val ivAndEncryptedBytes = iv + encryptedBytes
        return Base64.getEncoder().encodeToString(ivAndEncryptedBytes)
    }

    override fun decrypt(data: OtaiEncryptedData): String {
        require(data.isNotEmpty()) { "Encrypted data cannot be empty" }

        val ivAndEncryptedBytes = Base64.getDecoder().decode(data)
        require(ivAndEncryptedBytes.size > ENCRYPTION_INITIALIZATION_VECTOR_LENGTH) { "Invalid encrypted data length" }

        val iv = ivAndEncryptedBytes.copyOfRange(0, ENCRYPTION_INITIALIZATION_VECTOR_LENGTH)
        val encryptedData = ivAndEncryptedBytes.copyOfRange(ENCRYPTION_INITIALIZATION_VECTOR_LENGTH, ivAndEncryptedBytes.size)

        val cipher = Cipher.getInstance(ENCRYPTION_SCHEME)
        val gcmSpec = GCMParameterSpec(ENCRYPTION_AUTHENTICATION_TAG_LENGTH, iv)
        cipher.init(Cipher.DECRYPT_MODE, encryptionKey, gcmSpec)

        val decryptedData = cipher.doFinal(encryptedData)
        return String(decryptedData)
    }
}

fun interface PassVaultDecrypter {
    fun decrypt(data: OtaiEncryptedData): String
}

fun interface PassVaultEncrypter {
    fun encrypt(data: String): OtaiEncryptedData
}
