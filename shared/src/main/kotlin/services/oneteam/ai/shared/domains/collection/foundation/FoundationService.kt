package services.oneteam.ai.shared.domains.collection.foundation

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder
import org.jetbrains.exposed.sql.and
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.foundation.Foundation.Id
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import services.oneteam.ai.shared.withTenantTransactionScope

class FoundationService(
    val foundationRepository: FoundationRepository,
    val workspaceVersionService: WorkspaceVersionService,
    val check: Checks
) {

    suspend fun search(
        workspaceId: Workspace.Id,
        searchTerm: String?,
        parentKey: String?,
        parentConfigurationId: String?,
        pageRequest: PageRequest
    ): Page<Foundation.ForApi> = withTenantTransactionScope {

        // maybe do this as a subquery
        val parent =
            if (!parentKey.isNullOrBlank() && !parentConfigurationId.isNullOrBlank()) foundationRepository.getByKeyAndConfigurationId(
                workspaceId.value, parentKey, parentConfigurationId
            ) else foundationRepository.getRoot(
                workspaceId.value
            )

        val foundationSearchCriteria = FoundationSearchCriteria(
            workspaceId,
            searchTerm,
            parentId = parent?.id?.value,
        )

        val searchResult = foundationRepository.search(
            foundationSearchCriteria, pageRequest
        )

        return@withTenantTransactionScope Page(
            searchResult.page,
            searchResult.total,
            searchResult.items.map { it.toDTO() })
    }

    suspend fun searchByKeywords(
        workspaceId: Workspace.Id,
        pageRequest: PageRequest,
        keywords: String?,
        foundationConfigIds: List<FoundationConfiguration.Id>?
    ): Page<Foundation.ForApi> = withTenantTransactionScope {
        val foundationSearchCriteria = FoundationSearchCriteria(
            workspaceId, foundationConfigurationIdList = foundationConfigIds, name = keywords
        )
        val searchResult = foundationRepository.search(foundationSearchCriteria, pageRequest)
        return@withTenantTransactionScope Page(
            searchResult.page,
            searchResult.total,
            searchResult.items.map { it.toDTO() })
    }

    suspend fun obtainFoundation(
        params: ObtainFoundationParams
    ): ObtainFoundationResponse = withTenantTransactionScope {
        val isAllowNull = params.allowNull
        val foundation = select(params)
        val createIfNotExists = params.createIfNotExists
        if (foundation != null) {
            return@withTenantTransactionScope ObtainFoundationResponse(foundation, true)
        }
        if (createIfNotExists == true) {
            val foundationName = params.name?.value
            val parentId = params.parentId
            if (foundationName == null || foundationName == "" || parentId == null) {
                throw BadRequestException("name and parentId are required")
            }
            val workspace = workspaceVersionService.findVersion(params.workspaceId)
            val isFoundationContained = workspace.containsFoundation(params.foundationConfigurationId.value)
            val isRootFoundation = workspace.isRootFoundation(params.foundationConfigurationId.value)
            if (isRootFoundation || !isFoundationContained) {
                throw BadRequestException("Invalid Params")
            }
            val foundationForCreate = Foundation.ForCreate(
                name = params.name,
                key = params.key,
                foundationConfigurationId = params.foundationConfigurationId,
                workspaceId = params.workspaceId,
                parentId = parentId
            )
            val newFoundation = create(foundationForCreate)
            return@withTenantTransactionScope ObtainFoundationResponse(newFoundation, false)
        } else if (isAllowNull == true) {
            return@withTenantTransactionScope ObtainFoundationResponse(null, false)
        }
        throw BadRequestException("Foundation not found")
    }

    suspend fun select(params: ObtainFoundationParams): Foundation.ForApi? = withTenantTransactionScope {
        val predicate: SqlExpressionBuilder.() -> Op<Boolean> = {
            (Foundations.key eq params.key.value) and (Foundations.foundationConfigurationId eq params.foundationConfigurationId.value) and (Foundations.workspaceId eq params.workspaceId.value) and (params.name?.value?.takeIf { it.isNotBlank() }
                ?.let { Foundations.name eq it }
                ?: Op.TRUE) and (params.parentId?.value?.let { Foundations.parentId eq it } ?: Op.TRUE)
        }
        return@withTenantTransactionScope foundationRepository.select(predicate)
    }

    suspend fun validateFoundation(foundation: Foundation.ForCreate) = withTenantTransactionScope {
        val workspace = workspaceVersionService.findVersion(foundation.workspaceId)
        val orders = workspace.configuration.foundations.order
        val parentFoundation = get(id = foundation.parentId!!)
        val parentLevel = orders.indexOfFirst { it.value == parentFoundation.foundationConfigurationId.value }
        if (parentLevel + 1 < orders.size) {
            val nextFoundationConfigurationId = orders[parentLevel + 1].value
            if (foundation.foundationConfigurationId.value != nextFoundationConfigurationId) {
                throw BadRequestException("Invalid Request")
            }
        } else {
            throw BadRequestException("Invalid Request")
        }
    }

    /**
     * Create a new foundation for a workspace
     * - Check the foundationConfiguration should be the next level of parentId
     */
    suspend fun create(foundation: Foundation.ForCreate, skipValidation: Boolean = false): Foundation.ForApi =
        withTenantTransactionScope { tenant ->
            if (!skipValidation) {
                validateFoundation(foundation)
            }
            return@withTenantTransactionScope foundationRepository.create(tenant, foundation).toDTO()
        }

    /**
     * Update foundation for a workspace
     * - Check that the linked foundation configuration exists for the workspace
     */
    suspend fun update(foundation: Foundation.ForUpdate): Foundation.ForApi = withTenantTransactionScope {
        // to do we'll need to get the appropriate workspace configuration version to validate
        // to do validation:
        //   check if foundation configuration exists for the workspace
        return@withTenantTransactionScope foundationRepository.update(foundation).toDTO()
    }

    suspend fun updateProperties(foundationId: Id, properties: JsonObject, workspaceId: Workspace.Id? = null): Foundation.ForApi =
        withTenantTransactionScope {
            findOneOrThrow(foundationId, workspaceId)
            return@withTenantTransactionScope foundationRepository.updateProperties(foundationId, properties)
        }

    suspend fun delete(foundationId: Id) = withTenantTransactionScope {
        foundationRepository.delete(findOneOrThrow(foundationId).id)
    }

    suspend fun get(id: Id): Foundation.ForApi = withTenantTransactionScope {
        return@withTenantTransactionScope findOneOrThrow(id).toDTO()
    }

    suspend fun get(
        workspaceId: Workspace.Id, key: Foundation.Key, configurationId: FoundationConfiguration.Id
    ): Foundation.ForApi = withTenantTransactionScope {
        return@withTenantTransactionScope (check.exists(
            foundationRepository.getByKeyAndConfigurationId(
                workspaceId.value, key.value, configurationId.value
            )
        ) { "Unknown foundation" }).toDTO()
    }

    suspend fun getFoundationHierarchy(id: Id): List<Foundation.ForApi> = withTenantTransactionScope {
        val hierarchy = foundationRepository.getHierarchyForFoundation(id.value)
        check.exists(hierarchy) { "Unknown foundation" }
        return@withTenantTransactionScope hierarchy!!.map { it.toDTO() }
    }

    suspend fun root(workspaceId: Workspace.Id): Foundation.ForApi = withTenantTransactionScope {
        return@withTenantTransactionScope foundationRepository.getRoot(workspaceId.value).toDTO()
    }

    /**
     * Find a foundation by id or throw a not found exception.
     */
    private fun findOneOrThrow(foundationId: Id, workspaceId: Workspace.Id? = null): FoundationEntity {
        val foundationEntity = if (workspaceId != null) {
            foundationRepository.getByIdAndWorkspaceId(foundationId.value, workspaceId.value)
        } else {
            foundationRepository.getById(foundationId.value)
        }
        check.exists(foundationEntity) { "Unknown foundation" }
        return foundationEntity!!
    }

}

@Serializable
data class ObtainFoundationParams(
    val workspaceId: Workspace.Id,
    val foundationConfigurationId: FoundationConfiguration.Id,
    val key: Foundation.Key,
    val name: Foundation.Name? = null,
    val parentId: Id? = null,
    val allowNull: Boolean? = null,
    val createIfNotExists: Boolean? = false
)

@Serializable
data class ObtainFoundationResponse(
    val foundation: Foundation.ForApi?,
    val alreadyExists: Boolean = false,
)

@Serializable
data class FoundationPropertiesRequestBody(
    val properties: JsonObject,
    val workspaceId: Workspace.Id
)

