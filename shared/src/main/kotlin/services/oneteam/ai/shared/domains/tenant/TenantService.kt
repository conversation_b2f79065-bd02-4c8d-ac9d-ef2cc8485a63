package services.oneteam.ai.shared.domains.tenant

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction

@Serializable
data class Tenant(
    val id: Long, val name: String, val originUrl: String, val internalUrl: String, val internalSyncUrl: String
)

class TenantService(private val repository: TenantRepository) {

    private fun getByOriginUrl(originUrl: String): Tenant? = repository.getByOriginUrl(originUrl)

    private fun getByInternalUrl(internalUrl: String): Tenant? = repository.getByInternalUrl(internalUrl)

    suspend fun getByInternalUrlOrOriginUrl(url: String): Tenant? = newSuspendedTransaction {
        getByInternalUrl(url) ?: getByOriginUrl(url)
    }

    suspend fun findAll(): List<Tenant> = newSuspendedTransaction { repository.getAll() }
}