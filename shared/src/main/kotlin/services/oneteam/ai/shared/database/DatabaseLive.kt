package services.oneteam.ai.shared.database

import org.jetbrains.exposed.sql.Database
import services.oneteam.ai.shared.DatabaseConfig

class DatabaseLive(config: DatabaseConfig) : DBInterface {
    private val databaseConfig = config
    val standard = DataConnection(databaseConfig.application)
    val privileged = DataConnection(databaseConfig.privileged)

    override fun connect(): Database {
        return standard.database
    }

    override fun close() {
        standard.close()
    }

    override fun connectSuperUser(): Database {
        return privileged.database
    }

    override fun closeSuperUser() {
        privileged.close()
    }

    override fun isRLSEnabled(): Boolean {
        return databaseConfig.privileged.rls
    }
}