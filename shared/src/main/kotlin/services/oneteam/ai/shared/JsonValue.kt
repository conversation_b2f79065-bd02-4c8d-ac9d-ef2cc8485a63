package services.oneteam.ai.shared

import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonPrimitive

object JsonValue {
    fun valueOrNull(value: String?): JsonElement {
        return if (value != null) {
            return JsonPrimitive(value)
        } else {
            return JsonNull
        }
    }

    fun valueOrNull(value: Boolean?): JsonElement {
        return if (value != null) {
            return JsonPrimitive(value)
        } else {
            return JsonNull
        }
    }


    fun valueOrNull(value: Number?): JsonElement {
        return if (value != null) {
            return JsonPrimitive(value)
        } else {
            return JsonNull
        }
    }

}