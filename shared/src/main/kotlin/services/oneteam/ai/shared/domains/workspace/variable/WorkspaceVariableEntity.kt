package services.oneteam.ai.shared.domains.workspace.variable

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.or
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.variable.secured.WorkspaceSecuredValue

object WorkspaceVariableSchema : BaseLongIdTable("workspace_variables") {
    val ref = text("ref")

    // (value XOR securedValueId) depending on secured state
    val isSecured = bool("is_secured").default(false)
    val value = text("value").nullable()
    val securedValueId = long("workspace_secured_value_id").references(id).nullable()

    val workspaceVersionId = long("workspace_version_id").references(id).nullable()
    val workspaceId = long("workspace_id").references(id)
    val tenantId = long("tenant_id").references(id)

    init {
        uniqueIndex(tenantId, workspaceId, ref, workspaceVersionId)
        check {
            ((isSecured eq false) and securedValueId.isNull() and value.isNotNull()) or ((isSecured eq true) and securedValueId.isNotNull() and value.isNull())
        }
    }
}

class WorkspaceVariableEntity(
    id: EntityID<Long>,
) : BaseLongEntity(id, WorkspaceVariableSchema) {
    companion object : BaseLongEntityClass<WorkspaceVariableEntity>(WorkspaceVariableSchema)

    var ref by WorkspaceVariableSchema.ref
    var value by WorkspaceVariableSchema.value
    var secured by WorkspaceVariableSchema.isSecured
    var securedValueId by WorkspaceVariableSchema.securedValueId
    var workspaceVersionId by WorkspaceVariableSchema.workspaceVersionId
    var workspaceId by WorkspaceVariableSchema.workspaceId
    var tenantId by WorkspaceVariableSchema.tenantId;

    fun toDTO(): WorkspaceVariable.ForApi {
        return WorkspaceVariable.ForApi(
            id = WorkspaceVariable.Id(this.id.value),
            workspaceId = Workspace.Id(this.workspaceId),
            workspaceVersionId = this.workspaceVersionId?.let { WorkspaceVersion.Id(it) },
            ref = WorkspaceVariable.Ref(this.ref),
            value = this.value,
            isSecured = this.secured,
            workspaceSecuredValueId = this.securedValueId?.let { WorkspaceSecuredValue.Id(it) },
            metadata = EntityMetadata.from(this)
        )
    }
}

sealed class WorkspaceVariable {
    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    @JvmInline
    value class RevealedValue(val revealedValue: String)

    @Serializable
    @JvmInline
    value class Ref(val ref: String)

    @Serializable
    data class ForCreate(
        val value: RevealedValue,
        val isSecured: Boolean = true,
    ) : WorkspaceVariable()

    @Serializable
    data class ForApi(
        val id: Id,
        val workspaceId: Workspace.Id,
        val workspaceVersionId: WorkspaceVersion.Id? = null,
        val ref: Ref,
        val value: String? = null,
        val isSecured: Boolean = true,
        val workspaceSecuredValueId: WorkspaceSecuredValue.Id? = null,
        val metadata: EntityMetadata
    ) : WorkspaceVariable()

    @Serializable
    data class ForServiceFullyRevealed(
        val id: Id,
        val workspaceId: Workspace.Id,
        val workspaceVersionId: WorkspaceVersion.Id? = null,
        val ref: Ref,
        val value: RevealedValue,
        val isSecured: Boolean = true,
        val workspaceSecuredValueId: WorkspaceSecuredValue.Id? = null,
        val metadata: EntityMetadata
    ) : WorkspaceVariable()

}
