package services.oneteam.ai.shared.domains.auth

import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.selectAll
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.form.FormRepository.Companion.SORTABLE_FIELDS
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.extensions.paginate

class ApiKeyRepository() {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun create(tenant: Tenant, createParams: ApiKey.ForCreate, clientId: String, clientSecret: ApiKey.HashedClientSecret): ApiKey.ForApi {
        val entity = ApiKeyEntity.new {
            name = createParams.name
            description = createParams.description
            this.clientId = clientId
            this.clientSecret = clientSecret.value
            tenantId = tenant.id
        }
        return entity.toDTO();
    }

    fun search(
        pageRequest: PageRequest, searchCriteria: ApiKeySearchCriteria
    ): Page<ApiKey.ForApi> {
        val searchResult = searchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
            ApiKeyEntity.wrapRow( it )
        }
        return Page(
            searchResult.page, searchResult.total, searchResult.items.map { it.toDTO() }
        )
    }

    fun delete(
        apiKeyId: ApiKey.Id
    ) {
        ApiKeyEntity.findById(apiKeyId.value)?.delete()
    }

    fun getForLogin(clientId: String): ApiKey.ForLogin? {
        return ApiKeyEntity.find {
            ApiKeysSchema.clientId eq clientId
        }.firstOrNull()?.toDTOForLogin()
    }
}

private fun ApiKeySearchCriteria.toQuery(): Query {
    val query = ApiKeysSchema.selectAll()
    if (searchTerm?.isNotBlank() == true) {
        query.andWhere { ApiKeysSchema.name.lowerCase() like "%${searchTerm.lowercase()}%" }
    }
    return query;
}
