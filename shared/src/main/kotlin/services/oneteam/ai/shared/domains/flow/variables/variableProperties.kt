package services.oneteam.ai.shared.domains.flow.variables

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable(VariablePropertiesSerializer::class)
sealed class VariableProperties {

    @Serializable
    data class ListVariableProperties(
        val listOperation: ListOperation, val itemIndex: JsonElement? = null
    ) : VariableProperties()

    @Serializable
    data class FileVariableProperties(
        val fileOperation: FileOperation, val itemIndex: JsonElement? = null
    ) : VariableProperties()

    @Serializable
    data class JsonVariableProperties(
        val isUnstructured: Boolean = false,
        val items: List<VariableDefinition.VariableConfiguration>? = null,
        val properties: JsonVariableProperties? = null
    ) : VariableProperties()

    @Serializable
    data class OtherVariableProperties(
        val required: Boolean? = false
    ) : VariableProperties()

    @Serializable
    data class TableVariableProperties(
        val operation: TableOperation,
        val rowIdentifier: String? = null,
        val rowIndex: JsonElement? = null,
        val columnIdentifier: String? = null,
        val columns: List<VariableDefinition.VariableConfiguration>? = emptyList(),
    ) : VariableProperties()

}