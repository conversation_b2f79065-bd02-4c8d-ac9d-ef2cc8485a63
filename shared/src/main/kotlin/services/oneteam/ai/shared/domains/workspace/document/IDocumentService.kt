package services.oneteam.ai.shared.domains.workspace.document

import services.oneteam.ai.shared.domains.collection.form.FormAlertRequestSyncServerBody
import services.oneteam.ai.shared.domains.collection.form.SetAnswerParams
import services.oneteam.ai.shared.domains.workspace.CollaborationDocument
import kotlin.reflect.KClass

interface IDocumentService {

    suspend fun <T : CollaborationDocument> create(
        tenantId: Long,
        document: T,
        type: KClass<T>,
        timeoutMillis: Long? = null
    ): String

    suspend fun validate(document: String): ValidationResult

    suspend fun answerQuestion(
        documentId: String,
        cookie: String? = null,
        answers: List<SetAnswerParams>
    ): String

    suspend fun alertAnnotation(
        annotationDocumentId: String,
        cookie: String? = null,
        alerts: List<FormAlertRequestSyncServerBody>
    ): String


    suspend fun <T> show(
        id: String,
        cookie: String? = null,
        type: KClass<T>,
        _ignoreUnknownKeys: Boolean = false
    ): T where T : CollaborationDocument

    suspend fun <T : CollaborationDocument> update(
        id: String,
        cookie: String? = null,
        content: T,
        path: String,
        type: KClass<T>,
    ): String

    suspend fun <T : Any> upsertAtPosition(
        documentId: String,
        cookie: String? = null,
        position: String,
        content: T,
        type: KClass<T>,
        updateDelegate: (T) -> Unit = {}
    )
}

//JVM hacks. https://stackoverflow.com/questions/60037849/kotlin-reified-generic-in-virtual-function
//need to do this for virtual reified functions

suspend inline fun <reified T : CollaborationDocument> IDocumentService.create(
    tenantId: Long,
    document: T,
    timeoutMillis: Long? = null
) = create(tenantId, document, T::class, timeoutMillis)

suspend inline fun <reified T : CollaborationDocument> IDocumentService.show(
    id: String,
    cookie: String? = null,
    _ignoreUnknownKeys: Boolean = false
) = show(id, cookie, T::class, _ignoreUnknownKeys)


suspend inline fun <reified T : CollaborationDocument> IDocumentService.update(
    documentId: String,
    cookie: String? = null,
    content: T,
    path: String,
) = update(documentId, cookie, content, path, T::class)

suspend inline fun <reified T : Any> IDocumentService.upsertAtPosition(
    documentId: String,
    cookie: String? = null,
    position: String,
    content: T,
    noinline updateDelegate: (T) -> Unit = {}
) = upsertAtPosition(documentId, cookie, position, content, T::class, updateDelegate)