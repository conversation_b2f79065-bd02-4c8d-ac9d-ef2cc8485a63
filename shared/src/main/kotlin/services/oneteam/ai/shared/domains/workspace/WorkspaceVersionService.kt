package services.oneteam.ai.shared.domains.workspace

import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.*
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfigurationStatusType
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion.Id
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.domains.workspace.document.show
import services.oneteam.ai.shared.domains.workspace.validation.ConstraintError
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariableService
import services.oneteam.ai.shared.withTenantTransactionScope

/**
 * Service for managing workspace versions.
 *
 * This service will always look up the workspace by its key to ensure it exists,
 * and will throw NotFoundException if it does not, ensuring a 404 response is returned
 * when an invalid workspace is referenced.
 */
class WorkspaceVersionService(
    private val workspaceRepository: WorkspaceRepository,
    private val documentService: IDocumentService,
    private val workspaceVersionRepository: WorkspaceVersionRepository,
    private val workspaceVariableService: WorkspaceVariableService,
    val check: Checks
) {

    suspend fun createVersion(id: Workspace.Id, cookie: String): WorkspaceVersion.ForApi =
        withTenantTransactionScope { tenant ->
            val workspaceEntity = workspaceRepository.findOne(id)
            // TODO should we be ignoring unknown keys here when parsing the json? Ideally we'd want to know if the UI is creating invalid configurations??
            val json = documentService.show<Workspace.ForJson>(workspaceEntity.documentId!!.value, cookie, true)
            val hasErrors = hasErrors(json)
            if (hasErrors) {
                throw BadRequestException("Workspace version cannot be created when errors exist.")
            }

            val filteredFlows = json.filterActiveFlows()
            val version = workspaceVersionRepository.create(
                tenant,
                WorkspaceVersion.ForCreate(
                    workspaceId = Workspace.Id(workspaceEntity.id.value), configuration = filteredFlows
                )
            ).toDTO()

            workspaceVariableService.createVersion(
                variableRefs = json.getSecuredVariableRefs(),
                workspaceVersion = version
            )

            return@withTenantTransactionScope version
        }

    private fun hasErrors(json: Workspace.ForJson): Boolean {
        val filteredErrors = filterErrors(json)
        return filteredErrors.isNotEmpty()
    }

    private fun filterErrors(json: Workspace.ForJson): List<ConstraintError> {
        val inactiveFlows = mutableSetOf<FlowConfiguration.Id>()
        json.flows.entities.forEach() { (id, flow) ->
            if (flow.status == FlowConfigurationStatusType.INACTIVE) {
                inactiveFlows.add(id)
            }
        }
        val filteredErrors = json.errors?.filter { error ->
            val flowId = error.path.value.split(".").getOrElse(3) { // $.flows.entities.id
                return@filter true
            }
            return@filter !inactiveFlows.contains(FlowConfiguration.Id(flowId))
        }

        return filteredErrors ?: emptyList()
    }

    data class ConfigsResult(
        val matchedFormConfigs: List<FormConfiguration.Id>,
        val matchedFoundationConfigs: List<FoundationConfiguration.Id>,
        val matchedIntervals: List<IntervalId>
    )


    //search terms:
    // - form name
    // - form key

    // - foundation name

    // - series name
    // - series interval name
    // - series interval key

    //if you want to expand any search term to other properties, add them to the value of any of the annonymous objects below

    // todo this method should be moved to Workspace.ForJson? (encapsulation???)
    suspend fun searchConfigByKeywords(
        workspaceId: Workspace.Id, keywords: String
    ): ConfigsResult {
        val workspace = findVersion(workspaceId).configuration
        val tokens = keywords.split("\\s+".toRegex()).filter { it.isNotBlank() }

        val formSearchableQuery = workspace.forms.map { (id, cfg) ->
            object {
                val value = cfg.name + cfg.key
                val id = id
            }
        }

        val foundationSearchableQuery = workspace.foundations.entities.map { (id, entity) ->
            object {
                val value = entity.name.value
                val id = id
            }
        }

        val intervalSearchableQuery = workspace.series.flatMap { (_, series) ->
            series.intervals.entities.map { (id, interval) ->
                object {
                    val value = series.name.value + interval.name.value + interval.id.value
                    val id = id
                }
            }
        }

        fun fuzzyMatch(value: String): Boolean {
            return tokens.any { token ->
                value.contains(token, ignoreCase = true)
            }
        }

        val matchedFormIds = formSearchableQuery.filter { fuzzyMatch(it.value) }.map { it.id }

        val matchedFoundationIds = foundationSearchableQuery.filter { fuzzyMatch(it.value) }.map { it.id }

        val matchedIntervalIds = intervalSearchableQuery.filter { fuzzyMatch(it.value) }.map { it.id }

        return ConfigsResult(
            matchedFormIds, matchedFoundationIds, matchedIntervalIds
        )
    }

    suspend fun findVersion(workspaceId: Workspace.Id, versionId: Id? = null): WorkspaceVersion.ForApi =
        withTenantTransactionScope {
            workspaceRepository.findOne(workspaceId)
            val items = workspaceVersionRepository.search(
                PageRequest(0, 1, "", Sort(listOf(SortField("id", Sort.DESC)))),
                workspaceId,
                versionId,
            ).items

            if (items.isNotEmpty()) {
                return@withTenantTransactionScope items.first().toDTO()
            } else {
                throw NotFoundException("Unknown workspace $workspaceId version id $versionId")
            }
        }

    suspend fun search(
        workspaceId: Workspace.Id, pageRequest: PageRequest
    ): Page<WorkspaceVersion.ForApi> = withTenantTransactionScope {

        // check workspace exists so we can return 404 if it doesn't
        workspaceRepository.findOne(workspaceId)

        val searchResult = workspaceVersionRepository.search(pageRequest, workspaceId)
        return@withTenantTransactionScope Page(
            searchResult.page,
            searchResult.total,
            searchResult.items.map { it.toDTO() })
    }

    suspend fun getActiveWorkspaceVersion(workspaceId: Workspace.Id): WorkspaceVersion.ForApi? =
        withTenantTransactionScope {
            return@withTenantTransactionScope search(
                Workspace.Id(workspaceId.value), PageRequest(
                    pageSize = 1, pageNumber = 1, keyword = "", sort = Sort(listOf(SortField("id", Sort.DESC)))
                )
            ).items.firstOrNull()
        }

}

fun WorkspaceVersionEntity.toDTO(): WorkspaceVersion.ForApi {
    return WorkspaceVersion.ForApi(
        id = Id(this.id.value), workspaceId = Workspace.Id(this.workspaceId), configuration = this.configuration
    )
}