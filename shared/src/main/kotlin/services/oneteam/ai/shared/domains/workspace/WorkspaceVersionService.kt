package services.oneteam.ai.shared.domains.workspace

import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.domains.*
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfigurationStatusType
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion.Id
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.domains.workspace.document.show
import services.oneteam.ai.shared.domains.workspace.validation.ConstraintError

/**
 * Service for managing workspace versions.
 *
 * This service will always look up the workspace by its key to ensure it exists,
 * and will throw NotFoundException if it does not, ensuring a 404 response is returned
 * when an invalid workspace is referenced.
 */
class WorkspaceVersionService(
    private val workspaceRepository: WorkspaceRepository,
    private val documentService: IDocumentService,
    private val workspaceVersionRepository: WorkspaceVersionRepository,
    val check: Checks
) {

    suspend fun createVersion(id: Workspace.Id, cookie: String): WorkspaceVersion.ForApi {
        val workspaceEntity = workspaceRepository.findOne(id)
        // TODO should we be ignoring unknown keys here when parsing the json? Ideally we'd want to know if the UI is creating invalid configurations??
        val json = documentService.show<Workspace.ForJson>(workspaceEntity.documentId!!, cookie, true)
        val hasErrors = hasErrors(json)
        if (hasErrors) {
            throw BadRequestException("Workspace version cannot be created when errors exist.")
        }
        val version = workspaceVersionRepository.create(
            WorkspaceVersion.ForCreate(
                workspaceId = Workspace.Id(workspaceEntity.id.value), configuration = json
            )
        )

        return version.toDTO()
    }

    private fun hasErrors(json: Workspace.ForJson): Boolean {
        val filteredErrors = filterErrors(json)
        return filteredErrors.isNotEmpty()
    }

    private fun filterErrors(json: Workspace.ForJson): List<ConstraintError> {
        val inactiveFlows = mutableSetOf<FlowConfiguration.Id>()
        json.flows.entities.forEach() { (id, flow) ->
            if (flow.status == FlowConfigurationStatusType.INACTIVE) {
                inactiveFlows.add(id)
            }
        }
        val filteredErrors = json.errors?.filter { error ->
            val flowId = error.path.value.split(".").getOrElse(3) { // $.flows.entities.id
                return@filter true
            }
            return@filter !inactiveFlows.contains(FlowConfiguration.Id(flowId))
        }

        return filteredErrors ?: emptyList()
    }

    suspend fun createVersion(id: Workspace.Id, json: Workspace.ForJson): WorkspaceVersion.ForApi {
        workspaceRepository.findOne(id)
        val version = workspaceVersionRepository.create(
            WorkspaceVersion.ForCreate(
                workspaceId = Workspace.Id(id.value), configuration = json
            )
        )
        return version.toDTO()
    }

    data class ConfigsResult(
        val matchedFormConfigs: List<FormConfiguration.Id>,
        val matchedFoundationConfigs: List<FoundationConfiguration.Id>,
        val matchedIntervals: List<IntervalId>
    )

    suspend fun searchConfigByKeywords(workspaceId: Workspace.Id, keywords: String): ConfigsResult {
        val workspaceVersion = findVersion(workspaceId)
        val formsConfigs = workspaceVersion.configuration.forms
        val foundationConfigs = workspaceVersion.configuration.foundations.entities
        val seriesConfigs = workspaceVersion.configuration.series
        val matchedSeries = seriesConfigs.filter { (_, seriesConfig) ->
            seriesConfig.name.value.equals(keywords, ignoreCase = true) == true
        }.map { (key, _) ->
            key.value
        }
        var matchedIntervals: List<IntervalId> = listOf()
        seriesConfigs.forEach { (_, seriesConfig) ->
            val intervals = seriesConfig.intervals.entities.filter { (_, interval) ->
                interval.name.value.equals(keywords, ignoreCase = true)
            }.map { (key, _) -> key }
            matchedIntervals = matchedIntervals + intervals
        }
        val matchedFormConfigs = formsConfigs.filter { (_, value) ->
            value.name.contains(keywords, ignoreCase = true) == true || value.key.contains(
                keywords, ignoreCase = true
            ) == true || matchedSeries.contains(value.seriesId)
        }.map { (key, _) ->
            key
        }
        val matchedFoundationConfigs = foundationConfigs.filter { (_, value) ->
            value.name.value.contains(keywords, ignoreCase = true) == true
        }.map { (key, _) ->
            key
        }
        return ConfigsResult(matchedFormConfigs, matchedFoundationConfigs, matchedIntervals)
    }


    suspend fun findVersion(workspaceId: Workspace.Id, versionId: Id? = null): WorkspaceVersion.ForApi {
        workspaceRepository.findOne(workspaceId)
        val items = workspaceVersionRepository.search(
            PageRequest(0, 1, "", Sort(listOf(SortField("id", Sort.DESC)))),
            workspaceId,
            versionId,
        ).items
        if (items.isNotEmpty()) {
            return items.first().toDTO()
        } else {
            throw NotFoundException("Unknown workspace $workspaceId version id $versionId")
        }
    }

    suspend fun search(
        workspaceId: Workspace.Id, pageRequest: PageRequest
    ): Page<WorkspaceVersion.ForApi> {
        return DatabaseUtils.dbQueryWithTenant {
            workspaceRepository.findOne(workspaceId)

            val searchResult = workspaceVersionRepository.search(pageRequest, workspaceId)
            return@dbQueryWithTenant Page(
                searchResult.page, searchResult.total, searchResult.items.map { it.toDTO() })
        }
    }

    suspend fun getActiveWorkspaceVersion(workspaceId: Workspace.Id): WorkspaceVersion.ForApi? {
        return search(
            Workspace.Id(workspaceId.value), PageRequest(
                pageSize = 1,
                pageNumber = 1,
                keyword = "",
                sort = Sort(listOf(SortField("id", Sort.DESC)))
            )
        ).items.firstOrNull()
    }

}

fun WorkspaceVersionEntity.toDTO(): WorkspaceVersion.ForApi {
    return WorkspaceVersion.ForApi(
        id = Id(this.id.value), workspaceId = Workspace.Id(this.workspaceId), configuration = this.configuration
    )
}