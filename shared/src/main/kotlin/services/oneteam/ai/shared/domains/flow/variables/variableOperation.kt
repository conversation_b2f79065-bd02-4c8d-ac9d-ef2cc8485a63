package services.oneteam.ai.shared.domains.flow.variables

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

enum class VariableOperation {
    TableOperation, ListOperation, FileOperation
}

@Serializable
enum class TableOperation {
    @SerialName("setTable")
    SET_TABLE,

    @SerialName("setRow")
    SET_ROW,

    @SerialName("setColumn")
    SET_COLUMN,

    @SerialName("setCell")
    SET_CELL,
}


@Serializable
enum class ListOperation {
    @SerialName("setList")
    SET_LIST,

    @SerialName("setItem")
    SET_ITEM,

    @SerialName("removeItem")
    REMOVE_ITEM,
}


@Serializable
enum class FileOperation {
    @SerialName("setFiles")
    SET_FILES,

    @SerialName("addFiles")
    ADD_FILES,

    @SerialName("removeFiles")
    REMOVE_FILES,
}