package services.oneteam.ai.shared.domains.workspace.validation

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ArrayNode
import com.jayway.jsonpath.ParseContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.*

/**
 * Returns an error if the values in the given json path are duplicated.
 *
 * The json path should point to an array or map of values to check for duplicates.
 * JsonPath is used to read the values from the json document and find the duplicates.
 * Then an error is returned for each duplicate value found.
 * Ids are derived by replacing the last part of the json path with "id" and reading the value from the json document.
 * Ids are used if they are present in the json document, otherwise the index is used.
 *
 * @param path The json path to the array or map of values to check for duplicates. eg "$.series.*.name" or "$.forms[*].key"
 * @param field The field name to use in the error message
 * @param errorKey The key to use in the error message
 * @param parseContext The parse context to use for reading the json path
 */
class JsonPathDuplicateConstraint(
    private val path: String,
    private val field: String,
    private val errorKey: String,
    private val parseContext: ParseContext,
    private val caseSensitive: Boolean = false
) : Constraint<JsonNode> {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override fun toString(): String {
        return "${this.javaClass.simpleName}: path=$path / field=$field / errorKey=$errorKey / caseSensitive=$caseSensitive"
    }

    override fun validate(data: JsonNode): Errors {
        val ids: JsonNode = parseContext.parse(data).read(replaceWithId(path))
        val nodes: JsonNode = parseContext.parse(data).read(path)

        val useIds = ids is ArrayNode && ids.size() == nodes.size()
        if (!useIds) {
            logger.warn("Cannot use ids for path: $path - index will be used instead")
        }
        val errors = Errors()

        if (nodes is ArrayNode) {

            val values = nodes.map { if (caseSensitive) it.asText() else it.asText().lowercase(Locale.getDefault()) }
            val duplicatedValues = values.groupingBy { it }.eachCount().filter { it.value > 1 }

            if (duplicatedValues.isNotEmpty()) {
                handleDuplicates(values, duplicatedValues, ids, useIds, errors)
                return errors
            }
        } else {
            logger.warn("Json path is not an array: $path")
        }

        return errors
    }

    override fun errorKeys(): List<String> {
        return listOf(errorKey)
    }

    private fun handleDuplicates(
        values: List<String>,
        duplicatedValues: Map<String, Int>,
        ids: JsonNode,
        useIds: Boolean,
        errors: Errors
    ) {
        values.forEachIndexed { index, value ->
            if (duplicatedValues.containsKey(value)) {
                val id = ids[index]
                val path = path.replace("*", if (useIds && id != null) id.asText() else index.toString())
                if (useIds && id == null) {
                    logger.warn("Json path is using id but id is null: $path / $index - this is a problem that needs fixing, please check the data contains IDs")
                }
                errors.add(
                    ConstraintError(
                        Field(field),
                        Type("duplicate"),
                        Path(path),
                        ConstraintDetail("Duplicate value: $value"),
                        Message(errorKey)
                    )
                )
            }
        }
    }

    private fun replaceWithId(string: String): String {
        // replace last part of string after last dot with id
        return string.split(".").dropLast(1).joinToString(".") + ".id"
    }

}