package services.oneteam.ai.shared.domains

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.Column

@Serializable
data class Page<T>(val page: PageRequest, val total: Long, val items: List<T>) {
    fun first(): T? {
        return if (items.isNotEmpty()) {
            items.first()
        } else null
    }
}

/**
 * Page number is 1 based.
 */
@Serializable
data class PageRequest(val pageNumber: Int, val pageSize: Int, val keyword: String, val sort: Sort) {
    companion object {
        const val DEFAULT_PAGE = 1
        const val DEFAULT_PAGE_SIZE = 25
        const val MAX_PAGE_SIZE = 1000
        const val MIN_PAGE_SIZE = 1

        fun forOne(): PageRequest {
            return PageRequest(1, 1, "", Sort(listOf()))
        }
    }
}

@Serializable
data class Sort(val fields: List<SortField>) {
    companion object {
        const val ASC = "asc"
        const val DESC = "desc"
    }
}

@Serializable
data class SortField(val field: String, val direction: String = Sort.ASC) {
    companion object {
        // Parse a string like "name,asc" into a SortField object
        fun fromString(sort: String): SortField {
            val parts = sort.split(",")
            return SortField(parts[0], parts.getOrNull(1) ?: Sort.ASC)
        }
    }
}

/**
 * Used to define what fields can be sorted on and how they map to the actual column names.
 * This maps from the field name given in the query parameter to the actual column name.
 * e.g.
 *             SortableFields(
 *                 mapOf(
 *                     "name" to FormConfigurations.name,
 *                     "status" to FormConfigurations.status,
 *                     "metadata.updatedAt" to FormConfigurations.updatedAt
 *                 )
 *             )
 */
class SortableFields(val mapping: Map<String, Column<*>>) {
    fun contains(field: String): Boolean {
        return mapping.containsKey(field)
    }

    fun get(name: String): Column<*> {
        return mapping[name] ?: error("Unknown field $name")
    }
}