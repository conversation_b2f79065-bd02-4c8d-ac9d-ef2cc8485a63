package services.oneteam.ai.shared.domains.workspace.document

import kotlinx.serialization.Serializable

@Serializable
data class ValidationResult(
    val errors: List<Error> = emptyList()
)

@Serializable
@JvmInline
value class Field(val value: String)

@Serializable
@JvmInline
value class Type(val value: String)

@Serializable
@JvmInline
value class Path(val value: String)

@Serializable
@JvmInline
value class ConstraintDetail(val value: String?)

@Serializable
@JvmInline
value class Message(val value: String)

@Serializable
data class Error(
    val key: Field,
    val type: Type,
    val path: Path,
    val constraintDetail: ConstraintDetail?,
    val message: Message
)
