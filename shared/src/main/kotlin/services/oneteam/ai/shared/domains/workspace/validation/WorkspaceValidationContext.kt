package services.oneteam.ai.shared.domains.workspace.validation

import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration

interface ValidationContext

class WorkspaceValidationContext(val stepTypeConfigurations: Map<String, FlowStepTypeConfiguration>, val flowConfigurationEntities: Map<FlowConfiguration.Id, FlowConfiguration.ForJson>): ValidationContext {

}