package services.oneteam.ai.shared

import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerializationException
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.Json
import kotlinx.serialization.modules.SerializersModule
import kotlinx.serialization.modules.polymorphic
import services.oneteam.ai.shared.domains.flow.variables.SecuredVariableSerializer
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import java.time.Instant

private val variableInstanceModule = SerializersModule {
    polymorphic(VariableInstance::class) {
        defaultDeserializer { VariableInstance.Variable.serializer() }
        subclass(VariableInstance.Variable::class, VariableInstance.Variable.serializer())
        subclass(VariableInstance.SecuredVariable::class, SecuredVariableSerializer)
    }
}
val instantModule = SerializersModule {
    contextual(Instant::class, InstantSerializer)
}
val variableModule = SerializersModule {
    include(variableInstanceModule)
    include(instantModule)
}

const val otTypeDescrim = "polyType"

//https://github.com/Kotlin/kotlinx.serialization/issues/2684
//https://kotlinlang.org/api/kotlinx.serialization/kotlinx-serialization-json/kotlinx.serialization.json/ <- "It is typically used by constructing an application-specific instance"
//for performance alone we should use a singe serializer (see link above), but also organisationally it makes sense to have a single serializer with OTs modules registered to it.
//modules prevent error: "Class discriminator was missing and no default serializers were registered in the polymorphic scope of 'VariableInstance'".
//another additional benefit is we can build hierarchies programmatically instead of magically through annotations

/** The common serializer for the OT backend domain objects. Contains all modules required for polymorphic serialization within that domain */
val otSerializer = Json {
    serializersModule = variableModule
    classDiscriminator = otTypeDescrim
    encodeDefaults = true
    isLenient = true
    ignoreUnknownKeys = true
}

private object InstantSerializer : KSerializer<Instant> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("java.time.Instant", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: Instant) {
        encoder.encodeString(value.toString())
    }

    override fun deserialize(decoder: Decoder): Instant {
        val str = decoder.decodeString()
        try {
            return Instant.parse(str)
        } catch (e: Exception) {
            throw SerializationException("Invalid Instant string: $str")
        }
    }
}

