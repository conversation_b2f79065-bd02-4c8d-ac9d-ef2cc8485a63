package services.oneteam.ai.shared.domains.workspace.validation

class StringUpperCaseConstraint<T>(
    val fieldName: String,
    val producer: (T) -> String?,
    val errorKey: String,
) : Constraint<T> {

    override fun toString(): String {
        return "${this.javaClass.simpleName}: fieldName=$fieldName / errorKey=$errorKey"
    }

    override fun validate(value: T): Errors {
        val string = producer(value)
        return if (string != null && (string.all { !it.isLetter() || (it.isLetter() && it.isUpperCase()) } == false)) {
            Errors().add(
                ConstraintError(
                    Field(fieldName),
                    Type("uppercase"),
                    Path(fieldName),
                    ConstraintDetail("$fieldName must be upper case"),
                    Message(errorKey)
                )
            )
        } else {
            Errors()
        }
    }

    override fun errorKeys(): List<String> {
        return listOf(errorKey)
    }
}