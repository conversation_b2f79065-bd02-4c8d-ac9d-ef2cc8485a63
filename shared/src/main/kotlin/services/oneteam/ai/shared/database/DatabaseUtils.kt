package services.oneteam.ai.shared.database

import org.jetbrains.exposed.dao.Entity
import org.jetbrains.exposed.dao.EntityClass
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.workspace.WorkspaceEntity
import services.oneteam.ai.shared.extensions.fieldsIndexMap
import services.oneteam.ai.shared.extensions.setValueOrNull

const val RLS_TENANT_KEY = "app.current_tenant_id"
const val PRINCIPAL_KEY = "app.current_principal_id"
const val SYSTEM_USER_KEY = "app.is_system_user"

enum class RLSPolicy(val policyName: String) {
    TENANT_ISOLATION("tenant_isolation_policy"),
    WORKSPACE_ISOLATION("workspace_isolation_policy")
}

object DatabaseUtils {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    // table to workspace id mapping
    // the workspace table uses ID but all other tables use workspace_id
    val workspaceIdMaps = mapOf("workspaces" to "id").withDefault { "workspace_id" }


    fun <T : Entity<*>> runPreparedStatementList(
        sql: String,
        parameters: List<*>,
        table: BaseLongIdTable,
        entity: EntityClass<*, T>
    ): List<T> {

        val conn = TransactionManager.current().connection
        val statement = conn.prepareStatement(sql, false)
        parameters.forEachIndexed { index, value ->
            statement.setValueOrNull(index + 1, value, table.columns[index].columnType)
        }
        val resultSet = statement.executeQuery()

        val fieldsIndex = table.fieldsIndexMap()
        val results = mutableListOf<T>()
        while (resultSet.next()) {
            results.add(entity.wrapRow(ResultRow.create(resultSet, fieldsIndex)))
        }
        return results
    }

    fun enableRowLevelSecurityTenantPolicyForSchema(tableName: String) {
        val conn = TransactionManager.current().connection

        /*
        https://www.postgresql.org/docs/current/sql-createpolicy.html#:~:text=Note%20that%20there%20needs%20to,to%20all%20the%20restrictive%20policies.
        Note that there needs to be at least one permissive policy to grant access to records before restrictive policies can be usefully used to reduce that access. If only restrictive policies exist, then no records will be accessible. When a mix of permissive and restrictive policies are present, a record is only accessible if at least one of the permissive policies passes, in addition to all the restrictive policies.
        */
        // tenant RLS has to be permissive while the others have to be restrictive
        val sql =
            "CREATE POLICY tenant_isolation_policy ON \"$tableName\" AS PERMISSIVE USING (\"$tableName\".\"tenant_id\" = current_setting('$RLS_TENANT_KEY', TRUE)::int);"
        val statement = conn.prepareStatement(sql, false)
        statement.executeUpdate()
    }

    fun enableRowLevelSecurityWorkspacePolicyForSchema(tableName: String) {
        val conn = TransactionManager.current().connection

        // workspace policy is RESTRICTIVE ("AND" with tenant policy)
        val operations = if (WorkspaceEntity.table.tableName == tableName) {
            // for workspaces table, we use id as the column
            listOf("SELECT", "UPDATE", "DELETE")
        } else {
            listOf("ALL")
        }
        operations.forEach { operation ->
            val column = workspaceIdMaps.getValue(tableName)

            val sql = """
                DROP POLICY IF EXISTS ${RLSPolicy.WORKSPACE_ISOLATION.policyName}_${operation} ON "$tableName";
                CREATE POLICY ${RLSPolicy.WORKSPACE_ISOLATION.policyName}_$operation ON "$tableName" AS RESTRICTIVE FOR $operation USING
                         ((current_setting('app.is_system_user'::text, true)::boolean = true)
                           OR ($column IN (SELECT workspace_users.workspace_id
                                     FROM workspace_users
                                     WHERE status='ACTIVE' AND workspace_users.user_id =
                                           COALESCE(NULLIF(current_setting('app.current_principal_id', TRUE), ''), '-1')::int)));
                """.trimIndent()
            val statement = conn.prepareStatement(sql, false)
            statement.executeUpdate()
            logger.info("Updated workspace RLS for table {}.", tableName)

        }
    }

    fun enableRowLevelSecurityForTable(tableName: String) {
        val conn = TransactionManager.current().connection
        val sql = "ALTER TABLE \"$tableName\" ENABLE ROW LEVEL SECURITY;"
        val statement = conn.prepareStatement(sql, false)
        statement.executeUpdate()
    }

    fun isRLSEnabled(tableName: String): Boolean {
        val conn = TransactionManager.current().connection
        val query = """
            SELECT relrowsecurity
            FROM pg_class
            WHERE relname = ?;
        """
        val statement = conn.prepareStatement(query, false)
        statement[1] = tableName
        val resultSet = statement.executeQuery()
        return if (resultSet.next()) {
            resultSet.getBoolean(1)
        } else {
            false
        }
    }

    fun isPolicySetOnTable(tableName: String, policyName: String): Boolean {
        val conn = TransactionManager.current().connection
        val query = """
            SELECT EXISTS (
                SELECT 1
                FROM pg_policies
                WHERE tablename = ? AND policyname = ?
            )
        """
        val statement = conn.prepareStatement(query, false)
        statement[1] = tableName
        statement[2] = policyName
        val resultSet = statement.executeQuery()
        return if (resultSet.next()) {
            resultSet.getBoolean(1)
        } else {
            false
        }
    }

    fun listTables(schemaName: String): List<String> {
        val conn = TransactionManager.current().connection
        val query = """
            SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname=?;
        """
        val statement = conn.prepareStatement(query, false)
        statement[1] = schemaName
        val resultSet = statement.executeQuery()
        return resultSet.use {
            generateSequence {
                if (resultSet.next()) resultSet.getString(1) else null
            }.toList()
        }
    }

    fun listColumns(tableName: String): List<String> {
        val conn = TransactionManager.current().connection
        val query = """
            SELECT column_name FROM information_schema.columns WHERE table_name = ?;
        """
        val statement = conn.prepareStatement(query, false)
        statement[1] = tableName
        val resultSet = statement.executeQuery()
        return resultSet.use {
            generateSequence {
                if (resultSet.next()) resultSet.getString(1) else null
            }.toList()
        }
    }

    fun printRLS(schemaName: String): List<Pair<String, Boolean>> {
        val conn = TransactionManager.current().connection
        val query = """
            SELECT relname, relrowsecurity, relforcerowsecurity
            FROM pg_class
            WHERE relnamespace = 'public'::regnamespace
              AND relname IN
                  (SELECT table_name FROM information_schema.tables WHERE table_schema = '${schemaName}' AND table_type = 'BASE TABLE');
        """
        val statement = conn.prepareStatement(query, false)
        val resultSet = statement.executeQuery()
        return resultSet.use {
            generateSequence {
                if (resultSet.next()) Pair(resultSet.getString(1), resultSet.getBoolean(2)) else null
            }.toList()
        }
    }

    fun Transaction.nextValueOf(sequence: String): Long =
        exec("SELECT nextval('${sequence}');") { resultSet ->

            if (resultSet.next().not()) {
                throw Error("Missing nextValue in resultSet of sequence '${sequence}'")
            } else {
                resultSet.getLong(1)
            }

        } ?: throw Error("Unable to get nextValue of sequence '${sequence}'")
}

