package services.oneteam.ai.shared.extensions

import java.math.BigDecimal

fun List<*>.toBigDecimalList(): List<BigDecimal> {
    return this.mapNotNull {
        toNumberOrNull(it)
    }
}

// Handle both numbers and strings that can be parsed as numbers
fun isNumeric(any: Any?): Boolean = any is Number || (any is String && any.toDoubleOrNull() != null)

val allNumbers =
    { inputs: Array<out Any?> -> inputs.all { isNumeric(it) } }


fun toNumberOrNull(any: Any?): BigDecimal? = any.toString().toDoubleOrNull()?.toBigDecimal()?.setScale(scale)

val toNumber = { inputs: Array<out Any?> ->
    inputs.map {
        toNumberOrNull(it)
    }
}