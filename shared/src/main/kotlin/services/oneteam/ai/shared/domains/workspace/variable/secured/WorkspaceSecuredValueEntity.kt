package services.oneteam.ai.shared.domains.workspace.variable.secured

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable

object WorkspaceSecuredValueSchema : BaseLongIdTable("workspace_secured_values") {
    val value = text("value")
    val workspaceId = long("workspace_id").references(id)
    val tenantId = long("tenant_id").references(id)
}

class WorkspaceSecuredValueEntity (
    id: EntityID<Long>,
) : BaseLongEntity(id, WorkspaceSecuredValueSchema) {
    companion object : BaseLongEntityClass<WorkspaceSecuredValueEntity>(WorkspaceSecuredValueSchema)
    var value by WorkspaceSecuredValueSchema.value;
    var workspaceId by WorkspaceSecuredValueSchema.workspaceId;
    var tenantId by WorkspaceSecuredValueSchema.tenantId;

    fun toDTO(): WorkspaceSecuredValue.ForService {
        return WorkspaceSecuredValue.ForService(
            id = WorkspaceSecuredValue.Id(this.id.value),
            value = WorkspaceSecuredValue.EncryptedValue(this.value)
        )
    }
}

/**
 * This model should be not exposed to the outside world.
 */
sealed class WorkspaceSecuredValue {
    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    @JvmInline
    value class RevealedValue(val revealedValue: String)

    @Serializable
    @JvmInline
    value class EncryptedValue(val encryptedValue: String)

    /**
     * This is the model that is used to store the secured value in the database.
     * It is not exposed to the outside world.
     */
    @Serializable
    data class ForService(
        val id: Id,
        val value: EncryptedValue
    ) : WorkspaceSecuredValue()

    @Serializable
    data class ForServiceFullyRevealed(
        val id: Id,
        val value: RevealedValue,
    )
}