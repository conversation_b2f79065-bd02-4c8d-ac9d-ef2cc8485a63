package services.oneteam.ai.shared.domains.actions

import io.ktor.http.Url
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.encodeToJsonElement
import org.apache.commons.io.FilenameUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.collection.form.BlobService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.ProxyService.ProxyEndpointBody
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.helpers.CustomNanoId
import java.io.File
import java.net.URI
import java.util.Base64

// Light service wrapper around the (external) FilePress service API
class FilePressService(
    private val serviceEndpointUrl: String,
    private val proxyService: ExternalProxyService,
    private val blobService: BlobService,
    private val otStorageService: OTStorageService
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object {
        internal fun generateTemporaryStoragePath(
            tenantId: Long, workspaceId: Workspace.Id, fileId: String
        ): String {
            val path = listOf<String?>(
                tenantId.toString(), workspaceId.value.toString(), "tmp", CustomNanoId.generate(36), fileId
            )
            val filepath = path.filter { it != null }.reduce { acc, p -> "$acc/$p" }!!
            return filepath
        }

        internal fun getNameForGeneratedFile(templateUrl: String, filename: String?, fileExtension: String): String {
            val filenameWithoutExtension = if (filename != null) {
                File(filename).nameWithoutExtension
            } else {
                // URLs are URIs ;)
                "generated-${File(templateUrl).nameWithoutExtension}"
            }
            val nameWithExtension = File("$filenameWithoutExtension.$fileExtension").name
            // strip all illegal characters from the filename
            val sanitizedFilename = nameWithExtension.replace("[^a-zA-Z0-9._-]".toRegex(), "_")
            return sanitizedFilename
        }
    }

    fun getFileBytes(filePath: String): ByteArray = blobService.downloadFileBytes(filePath)

    private fun generateSasTokenForCreate(blobPath: String): String {
        return blobService.generateSasToken(blobPath, true)
    }

    private fun generateSasTokenForRead(blobPath: String): String {
        return blobService.generateSasToken(blobPath, false)
    }

    private fun maybeMakeTemplateUrlRemotelyAccessible(templateUrl: String): String {
        // special handling for if 'url' is just a relative path (within our own blob storage)
        if (blobService.isOtaiStorage(URI(templateUrl))) {
            return generateSasTokenForRead(templateUrl)
        }
        return templateUrl
    }

    suspend fun generateDocxFromRemoteTemplate(
        tenantId: Long, workspaceId: Workspace.Id, templateUrl: String, replacements: JsonElement, filename: String?
    ): GeneratedFile {
        val outputFilenameWithExt = getNameForGeneratedFile(
            templateUrl = templateUrl, filename = filename, fileExtension = "docx"
        )

        val fileId = CustomNanoId.generate(10)
        val generationOutputUrl = generateTemporaryStoragePath(tenantId, workspaceId, fileId)
        logger.debug("Generated temporary storage path: {}", generationOutputUrl)

        val remoteTemplateUrl = maybeMakeTemplateUrlRemotelyAccessible(templateUrl)

        val filePressRequestBody = Json.encodeToJsonElement<FilePressApi.GenerateDocxFromRemoteTemplate.RequestBody>(
            FilePressApi.GenerateDocxFromRemoteTemplate.RequestBody(
                templateUrl = remoteTemplateUrl,
                replacements = replacements,
                output = FilePressApi.GenerateDocxFromRemoteTemplate.RequestBody.Output(
                    url = generateSasTokenForCreate(generationOutputUrl), filename = outputFilenameWithExt
                )
            )
        )

        val body = ProxyEndpointBody(
            url = serviceEndpointUrl, method = "POST", body = filePressRequestBody, headers = mapOf(
                "Content-Type" to "application/json", "Accept" to "application/json"
            )
        )

        val result = proxyService.call(body);
        if (result.status != ProxyService.ProxyEndpointResponseStatus.SUCCESS) {
            logger.error("FilePress service error: {}", result.error)
            throw BadRequestException(result.error.toString())
        }
        logger.trace("FilePress service response: {}", result.response)

        val documentUrl = generateSasTokenForRead(generationOutputUrl)
        return GeneratedFile(
            path = documentUrl,
            name = outputFilenameWithExt,
        )
    }

    fun downloadFile(
        tenantId: Long,
        workspaceId: Workspace.Id,
        fileUrl: String,
        filename: String?,
        urlSource: DownloadUrlSource? = DownloadUrlSource.INTERNET
    ): DownloadedFile {
        val fileId = CustomNanoId.generate(10)

        val downloadUrl = when (urlSource) {
            DownloadUrlSource.ONETEAM_STORAGE -> otStorageService.getFileUrl(fileUrl)
            DownloadUrlSource.INTERNET -> fileUrl
            else -> fileUrl
        }
        val fileUri = URI(downloadUrl)
        val connection = fileUri.toURL().openConnection()
        connection.connect()

        val outputFilename = filename
            ?: connection.headerFields["Content-Disposition"]?.firstOrNull()?.substringAfter("filename=")?.replace("\"", "")
            ?: FilenameUtils.getName(fileUri.path)

        val downloadFilePath = generateTemporaryStoragePath(tenantId, workspaceId, fileId)
        val inputStream =  connection.getInputStream();
        val sasUrl = blobService.uploadFromStream(downloadFilePath, inputStream)
        val downloadedFilePath = Url(sasUrl.trim('"').replace("%2F", "/")).encodedPath

        return DownloadedFile(
            path = downloadedFilePath,
            name = if (outputFilename.isNullOrEmpty()) fileId else outputFilename
        )
    }

    fun convertFile(
        tenantId: Long,
        workspaceId: Workspace.Id,
        fileUrl: String,
        outputFilename: String,
        outputFileFormat: OutputFileFormat,
    ): ConvertedFile {
        require(blobService.isOtaiStorage(URI(fileUrl))) {
            "File URL cannot be accessed or converted: $fileUrl"
        }
        // url needs to be scoped correctly to the tenant and workspace
        val parts = URI(fileUrl).path.split("/")
        val tenant = parts[2]
        val workspace = parts[3]
        require(tenant.toLong() == tenantId && workspace.toLong() == workspaceId.value) {
            "File URL cannot be accessed or converted: $fileUrl"
        }

        require(outputFileFormat == OutputFileFormat.BASE64) {
            "Only BASE64 output format is supported for file conversion"
        }

        val binaryData = getFileBytes(fileUrl)
        val encodedString: String = Base64.getEncoder().encodeToString(binaryData)
        return ConvertedFile(
            path = null,
            name = outputFilename,
            content = encodedString
        )
    }
}

// This is for communicating with the (external) FilePress service API
private class FilePressApi {
    class GenerateDocxFromRemoteTemplate {
        @Serializable
        data class RequestBody(val templateUrl: String, val replacements: JsonElement, val output: Output) {
            @Serializable
            data class Output(val url: String? = null, val filename: String? = null)
        }

        @Serializable
        data class ResponseBody(val message: String)
    }
}

@Serializable
data class GeneratedFile(
    val path: String,
    val name: String,
)

@Serializable
data class DownloadedFile(
    val path: String,
    val name: String,
)

@Serializable
data class ConvertedFile(
    val path: String? = null,
    val name: String? = null,
    val content: String? = null, // base64 encoded content
)

@Serializable
enum class DownloadUrlSource {
    @SerialName("INTERNET")
    INTERNET,
    @SerialName("ONETEAM_STORAGE")
    ONETEAM_STORAGE,
}

@Serializable
enum class OutputFileFormat {
    @SerialName("base64")
    BASE64,
}