package services.oneteam.ai.shared.domains.workspace

import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.selectAll
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.domains.*
import services.oneteam.ai.shared.extensions.paginate

class WorkspaceVersionRepository() {
    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "id" to WorkspaceVersions.id
            )
        )
    }

    suspend fun search(
        pageRequest: PageRequest,
        workspaceId: Workspace.Id,
        versionId: WorkspaceVersion.Id? = null,
    ): Page<WorkspaceVersionEntity> {
        return DatabaseUtils.dbQueryWithTenant {

            val query = WorkspaceVersions.selectAll()
            query.andWhere { WorkspaceVersions.workspaceId eq workspaceId.value }
            if (versionId != null) {
                query.andWhere { WorkspaceVersions.id eq versionId.value }
            }
            return@dbQueryWithTenant query.paginate(pageRequest, SORTABLE_FIELDS) {
                WorkspaceVersionEntity.wrapRow(
                    it
                )
            }
        }
    }

    suspend fun searchLatestVersion(
        workspaceId: Workspace.Id
    ): WorkspaceVersionEntity? {
        return search(PageRequest(0, 1, "",Sort(listOf(SortField("id", Sort.DESC)))), workspaceId, null)
            .items.firstOrNull()
    }

    suspend fun create(workspaceVersion: WorkspaceVersion.ForCreate): WorkspaceVersionEntity {
        return DatabaseUtils.dbQueryWithTenant { tenant ->
            val dao = WorkspaceVersionEntity.new {
                workspaceId = workspaceVersion.workspaceId.value
                configuration = workspaceVersion.configuration
                tenantId = tenant.id
            }
            dao.configuration
            return@dbQueryWithTenant dao
        }
    }

}