package services.oneteam.ai.shared.domains.collection.foundation

import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService

suspend fun getFoundationConfiguration(
    workspaceVersionService: WorkspaceVersionService,
    foundation: Foundation
): FoundationConfiguration.ForApi? {
    val workspace = workspaceVersionService.findVersion(foundation.workspaceId)
    return workspace.configuration.foundations.entities[foundation.foundationConfigurationId]
}

fun constructFoundationMinimalFromFoundation(
    foundation: Foundation.ForApi,
    foundationConfiguration: FoundationConfiguration.ForApi
): Event.FoundationMinimal {
    return Event.FoundationMinimal(
        id = foundation.id,
        key = foundation.key,
        name = foundation.name,
        parentId = foundation.parentId,
        foundationConfiguration = constructFoundationConfiguration(foundationConfiguration),
    )
}

fun constructFoundationConfiguration(foundationConfiguration: FoundationConfiguration.ForApi): Event.FoundationConfigurationMinimal {
    return Event.FoundationConfigurationMinimal(
        id = foundationConfiguration.id,
        name = foundationConfiguration.name,
        parentId = null,
        description = foundationConfiguration.description,
        relationship = foundationConfiguration.relationship,
    )
}