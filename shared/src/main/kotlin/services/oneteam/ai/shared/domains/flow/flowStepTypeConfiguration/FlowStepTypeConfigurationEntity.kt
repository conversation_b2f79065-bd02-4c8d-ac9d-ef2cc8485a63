package services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration

import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.json.jsonb
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.otSerializer

object FlowStepTypeConfigurationSchema : BaseLongIdTable("flow_step_type_configuration") {
    val tenantId = long("tenant_id")
    val type = text("type").apply {
        check("type_check") { it inList listOf("action", "trigger", "iterator") }
    }
    val name = text("name")
    val description = text("description").nullable()
    val properties = jsonb<FlowStepType.Properties>("properties", otSerializer).nullable()
    val primaryIdentifier = text("primary_identifier")

    init {
        uniqueIndex(tenantId)
    }
}

class FlowStepTypeConfigurationEntity(id: EntityID<Long>) : BaseLongEntity(id, FlowStepTypeConfigurationSchema) {
    companion object : BaseLongEntityClass<FlowStepTypeConfigurationEntity>(FlowStepTypeConfigurationSchema)

    var tenantId by FlowStepTypeConfigurationSchema.tenantId
    var type by FlowStepTypeConfigurationSchema.type
    var name by FlowStepTypeConfigurationSchema.name
    var description by FlowStepTypeConfigurationSchema.description
    var properties by FlowStepTypeConfigurationSchema.properties
    var primaryIdentifier by FlowStepTypeConfigurationSchema.primaryIdentifier

    fun toDTO(): FlowStepTypeConfiguration {
        return FlowStepTypeConfiguration(
            id = this.id.value,
            primaryIdentifier = this.primaryIdentifier,
            type = this.type,
            tenantId = this.tenantId,
            name = this.name,
            description = this.description,
            properties = this.properties
        )
    }

}