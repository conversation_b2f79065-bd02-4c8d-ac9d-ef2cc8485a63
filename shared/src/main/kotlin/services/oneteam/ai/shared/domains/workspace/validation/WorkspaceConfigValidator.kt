package services.oneteam.ai.shared.domains.workspace.validation

import com.fasterxml.jackson.databind.JsonNode
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.document.WorkspaceDocumentValidator
import services.oneteam.ai.shared.helpers.TolerantJsonParser

class WorkspaceConfigValidator(val workspace: Workspace.ForJson, val context: WorkspaceValidationContext) :
    Constraint<JsonNode> {

    override fun validate(data: JsonNode): Errors {
        val errors = Errors()
        workspace.validateConfiguration("", context).getErrors().forEach { errors.add(it) }
        return errors
    }


    override fun errorKeys(): List<String> {
        return emptyList()
    }

}

class WorkspaceConfigValidatorBuilder(private val stepTypeConfigurationService: FlowStepTypeConfigurationService) {
    private val tolerantJsonParser = TolerantJsonParser()

    suspend fun build(data: JsonNode): Validator<JsonNode> {

        // deserialize json to workspace
        val workspace: Workspace.ForJson = tolerantJsonParser.parse(Workspace.ForJson.serializer(), data.toString())

        val requiredStepTypeConfigurationPrimaryIdentifiers = mutableSetOf<String>()

        fun populateStepTypeConfiguration(typePrimaryIdentifier: String?) {
            if (typePrimaryIdentifier != null) {
                requiredStepTypeConfigurationPrimaryIdentifiers.add(typePrimaryIdentifier)
            }
        }

        // collect all steps into a list
        for (flow in workspace.flows.entities.values) {
            for (step in flow.allSteps()) {
                populateStepTypeConfiguration(step.properties.typePrimaryIdentifier)
            }
            if (flow.triggers == null) {
                continue
            }
            for (trigger in flow.triggers.values) {
                populateStepTypeConfiguration(trigger.properties.typePrimaryIdentifier)
            }
        }

        // find all step type configurations
        val stepTypeConfigurations = getStepTypeConfigurations(requiredStepTypeConfigurationPrimaryIdentifiers)

        // build validator
        return WorkspaceDocumentValidator.build(
            data,
            WorkspaceConfigValidator(workspace, WorkspaceValidationContext(stepTypeConfigurations, workspace.flows.entities))
        )
    }

    private suspend fun getStepTypeConfigurations(typePrimaryIdentifiers: Set<String>): Map<String, FlowStepTypeConfiguration> {
        val stepTypeConfigurations = mutableMapOf<String, FlowStepTypeConfiguration>()

        val query = mapOf("primaryIdentifier" to typePrimaryIdentifiers.toList())
        val triggerFlowStepTypes = stepTypeConfigurationService.getAllByQuery(query)
        triggerFlowStepTypes.forEach { configuration ->
            stepTypeConfigurations[configuration.primaryIdentifier] = configuration
        }
        return stepTypeConfigurations
    }
}