package services.oneteam.ai.shared.domains

import services.oneteam.ai.shared.domains.workspace.validation.Bounds
import services.oneteam.ai.shared.domains.workspace.validation.Errors

class ValidationErrors() {
    val errors = mutableListOf<ValidationError>()

    fun add(localizationKey: String, field: String, context: Bounds? = null): ValidationErrors {
        errors.add(ValidationError(localizationKey, field, context))
        return this
    }

    fun throwIfInvalid() {
        if (errors.isNotEmpty()) {
            throw BadRequestException(this)
        }
    }

    override fun toString(): String {
        return errors.joinToString { "${it.field}: ${it.localizationKey}" }
    }

    companion object {
        fun from(errors: Errors): ValidationErrors {
            val validationErrors = ValidationErrors()
            errors.getErrors().forEach { e ->
                validationErrors.add(
                    localizationKey = e.message.value,
                    field = e.key.value,
                    context = e.bounds
                )
            }
            return validationErrors
        }
    }

}

data class ValidationError(val localizationKey: String, val field: String, val context: Bounds? = null)
