package services.oneteam.ai.shared.domains.workspace.document

import com.fasterxml.jackson.databind.JsonNode
import services.oneteam.ai.shared.domains.workspace.validation.*

const val ERRORS_COMMON_NAME_DUPLICATE = "errors.common.name.duplicate"

class WorkspaceDocumentValidator {
    companion object {
        fun build(jsonDocument: JsonNode, validator: WorkspaceConfigValidator): Validator<JsonNode> {

            val jsonPathParseContext = JsonPathContext().build()

            return Validator(
                listOf(
                    JsonSchemaConstraint.fromClassPathResource("documentSchema.json"),

                    JsonPathDuplicateConstraint(
                        "$.series.*.name",
                        "name",
                        ERRORS_COMMON_NAME_DUPLICATE,
                        jsonPathParseContext
                    ),
                    JsonPathDuplicateConstraint(
                        "$.forms.*.key",
                        "key",
                        "errors.common.key.duplicate",
                        jsonPathParseContext
                    ),
                    JsonPathDuplicateConstraint(
                        "$.foundations.entities.*.identifier",
                        "identifier",
                        ERRORS_COMMON_NAME_DUPLICATE,
                        jsonPathParseContext
                    ),
                    JsonPathMultiLevelDuplicateValidator(
                        "$.series.*.intervals.entities.*.name",
                        "name",
                        ERRORS_COMMON_NAME_DUPLICATE,
                        jsonPathParseContext
                    ).build(jsonDocument),
                    JsonPathDuplicateConstraint(
                        "$.labels.*.name",
                        "name",
                        ERRORS_COMMON_NAME_DUPLICATE,
                        jsonPathParseContext
                    ),
                    JsonPathDuplicateConstraint(
                        "$.variables.*.name",
                        "name",
                        ERRORS_COMMON_NAME_DUPLICATE,
                        jsonPathParseContext
                    ),

                    validator
                )
            )
        }
    }
}
