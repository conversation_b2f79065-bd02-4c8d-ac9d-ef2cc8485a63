package services.oneteam.ai.shared.domains.workspace

import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.database.DatabaseUtils.nextValueOf
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.extensions.paginate
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

data class WorkspaceSearchCriteria(
    val searchTerm: String? = "", // search by name
    val userId: Long? = null, // filter by user ID if needed
    val workspaceId: Workspace.Id? = null
) {
    companion object {
        fun byId(id: Workspace.Id): WorkspaceSearchCriteria {
            return WorkspaceSearchCriteria(workspaceId = id)
        }
    }
}

class WorkspaceRepository(private val checks: Checks, private val userRepository: UserRepository) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "key" to Workspaces.key,
                "name" to Workspaces.name,
                "id" to Workspaces.id
            )
        )
    }

    suspend fun findOne(id: Workspace.Id): WorkspaceEntity {
        return checks.exists(getById(id)) { "Workspace $id not found" }
    }

    suspend fun findOne(key: Workspace.Key): WorkspaceEntity {
        return checks.exists(getByKey(key)) { "Workspace $key not found" }
    }

    suspend fun getById(id: Workspace.Id): WorkspaceEntity? {
        return DatabaseUtils.dbQueryWithTenant {
            val dao =
                WorkspaceEntity.find { Workspaces.id eq id.value and (Workspaces.deleted eq false) }.singleOrNull()
            return@dbQueryWithTenant dao
        }
    }

    suspend fun getByKey(key: Workspace.Key): WorkspaceEntity? {
        return DatabaseUtils.dbQueryWithTenant {
            val dao =
                WorkspaceEntity.find { Workspaces.key eq key.value and (Workspaces.deleted eq false) }.singleOrNull()
            return@dbQueryWithTenant dao
        }
    }

    suspend fun create(workspace: Workspace.ForCreate): WorkspaceEntity {
        val user = userRepository.findOne(coroutineContext[RequestContext]!!.principalId()!!)

        // get the next ID for the workspace so we can provide it in the insert - then we don't need to do a select after insert to get the ID for the workspace_users insert
        val wId = TransactionManager.current().nextValueOf("workspaces_id_seq")

        return DatabaseUtils.dbQueryWithTenant { tenant ->
            // insert the workspace_user row first so the insert into workspaces will not fail due to RLS
            // foreign key has been removed for now, we can add it back later if we find a way to avoid insert using returning *
            val workspaceUserMapping = WorkspaceUserEntity.new {
                // use the ID we got from the sequence
                workspaceId = EntityID(wId, Workspaces)
                userId = user.id
                tenantId = tenant.id
                accessLevel = WorkspaceUser.AccessLevel.entries // add all access levels for creator
                status = WorkspaceUser.Status.ACTIVE
            }.apply {
                flush()
            }
            logger.debug(
                "inserted workspace_user {} for user {} in workspace {}",
                workspaceUserMapping.accessLevel,
                user.id,
                wId
            )

            val dao = WorkspaceEntity.new(wId) { // provide the ID
                name = workspace.name.value
                key = workspace.key.value
                description = workspace.description?.value
                documentId = workspace.documentId?.value
                tenantId = tenant.id
            }
            logger.debug("inserted workspace $wId for tenant ${tenant.id} with name ${workspace.name.value}")
            return@dbQueryWithTenant dao

            // In theory, this should NOT use "returning *" but it clearly does. And thus fails RLS.
            // I'm trying to talk to JetBrains about this, but for now we have to use the workaround below.
            // Use DSL returning nothing so we avoid the select which would fail RLS since we haven't inserted the workspace yet
//            Workspaces.insert {
//                it[id] = wId // provide the ID
//                it[name] = workspace.name.value
//                it[key] = workspace.key.value
//                it[description] = workspace.description?.value
//                it[documentId] = workspace.documentId?.value
//                it[tenantId] = tenant.id
//            }
        }

    }


    suspend fun update(workspace: Workspace.ForUpdate): WorkspaceEntity {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = WorkspaceEntity.findByIdAndUpdate(workspace.id.value) {
                it.name = workspace.name.value
                it.documentId = workspace.documentId?.value
                it.description = workspace.description?.value
            }
            return@dbQueryWithTenant dao!!
        }
    }

    suspend fun updateDetails(workspace: Workspace.ForUpdateDetails): WorkspaceEntity {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = WorkspaceEntity.findByIdAndUpdate(workspace.id.value) {
                it.name = workspace.name.value
                it.description = workspace.description?.value
            }
            return@dbQueryWithTenant dao!!
        }
    }

    suspend fun delete(id: Workspace.Id) {
        return DatabaseUtils.dbQueryWithTenant {
            WorkspaceEntity.findByIdAndUpdate(id.value) {
                it.deleted = true
            }
        }
    }

    suspend fun getAll(): List<WorkspaceEntity> = DatabaseUtils.dbQueryWithTenant {
        //make the iterator non-lazy
        return@dbQueryWithTenant WorkspaceEntity.find { Workspaces.deleted eq false }.map { it }
    }

    suspend fun searchByCriteria(
        pageRequest: PageRequest,
        searchCriteria: WorkspaceSearchCriteria
    ): Page<WorkspaceEntity> {

        return DatabaseUtils.dbQueryWithTenant {
            return@dbQueryWithTenant searchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
                WorkspaceEntity.wrapRow(
                    it
                )
            }
        }
    }
}

/**
 * Workspaces table is restricted by RLS to only those who are linked to a workspace via workspace_users.
 */
fun WorkspaceSearchCriteria.toQuery(): Query {
    val query = Workspaces.join(WorkspaceUsersSchema, JoinType.INNER) { Workspaces.id eq WorkspaceUsersSchema.workspaceId }
        .selectAll()
        .andWhere { Workspaces.deleted eq false }
    if (searchTerm?.isNotBlank() == true) {
        query.andWhere { Workspaces.name.lowerCase() like "%${searchTerm.lowercase()}%" }
    }
    if (userId != null) {
        query.andWhere {
            WorkspaceUsersSchema.userId eq userId
        }
    }

    return query
}
