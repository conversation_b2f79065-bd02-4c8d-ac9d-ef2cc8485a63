package services.oneteam.ai.shared.domains.event

import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.middlewares.RequestContext
import java.time.Instant
import java.util.*
import kotlin.coroutines.coroutineContext

class EventService(
    private val eventDispatcher: EventDispatcher,
) {
    suspend fun create(event: Event.ForCreate): Event {
        val tenant = coroutineContext[RequestContext]!!.tenant

        val forJsonEvent = Event.ForJson(
            workspaceId = event.workspaceId,
            eventProperties = event.eventProperties,
            id = Event.Id(UUID.randomUUID().toString()),
            tenantId = tenant.id,
            status = EventStatus.QUEUED,
            entityMetadata = EntityMetadata(Instant.now(), Instant.now()),
            eventGroupId = event.eventGroupId,
        )

        eventDispatcher.enqueueEvent(forJsonEvent, coroutineContext)

        return forJsonEvent
    }
}