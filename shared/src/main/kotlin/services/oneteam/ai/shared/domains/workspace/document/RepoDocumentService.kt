package services.oneteam.ai.shared.domains.workspace.document

import automergeRepo.src.main.kotlin.Repo
import automergeRepo.src.main.kotlin.printDoc
import kotlinx.serialization.InternalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.json.Json
import kotlinx.serialization.serializer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.collection.form.FormAlertRequestSyncServerBody
import services.oneteam.ai.shared.domains.collection.form.SetAnswerParams
import services.oneteam.ai.shared.domains.workspace.CollaborationDocument
import kotlin.reflect.KClass

class RepoDocumentService(
    //temporary until all functionality is migrated
    private val apiDocumentService: ApiDocumentService, val repo: Repo
) : IDocumentService {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun <T : CollaborationDocument> create(
        document: T,
        type: KClass<T>,
        timeoutMillis: Long?
    ): String {
        return repo.create(document, type.java).docId
    }

    override suspend fun validate(document: String): ValidationResult {
        return apiDocumentService.validate(document)
    }

    override suspend fun answerQuestion(documentId: String, cookie: String?, answers: List<SetAnswerParams>): String {
        return apiDocumentService.answerQuestion(documentId, cookie, answers)
    }

    override suspend fun alertAnnotation(
        annotationDocumentId: String,
        cookie: String?,
        alerts: List<FormAlertRequestSyncServerBody>
    ): String {
        return apiDocumentService.alertAnnotation(annotationDocumentId, cookie, alerts)
    }

    override suspend fun <T : CollaborationDocument> show(
        id: String, cookie: String?, type: KClass<T>, _ignoreUnknownKeys: Boolean
    ): T {
        val handle = repo.find(id, clazz = type.java).whenReady()

        return handle.document(type.java)
    }

    override suspend fun <T : CollaborationDocument> update(
        id: String, cookie: String?, content: T, path: String, type: KClass<T>
    ): String {
        return apiDocumentService.update(id, cookie, content, path, type)
    }

    override suspend fun <T : Any> upsertAtPosition(
        documentId: String,
        cookie: String?,
        position: String,
        content: T,
        type: KClass<T>,
        updateDelegate: (T) -> Unit
    ) {
        apiDocumentService.upsertAtPosition(documentId, cookie, position, content, type, updateDelegate)
    }
}