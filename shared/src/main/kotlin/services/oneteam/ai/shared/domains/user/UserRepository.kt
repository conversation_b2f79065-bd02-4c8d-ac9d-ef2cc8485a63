package services.oneteam.ai.shared.domains.user

import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.like
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.selectAll
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.workspace.WorkspaceSearchCriteria
import services.oneteam.ai.shared.domains.workspace.Workspaces
import services.oneteam.ai.shared.domains.workspace.toQuery
import services.oneteam.ai.shared.extensions.paginate

data class UserSearchCriteria(
    val tenantId: Long,
    val searchTerm: String? // search by name
) {
    companion object {
        // empty companion object so we can add extension functions
    }
}

class UserRepository(private val checks: Checks) {
    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "email" to UserSchema.email
            )
        )
    }

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    suspend fun findOne(id: Long): UserEntity {
        return checks.exists(getById(id)) { "User $id not found" }
    }

    suspend fun getById(id: Long): UserEntity? {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = UserEntity.findById(id)
            return@dbQueryWithTenant dao
        }
    }

    suspend fun create(user: User.ForCreate): UserEntity {
        return DatabaseUtils.dbQueryWithTenant { tenant ->
            val dao = UserEntity.new {
                email = user.email
                tenantId = tenant.id
                properties = user.properties
            }
            return@dbQueryWithTenant dao
        }
    }

    suspend fun updateOrCreate(user: User.ForCreate): UserEntity {
        return DatabaseUtils.dbQueryWithTenant { tenant ->
            val existingUser = UserEntity.findById(user.id.value)
            if (existingUser == null) {
                println("Creating new user")
                return@dbQueryWithTenant UserEntity.new(user.id.value) {
                    email = user.email
                    tenantId = tenant.id
                    properties = user.properties
                }
            }
            if (existingUser.email != user.email || existingUser.properties == null || existingUser.properties?.firstName != user.properties?.firstName || existingUser.properties?.lastName != user.properties?.lastName) {
                return@dbQueryWithTenant UserEntity.findByIdAndUpdate(existingUser.id.value) {
                    existingUser.email = user.email
                    existingUser.properties = user.properties
                }!!
            }
            return@dbQueryWithTenant existingUser
        }
    }

    suspend fun getAll(): List<UserEntity> = DatabaseUtils.dbQueryWithTenant {
        //make the iterator non-lazy
        return@dbQueryWithTenant UserEntity.all().map { it }
    }

    suspend fun searchByCriteria(pageRequest: PageRequest, userSearchCriteria: UserSearchCriteria): Page<UserEntity> {
        return DatabaseUtils.dbQueryWithTenant {
            return@dbQueryWithTenant userSearchCriteria.toQuery().paginate(pageRequest, UserRepository.SORTABLE_FIELDS) {
                UserEntity.wrapRow(
                    it
                )
            }
        }
    }

}

fun UserSearchCriteria.toQuery(): Query {
    val query = UserSchema.selectAll().andWhere { UserSchema.tenantId eq tenantId }
    if (searchTerm?.isNotBlank() == true) {
        val lowerSearchTerm = "%${searchTerm.lowercase()}%"
        query.andWhere {
            (UserSchema.email.lowerCase() like lowerSearchTerm) or
            (UserSchema.properties.extract<String>("firstName").lowerCase() like lowerSearchTerm) or
            (UserSchema.properties.extract<String>("lastName").lowerCase() like lowerSearchTerm)
        }
    }

    return query
}
