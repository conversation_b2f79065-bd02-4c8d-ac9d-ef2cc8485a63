package services.oneteam.ai.shared.domains.workspace.validation

class NumberConstraint<T>(
    val fieldName: String,
    val producer: (T) -> Long?,
    val errorKey: String,
    val bounds: NumberBounds
) : Constraint<T> {

    override fun toString(): String {
        return "${this.javaClass.simpleName}: fieldName=$fieldName / bounds=$bounds / errorKey=$errorKey"
    }

    override fun validate(value: T): Errors {
        val number = producer(value)
        if (number != null && (
                    (bounds.min != null && number < bounds.min) ||
                            (bounds.max != null && number > bounds.max))
        ) {
            return Errors().add(
                ConstraintError(
                    Field(fieldName),
                    Type("number bounds"),
                    Path(fieldName),
                    ConstraintDetail("$fieldName cannot be less than ${bounds.min} (value was $number)"),
                    Message(errorKey),
                    bounds
                )
            )
        }
        return Errors()
    }

    override fun errorKeys(): List<String> {
        return listOf(errorKey)
    }
}
