package services.oneteam.ai.shared.helpers

import kotlinx.serialization.DeserializationStrategy
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * A JSON parser that first tries to parse JSON with strict rules and if it fails, it tries to parse it leniently.
 * Failures will be logged so we know that somehow we are creating invalid JSON and we can address it at the source,
 * but it won't stop the application from working. To be decided if this is a good approach or not...
 */
class TolerantJsonParser(
    private val strictJson: Json = Json { ignoreUnknownKeys = false },
    private val lenientJson: Json = Json { ignoreUnknownKeys = true }
) {

    private val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun <T> parse(deserializer: DeserializationStrategy<T>, json: String): T {
        return try {
            strictJson.decodeFromString(deserializer, json)
        } catch (e: Exception) {
            logger.warn("Failed to parse workspace JSON with strict parser, trying lenient parser") // warn without sensitive data
            logger.trace("Failed to parse workspace JSON with strict parser, trying lenient parser", e) // trace with potentially sensitive data
            lenientJson.decodeFromString<T>(deserializer, json)
        }
    }

    fun parseLenient(json: String): JsonElement {
        return lenientJson.parseToJsonElement(json)
    }

}