package services.oneteam.ai.shared.domains.flow.variables

import kotlinx.serialization.Serializable
import services.oneteam.ai.shared.domains.VariableDataType

@Serializable
abstract class VariableDefinition : Variable() {

    @Serializable
    data class VariableConfiguration(
        val id: String? = null,
        override val type: VariableDataType,
        override val identifier: VariableIdentifier = "",
        override val properties: VariableProperties? = null,
    ) : VariableDefinition() {
        
    }
}