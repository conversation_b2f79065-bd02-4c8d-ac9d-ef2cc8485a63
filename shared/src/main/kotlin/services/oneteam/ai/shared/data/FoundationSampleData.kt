package services.oneteam.ai.shared.data

import kotlinx.serialization.Serializable
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.workspace.FormConfiguration
import services.oneteam.ai.shared.domains.workspace.IntervalId

/**
 * Represents a collection foundation to create
 */
@Serializable
data class FoundationSampleData(
    val name: Foundation.Name,
    val key: Foundation.Key,
    val parentKey: Foundation.Key? = null,
    val forms: List<FormSampleData>? = null
) {
}

/**
 * Represents a form to create for the foundation
 */
@Serializable
data class FormSampleData(
    val formConfigurationId: FormConfiguration.Id,
    val intervalId: IntervalId? = null,
    val answers: String? = null
) {
}