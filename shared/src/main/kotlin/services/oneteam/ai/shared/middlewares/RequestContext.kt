package services.oneteam.ai.shared.middlewares

import services.oneteam.ai.shared.domains.auth.UserSession
import services.oneteam.ai.shared.domains.tenant.Tenant
import kotlin.coroutines.AbstractCoroutineContextElement
import kotlin.coroutines.CoroutineContext

class RequestContext(
    val tenant: Tenant,
    val principal: Any? = null,
) : AbstractCoroutineContextElement(RequestContext) {
    companion object Key : CoroutineContext.Key<RequestContext>

    override fun toString(): String {
        return "RequestContext(principal=$principal,tenant=$tenant)"
    }

    fun principalId(): Long? {
        return (principal as UserSession?)?.user?.id
    }

}