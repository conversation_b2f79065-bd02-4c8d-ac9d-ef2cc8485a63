package services.oneteam.ai.shared

import org.jetbrains.exposed.sql.transactions.TransactionManager
import services.oneteam.ai.shared.database.DatabaseLive
import services.oneteam.ai.shared.domains.AuditService

class ExposedInitializer {
    fun init(enableAudit: <PERSON><PERSON><PERSON>, enableRLS: <PERSON><PERSON>an, database: DatabaseLive) {
        // load audit service to register hooks
        AuditService(enableAudit)

        if (enableRLS) {
            DatabaseVerification("public", database).ensureTenantIsolation()
        }

        // set default connection
        TransactionManager.defaultDatabase = database.standard.database

    }
}