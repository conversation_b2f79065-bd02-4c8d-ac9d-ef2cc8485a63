package services.oneteam.ai.shared.domains.event

import kotlinx.serialization.DeserializationStrategy
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.*
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace

@Serializable
enum class EventStatus {
    @SerialName("queued")
    QUEUED,

    @SerialName("processed")
    PROCESSED,
}

val manualKeys = setOf(EventKey.START_FLOW_MANUALLY_FROM_FORM, EventKey.START_FLOW_MANUALLY_FROM_FOUNDATION)

@Serializable
enum class EventKey(val serializer: DeserializationStrategy<Event.EventProperties>) {
    @SerialName("START_flow_manually_from_form")
    START_FLOW_MANUALLY_FROM_FORM(Event.EventProperties.StartFlowManuallyFromFormProperties.serializer()),

    @SerialName("START_flow_manually_from_foundation")
    START_FLOW_MANUALLY_FROM_FOUNDATION(Event.EventProperties.StartFlowManuallyFromFoundationProperties.serializer()),

    @SerialName("CREATE_collection_foundation")
    CREATE_COLLECTION_FOUNDATION(Event.EventProperties.CreateCollectionFoundationProperties.serializer()),

    @SerialName("UPDATE_collection_foundation")
    UPDATE_COLLECTION_FOUNDATION(Event.EventProperties.UpdateCollectionFoundationProperties.serializer()),

    @SerialName("DELETE_collection_foundation")
    DELETE_COLLECTION_FOUNDATION(Event.EventProperties.DeleteCollectionFoundationProperties.serializer()),

    @SerialName("CREATE_collection_form")
    CREATE_COLLECTION_FORM(Event.EventProperties.CreateCollectionFormProperties.serializer()),

    @SerialName("UPDATE_collection_form")
    UPDATE_COLLECTION_FORM(Event.EventProperties.UpdateCollectionFormProperties.serializer()),

    @SerialName("DELETE_collection_form")
    DELETE_COLLECTION_FORM(Event.EventProperties.DeleteCollectionFormProperties.serializer()),

    @SerialName("UPDATE_collection_form_answer")
    UPDATE_COLLECTION_FORM_ANSWER(Event.EventProperties.UpdateCollectionFormAnswerProperties.serializer()),

    @SerialName("RECEIVE_incoming_webhook_flow")
    RECEIVE_INCOMING_WEBHOOK_FLOW(Event.EventProperties.ReceiveIncomingWebhookForFlow.serializer()),

    @SerialName("RECEIVE_incoming_webhook_workspace")
    RECEIVE_INCOMING_WEBHOOK_WORKSPACE(Event.EventProperties.ReceiveIncomingWebhookForWorkspace.serializer()),

}


@Serializable
sealed class Event {
    abstract val workspaceId: Workspace.Id

    @Serializable
    @JvmInline
    value class Id(val value: String)

    abstract val eventProperties: EventProperties

    @Serializable(EventPropertiesSerializer::class)
    @OptIn(ExperimentalSerializationApi::class)
    sealed class EventProperties {
        abstract val key: EventKey

        @Serializable
        data class StartFlowManuallyFromFormProperties(
            val buttonLabel: String?,
            val form: FormMinimal?,
            val userId: Long? = null,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.START_FLOW_MANUALLY_FROM_FORM
        }

        @Serializable
        data class StartFlowManuallyFromFoundationProperties(
            val buttonLabel: String?,
            val foundation: FoundationMinimal?,
            val userId: Long? = null,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.START_FLOW_MANUALLY_FROM_FOUNDATION
        }

        @Serializable
        data class CreateCollectionFoundationProperties(
            val foundation: FoundationMinimal?,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.CREATE_COLLECTION_FOUNDATION
        }

        @Serializable
        data class UpdateCollectionFoundationProperties(
            val foundation: FoundationMinimal?,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.UPDATE_COLLECTION_FOUNDATION
        }

        @Serializable
        data class DeleteCollectionFoundationProperties(
            val foundation: FoundationMinimal?,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.DELETE_COLLECTION_FOUNDATION
        }

        @Serializable
        data class CreateCollectionFormProperties(
            val form: FormMinimal?,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.CREATE_COLLECTION_FORM
        }

        @Serializable
        data class UpdateCollectionFormProperties(
            val form: FormMinimal?,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.UPDATE_COLLECTION_FORM
        }

        @Serializable
        data class DeleteCollectionFormProperties(
            val form: FormMinimal?,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.DELETE_COLLECTION_FORM
        }

        @Serializable
        data class UpdateCollectionFormAnswerProperties(
            val form: FormMinimal?,
            val changes: Changes? = null,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.UPDATE_COLLECTION_FORM_ANSWER

            @Serializable
            data class Changes(
                val questionIds: List<String> = emptyList(),
            )
        }

        @Serializable
        data class ReceiveIncomingWebhookForFlow(
            val payload: JsonElement,
            val flowConfigurationId: String? = null,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.RECEIVE_INCOMING_WEBHOOK_FLOW
        }

        @Serializable
        data class ReceiveIncomingWebhookForWorkspace(
            val payload: JsonElement,
            val triggerIdentifier: String? = null,
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.RECEIVE_INCOMING_WEBHOOK_WORKSPACE
        }
    }


    @Serializable
    data class ForCreate(
        override val workspaceId: Workspace.Id,
        override val eventProperties: EventProperties,
        val eventGroupId: String? = null,
    ) : Event()

    @Serializable
    data class ForApi(
        override val workspaceId: Workspace.Id,
        override val eventProperties: EventProperties,
        val id: Id,
        val tenantId: Long,
        val eventGroupId: String? = null,
        val shouldNotifyUser: Boolean = true,
    ) : Event() {
        fun toMap(): JsonElement {
            // is this good enough? or does it need customisation?
            return Json.encodeToJsonElement(this)
        }
    }

    @Serializable
    data class ForJson(
        override val workspaceId: Workspace.Id,
        override val eventProperties: EventProperties,
        val id: Id,
        val status: EventStatus,
        val tenantId: Long,
        val entityMetadata: EntityMetadata,
        val eventGroupId: String? = null,
        var shouldNotifyUser: Boolean = true
    ) : Event()

    @Serializable
    data class FormMinimal(
        val id: Long,
        val foundationId: Long,
        val foundation: FoundationMinimal? = null,
        val formConfiguration: FormConfigurationMinimal,
        val documentId: String?,
        val intervalId: String?,
    )

    @Serializable
    data class FormConfigurationMinimal(
        val id: String,
        val key: String,
        val name: String,
        val seriesId: String?,
    )


    @Serializable
    data class FoundationMinimal(
        val id: Foundation.Id,
        val name: Foundation.Name,
        val key: Foundation.Key,
        val foundationConfiguration: FoundationConfigurationMinimal,
        val parentId: Foundation.Id?,
    )


    @Serializable
    data class FoundationConfigurationMinimal(
        val id: FoundationConfiguration.Id,
        val name: FoundationConfiguration.Name,
        val description: FoundationConfiguration.Description?,
        val relationship: FoundationConfiguration.Relationship,
        val parentId: FoundationConfiguration.Id?,
        val disableAutoSuggestKey: Boolean? = false
    )

    @Serializable
    data class Question(
        val id: String,
        val type: String,
        val text: String,
        val identifier: String,
        val properties: JsonElement?, // unstructured data
        val answer: JsonElement?,
    )
}


object EventPropertiesSerializer :
    JsonContentPolymorphicSerializer<Event.EventProperties>(Event.EventProperties::class) {
    override fun selectDeserializer(element: JsonElement): DeserializationStrategy<Event.EventProperties> {
        val keyStr = element.jsonObject["key"]?.jsonPrimitive?.contentOrNull
            ?: throw SerializationException("Missing 'key' field")

        val key = getEnumFromSerialName(keyStr, EventKey::class.java)
        return key?.serializer ?: throw SerializationException("Unknown key: $keyStr")
    }
}


fun <T : Enum<T>> getEnumFromSerialName(value: String, enumClass: Class<T>): T? {
    return enumClass.enumConstants.firstOrNull { enumConstant ->
        val serialName = enumClass.getField(enumConstant.name).getAnnotation(SerialName::class.java)?.value
        serialName == value
    }
}