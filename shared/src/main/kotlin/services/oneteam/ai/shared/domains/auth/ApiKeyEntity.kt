package services.oneteam.ai.shared.domains.auth

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.domains.EntityMetadata

object ApiKeysSchema : BaseLongIdTable("api_keys") {
    val name = text("name")
    val description = text("description").nullable()
    val clientId = text("client_id")
    val clientSecret = text("client_secret").index()

    val tenantId = long("tenant_id").references(id)
}

class ApiKeyEntity(
    id: EntityID<Long>,
) : BaseLongEntity(id, ApiKeysSchema) {
    companion object : BaseLongEntityClass<ApiKeyEntity>(ApiKeysSchema)

    var name by ApiKeysSchema.name;
    var description by ApiKeysSchema.description;
    var clientSecret by ApiKeysSchema.clientSecret;
    var clientId by ApiKeysSchema.clientId;
    var tenantId by ApiKeysSchema.tenantId;

    fun toDTO(): ApiKey.ForApi {
        return ApiKey.ForApi(
            id = ApiKey.Id(this.id.value),
            name = this.name,
            description = this.description,
            scopes = emptyList(),
            metadata = EntityMetadata.from(this)
        )
    }

    fun toDTOForLogin(): ApiKey.ForLogin {
        return ApiKey.ForLogin(
            id = ApiKey.Id(this.id.value),
            clientId = this.clientId,
            clientSecret = ApiKey.HashedClientSecret(this.clientSecret),
            scopes = emptyList(),
            metadata = EntityMetadata.from(this)
        )
    }
}

sealed class ApiKey {
    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    @JvmInline
    value class RevealedApiKey(val value: String)

    @Serializable
    @JvmInline
    value class HashedClientSecret(val value: String)

    @Serializable
    data class ForApi(
        val id: Id,
        val name: String,
        val description: String? = null,
        val scopes: List<String> = emptyList(),
        val metadata: EntityMetadata
    ) : ApiKey()

    @Serializable
    data class ForCreate(
        val name: String,
        val description: String? = null,
        val scopes: List<String> = emptyList(),
    ) : ApiKey()

    @Serializable
    data class RevealedAfterCreate(
        val id: Id,
        val name: String,
        val description: String? = null,
        val scopes: List<String> = emptyList(),
        val apiKey: RevealedApiKey,
        val metadata: EntityMetadata
    ) : ApiKey()

    data class ForLogin(
        val id: Id,
        val clientId: String,
        val clientSecret: HashedClientSecret,
        val scopes: List<String> = emptyList(),
        val metadata: EntityMetadata
    ) : ApiKey()
}
