package services.oneteam.ai.shared.domains.workspace

import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.json.jsonb
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.domains.tenant.TenantSchema

object Workspaces : BaseLongIdTable("workspaces") {
    val name = text("name")
    val key = text("key")
    val description = text("description").nullable()
    val tenantId = long("tenant_id").references(TenantSchema.id)
    val configuration = jsonb<Workspace>("configuration", Json.Default).nullable()
    val documentId = text("document_id").nullable()
    val deleted = bool("deleted").default(false)

    init {
        uniqueIndex(name, tenantId)
        uniqueIndex(key, tenantId)
    }
}

object WorkspaceVersions : BaseLongIdTable("workspace_versions") {
    val configuration = jsonb<Workspace.ForJson>("configuration", Json { encodeDefaults = true })
    val workspaceId = long("workspace_id").references(id)
    val tenantId = long("tenant_id").references(id)
}

