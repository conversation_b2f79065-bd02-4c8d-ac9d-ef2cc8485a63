package services.oneteam.ai.shared.helpers

import io.viascom.nanoid.NanoId
import org.jetbrains.annotations.NotNull
import java.security.SecureRandom
import java.util.Random

object CustomNanoId {
    fun generate(
        @NotNull
        size: Int = 21,
        @NotNull
        alphabet: String = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",
        @NotNull
        additionalBytesFactor: Double = 1.6,
        @NotNull
        random: Random = SecureRandom()
    ): String {
        return "a${NanoId.generate(size - 1, alphabet, additionalBytesFactor, random)}"
    }
}