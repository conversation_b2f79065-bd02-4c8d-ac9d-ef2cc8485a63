package services.oneteam.ai.shared.helpers

import kotlinx.serialization.json.*

object JsonElementToAny {
    private fun jsonElementToMap(jsonElement: JsonElement): Map<String, Any?> {
        return jsonElement.jsonObject.mapValues { (_, value) -> jsonElementToAny(value) }
    }

    private fun jsonElementToList(jsonElement: JsonElement): List<Any?> {
        return jsonElement.jsonArray.map { jsonElementToAny(it) }
    }

    fun jsonElementToAny(jsonElement: JsonElement): Any? {
        return when (jsonElement) {
            is JsonObject -> jsonElementToMap(jsonElement)
            is JsonArray -> jsonElementToList(jsonElement)
            is JsonPrimitive -> {
                when {
                    jsonElement.isString -> jsonElement.content
                    jsonElement.booleanOrNull != null -> jsonElement.boolean
                    jsonElement.intOrNull != null -> jsonElement.int
                    jsonElement.longOrNull != null -> jsonElement.long
                    jsonElement.floatOrNull != null -> jsonElement.float
                    jsonElement.doubleOrNull != null -> jsonElement.double
                    jsonElement is JsonNull -> null
                    else -> throw IllegalArgumentException("Unsupported primitive type: $jsonElement")
                }
            }
        }
    }
}