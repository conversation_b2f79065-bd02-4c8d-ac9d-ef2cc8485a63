package services.oneteam.ai.shared.domains.actions

import kotlinx.serialization.Serializable
import java.net.URI

class OTStorageService(
    private val storageConfig: OTStorageServiceConfig
) {
    @Serializable
    data class OTStorageServiceConfig(
        val storageName: String,
        val containerName: String,
        val sasToken: String
    )

    /**
     * Generates a URL to access a file in the OT storage service.
     *
     * @param filePath The path to the file within the storage container.
     * @return A URL string that can be used to access the file.
     */
    fun getFileUrl(filePath: String): String {
        // convoluted but this is the way to get proper URL encoding for the file path
        // we can't pass in the sasToken cos otherwise it also get encoded
        val fileUrl = URI(
            "https",
            "${storageConfig.storageName}.blob.core.windows.net",
            "/${storageConfig.containerName}/$filePath",
            null
        ).toString() + "?${storageConfig.sasToken}"
        return fileUrl
    }
}
