package services.oneteam.ai.shared.domains.workspace.validation

class StringRequiredConstraint<T>(
    val fieldName: String,
    val producer: (T) -> String?,
    val errorKey: String
) : Constraint<T> {

    override fun validate(value: T): Errors {
        return if (producer(value).isNullOrEmpty()) {
            Errors().add(
                ConstraintError(
                    Field(fieldName),
                    Type("required"),
                    Path(fieldName),
                    ConstraintDetail("$fieldName is required"),
                    Message(errorKey)
                )
            )
        } else {
            Errors()
        }
    }

    override fun toString(): String {
        return "${this.javaClass.simpleName}: fieldName=$fieldName / errorKey=$errorKey"
    }

    override fun errorKeys(): List<String> {
        return listOf(errorKey)
    }

}