package services.oneteam.ai.shared.domains.flow.configuration

import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration.Step.Variant
import services.oneteam.ai.shared.domains.workspace.validation.ConstraintDetail
import services.oneteam.ai.shared.domains.workspace.validation.ConstraintError
import services.oneteam.ai.shared.domains.workspace.validation.Errors
import services.oneteam.ai.shared.domains.workspace.validation.Field
import services.oneteam.ai.shared.domains.workspace.validation.Message
import services.oneteam.ai.shared.domains.workspace.validation.Path
import services.oneteam.ai.shared.domains.workspace.validation.Type
import services.oneteam.ai.shared.domains.workspace.validation.WorkspaceValidationContext
import services.oneteam.ai.shared.helpers.getContent

class FlowStepRequiredInputsValidator(
    private val context: WorkspaceValidationContext,
    private val id: FlowConfiguration.Step.Id,
    variant: Variant,
    private val properties: FlowConfiguration.Step.Properties,
    private val path: String,
    private val errors: Errors,
) {
    private val triggerOrSteps: String =
        if (variant == Variant.TRIGGER) "triggers" else "steps"

    fun validate() {
        // get the step type configuration for this particular step
        // check that configuration.properties.required=true fields are present in the step inputs, matching by identifier
        if (!properties.typePrimaryIdentifier.isNullOrEmpty()) {

            val stepTypeConfiguration = context.stepTypeConfigurations[properties.typePrimaryIdentifier]
            if (stepTypeConfiguration != null) {
                val requiredFields =
                    stepTypeConfiguration.properties?.configuration?.content?.filter { (it.properties.hidden == null || it.properties.hidden.toString() == "false") && it.properties.required.toString() == "true" }
                requiredFields?.forEach { content ->
                    if (properties.inputs[content.identifier] == null || properties.inputs[content.identifier]?.getContent().isNullOrEmpty()) {
                        errors.add(
                            ConstraintError(
                                Field(content.identifier),
                                Type("required"),
                                Path("$path.$triggerOrSteps.${id.value}.properties.inputs.${content.identifier}"),
                                ConstraintDetail("Field is required"),
                                Message("Field is required")
                            )
                        )
                    }
                }
            }
        }
    }
}