package services.oneteam.ai.shared.domains.workspace.validation

class StringLengthConstraint<T>(
    val fieldName: String,
    val producer: (T) -> String?,
    val errorKey: String,
    val bounds: StringBounds
) : Constraint<T> {

    override fun toString(): String {
        return "${this.javaClass.simpleName}: fieldName=$fieldName / bounds=$bounds / errorKey=$errorKey"
    }

    override fun validate(value: T): Errors {
        val string = producer(value)
        return if (string != null && (
                    (bounds.min != null && string.length < bounds.min) ||
                            (bounds.max != null && string.length > bounds.max))
        ) {
            Errors().add(
                ConstraintError(
                    Field(fieldName),
                    Type("length"),
                    Path(fieldName),
                    ConstraintDetail("$fieldName must be between ${bounds.min} and ${bounds.max} characters"),
                    Message(errorKey),
                    bounds
                )
            )
        } else {
            Errors()
        }
    }

    override fun errorKeys(): List<String> {
        return listOf(errorKey)
    }
}
