package services.oneteam.ai.shared

import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.database.DBInterface
import services.oneteam.ai.shared.database.DatabaseUtils

/**
 * Checks to ensure state of database is correct.
 */
class DatabaseVerification(val schemaName: String, private val database: DBInterface) {
    private val excludeTableFromTenantRLS = listOf<String>("flyway_schema_history", "tenants")
    private val excludeTableFromWorkspaceRLS = listOf<String>("workspace_users")
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    /**
     * Adds tenant_isolation_policy to tables that don't have it (excluding those in excludeTableFromRLS).
     */
    fun ensureTenantIsolation() {
        logger.info("Verifying database schema $schemaName")
        transaction(database.connectSuperUser()) {
            DatabaseUtils.listTables("public").filter { !excludeTableFromTenantRLS.contains(it) }.forEach { table ->
                // create tenant policy
                DatabaseUtils.isPolicySetOnTable(table, "tenant_isolation_policy").let { hasPolicy ->
                    if (!hasPolicy) {
                        logger.info("tenant_isolation_policy not set on table $table. Setting...")
                        DatabaseUtils.enableRowLevelSecurityTenantPolicyForSchema(table)
                    } else {
                        logger.info("tenant_isolation_policy is already set on table $table")
                    }
                }
                if (!excludeTableFromWorkspaceRLS.contains(table)) {
                    val recreatePolicy = true
                    if (table == "workspaces" || DatabaseUtils.listColumns(table).contains("workspace_id")) {
                        // create workspace policy
                        DatabaseUtils.isPolicySetOnTable(table, "workspace_isolation_policy").let { hasPolicy ->
                            if (!hasPolicy || recreatePolicy) {
                                logger.info("workspace_isolation_policy not set on table $table. Setting...")
                                DatabaseUtils.enableRowLevelSecurityWorkspacePolicyForSchema(table)
                            } else {
                                logger.info("workspace_isolation_policy is already set on table $table")
                            }
                        }
                    } else {
                        logger.info("Table $table does not have workspace_id column, skipping workspace policy creation.")
                    }
                } else {
                    logger.info("Table $table is excluded from workspace RLS policies.")
                }

                // enable RLS
                DatabaseUtils.isRLSEnabled(table).let { rlsEnabled ->
                    if (!rlsEnabled) {
                        logger.info("RLS not enabled on table $table. Enabling...")
                        DatabaseUtils.enableRowLevelSecurityForTable(table)
                    } else {
                        logger.info("RLS is already enabled on table $table")
                    }
                }
            }
            logger.info("Database schema $schemaName status:")
            val status = DatabaseUtils.printRLS(schemaName)
            status.filter { it.second }.joinToString("\n").let { logger.info("Tables with RLS: \n$it") }
            status.filter { !it.second }.joinToString("\n").let { logger.info("Tables WITHOUT RLS: \n$it") }
        }
    }

}