package services.oneteam.ai.shared.extensions

import kotlinx.serialization.json.*

fun JsonElement.toSimpleValue(): Any? {
    return when (this) {
        is JsonObject -> this.mapValues { it.value.toSimpleValue() }
        is JsonArray -> this.map { it.toSimpleValue() }
        is JsonPrimitive -> when {
            this.isString -> this.content
            this.booleanOrNull != null -> this.boolean
            this.intOrNull != null -> this.int
            this.doubleOrNull != null -> this.double
            else -> null
        }

        JsonNull -> null
    }
}
