package services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration

import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import services.oneteam.ai.shared.database.DatabaseUtils

class FlowStepTypeConfigurationRepository {
    suspend fun getById(id: Long): FlowStepTypeConfigurationEntity {
        return DatabaseUtils.dbQueryWithTenant {
            FlowStepTypeConfigurationEntity.findById(id)
                ?: throw IllegalArgumentException("FlowStepTypeConfiguration with id $id not found")
        }
    }

    suspend fun getByPrimaryIdentifier(primaryId: String): FlowStepTypeConfigurationEntity? {
        val entities = get {
            FlowStepTypeConfigurationSchema.primaryIdentifier eq primaryId
        }
        return entities.firstOrNull()
    }

    suspend fun get(predicate: SqlExpressionBuilder.() -> Op<Boolean>): List<FlowStepTypeConfigurationEntity> {
        return DatabaseUtils.dbQueryWithTenant {
            FlowStepTypeConfigurationEntity.find { SqlExpressionBuilder.predicate() }.toList()
        }
    }

    suspend fun create(flowStepTypeConfiguration: FlowStepTypeConfiguration): FlowStepTypeConfigurationEntity {
        return DatabaseUtils.dbQueryWithTenant { tenant ->
            val dao = FlowStepTypeConfigurationEntity.new {
                type = flowStepTypeConfiguration.type
                name = flowStepTypeConfiguration.name
                description = flowStepTypeConfiguration.description
                properties = flowStepTypeConfiguration.properties
                primaryIdentifier = flowStepTypeConfiguration.primaryIdentifier
                tenantId = tenant.id
            }
            return@dbQueryWithTenant dao
        }
    }

    suspend fun delete(id: Long): JsonObject {
        return DatabaseUtils.dbQueryWithTenant {
            val deletedCount = FlowStepTypeConfigurationSchema.deleteWhere { FlowStepTypeConfigurationSchema.id eq id }
            return@dbQueryWithTenant buildJsonObject {
                if (deletedCount > 0) {
                    put("message", "FlowStepTypeConfiguration with id $id has been deleted")
                } else {
                    put("message", "FlowStepTypeConfiguration with id $id was not found")
                }
            }
        }
    }

    suspend fun getAll(): List<FlowStepTypeConfigurationEntity> = DatabaseUtils.dbQueryWithTenant {
        return@dbQueryWithTenant FlowStepTypeConfigurationEntity.all().map { it }
    }

    suspend fun getAllByQuery(query: Map<String, List<String>>): List<FlowStepTypeConfigurationEntity> = DatabaseUtils.dbQueryWithTenant{
        return@dbQueryWithTenant FlowStepTypeConfigurationEntity.find {
            query.entries.fold(Op.TRUE as Op<Boolean>) { acc, entry ->
                val (key, values) = entry
                val column = when (key) {
                    "type" -> FlowStepTypeConfigurationSchema.type
                    "name" -> FlowStepTypeConfigurationSchema.name
                    "primaryIdentifier" -> FlowStepTypeConfigurationSchema.primaryIdentifier
                    else -> null
                }

                if (column != null && values.isNotEmpty()) {
                    acc and (column inList values)
                } else acc
            }
        }.toList()
    }

}