package services.oneteam.ai.shared.domains.workspace.variable.secured

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.workspace.Workspace

class WorkspaceSecuredValueRepository {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun create(encryptedValue: WorkspaceSecuredValue.EncryptedValue, workspaceId: Workspace.Id, tenant: Tenant): WorkspaceSecuredValue.Id {
        val dao = WorkspaceSecuredValueEntity.new {
            value = encryptedValue.encryptedValue
            tenantId = tenant.id
            this.workspaceId = workspaceId.value
        }
        return WorkspaceSecuredValue.Id(dao.id.value)
    }

    fun delete(workspaceSecuredValueId: WorkspaceSecuredValue.Id) {
        WorkspaceSecuredValueEntity.findById(workspaceSecuredValueId.value)?.delete()
    }

    /**
     * Retrieves a list of SecuredValue entities by their IDs.
     * @param workspaceSecuredValueIds List of SecuredValue.Id to retrieve.
     * @return List of SecuredValue.ForApi DTOs. that match the provided IDs. (i.e. returned list may be smaller than input)
     */
    fun getMany(workspaceSecuredValueIds: List<WorkspaceSecuredValue.Id>): List<WorkspaceSecuredValue.ForService> {
        val uniqueIds = workspaceSecuredValueIds.toSet().map { it.value }
        return WorkspaceSecuredValueEntity.find { WorkspaceSecuredValueSchema.id inList uniqueIds }.toList().map { it.toDTO() }
    }
}