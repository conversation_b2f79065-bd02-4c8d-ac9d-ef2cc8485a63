package services.oneteam.ai.shared.domains.workspace.validation

import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.JsonPath
import com.jayway.jsonpath.ParseContext
import com.jayway.jsonpath.spi.json.JacksonJsonNodeJsonProvider

class JsonPathContext {
    fun build(): ParseContext {
        return JsonPath.using(
            Configuration.builder()
                .jsonProvider(JacksonJsonNodeJsonProvider())
                .build()
        )
    }
}