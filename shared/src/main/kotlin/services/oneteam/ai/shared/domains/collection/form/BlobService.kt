package services.oneteam.ai.shared.domains.collection.form

import com.azure.storage.blob.BlobClient
import com.azure.storage.blob.BlobClientBuilder
import com.azure.storage.blob.sas.BlobSasPermission
import com.azure.storage.blob.sas.BlobServiceSasSignatureValues
import com.azure.storage.common.StorageSharedKeyCredential
import com.azure.storage.common.sas.SasProtocol
import java.io.File
import java.net.URI
import java.time.OffsetDateTime


class BlobService(val storageName: String, val accessKey: String, val containerName: String) {
    private fun getBlobClient(blobName: String): BlobClient {
        val sharedKeyCredential = StorageSharedKeyCredential(storageName, accessKey)
        val endpoint = "https://${storageName}.blob.core.windows.net"
        val cleanBlobName = Regex("^/$containerName/").replace(blobName, "")
        return BlobClientBuilder()
            .endpoint(endpoint)
            .credential(sharedKeyCredential)
            .containerName(containerName)
            .blobName(cleanBlobName)
            .buildClient()
    }

    private fun generateSasUrl(blobClient: BlobClient, createFlag: Boolean, durationInMinutes: Long): String {
        val blockBlobClient = blobClient.getBlockBlobClient()

        val expiryTime = OffsetDateTime.now().plusMinutes(durationInMinutes)

        val blobSasPermission = BlobSasPermission().apply {
            if (createFlag) {
                setCreatePermission(true)
            } else {
                setReadPermission(true)
            }
        }

        val values = BlobServiceSasSignatureValues(expiryTime, blobSasPermission)
            .setStartTime(OffsetDateTime.now())
            .setProtocol(SasProtocol.HTTPS_ONLY)

        val sasToken = blockBlobClient.generateSas(values)
        return "${blobClient.blobUrl}?$sasToken"
    }

    fun generateSasToken(blobName: String, createFlag: Boolean = true, durationInMinutes: Long = 1): String {
        val blobClient = getBlobClient(blobName)
        return generateSasUrl(blobClient, createFlag, durationInMinutes)
    }

    fun uploadFile(blobName: String, file: File, durationInMinutes: Long = 1): String {
        val blobClient = getBlobClient(blobName)
        blobClient.upload(file.inputStream(), file.length(), true)
        return generateSasUrl(blobClient, createFlag = false, durationInMinutes)
    }

    fun uploadFromStream(blobName: String, inputStream: java.io.InputStream?, durationInMinutes: Long = 1): String {
        val blobClient = getBlobClient(blobName)
        blobClient.upload(inputStream, true)
        return generateSasUrl(blobClient, createFlag = false, durationInMinutes)
    }

    /**
     * Check if the given URI is a valid URI pointing to the OTAI storage.
     * If the host is null, it will be considered valid if the path starts with the container name.
     * If the host is not null, it will be considered valid if the host matches the storage name and the path starts with the container name.
     * If the host is not null, the schema must be either "https" or "http".
     * NB: This doesn't check whether the blob actually exists or not, just whether the formed URI is valid.
     * @param url the URI to check.
     * @return true if the URI is a valid OTAI storage URI, false otherwise.
     */
    fun isOtaiStorage(url: URI): Boolean {
        val hostMatches = url.host == null || url.host == "${storageName}.blob.core.windows.net"
        if (!hostMatches) {
            return false
        }

        if (url.host != null) {
            val schemeMatches = url.scheme == "https" || url.scheme == "http"
            if (!schemeMatches) {
                return false
            }
        }

        return url.path.startsWith("/$containerName/")
    }

}
