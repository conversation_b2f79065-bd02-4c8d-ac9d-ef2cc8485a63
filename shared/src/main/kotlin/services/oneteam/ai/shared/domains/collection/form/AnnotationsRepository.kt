package services.oneteam.ai.shared.domains.collection.form

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement
import services.oneteam.ai.shared.domains.workspace.CollaborationDocument
import services.oneteam.ai.shared.domains.workspace.CollaborationDocumentType

@Serializable
sealed class FormAnnotation {

    @Serializable
    data class ForJson(
        val id: Form.Id,
        val annotations: Map<String, JsonElement>,
        val state: Map<String, JsonElement>,
    ): CollaborationDocument {
        override val type = CollaborationDocumentType.FORM_ANNOTATION

        override fun descriptor(): String {
            return id.value.toString()
        }
    }
}