package services.oneteam.ai.shared.domains.event

import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.ArrayDeque

interface EventQueue {
    suspend fun push(event: Event.ForJson)
    suspend fun poll(): Event.ForJson?
    suspend fun size(): Int
}

class InMemoryEventQueue : EventQueue {
    private val queue : ArrayDeque<Event.ForJson> = ArrayDeque()
    private val mutex = Mutex()

    override suspend fun push(event: Event.ForJson) {
        mutex.withLock {
            queue.add(event)
        }
    }

    override suspend fun poll(): Event.ForJson? {
        return mutex.withLock {
            queue.pollFirst()
        }
    }

    override suspend fun size(): Int {
        return mutex.withLock {
            queue.size
        }
    }
}