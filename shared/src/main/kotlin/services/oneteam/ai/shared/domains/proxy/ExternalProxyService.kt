package services.oneteam.ai.shared.domains.proxy

import io.ktor.client.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext


class ExternalProxyService(
    private val serviceAccountToken: String,
    private val _client: HttpClient = HttpClient {
        install(ContentNegotiation) { json() }
        install(HttpTimeout)
    }
) : ProxyService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun call(
        body: ProxyService.ProxyEndpointBody,
        isExternalResponse: Boolean?,
        timeoutMillis: Long?
    ): ProxyService.ProxyEndpointResponse {
        try {
            logger.trace("Proxy call to ${body.url} has started")
            val method = getClientMethod(body.method)

            val res = _client.method(body.url, createBlock(body, timeoutMillis))

            val isSuccess = res.status.value.toString().substring(0, 1) == "2"
            if (isSuccess) {
                logger.trace("Proxy call to ${body.url} was successful")
            } else {
                logger.error("Proxy call to ${body.url} failed with status ${res!!.status.value}")
            }

            return if (isExternalResponse == true) {
                val externalEndpointResponse = ProxyService.ExternalEndpointResponse(
                    status = res.status.value,
                    payload = Json.parseToJsonElement(res.bodyAsText()),
                    errors = if (!isSuccess) JsonPrimitive(res.bodyAsText()) else JsonNull
                )
                ProxyService.ProxyEndpointResponse(
                    response = Json.encodeToString(externalEndpointResponse),
                    status = if (isSuccess) ProxyService.ProxyEndpointResponseStatus.SUCCESS else ProxyService.ProxyEndpointResponseStatus.FAIL,
                    error = if (!isSuccess) res.bodyAsText() else null
                )
            } else {
                ProxyService.ProxyEndpointResponse(
                    response = res.bodyAsText(),
                    status = if (isSuccess) ProxyService.ProxyEndpointResponseStatus.SUCCESS else ProxyService.ProxyEndpointResponseStatus.FAIL,
                    error = if (!isSuccess) res.bodyAsText() else null
                )
            }
        } catch (e: Exception) {
            logger.error("Proxy call to ${body.url} failed with error: ${e.message}", e)
            return ProxyService.ProxyEndpointResponse(
                response = null, status = ProxyService.ProxyEndpointResponseStatus.FAIL, error = e.message
            )
        }
    }

    private suspend fun createBlock(
        proxyParams: ProxyService.ProxyEndpointBody,
        timeoutMillis: Long?
    ): HttpRequestBuilder.() -> Unit {
        val tenant = coroutineContext[RequestContext]!!.tenant
        return {
            if (timeoutMillis != null && timeoutMillis > 0) {
                // see: https://ktor.io/docs/client-timeout.html#configure_plugin
                timeout { requestTimeoutMillis = timeoutMillis }
            }
            header(HttpHeaders.Referrer, tenant.originUrl)

            if (proxyParams.authentication?.get("useOtaiServiceAccount") == "true") {
                header("Authorization", "Bearer $serviceAccountToken")
            }

            proxyParams.headers?.forEach { (key, value) -> header(key, value) }
            proxyParams.authentication?.forEach { (key, value) -> header(key, value) }
            proxyParams.options?.forEach { (key, value) -> header(key, value) }
            proxyParams.queryParams?.forEach { (key, value) -> parameter(key, value) }

            if (proxyParams.body != null) {
                contentType(proxyParams.contentType)
                setBody(proxyParams.body)
            }
        }
    }

    private fun getClientMethod(method: String): suspend HttpClient.(
        String, HttpRequestBuilder.() -> Unit
    ) -> HttpResponse {
        return when (method) {
            HttpMethod.Get.value -> { urlString, block -> get(urlString, block) }
            HttpMethod.Post.value -> { urlString, block -> post(urlString, block) }
            HttpMethod.Put.value -> { urlString, block -> put(urlString, block) }
            HttpMethod.Delete.value -> { urlString, block -> delete(urlString, block) }
            HttpMethod.Patch.value -> { urlString, block -> patch(urlString, block) }
            HttpMethod.Head.value -> { urlString, block -> head(urlString, block) }
            HttpMethod.Options.value -> { urlString, block -> options(urlString, block) }
            else -> throw Exception("Not supported yet")
        }
    }
}
