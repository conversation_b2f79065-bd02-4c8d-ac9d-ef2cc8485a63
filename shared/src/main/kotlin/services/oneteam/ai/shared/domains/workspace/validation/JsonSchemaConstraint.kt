package services.oneteam.ai.shared.domains.workspace.validation

import com.fasterxml.jackson.databind.JsonNode
import com.networknt.schema.JsonSchema
import com.networknt.schema.JsonSchemaFactory
import com.networknt.schema.SpecVersion
import java.io.InputStreamReader

/**
 * A constraint that validates a JSON document against a JSON schema.
 *
 * Json schema can check for required fields, types, and more.
 *
 * An example of a constraint error:
 *
 * Given a JSON schema for validating the forms field:
 * ```
 *     "forms": {
 *       "type": "array",
 *       "items": {
 *         "type": "object",
 *         "required": ["description", "id", "key", "metadata", "name"],
 *         "properties": {
 *           "description": {
 *             "type": "string"
 *           },
 *           "id": {
 *             "type": "string"
 *           },
 *           "key": {
 *             "$ref": "#/definitions/key"
 *           },
 *           "metadata": {
 *             "$ref": "#/definitions/metadata"
 *           },
 *           "name": {
 *             "type": "string"
 *           }
 *         }
 *       }
 *     },
 * ```
 *
 * And a JSON document that doesn't contain any of the required fields:
 * ```
 *   "forms": [{}]
 * ```
 *
 * The constraint will return an error:
 * ```
 *   {
 *     "key": "description",
 *     "type": "required",
 *     "path": "$.forms[0]",
 *     "constraintDetail": "[\"description\",\"id\",\"key\",\"metadata\",\"name\"]",
 *     "message": "$.forms[0]: required property 'description' not found"
 *   }
 * ```
 */
class JsonSchemaConstraint(
    private val schema: JsonSchema,
) : Constraint<JsonNode> {

    override fun validate(data: JsonNode): Errors {
        val errors = Errors()
        val assertions = schema.validate(data)

        if (assertions.isNotEmpty()) {

            assertions.forEach {
                val field =
                    if (it.type == "required") it.property else it.instanceLocation.toString().substringAfterLast(".")

                errors.add(
                    ConstraintError(
                        Field(field),
                        Type(it.type),
                        Path(it.instanceLocation.toString()),
                        ConstraintDetail(it.schemaNode.toString()),
                        Message(it.message)
                    )
                )
            }
        }

        return errors
    }

    override fun errorKeys(): List<String> {
        return emptyList()
    }

    companion object {

        fun fromClassPathResource(path: String): JsonSchemaConstraint {
            val jsonSchemaFactory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012)
            val schemaStream = this::class.java.classLoader.getResourceAsStream(path)
                ?: throw IllegalArgumentException("Schema file not found")
            val schema = schemaStream.use {
                jsonSchemaFactory.getSchema(InputStreamReader(it).readText())
            }
            return fromJsonSchema(schema)
        }

        fun fromJsonSchema(schema: JsonSchema): JsonSchemaConstraint {
            return JsonSchemaConstraint(schema)
        }
    }
}