package services.oneteam.ai.shared.domains.workspace

import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest

class WorkspaceUserService(private val workspaceUserRepository: WorkspaceUserRepository) {

    suspend fun search(
        pageRequest: PageRequest, workspaceUserSearchCriteria: WorkspaceUserSearchCriteria
    ): Page<WorkspaceUser.ForApi> {
        val searchResult = workspaceUserRepository.searchByCriteria(
            pageRequest, workspaceUserSearchCriteria
        )
//        println(searchResult)
        return Page(
            searchResult.page, searchResult.total, searchResult.items.map { it.toDTO() })
    }
}