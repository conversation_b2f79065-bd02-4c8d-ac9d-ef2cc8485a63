package services.oneteam.ai.shared.domains.workspace

import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion.Id

class WorkspaceUserService(private val workspaceUserRepository: WorkspaceUserRepository) {

    suspend fun search(
        pageRequest: PageRequest, workspaceUserSearchCriteria: WorkspaceUserSearchCriteria
    ): Page<WorkspaceUser.ForApi> {
        val searchResult = workspaceUserRepository.searchByCriteria(
            pageRequest, workspaceUserSearchCriteria
        )
        return Page(
            searchResult.page, searchResult.total, searchResult.items.map { it.toDTO() })
    }
}


fun WorkspaceUserEntity.toDTO(): WorkspaceUser.ForApi {
    return WorkspaceUser.ForApi(
        id = WorkspaceUser.Id(this.id.value),
        workspaceId = Workspace.Id(this.workspaceId.value),
        userId = User.Id(this.userId.value),
        accessLevel = this.accessLevel,
        status = this.status
    )
}