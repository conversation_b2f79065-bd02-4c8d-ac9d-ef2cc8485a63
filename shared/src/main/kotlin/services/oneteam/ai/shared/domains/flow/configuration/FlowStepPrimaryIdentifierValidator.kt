package services.oneteam.ai.shared.domains.flow.configuration

import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration.Step.Variant
import services.oneteam.ai.shared.domains.workspace.validation.*

class FlowStepPrimaryIdentifierValidator(
    private val id: FlowConfiguration.Step.Id,
    variant: Variant,
    private val properties: FlowConfiguration.Step.Properties,
    private val path: String,
    private val errors: Errors
) {
    private val triggerOrSteps: String =
        if (variant == Variant.TRIGGER) "trigger" else "steps"

    fun validate() {
        if (properties.typePrimaryIdentifier.isNullOrEmpty()) {
            errors.add(
                ConstraintError(
                    Field("typePrimaryIdentifier"),
                    Type("required"),
                    Path("$path.$triggerOrSteps.${id.value}.properties.typePrimaryIdentifier"),
                    ConstraintDetail("TypePrimaryIdentifier must not be empty"),
                    Message("TypePrimaryIdentifier must not be empty")
                )
            )
        }
    }
}