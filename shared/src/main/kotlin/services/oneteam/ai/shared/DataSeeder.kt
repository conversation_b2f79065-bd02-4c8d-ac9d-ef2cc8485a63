package services.oneteam.ai.shared

import kotlinx.serialization.json.Json
import org.jetbrains.exposed.dao.EntityClass
import org.jetbrains.exposed.sql.Op
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.DBInterface
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationEntity

class DataSeeder(val database: DBInterface) {

    inline fun <reified T, E : BaseLongEntity> seed(filePath: String, force: Boolean? = false): DataSeeder where T : Seedable<E> {
        val logger = LoggerFactory.getLogger(javaClass)

        val seedData = Json.decodeFromString<List<T>>(this::class.java.getResource(filePath)!!.readText())
        try {
            seedData.forEach { upsertSeed(it) }
        } catch (e: Exception) {
            // if force
            if (force == true) {
                // remove all rows on the table
                logger.error("Error seeding data, forcing seed by removing existing DB table", e)
                this.removeExistingSeed(seedData.first())

                try {
                    seedData.forEach { this.upsertSeed(it) }
                } catch (e: Exception) {
                    logger.error("Error seeding data even after removing", e)
                    throw e
                }
            } else {
                throw e
            }
        }
        return this
    }

    fun <E : BaseLongEntity> removeExistingSeed(seedable: Seedable<E>) {
        val ec = seedable.getRelatedEntity()
        ec.all().forEach { it.delete() }
    }

    fun <E : BaseLongEntity> upsertSeed(seedable: Seedable<E>) {
        val ec = seedable.getRelatedEntity()
        val existing = ec.find(seedable.getLocatorPredicate()).firstOrNull()
        if (existing == null) ec.new { this.apply(seedable.getUpdateBody()) }
        else if (!seedable.compareObjectWithEntity(existing)) existing.apply(seedable.getUpdateBody())
    }
}

interface Seedable<E : BaseLongEntity> {

    /**
     * Provides a function that applies updates to an existing entity.
     * The entity context (receiver) is the entity instance to update.
     */
    fun getUpdateBody(): E.() -> Unit

    /**
     * Defines the database condition (predicate) that locates the entity
     * corresponding to this instance's data (e.g. by primary identifier).
     */
    fun getLocatorPredicate(): Op<Boolean>

    /**
     * Specifies the related Exposed entity class used to
     * find or create an entity of type [E] in the database.
     */
    fun getRelatedEntity(): EntityClass<Long, E>

    /**
     * Compares the current instance with the related entity in the database.
     * Returns `true` if both match; otherwise `false`.
     */
    fun compareObjectWithEntity(dbEntity: E): Boolean
}
