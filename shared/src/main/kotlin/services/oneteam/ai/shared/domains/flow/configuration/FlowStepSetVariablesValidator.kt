package services.oneteam.ai.shared.domains.flow.configuration

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.workspace.validation.*

class FlowStepSetVariablesValidator(
    private val id: FlowConfiguration.Step.Id,
    private val properties: FlowConfiguration.Step.Properties,
    private val path: String,
    private val errors: Errors
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun validate() {
        setVariablesMinVariables()
        setVariablesMinIdentifierLength()
        warnings()
    }

    private fun warnings() {
        if (!properties.typePrimaryIdentifier.isNullOrEmpty()) {
            logger.warn("Step type primary identifier should be null at $path for step ${id.value}")
        }
    }

    private fun setVariablesMinVariables() {
        if (properties.variables.isNullOrEmpty()) {
            errors.add(
                ConstraintError(
                    Field("variables"),
                    Type("required"),
                    Path("$path.steps.${id.value}.properties.variables"),
                    ConstraintDetail("Variables must not be empty"),
                    Message("Variables must not be empty")
                )
            )
        }
    }

    private fun setVariablesMinIdentifierLength() {
        properties.variables?.forEachIndexed { index, variable ->
            if (variable.identifier.isEmpty()) {
                errors.add(
                    ConstraintError(
                        Field("identifier"),
                        Type("required"),
                        Path("$path.steps.${id.value}.properties.variables.$index.identifier"),
                        ConstraintDetail("Variable identifier is required"),
                        Message("Variable identifier is required")
                    )
                )
            }
        }
    }
}