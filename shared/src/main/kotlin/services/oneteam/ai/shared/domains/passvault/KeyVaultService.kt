package services.oneteam.ai.shared.domains.passvault

import com.azure.identity.AzureCliCredentialBuilder
import com.azure.identity.DefaultAzureCredentialBuilder
import com.azure.identity.ManagedIdentityCredentialBuilder
import com.azure.security.keyvault.secrets.SecretClient
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Serializable
enum class KeyVaultServiceProvider {
   @SerialName("AZURE")
    AZURE,
    @SerialName("IN_MEMORY")
    IN_MEMORY
}

sealed interface KeyVaultService {
    /**
     * Retrieves a secret from Azure Key Vault by its name.
     *
     * @param secretName The name of the secret to retrieve.
     * @return The value of the secret.
     * @throws IllegalArgumentException if the secret does not exist or is not found.
     */
    fun retrieveSecret(secretName: String): String;
}

// Keeps the value in memory after retrieval, backed by an LRU cache with some internal capacity.
class CachedKeyVaultService(
    private val keyVaultService: KeyVaultService
) : KeyVaultService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    class LRUCache<K, V>(capacity: Int) : LinkedHashMap<K, V>(capacity) {
        private val cacheCapacity = capacity

        override fun removeEldestEntry(eldest: MutableMap.MutableEntry<K, V>): Boolean {
            return size > cacheCapacity
        }
    }

    private val cache = LRUCache<String, String>(100)

    override fun retrieveSecret(secretName: String): String {
        return cache.getOrPut(secretName) {
            logger.debug("Cache miss for secret '{}'", secretName)
            keyVaultService.retrieveSecret(secretName)
        }
    }
}

class KeyVaultServiceFactory(
    private val factoryOptions: KeyVaultServiceFactoryOptions
) {
    data class KeyVaultServiceFactoryOptions(
        val provider: String,
        val azureConfig: AzureKeyVaultService.AzureKeyVaultServiceConfig? = null,
        val inMemoryConfig: InMemoryKeyVaultService.InMemoryKeyVaultServiceConfig? = null,
    )

    fun create(): KeyVaultService {
        val provider = KeyVaultServiceProvider.valueOf(factoryOptions.provider)
        return when (provider) {
            KeyVaultServiceProvider.AZURE -> AzureKeyVaultService(factoryOptions.azureConfig!!)
            KeyVaultServiceProvider.IN_MEMORY -> InMemoryKeyVaultService(factoryOptions.inMemoryConfig!!)
        }
    }
}

class AzureKeyVaultService(val config: AzureKeyVaultServiceConfig): KeyVaultService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val credentialStrategy: CredentialStrategy

    init {
        this.credentialStrategy = config.credentialStrategy?.let {
            CredentialStrategy.valueOf(it)
        } ?: CredentialStrategy.DEFAULT_AZURE_CREDENTIAL

        logger.info("Initializing Azure key-vault service for Key Vault '{}' with credential strategy '{}'.",
            config.keyVaultName, credentialStrategy)
    }

    data class AzureKeyVaultServiceConfig(
        val keyVaultName: String,
        val secretNamePrefix: String? = null,
        val credentialStrategy: String? = null
    )

    @Serializable
    enum class CredentialStrategy {
        @SerialName("AZURE_CLI_CREDENTIAL")
        AZURE_CLI_CREDENTIAL,           // requires `az login` to be run first
        @SerialName("MANAGED_IDENTITY_CREDENTIAL")
        MANAGED_IDENTITY_CREDENTIAL,    // requires Managed Identity to be configured on the Azure resource
        @SerialName("DEFAULT_AZURE_CREDENTIAL")
        DEFAULT_AZURE_CREDENTIAL,       // noisy in logs as it literally cycles through all possible methods
    }

    private fun getSecretClient(): SecretClient {
        val credential = when (credentialStrategy) {
            CredentialStrategy.AZURE_CLI_CREDENTIAL -> AzureCliCredentialBuilder().build()
            CredentialStrategy.MANAGED_IDENTITY_CREDENTIAL -> ManagedIdentityCredentialBuilder().build()
            CredentialStrategy.DEFAULT_AZURE_CREDENTIAL -> DefaultAzureCredentialBuilder().build()
        }

        val vaultUri = "https://${config.keyVaultName}.vault.azure.net/"
        return com.azure.security.keyvault.secrets.SecretClientBuilder()
            .vaultUrl(vaultUri)
            .credential(credential)
            .buildClient()
    }

    override fun retrieveSecret(secretName: String): String {
        val secretClient = getSecretClient()

        val trueSecretName = getTrueSecretName(secretName, maybePrefix = config.secretNamePrefix)
        logger.trace("Retrieving secret {} from Key Vault {}", secretName, config.keyVaultName)

        try {
            val retrievedSecret = secretClient.getSecret(trueSecretName);
            return retrievedSecret.value
        } catch (e: Exception) {
            throw IllegalArgumentException(
                "Secret with name '$trueSecretName' does not exist in Key Vault '${config.keyVaultName}'.",
                e
            )
        }
    }
}

@OptIn(ExperimentalSerializationApi::class)
class InMemoryKeyVaultService(val config: InMemoryKeyVaultServiceConfig): KeyVaultService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    private val secrets = mutableMapOf<String, String>()

    @Serializable
    data class InMemoryKeyVaultServiceConfig(
        val loadSeedData: Boolean = false,
        val keyVaultName: String,
        val secretNamePrefix: String? = null
    )

    constructor(keyvaultName: String, secretNamePrefix: String? = null): this(InMemoryKeyVaultServiceConfig(
        keyVaultName = keyvaultName,
        secretNamePrefix = secretNamePrefix,
    ))

    init {
        if (config.loadSeedData) {
            logger.info("Loading secrets from InMemoryKeyVaultSeed.json into In-Memory Key Vault '{}'.", config.keyVaultName)
            val seedData = Json.decodeFromStream<List<Map<String, String>>>(this::class.java.getResourceAsStream("/InMemoryKeyVaultSeed.json")!!)
            seedData.forEach { secret ->
                val name = secret["secretName"] ?: throw IllegalStateException("Secret name is missing in seed data.")
                val value = secret["value"] ?: throw IllegalStateException("Secret value is missing in seed data.")
                addSecret(name, value)
                logger.info("Loaded $name secrets into In-Memory Key Vault '{}'.", config.keyVaultName)
            }
            logger.info("Loaded ${seedData.size} secrets from InMemoryKeyVaultSeed.json into In-Memory Key Vault '{}'.", config.keyVaultName)
        }
        logger.info("Initialized In-Memory Key Vault service for Key Vault '{}' with prefix '{}'.", config.keyVaultName, config.secretNamePrefix)
    }

    override fun retrieveSecret(secretName: String): String {
        val trueSecretName = getTrueSecretName(secretName, maybePrefix = config.secretNamePrefix)
        logger.trace("Retrieving secret {} from Key Vault {}", secretName, config.keyVaultName)
        return secrets[trueSecretName] ?: throw IllegalArgumentException(
            "Secret with name '$trueSecretName' does not exist in Key Vault '${config.keyVaultName}'."
        )
    }

    internal fun addSecret(secretName: String, secretValue: String) {
        val trueSecretName = getTrueSecretName(secretName, maybePrefix = config.secretNamePrefix)
        secrets[trueSecretName] = secretValue
    }
    internal fun removeSecret(secretName: String) {
        val trueSecretName = getTrueSecretName(secretName, maybePrefix = config.secretNamePrefix)
        secrets.remove(trueSecretName)
    }
    internal fun clearSecrets() {
        secrets.clear()
    }
}

internal fun getTrueSecretName(secretName: String, maybePrefix: String? = null): String {
    if (maybePrefix != null) {
        return "${maybePrefix}-${secretName}"
    }
    return secretName
}
