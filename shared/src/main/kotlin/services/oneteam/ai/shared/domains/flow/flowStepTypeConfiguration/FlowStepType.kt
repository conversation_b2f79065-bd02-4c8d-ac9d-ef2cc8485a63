package services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration

import kotlinx.serialization.*
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Seedable
import services.oneteam.ai.shared.domains.event.EventKey

@Serializable
data class FlowStepType(
    val type: String,
    val name: String,
    val primaryIdentifier: String,
    val description: String? = null,
    val properties: Properties
) : Seedable<FlowStepTypeConfigurationEntity> {
    override fun getUpdateBody(): FlowStepTypeConfigurationEntity.() -> Unit = {
        type = <EMAIL>
        name = <EMAIL>
        description = <EMAIL>
        properties = <EMAIL>
        primaryIdentifier = <EMAIL>
        // TODO tenant is hardcoded for now
        tenantId =
            1 // need to add some functionality for globally available data, pretty sure we can just say that if tenant = 0 then everyone can see it
    }

    override fun getLocatorPredicate(): Op<Boolean> =
        FlowStepTypeConfigurationSchema.primaryIdentifier eq primaryIdentifier

    override fun getRelatedEntity() = FlowStepTypeConfigurationEntity

    override fun compareObjectWithEntity(dbEntity: FlowStepTypeConfigurationEntity): Boolean {
        if (dbEntity.type != type) return false
        if (dbEntity.name != name) return false
        if (dbEntity.description != description) return false
        if (dbEntity.properties != properties) return false
        return true
    }

    @Serializable
    class Properties(
        val icon: Icon? = null,
        val category: Category? = null,
        val isLocked: Boolean = false,
        val isHidden: Boolean = false,
        val deprecated: Deprecated? = null,
        val configuration: Configuration? = null,
    ) {

        @Serializable
        enum class FillStyle {
            filled, outlined
        }

        @Serializable
        data class Icon(
            val name: String, val fillStyle: FillStyle? = null
        )

        @Serializable
        data class Category(
            val name: String, val icon: Icon? = null, val order: Int? = null,
        )

        @Serializable
        data class Deprecated(
            val replacement: String? = null, val deprecatedAt: String? = null,
        )

        @Serializable
        class Configuration(
            val content: List<Content>? = null,
            val subscribeTo: Map<EventKey, TriggerEventSubscription>? = null,
            val apiCall: ApiCall? = null,
            val variableMappings: List<TriggerEventSubscription.VariableMapping>? = null,
            val documentation: List<DocumentationSection>? = null,
            val userConfiguredStartingVariables: Boolean? = null
        ) {

            fun findContentByIdentifier(identifier: String): Content? {
                return content?.find { it.identifier == identifier }
            }

            @Serializable
            data class TriggerEventSubscription(
                val key: EventKey, val condition: JsonElement, val variableMappings: List<VariableMapping>
            ) {
                @Serializable
                data class VariableMapping(
                    val type: String,
                    val identifier: String,
                    val value: JsonElement,
                    val properties: JsonObject? = null
                )
            }

            @OptIn(ExperimentalSerializationApi::class)
            @Serializable
            data class ApiCall(
                val url: String,
                val method: String,
                val headers: JsonElement? = null,
                val body: JsonElement? = null,
                val bodyProperties: Map<String, Map<String, Boolean>>? = null,
                val queryParams: Map<String, String>? = null,
                val response: Response,
                @EncodeDefault val internal: Boolean = false
            ) {

                @Serializable
                data class Response(
                    val type: String, val properties: Property
                ) {
                    @Serializable
                    data class Property(
                        val items: List<Item>? = null
                    )

                    @Serializable
                    data class Item(
                        val type: String, val identifier: String?, val properties: Property? = null
                    )
                }
            }

            @Serializable
            data class DocumentationSection(
                val title: String,
                val richText: List<RichTextNode>? = null,
            ) {
                @Serializable
                data class RichTextNode(
                    val type: RichTextNodeType,
                    val text: String,
                    val hidden: JsonElement? = null, // boolean or Condition
                ) {
                    @Serializable
                    enum class RichTextNodeType {
                        @SerialName("paragraph")
                        PARAGRAPH,

                        @SerialName("codeBlock")
                        CODE_BLOCK
                    }
                }
            }

            @Serializable
            data class Content(
                val text: String, val type: String, val identifier: String, val properties: Properties
            ) {
                @Transient
                val logger: Logger = LoggerFactory.getLogger(javaClass)

                @Serializable
                data class Properties(
                    val type: String? = null,
                    val required: JsonElement? = null, // boolean or Condition
                    val hidden: JsonElement? = null, // boolean or Condition
                    val disabled: JsonElement? = null, // boolean or Condition
                    val regex: String? = null,
                    val options: List<Option>? = null,
                    val dynamicOptions: DynamicOptions? = null,
                    val properties: Properties? = null,
                    val defaultValue: JsonElement? = null,
                    val styling: TableStyling? = null,
                    val columns: List<Content>? = null,
                    val description: String? = null,
                ) {
                    @Serializable
                    data class Option(
                        val label: String, val value: String, val description: String? = null,
                    )
                }

                @Serializable
                enum class TableStyling {
                    @SerialName("listOfInputs")
                    LIST_OF_INPUTS,

                    @SerialName("tableList")
                    TABLE_LIST

                }

                @Serializable
                data class DynamicOptions(
                    val tag: String? = null, val body: JsonElement? = null
                )
            }
        }
    }
}
