package services.oneteam.ai.shared.database

import org.jetbrains.exposed.dao.*
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IdTable
import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.datetime
import org.jetbrains.exposed.sql.javatime.timestamp
import java.time.Instant
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset

fun currentUtc(): LocalDateTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDateTime()

//https://github.com/paulkagiri/ExposedDatesAutoFill/blob/master/src/main/kotlin/app/Models.kt
abstract class BaseIdTable<T : Comparable<T>>(name: String) : IdTable<T>(name) {
    val createdAt = datetime("created_at").clientDefault { currentUtc() }
    val updatedAt = datetime("updated_at").clientDefault { currentUtc() }
}

abstract class BaseIdEntity<T : Comparable<T>>(id: EntityID<T>, table: BaseIdTable<T>) : Entity<T>(id) {
    val createdAt by table.createdAt
    var updatedAt by table.updatedAt
}

abstract class BaseIdEntityClass<T : Comparable<T>, E : BaseIdEntity<T>>(table: BaseIdTable<T>) :
    EntityClass<T, E>(table) {
    init {
        EntityHook.subscribe { action ->
            if (action.changeType == EntityChangeType.Updated) {
                try {
                    action.toEntity(this)?.updatedAt = currentUtc()
                } catch (e: Exception) {
                    //nothing much to do here
                }
            }
        }
    }
}

abstract class BaseLongIdTable(name: String) : LongIdTable(name) {
    val createdAt = timestamp("created_at").clientDefault { Instant.now() }
    val updatedAt = timestamp("updated_at").clientDefault { Instant.now() }
}

abstract class BaseLongEntity(id: EntityID<Long>, table: BaseLongIdTable) : LongEntity(id) {
    val createdAt by table.createdAt
    var updatedAt by table.updatedAt
}

abstract class BaseLongEntityClass<E : BaseLongEntity>(table: BaseLongIdTable) : LongEntityClass<E>(table) {
    init {
        EntityHook.subscribe { action ->
            if (action.changeType == EntityChangeType.Updated) {
                try {
                    action.toEntity(this)?.updatedAt = Instant.now()
                } catch (e: Exception) {
                    //nothing much to do here
                }
            }
        }
    }
}
