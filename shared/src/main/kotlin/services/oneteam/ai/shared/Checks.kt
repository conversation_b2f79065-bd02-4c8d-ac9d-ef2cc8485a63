package services.oneteam.ai.shared

import services.oneteam.ai.shared.domains.ApplicationException
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.NotFoundException


class Checks {
    fun sameInt(left: Int, right: Int, lazyMessage: () -> String) {
        if (left != right) {
            throw BadRequestException(lazyMessage())
        }
    }

    fun <T> exists(value: T?, lazyMessage: () -> String): T {
        if (value == null) {
            throw NotFoundException(lazyMessage())
        }
        return value
    }

    fun `true`(condition: <PERSON><PERSON>an, lazyMessage: () -> String) {
        if (!condition) {
            throw ApplicationException(lazyMessage())
        }
    }
}