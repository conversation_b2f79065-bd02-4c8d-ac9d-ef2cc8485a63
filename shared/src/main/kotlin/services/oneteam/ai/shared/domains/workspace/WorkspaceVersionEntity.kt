package services.oneteam.ai.shared.domains.workspace

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass

@Serializable
sealed class WorkspaceVersion {
    abstract val workspaceId: Workspace.Id
    abstract val configuration: Workspace.ForJson

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    data class ForCreate(
        override val workspaceId: Workspace.Id,
        override val configuration: Workspace.ForJson
    ) : WorkspaceVersion()


    @Serializable
    data class ForApi(
        val id: Id,
        override val workspaceId: Workspace.Id,
        override val configuration: Workspace.ForJson
    ) : WorkspaceVersion(){
        fun isRootFoundation(foundationConfigurationId: String): Boolean {
            val orders = this.configuration.foundations.order
            return orders[0].value == foundationConfigurationId
        }

        fun containsFoundation(foundationConfigurationId: String): Boolean {
            val orders = this.configuration.foundations.order
            return orders.any { it.value == foundationConfigurationId }
        }
    }

}

class WorkspaceVersionEntity(id: EntityID<Long>) : BaseLongEntity(id, Workspaces) {
    companion object : BaseLongEntityClass<WorkspaceVersionEntity>(WorkspaceVersions)

    var workspaceId by WorkspaceVersions.workspaceId
    var tenantId by WorkspaceVersions.tenantId
    var configuration by WorkspaceVersions.configuration
}