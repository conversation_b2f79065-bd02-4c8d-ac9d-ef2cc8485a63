package services.oneteam.ai.shared.domains.collection.form

import io.ktor.http.*
import kotlinx.serialization.json.*
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.DocumentId
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.helpers.CustomNanoId
import services.oneteam.ai.shared.helpers.JsonElementToAny
import java.net.URI

fun isQuestionInTable(questionId: BaseSection.Id, tableQuestion: BaseSection.TableQuestion): Boolean {
    return tableQuestion.properties?.columns?.any { it.id == questionId } ?: false
}

fun findQuestionInSections(questionId: BaseSection.Id, sections: List<BaseSection>?): BaseSection.BaseQuestion? {
    return sections?.firstNotNullOfOrNull { section ->
        when (section) {
            is BaseSection.BaseQuestion -> if (section.id == questionId) {
                section
            } else if (section.type == QuestionType.TABLE && isQuestionInTable(
                    questionId, section as BaseSection.TableQuestion
                )
            ) {
                section
            } else if (section.type == QuestionType.LIST) {
                (section as BaseSection.ListQuestion).properties?.items?.firstOrNull { it -> it.id == questionId }
            } else {
                null
            }

            is BaseSection.Section -> section.content?.let { findQuestionInSections(questionId, it) }
        }
    }
}


fun getQuestionById(
    questionId: BaseSection.Id, sections: List<BaseSection>?
): BaseSection.BaseQuestion? {
    val question = findQuestionInSections(questionId, sections) ?: return null

    return if (question.id.value != questionId.value) {
        (question as BaseSection.TableQuestion).properties?.columns
            ?.find { col -> col.id.value == questionId.value }
    } else question
}

fun checkSelectQuestionOption(value: JsonElement, options: List<JsonElement>): Boolean {
    return options.contains(value)
}

fun generateAnswerForSetAnswer(
    question: BaseSection.BaseQuestion,
    value: Any
): FormAnswer<*>? {
    return when (question.type) {
        QuestionType.TEXT -> FormAnswer.TextAnswer(question.id, value.toString().trim('"'))
        QuestionType.NUMBER -> {
            if (value == "")
                FormAnswer.NumberAnswer(question.id)
            else
                value.toString().trim('"').let { numberValue ->
                    FormAnswer.NumberAnswer(question.id, numberValue)
                }
        }

        QuestionType.DATE -> FormAnswer.DateAnswer(question.id, value.toString().trim('"'))
        QuestionType.BOOLEAN -> {
            val stringValue = value.toString().trim('"')
            if (stringValue == "" || stringValue == "null") {
                FormAnswer.BooleanAnswer(question.id)
            } else if (stringValue == "true" || stringValue == "false") {
                FormAnswer.BooleanAnswer(question.id, stringValue.toBoolean())
            } else {
                throw IllegalArgumentException("Invalid value for BOOLEAN question ID: ${question.id.value}, value: ${stringValue}")
            }
        }

        QuestionType.SELECT -> {
            val stringValue = value.toString().trim('"')
            if (stringValue == "" || stringValue == "null") {
                FormAnswer.SelectAnswer(question.id)
            } else {
                if (checkSelectQuestionOption(
                        JsonPrimitive(stringValue),
                        (question as BaseSection.SelectQuestion).properties?.options?.map { JsonPrimitive(it.value) }
                            ?: emptyList()
                    )
                ) {
                    FormAnswer.SelectAnswer(question.id, stringValue.ifEmpty { null })
                } else {
                    throw IllegalArgumentException("Invalid value for SELECT question ID: ${question.id.value}, value: ${stringValue}")
                }
            }
        }

        QuestionType.MULTISELECT -> {
            if (value == "" || value == null) {
                FormAnswer.MultiSelectAnswer(question.id)
            } else {
                try {
                    if (checkSelectQuestionOption(
                            value as? JsonArray
                                ?: throw IllegalArgumentException("Invalid value for MULTISELECT question ID: ${question.id.value}, value: ${value.toString()}"),
                            (question as BaseSection.MultiSelectQuestion).properties?.options?.map { JsonPrimitive(it.value) }
                                ?: emptyList()
                        )
                    ) {
                        FormAnswer.MultiSelectAnswer(
                            question.id,
                            value as List<JsonElement>
                        )
                    } else throw IllegalArgumentException("Invalid value for MULTISELECT question ID: ${question.id.value}, value: ${value.toString()}")
                } catch (e: Exception) {
                    throw IllegalArgumentException("Invalid value for MULTISELECT question ID: ${question.id.value}, value: ${value.toString()}")
                }
            }
        }

        QuestionType.FILES -> FormAnswer.FileAnswer(question.id, value.let {
            when (value) {
                is JsonArray -> value.map { jsonElement ->
                    Json.decodeFromJsonElement<FileAnswerValue>(jsonElement)
                }

                is List<*> -> value.filterIsInstance<FileAnswerValue>()
                else -> emptyList()
            }
        })

        QuestionType.LIST -> {
            if (value == "" || value == null) {
                FormAnswer.ListAnswer(question.id, OrderedMap.empty())
            } else {
                val listOf = (question as BaseSection.ListQuestion).properties?.items?.get(0)
                    ?: throw IllegalArgumentException("List question must have items defined")
                val itemValues = value as? JsonArray
                    ?: value as? ArrayList<*>
                    ?: throw IllegalArgumentException("Invalid value for LIST question ID: ${question.id.value}, value: ${value.toString()}")
                val itemValuesList = itemValues.map { itemValue ->
                    val itemId = ItemId(CustomNanoId.generate())
                    val itemAnswer = generateAnswerForSetAnswer(listOf, itemValue) ?: return null
                    ListAnswerItem(itemId, mapOf(listOf.id to itemAnswer))
                }
                FormAnswer.ListAnswer(
                    question.id,
                    OrderedMap(itemValuesList)
                )
            }
        }

        else -> null
    }
}

fun validateAndGenerateJsonOrTableValue(
    items: List<BaseSection.BaseQuestion>?,
    value: Map<String, Any>,
    useIdentifier: Boolean = true // true: use identifier, false: use id
): Map<BaseSection.Id, FormAnswer<*>> {
    if (items == null) return emptyMap()

    val answerValues = mutableMapOf<BaseSection.Id, FormAnswer<*>>()

    for (item in items) {
        val id = if (useIdentifier) item.identifier else item.id.value
        val fieldValue = value[id] ?: continue
        val answer = generateAnswerForSetAnswer(item, fieldValue) ?: continue

        answerValues[item.id] = answer
    }
    return answerValues
}

enum class TablePathType {
    TABLE, ROW, COLUMN
}

fun generateTablePath(
    level: TablePathType,
    tableQuestionId: String,
    rowId: String? = null,
    columnQuestionId: String? = null,
): List<String> {
    val path = mutableListOf("answers", tableQuestionId)
    val tablePathList = listOf("value", "entities", "rowId", "columns", "questionId")
    val depth = when (level) {
        TablePathType.TABLE -> return path
        TablePathType.ROW -> 3
        TablePathType.COLUMN -> 5
    }
    for (i in 0 until depth) {
        when (tablePathList[i]) {
            "rowId" -> {
                if (rowId != null) {
                    path.add(rowId)
                }
            }

            "questionId" -> {
                if (columnQuestionId != null) {
                    path.add(columnQuestionId)
                }
            }

            else -> path.add(tablePathList[i])
        }
    }
    return path
}

suspend fun validateAndGenerateTableAnswer(
    question: BaseSection.BaseQuestion,
    formAnswer: FormAnswerRequestBody,
    answersDocumentId: DocumentId,
    documentService: IDocumentService,
    formId: Form.Id,
    formService: FormService
): List<SetAnswerParams>? {

    if (formAnswer.operation == SetAnswerOperation.SET_TABLE) {
        val getRowId = fun(tableRowData: JsonElement): RowId {
            if (formAnswer.valueRowIdShouldUpdate?.toBoolean() == true && tableRowData.jsonObject.contains("_rowId")) {
                return RowId(tableRowData.jsonObject["_rowId"]!!.jsonPrimitive.content)
            }
            return RowId(CustomNanoId.generate())
        }

        val tableValue = formAnswer.value?.jsonArray

        // Check if tableValue is a file answer (xlsx, csv, xls) and if so, convert it to a table
        if (tableValue != null && tableValue.size > 0 && tableValue[0] is JsonObject && tableValue[0].jsonObject.size == 2 && tableValue[0].jsonObject.containsKey(
                "path"
            ) && tableValue[0].jsonObject.containsKey("name")
        ) {
            var tableDataFromSpreadSheets: List<Any> = emptyList()
            var fileAnswersTotal = 0
            for (potentialFileAnswer in tableValue) {
                if (potentialFileAnswer !is JsonObject || potentialFileAnswer.size != 2 || !potentialFileAnswer.containsKey(
                        "name"
                    ) || !potentialFileAnswer.containsKey("path")
                ) {
                    continue
                }
                val fileVariable = potentialFileAnswer.jsonObject
                val isCsvFile = fileVariable["name"]?.jsonPrimitive?.content?.endsWith(".csv") ?: false
                val isSpreadSheet =
                    isCsvFile || fileVariable["name"]?.jsonPrimitive?.content?.endsWith(".xlsx") ?: false ||
                            fileVariable["name"]?.jsonPrimitive?.content?.endsWith(".xls") ?: false
                if (!isSpreadSheet) {
                    continue
                }
                fileAnswersTotal++
                val signedUrl = formService.getSignedUrl(
                    formId,
                    question.id.value,
                    null,
                    fileVariable["path"]?.jsonPrimitive?.content
                )

                val df = formService.readFile(signedUrl, isCsvFile)
                val fileData = formService.matchFileDataWithForm(formId, question.id.value, df)

                if (fileData !== null) {
                    tableDataFromSpreadSheets += fileData
                }
            }

            if (fileAnswersTotal > 0) {
                val formAnswerBody = FormAnswerRequestBody(
                    questionId = BaseSection.Id(question.id.value),
                    value = TypeToJsonElementConverter.toJsonElement(tableDataFromSpreadSheets),
                    operation = SetAnswerOperation.SET_TABLE
                )
                return validateAndGenerateTableAnswer(
                    question,
                    formAnswerBody,
                    answersDocumentId,
                    documentService,
                    formId,
                    formService
                )
            }
        }

        val tableRowAnswers = tableValue?.associate { tableRow ->
            val rowId = getRowId(tableRow)
            rowId to TableAnswerRow(
                rowId, validateAndGenerateJsonOrTableValue(
                    (question as BaseSection.TableQuestion).properties?.columns,
                    JsonElementToAny.jsonElementToAny(tableRow) as Map<String, Any>,
                    false
                )
            )
        }

        val path = generateTablePath(TablePathType.TABLE, question.id.value)
        val tableAnswer = FormAnswer.TableAnswer(
            question.id,
            OrderedMap.of(tableRowAnswers?.keys?.toList() ?: emptyList(), tableRowAnswers ?: emptyMap())
        )
        val setAnswerParams = SetAnswerParams(path, tableAnswer)
        return listOf(setAnswerParams)
    }

    if (formAnswer.operation == SetAnswerOperation.SET_ROW) {
        val answerValues = validateAndGenerateJsonOrTableValue(
            (question as BaseSection.TableQuestion).properties?.columns,
            JsonElementToAny.jsonElementToAny(formAnswer.value!!) as Map<String, Any>,
            false
        )
        var rowId: RowId = getOrGenerateRowId(formAnswer, documentService, answersDocumentId, question.id)
        val path = generateTablePath(TablePathType.ROW, question.id.value, rowId.value)
        val rowAnswer = TableAnswerRow(rowId, answerValues)
        val tableAnswer = FormAnswer.TableAnswer(
            question.id,
            OrderedMap.of(listOf(rowId), mapOf(rowId to rowAnswer))
        )
        val setAnswerParams = SetAnswerParams(path, tableAnswer)
        return listOf(setAnswerParams)
    }

    if (formAnswer.operation == SetAnswerOperation.SET_COLUMN) {
        require(formAnswer.value?.jsonArray != null) { "Invalid value for column, expected JSON array" }

        val rowIds = getExistingRowIdsFromDocument(documentService, answersDocumentId, question.id)
        val columnValues = formAnswer.value!!.jsonArray
        val column = (question as BaseSection.TableQuestion).properties?.columns
            ?.find { col -> col.id.value == formAnswer.columnId }
            ?: return null

        val setAnswerParamsList = mutableListOf<SetAnswerParams>()

        val maxRows = maxOf(rowIds.size, columnValues.size)

        for (i in 0 until maxRows) {
            val rowId = if (i < rowIds.size) rowIds[i] else RowId(CustomNanoId.generate())
            val columnValue = if (i < columnValues.size) JsonElementToAny.jsonElementToAny(columnValues[i]) else null

            val path = generateTablePath(
                TablePathType.COLUMN,
                question.id.value,
                rowId.value,
                formAnswer.columnId
            )
            val columnAnswer = generateAnswerForSetAnswer(column, columnValue ?: "") ?: return null
            val rowAnswer = TableAnswerRow(rowId, mapOf(column.id to columnAnswer))
            val tableAnswer = FormAnswer.TableAnswer(
                question.id,
                OrderedMap.of(listOf(rowId), mapOf(rowId to rowAnswer))
            )
            setAnswerParamsList.add(SetAnswerParams(path, tableAnswer))
        }

        return setAnswerParamsList
    }

    if (formAnswer.operation == SetAnswerOperation.SET_CELL) {
        var rowId: RowId = getOrGenerateRowId(formAnswer, documentService, answersDocumentId, question.id)
        val isNewRow =
            formAnswer.rowIndex.toString().isNullOrBlank() && (formAnswer.rowId == null || formAnswer.rowId.toString()
                .isEmpty())
        val path = if (isNewRow) {
            generateTablePath(TablePathType.ROW, question.id.value, rowId.value)
        } else {
            generateTablePath(
                TablePathType.COLUMN,
                question.id.value,
                rowId.value,
                formAnswer.columnId
            )
        }
        val column = (question as BaseSection.TableQuestion).properties?.columns
            ?.find { col -> col.id.value == formAnswer.columnId }
            ?: return null
        val columnAnswer = generateAnswerForSetAnswer(column, formAnswer.value ?: "") ?: return null
        val rowAnswer = TableAnswerRow(rowId, mapOf(column.id to columnAnswer))
        val tableAnswer = FormAnswer.TableAnswer(
            question.id,
            OrderedMap.of(listOf(rowId), mapOf(rowId to rowAnswer))
        )
        val setAnswerParams = SetAnswerParams(path, tableAnswer)
        return listOf(setAnswerParams)
    }

    throw IllegalArgumentException("Should not reach here")
}

private suspend fun getExistingAnswerFromDocument(
    documentService: IDocumentService,
    answersDocumentId: DocumentId,
    questionId: BaseSection.Id,
): FormAnswer<*>? {
    val answers = documentService.show(answersDocumentId, cookie = null, FormAnswer.ForJson::class, true)
    val answer: FormAnswer<*>? = answers.getAnswer(questionId)
    return answer
}

private suspend fun getExistingRowIdsFromDocument(
    documentService: IDocumentService,
    answersDocumentId: DocumentId,
    questionId: BaseSection.Id,
): List<RowId> {
    val answer = getExistingAnswerFromDocument(documentService, answersDocumentId, questionId)
    val rowIds = if (answer != null) {
        (answer as FormAnswer.TableAnswer).value.order
    } else emptyList()
    return rowIds
}

private suspend fun getOrGenerateRowId(
    formAnswer: FormAnswerRequestBody,
    documentService: IDocumentService,
    answersDocumentId: DocumentId,
    tableQuestionId: BaseSection.Id,
): RowId {
    if (formAnswer.rowId != null && formAnswer.rowId.toString().isNotEmpty()) {
        return RowId(formAnswer.rowId.toString())
    }

    val rowIndexAsString = formAnswer.rowIndex?.toString()?.trim() ?: ""
    val rowIndexIsProvided = rowIndexAsString.isNotBlank() && rowIndexAsString.isNotEmpty()

    if (
        rowIndexIsProvided
    ) {
        try {
            val tableRowIds = getExistingRowIdsFromDocument(documentService, answersDocumentId, tableQuestionId)
            val rowIndex = formAnswer.rowIndex.toString().toInt() - 1


            require(rowIndex >= 0 && rowIndex < tableRowIds.size) { "Invalid row index" }

            return tableRowIds[rowIndex]
        } catch (e: NumberFormatException) {
            throw IllegalArgumentException("Invalid row index")
        }
    }

    // Check if the last row is a placeholder
    val existingAnswer = getExistingAnswerFromDocument(documentService, answersDocumentId, tableQuestionId)
    if (existingAnswer != null) {
        val tableAnswer = existingAnswer as FormAnswer.TableAnswer
        val lastRowId = tableAnswer.value.order.lastOrNull()
        if (lastRowId != null) {
            val lastRowAnswer = tableAnswer.value.entities[lastRowId]
            if (lastRowAnswer?.columns != null && lastRowAnswer.columns.isEmpty()) {
                return lastRowId
            }
        }
    }

    val newRowId = CustomNanoId.generate();
    return RowId(newRowId)
}

enum class ListPathType {
    LIST, ITEM, ITEM_VALUE
}

fun generateListPath(
    level: ListPathType,
    listQuestionId: String,
    itemId: String? = null,
    itemQuestionId: String? = null,
): List<String> {
    val path = mutableListOf("answer", listQuestionId)
    val pathList = listOf("value", "entities", "itemId", "item", "questionId")
    val depth = when (level) {
        ListPathType.LIST -> return path
        ListPathType.ITEM -> 3
        ListPathType.ITEM_VALUE -> 5
    }
    for (i in 0 until depth) {
        when (pathList[i]) {
            "itemId" -> {
                if (itemId != null) {
                    path.add(itemId)
                }
            }

            "questionId" -> {
                if (itemQuestionId != null) {
                    path.add(itemQuestionId)
                }
            }

            else -> path.add(pathList[i])
        }
    }
    return path
}

private suspend fun getOrGenerateItemId(
    formAnswer: FormAnswerRequestBody,
    documentService: IDocumentService,
    answersDocumentId: DocumentId,
    listQuestionId: BaseSection.Id,
): ItemId {
    if (formAnswer.itemId != null && formAnswer.itemId.toString().isNotEmpty()) {
        return ItemId(formAnswer.itemId.toString())
    }

    val itemIndexAsString = formAnswer.itemIndex.toString().trim()
    val itemIndexIsProvided =
        formAnswer.itemIndex != null && itemIndexAsString.isNotBlank() && itemIndexAsString.isNotEmpty()

    if (itemIndexIsProvided) {
        try {
            val answers = documentService.show(answersDocumentId, cookie = null, FormAnswer.ForJson::class, true)
            val answer: FormAnswer<*>? = answers.getAnswer(listQuestionId)
            val listItemIds = (answer as FormAnswer.ListAnswer).value.order
            val itemIndex = formAnswer.itemIndex.toString().toInt() - 1
            require(itemIndex >= 0 && itemIndex < listItemIds.size) { "Invalid item index" }
            return listItemIds[itemIndex]
        } catch (e: NumberFormatException) {
            throw IllegalArgumentException("Invalid item index")
        }
    }

    val newItemId = CustomNanoId.generate();
    return ItemId(newItemId)
}


suspend fun validateAndGenerateListAnswer(
    question: BaseSection.BaseQuestion,
    formAnswer: FormAnswerRequestBody,
    answersDocumentId: DocumentId,
    documentService: IDocumentService,
): List<SetAnswerParams>? {
    return when (formAnswer.listOperation) {
        SetListAnswerOperation.SET_LIST -> {
            val answers = documentService.show(
                answersDocumentId, cookie = null, FormAnswer.ForJson::class, true
            )
            val answer: FormAnswer<*>? = answers.getAnswer(question.id)
            val itemIds = (answer as FormAnswer.ListAnswer).value.order
            val itemValues = formAnswer.value!!.jsonArray

            val item = (question as BaseSection.ListQuestion).properties?.items
                ?.firstOrNull() // Only one type of item question is allowed in ListQuestion for now
                ?: return null

            val order = mutableListOf<ItemId>()
            val entities = mutableMapOf<ItemId, ListAnswerItem>()

            for (i in 0 until itemValues.size) {
                val itemId = itemIds.getOrNull(i) ?: ItemId(CustomNanoId.generate())
                val itemValue = itemValues.getOrNull(i)?.let(JsonElementToAny::jsonElementToAny)

                order.add(itemId)
                val itemAnswer = generateAnswerForSetAnswer(item, itemValue ?: "") ?: return null
                entities[itemId] = ListAnswerItem(itemId, mapOf(item.id to itemAnswer))
            }

            val path = generateListPath(ListPathType.LIST, question.id.value)
            val listAnswer = FormAnswer.ListAnswer(
                question.id,
                OrderedMap(order, entities)
            )
            listOf(SetAnswerParams(path, listAnswer))
        }

        SetListAnswerOperation.SET_ITEM -> {
            val itemId: ItemId = getOrGenerateItemId(formAnswer, documentService, answersDocumentId, question.id)
            val item = (question as BaseSection.ListQuestion).properties?.items
                ?.firstOrNull() // Only one type of item question is allowed in ListQuestion for now
                ?: return null
            val appendNewItem = (formAnswer.itemIndex == null || formAnswer.itemIndex.toString()
                .isEmpty()) && (formAnswer.itemId == null || formAnswer.itemId.toString().isEmpty())
            val path = if (appendNewItem) {
                generateListPath(ListPathType.ITEM, question.id.value, itemId.value)
            } else {
                generateListPath(
                    ListPathType.ITEM_VALUE,
                    question.id.value,
                    itemId.value,
                    item.id.value
                )
            }

            val itemAnswer = generateAnswerForSetAnswer(item, formAnswer.value ?: "") ?: return null
            val listItemAnswer = ListAnswerItem(itemId, mapOf(item.id to itemAnswer))
            val listAnswer = FormAnswer.ListAnswer(
                question.id,
                OrderedMap(listOf(listItemAnswer))
            )
            val setAnswerParams = SetAnswerParams(path, listAnswer)
            listOf(setAnswerParams)
        }

        SetListAnswerOperation.REMOVE_ITEM -> {
            require(formAnswer.itemId != null || formAnswer.itemIndex != null) { "Invalid value for list item, expected JSON array" }
            val answers = documentService.show(answersDocumentId, cookie = null, FormAnswer.ForJson::class, true)
            val answer: FormAnswer.ListAnswer = answers.getAnswer(question.id) as FormAnswer.ListAnswer

            val itemId: ItemId = getOrGenerateItemId(formAnswer, documentService, answersDocumentId, question.id)
            val path = generateListPath(ListPathType.LIST, question.id.value)
            answer.value.remove(itemId)
            val listAnswer = FormAnswer.ListAnswer(
                question.id,
                answer.value
            )
            val setAnswerParams = SetAnswerParams(path, listAnswer)
            listOf(setAnswerParams)
        }

        else -> throw IllegalArgumentException("Should not reach here")
    }
}

fun isUrlReferencingQuestionInOtaiStorage(url: URI, questionId: String, uploadService: BlobService): Boolean {
    if (!uploadService.isOtaiStorage(url)) {
        return false
    }

    val questionFolderName = url.path.split("/").getOrNull(4)
    return questionFolderName == questionId
}

fun getAccessibleUrl(url: URI, uploadService: BlobService): URI {
    if (uploadService.isOtaiStorage(url)) {
        return URI(uploadService.generateSasToken(url.path, createFlag = false))
    }
    return url;
}

fun generateFilePath(
    tenantId: Long,
    workspaceId: String,
    formId: String,
    questionId: String,
    rowId: String? = null
): String {
    val path = arrayOf<String?>(
        tenantId.toString(),
        workspaceId,
        formId,
        questionId,
        rowId,
        CustomNanoId.generate(10)
    )
    return path.filterNotNull().reduce { acc, p -> "$acc/$p" }
}

fun getBlobSasUrl(
    value: String?,
    fileQuestionId: BaseSection.Id,
    tenantId: Long,
    workspace: Workspace.ForJson,
    formId: Form.Id,
    blobService: BlobService,
): String {
    val answer = value.toString().trim('"').replace("%2F", "/")
    val parsedURL = URI(answer)
    // we have a tricky edge case here; the URL could be relative with no host... in which case we need to get the full url
    // matches question so we don't need to do anything
    // matches something within our storage so we need to generate a sas token and download
    // matches something outside our storage so we download
    val isReferencingQuestion = isUrlReferencingQuestionInOtaiStorage(parsedURL, fileQuestionId.value, blobService)
    if (isReferencingQuestion) {
        return Url(parsedURL).encodedPath
    }

    val accessUrl = getAccessibleUrl(parsedURL, blobService)
    accessUrl.toURL().openStream().use { input ->
        val filepath = generateFilePath(
            tenantId,
            workspace.id.value.toString(),
            formId.value.toString(),
            fileQuestionId.value
        )
        val sasUrl = blobService.uploadFromStream(filepath, input)
        return Url(sasUrl.trim('"').replace("%2F", "/")).encodedPath
    }
}

private fun setFileAnswerParams(questionId: BaseSection.Id, fileName: String?, filePath: String?): SetAnswerParams {
    val path = listOf("answer", questionId.value)
    val fileAnswer = if (fileName.isNullOrEmpty() || filePath.isNullOrEmpty()) {
        FormAnswer.FileAnswer(
            questionId,
            emptyList()
        )
    } else {
        FormAnswer.FileAnswer(
            questionId,
            listOf(
                FileAnswerValue(
                    filePath,
                    fileName,
                )
            )
        )
    }
    return SetAnswerParams(path, fileAnswer)
}

fun normaliseInputsForFileAnswer(fileAnswer: JsonElement?): Array<FileAnswerValue>? {
    if (fileAnswer == null) {
        return null;
    }
    when (fileAnswer) {
        is JsonObject -> {
            require(!(fileAnswer["path"]?.jsonPrimitive?.content.isNullOrEmpty() || fileAnswer["name"]?.jsonPrimitive?.content.isNullOrEmpty())) {
                "Invalid value for FILES question ID: ${fileAnswer}"
            }

            return arrayOf(
                FileAnswerValue(
                    fileAnswer["path"]?.jsonPrimitive?.content.toString(),
                    fileAnswer["name"]?.jsonPrimitive?.content.toString(),
                )
            )
        }

        is JsonArray -> {
            return fileAnswer.map { fileAnswer ->
                return@map normaliseInputsForFileAnswer(fileAnswer)
            }.filter { fileAnswers ->
                return@filter fileAnswers != null
            }.map { fileAnswers ->
                return@map fileAnswers as Array<FileAnswerValue>
            }.toTypedArray().flatten().toTypedArray()
        }

        else -> {
            throw IllegalArgumentException("Invalid value for FILES question ID: ${fileAnswer}")
        }
    }
    return null
}

suspend fun validateAndGenerateFileAnswer(
    question: BaseSection.BaseQuestion,
    formAnswer: FormAnswerRequestBody,
    answersDocumentId: DocumentId,
    documentService: IDocumentService,
    tenantId: Long,
    workspace: Workspace.ForJson,
    formId: Form.Id,
    uploadService: BlobService,
): List<SetAnswerParams> {
    // We support formAnswer.value as a single file {} or an array of files[{}]
    return when (formAnswer.fileOperation) {
        SetFileAnswerOperation.SET_FILE -> {
            val fileAnswerValueList = normaliseInputsForFileAnswer(formAnswer.value)
            require(!fileAnswerValueList.isNullOrEmpty()) { "Invalid file answer value" }

            val setAnswerParamsList = mutableListOf<SetAnswerParams>()
            setAnswerParamsList.add(setFileAnswerParams(question.id, null, null)) //delete existing answers

            fileAnswerValueList.forEach { fileAnswerValue ->
                val blobSasUrl =
                    getBlobSasUrl(fileAnswerValue.path, question.id, tenantId, workspace, formId, uploadService)
                setAnswerParamsList.add(setFileAnswerParams(question.id, fileAnswerValue.name, blobSasUrl))
            }
            setAnswerParamsList
        }

        SetFileAnswerOperation.ADD_FILE -> {
            val fileAnswerValueList = normaliseInputsForFileAnswer(formAnswer.value)
            require(!fileAnswerValueList.isNullOrEmpty()) { "Invalid file answer value" }

            val setAnswerParamsList = mutableListOf<SetAnswerParams>()

            fileAnswerValueList.forEach { fileAnswerValue ->
                val blobSasUrl =
                    getBlobSasUrl(fileAnswerValue.path, question.id, tenantId, workspace, formId, uploadService)
                setAnswerParamsList.add(setFileAnswerParams(question.id, fileAnswerValue.name, blobSasUrl))
            }
            setAnswerParamsList
        }

        SetFileAnswerOperation.REMOVE_FILE -> {
            val answers = documentService.show(answersDocumentId, cookie = null, FormAnswer.ForJson::class, true)
            val answer: FormAnswer.FileAnswer = answers.getAnswer(question.id) as FormAnswer.FileAnswer
            require(formAnswer.fileIndex != null && formAnswer.fileIndex - 1 >= 0 && formAnswer.fileIndex - 1 <= answer.value.size) { "Invalid file index" }
            val removedFileAnswer = answer.value[formAnswer.fileIndex]
            val setAnswerParams = setFileAnswerParams(question.id, "undefined", removedFileAnswer.path)
            listOf(setAnswerParams)
        }

        else -> throw IllegalArgumentException("Should not reach here")
    }
}

suspend fun validateAndGenerateAnswer(
    question: BaseSection.BaseQuestion,
    formAnswer: FormAnswerRequestBody,
    answersDocumentId: DocumentId,
    documentService: IDocumentService,
    tenantId: Long,
    workspace: Workspace.ForJson,
    formId: Form.Id,
    uploadService: BlobService,
    formService: FormService
): List<SetAnswerParams>? {
    return when (question.type) {
        QuestionType.TABLE -> {
            validateAndGenerateTableAnswer(
                question,
                formAnswer,
                answersDocumentId,
                documentService,
                formId,
                formService
            )
        }

        QuestionType.JSON -> {
            val answerValues = validateAndGenerateJsonOrTableValue(
                (question as BaseSection.JsonQuestion).properties?.items,
                formAnswer.value as? Map<String, Any> ?: throw IllegalArgumentException("Invalid JSON value")
            )

            val path = listOf("answer", question.id.value)
            val answer = FormAnswer.JsonAnswer(formAnswer.questionId, answerValues)
            listOf(SetAnswerParams(path, answer))
        }

        QuestionType.LIST -> {
            validateAndGenerateListAnswer(question, formAnswer, answersDocumentId, documentService)
        }

        QuestionType.FILES -> {
            validateAndGenerateFileAnswer(
                question,
                formAnswer,
                answersDocumentId,
                documentService,
                tenantId,
                workspace,
                formId,
                uploadService
            )
        }

        else -> generateAnswerForSetAnswer(question, formAnswer.value ?: "")?.let {
            val path = listOf("answer", question.id.value)
            SetAnswerParams(path, it)
        }?.let { listOf(it) }
    }
}

fun generateFormAlertsRequestSyncServerBody(
    formAlert: FormAlertRequestBody,
    path: MutableList<String>,
    location: AlertLocation
): FormAlertRequestSyncServerBody {
    require(!(formAlert.type == null || formAlert.message == null)) { "Alert type and message are required" }
    return FormAlert(
        formAlert.type,
        formAlert.operation,
        formAlert.groupIdentifier,
        formAlert.message,
        null,
        location,
    ).let { FormAlertRequestSyncServerBody(path, it) }
}

suspend fun validateAndGenerateAlert(
    documentService: IDocumentService,
    answersDocumentId: DocumentId,
    question: BaseSection.BaseQuestion,
    formAlert: FormAlertRequestBody
): List<FormAlertRequestSyncServerBody> {
    val path = mutableListOf("annotations")

    suspend fun rowExists(answersDocumentId: DocumentId, question: BaseSection.BaseQuestion, rowId: RowId): Boolean {
        val answers =
            documentService.show<FormAnswer.ForJson>(answersDocumentId, cookie = null, FormAnswer.ForJson::class, true)
        val answer: FormAnswer<*>? = answers.getAnswer(question.id)

        if (answer is FormAnswer.TableAnswer) {
            return answer.value.entities.containsKey(rowId)
        }

        return false
    }

    val location: AlertLocation = when (question.type) {
        QuestionType.TABLE -> {
            val tableQuestion = question as BaseSection.TableQuestion
            if (tableQuestion.id == formAlert.questionId
                && formAlert.rowId == null && formAlert.columnId == null
            ) { // Alert on Table
                AlertLocation(tableQuestion.id.value)
            } else if (tableQuestion.id == formAlert.questionId
                && (formAlert.rowId != null || formAlert.columnId != null)
            ) { // Alert on Table Row
                if (formAlert.rowId != null) {
                    require(
                        rowExists(
                            answersDocumentId,
                            tableQuestion,
                            RowId(formAlert.rowId.value)
                        )
                    ) {
                        "Invalid value, row ID ${formAlert.rowId} does not exist on tablebr"
                    }
                }
                if (formAlert.columnId != null) {
                    require(
                        tableQuestion.properties?.columns?.firstOrNull { it.id == formAlert.columnId } != null
                    ) {
                        "Invalid value, column ID ${formAlert.columnId} does not exist on table"
                    }
                }

                AlertLocation(question.id.value, formAlert.columnId?.value, formAlert.rowId?.value)
            } else { // Alert on Table Column or Cell
                require(
                    !(formAlert.rowId != null && !rowExists(
                        answersDocumentId,
                        question,
                        RowId(formAlert.rowId.value)
                    ))
                ) {
                    "Invalid value, row ID ${formAlert.rowId} does not exist on table"
                }

                AlertLocation(question.id.value, formAlert.questionId.value, formAlert.rowId?.value)
            }
        }

        else -> {
            require(formAlert.rowId == null && formAlert.columnId == null) {
                "Invalid alert, rowId and columnId are not allowed on this question type ${question.type}"
            }

            AlertLocation(question.id.value)
        }
    }

    return listOf(generateFormAlertsRequestSyncServerBody(formAlert, path, location))
}