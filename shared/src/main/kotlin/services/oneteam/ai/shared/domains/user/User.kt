package services.oneteam.ai.shared.domains.user

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.json.jsonb
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.otSerializer


object UserSchema : BaseLongIdTable("users") {
    val email = varchar("email", length = 255)
    val tenantId = long("tenant_id")
    val properties = jsonb<User.Properties>("properties", otSerializer).nullable()

    init {
        uniqueIndex(email, tenantId)
    }
}

class UserEntity(id: EntityID<Long>) : BaseLongEntity(id, UserSchema) {
    companion object : BaseLongEntityClass<UserEntity>(UserSchema)

    var email by UserSchema.email
    var tenantId by UserSchema.tenantId
    var properties by UserSchema.properties

    fun toDTO(): User.ForApi {
        return User.ForApi(
            id = User.Id(this.id.value),
            email = this.email,
            tenantId = this.tenantId,
            properties = this.properties
        )
    }
}

@Serializable
sealed class User {

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    data class ForCreate(val id: Id, val email: String, val tenantId: Long, val properties: Properties? = null)

    @Serializable
    data class ForApi(val id: Id, val email: String, val tenantId: Long, val properties: Properties? = null)

    @Serializable
    data class Properties(
        val firstName: String,
        val lastName: String
    )

}