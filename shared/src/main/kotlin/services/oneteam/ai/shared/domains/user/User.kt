package services.oneteam.ai.shared.domains.user

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable


object UserSchema : BaseLongIdTable("users") {
    val email = varchar("email", length = 255)
    val tenantId = long("tenant_id")

    init {
        uniqueIndex(email, tenantId)
    }
}

class UserEntity(id: EntityID<Long>) : BaseLongEntity(id, UserSchema) {
    companion object : BaseLongEntityClass<UserEntity>(UserSchema)

    var email by UserSchema.email
    var tenantId by UserSchema.tenantId
}

@Serializable
sealed class User {

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    data class ForCreate(val id: Id, val email: String, val tenantId: Long)

    data class ForApi(val id: Id, val email: String, val tenantId: Long)

}