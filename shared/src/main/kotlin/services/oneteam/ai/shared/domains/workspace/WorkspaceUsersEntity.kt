package services.oneteam.ai.shared.domains.workspace

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.json.jsonb
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.domains.tenant.TenantSchema
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserSchema


class WorkspaceUserEntity(id: EntityID<Long>) : BaseLongEntity(id, WorkspaceUsersSchema) {
    companion object : BaseLongEntityClass<WorkspaceUserEntity>(WorkspaceUsersSchema)

    var workspaceId by WorkspaceUsersSchema.workspaceId;
    var userId by WorkspaceUsersSchema.userId;
    var tenantId by WorkspaceUsersSchema.tenantId;
    var status by WorkspaceUsersSchema.status;
    var accessLevel: List<WorkspaceUser.AccessLevel> by WorkspaceUsersSchema.accessLevel

}

object WorkspaceUsersSchema : BaseLongIdTable("workspace_users") {
    val status = enumerationByName("status", 50, WorkspaceUser.Status::class)
    val accessLevel = jsonb<List<WorkspaceUser.AccessLevel>>("access_level", Json { encodeDefaults = true })
    val workspaceId = reference("workspace_id", Workspaces)
    val userId = reference("user_id", UserSchema)
    val tenantId = long("tenant_id").references(TenantSchema.id)
}


@Serializable
sealed class WorkspaceUser {

    @Serializable
    enum class AccessLevel {
        SETTINGS, CONFIGURATION, COLLECTION
    }

    @Serializable
    enum class Status {
        ACTIVE, INACTIVE
    }

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    data class ForCreate(
        val workspaceId: Workspace.Id,
        val userId: User.Id,
        val accessLevel: List<AccessLevel>,
        val status: Status
    )

    @Serializable
    data class ForUpdate(
        val workspaceId: Workspace.Id,
        val userId: User.Id,
        val accessLevel: List<AccessLevel>,
        val status: Status
    )

    @Serializable
    data class ForApi(
        val id: Id,
        val workspaceId: Workspace.Id,
        val userId: User.Id,
        val accessLevel: List<AccessLevel>,
        val status: Status
    )

}