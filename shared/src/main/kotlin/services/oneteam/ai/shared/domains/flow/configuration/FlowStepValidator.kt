package services.oneteam.ai.shared.domains.flow.configuration

import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration.Step.Variant
import services.oneteam.ai.shared.domains.workspace.validation.ConstraintDetail
import services.oneteam.ai.shared.domains.workspace.validation.ConstraintError
import services.oneteam.ai.shared.domains.workspace.validation.Errors
import services.oneteam.ai.shared.domains.workspace.validation.Field
import services.oneteam.ai.shared.domains.workspace.validation.Message
import services.oneteam.ai.shared.domains.workspace.validation.Type
import services.oneteam.ai.shared.domains.workspace.validation.WorkspaceValidationContext
import services.oneteam.ai.shared.helpers.getContent
import kotlin.text.isNullOrEmpty

class FlowStepValidator(val context: WorkspaceValidationContext) {

    fun validate(
        id: FlowConfiguration.Step.Id,
        variant: Variant,
        properties: FlowConfiguration.Step.Properties,
        path: String,
        errors: Errors
    ) {

        when (variant) {
            Variant.SET_VARIABLES -> {
                FlowStepSetVariablesValidator(id, properties, path, errors).validate()
            }

            Variant.CONDITION -> {
                FlowStepConditionValidator(id, properties, path, errors).validate()
            }

            Variant.ACTION,
            Variant.TRIGGER -> {
                FlowStepPrimaryIdentifierValidator(
                    id,
                    variant,
                    properties, path, errors
                ).validate()
                FlowStepRequiredInputsValidator(
                    context, id,
                    variant,
                    properties, path, errors
                ).validate()
            }

            Variant.ITERATOR -> {
                FlowStepPrimaryIdentifierValidator(
                    id,
                    variant,
                    properties, path, errors
                ).validate()
                FlowStepRequiredInputsValidator(
                    context, id,
                    variant,
                    properties, path, errors
                ).validate()

                val stepsInIterator = properties.configuration?.steps
                stepsInIterator?.forEach { (_, stepInIterator) ->
                    FlowStepValidator(context).validate(
                        stepInIterator.id, stepInIterator.variant, stepInIterator.properties,
                        "$path.steps.${id.value}.properties.configuration",
                        errors
                    )
                }
            }

            Variant.FLOW -> {
                val flowConfigurationIdString = properties.inputs["flowConfigurationId"]
                if (flowConfigurationIdString == null || flowConfigurationIdString.getContent().isEmpty()) {
                    errors.add(
                        ConstraintError(
                            Field("flowConfigurationId"),
                            Type("required"),
                            services.oneteam.ai.shared.domains.workspace.validation.Path("$path.steps.${id.value}.properties.inputs.flowConfigurationId"),
                            ConstraintDetail("FlowConfigurationId must not be empty"),
                            Message("FlowConfigurationId must not be empty")
                        )
                    )
                } else {
                    val flowConfiguration =
                        context.flowConfigurationEntities[FlowConfiguration.Id(flowConfigurationIdString.getContent())]
                    if (flowConfiguration?.status == FlowConfigurationStatusType.INACTIVE) {
                        errors.add(
                            ConstraintError(
                                Field("flowConfigurationId"),
                                Type("inactiveFlow"),
                                services.oneteam.ai.shared.domains.workspace.validation.Path("$path.steps.${id.value}.properties.inputs.flowConfigurationId"),
                                ConstraintDetail("FlowConfigurationId must reference an active flow"),
                                Message("FlowConfigurationId must reference an active flow")
                            )
                        )
                    }

                    val startingVariables = flowConfiguration?.startingVariables
                    startingVariables?.forEach { startingVariable ->
                        if (startingVariable.properties?.required == true && properties.inputs[startingVariable.identifier]?.getContent().isNullOrEmpty()) {
                            errors.add(
                                ConstraintError(
                                    Field(startingVariable.identifier),
                                    Type("required"),
                                    services.oneteam.ai.shared.domains.workspace.validation.Path("$path.steps.${id.value}.properties.inputs.${startingVariable.identifier}"),
                                    ConstraintDetail("${startingVariable.identifier} must not be empty"),
                                    Message("${startingVariable.identifier} must not be empty")
                                )
                            )
                        }
                    }
                }
            }
        }
    }


}