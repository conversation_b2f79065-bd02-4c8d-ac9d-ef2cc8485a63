package services.oneteam.ai.shared.extensions

import org.jetbrains.exposed.dao.Entity
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.ResultRow
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields

fun <T : Entity<Long>> Query.paginate(
    pageRequest: PageRequest,
    sortableFields: SortableFields,
    wrapFunction: (ResultRow) -> T,
): Page<T> {

    val total = this.count()

    val order = pageRequest.sort.fields.map {
        sortableFields.get(it.field) to org.jetbrains.exposed.sql.SortOrder.valueOf(it.direction.uppercase())
    }.toTypedArray()

    val results =
        this.orderBy(*order).limit(pageRequest.pageSize)
            .offset((pageRequest.pageNumber.toLong() - 1) * pageRequest.pageSize)
            .toList()

    return Page(pageRequest, total, results.map { wrapFunction(it) }.toList())
}
