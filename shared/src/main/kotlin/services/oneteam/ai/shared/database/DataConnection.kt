package services.oneteam.ai.shared.database

import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import org.jetbrains.exposed.sql.Database
import services.oneteam.ai.shared.DBConnectionConfig

class DataConnection(private val dbcConfig: DBConnectionConfig) {

    private fun createDataSource(): HikariDataSource {
        val config = HikariConfig().apply {
            jdbcUrl = dbcConfig.jdbcUrl
            driverClassName = dbcConfig.driverClassName
            username = dbcConfig.username
            password = dbcConfig.password
            maximumPoolSize = dbcConfig.maximumPoolSize
            poolName = dbcConfig.poolName
        }
        config.validate()
        return HikariDataSource(config)
    }

    val dataSource: HikariDataSource by lazy { createDataSource() }

    val database by lazy { Database.connect(dataSource) }

    fun close() {
        dataSource.close()
    }
}