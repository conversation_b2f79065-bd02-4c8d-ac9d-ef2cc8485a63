package services.oneteam.ai.shared.domains

import kotlinx.serialization.json.*
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import java.math.BigDecimal

// we should convert this to an enum class if we can
// but what would we do with custom types?
object ValueTypes {
    const val TEXT = "text"
    const val SELECT = "select" // TODO this isn't a type, but a UI element
    const val DATE = "date"
    const val NUMBER = "number"
    const val BOOLEAN = "boolean"
    const val LIST = "list"
    const val TABLE = "table"
    const val JSON = "json"
    const val FILES = "files"
}

object TypeToJsonElementConverter {

    fun convert(
        type: String,
        value: Any?,
        defaultStrategy: DefaultStrategy = UseDefaultByType
    ): JsonElement {

        if (value == null || value is JsonNull) {
            return defaultStrategy.toDefault(type)
        }

        val stringValue = value.toString().trim('"')

        when (type) {
            ValueTypes.TEXT -> return JsonPrimitive(stringValue)
            ValueTypes.SELECT -> return JsonPrimitive(stringValue)
            ValueTypes.DATE -> return JsonPrimitive(stringValue)
            ValueTypes.NUMBER -> return JsonPrimitive(stringValue.toBigDecimal())
            ValueTypes.BOOLEAN -> return JsonPrimitive(stringValue.toBoolean())
            ValueTypes.LIST -> {
                return JsonArray((value as List<*>).map { toJsonElement(it!!) }.toList())
            }

            ValueTypes.TABLE, ValueTypes.JSON -> return toJsonElement(value)
            ValueTypes.FILES -> return toJsonElement(value)
        }

        throw IllegalArgumentException("Unsupported type `$type`")

    }

    fun toJsonElement(value: Any?): JsonElement {
        when (value) {
            null -> return JsonNull
            is JsonPrimitive, JsonObject, JsonArray -> return value as JsonElement
            is String -> return JsonPrimitive(value)
            is Int -> return JsonPrimitive(BigDecimal(value).convertToNiceBigDecimal())
            is Long -> return JsonPrimitive(BigDecimal(value).convertToNiceBigDecimal())
            is Double -> return JsonPrimitive(BigDecimal(value).convertToNiceBigDecimal())
            is BigDecimal -> return JsonPrimitive(value.convertToNiceBigDecimal())
            is Boolean -> return JsonPrimitive(value)
            is List<*> -> return JsonArray(value.map { toJsonElement(it) }.toList())
            is Map<*, *> -> return JsonObject(value.map { (k, v) -> k.toString() to toJsonElement(v) }.toMap())
            else -> throw IllegalArgumentException("Unsupported type ${value.javaClass}")
        }
    }

    fun defaultByType(type: String): JsonElement {
        return UseDefaultByType.toDefault(type)
    }
}

fun interface DefaultStrategy {
    fun toDefault(type: String): JsonElement
}

object DefaultToNull : DefaultStrategy {
    override fun toDefault(type: String): JsonElement {
        return JsonNull
    }
}

object UseDefaultByType : DefaultStrategy {
    override fun toDefault(type: String): JsonElement {
        return when (type) {
            ValueTypes.TEXT -> JsonPrimitive("")
            ValueTypes.DATE -> JsonNull
            ValueTypes.NUMBER -> JsonPrimitive(0)
            ValueTypes.BOOLEAN -> JsonPrimitive(false)
            ValueTypes.JSON -> JsonNull
            ValueTypes.TABLE -> JsonNull
            ValueTypes.LIST -> JsonNull
            else -> JsonNull
        }
    }
}