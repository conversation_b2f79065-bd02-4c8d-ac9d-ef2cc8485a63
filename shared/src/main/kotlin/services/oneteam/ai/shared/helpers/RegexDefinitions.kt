package services.oneteam.ai.shared.helpers

object RegexDefinitions {
    /*
     * Matches a function with arguments.
     * eg `$function(arg1, arg2)`
     */
    val function = Regex("\\$\\b\\w+\\b\\([^)]*\\)")

    val double = Regex("^-?\\d+(\\.\\d+)?$")

    val long = Regex("^-?\\d+$")

    val number = Regex("^-?\\d+(\\.\\d+)?$")

    val date = Regex("\\d{4}-\\d{2}-\\d{2}")

    val placeholder = Regex("\\{\\{.*?}}")
}