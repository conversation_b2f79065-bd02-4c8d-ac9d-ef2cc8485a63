package services.oneteam.ai.shared.domains.workspace.variable.secured

import services.oneteam.ai.shared.domains.passvault.KeyVaultService
import services.oneteam.ai.shared.domains.passvault.PassVault
import services.oneteam.ai.shared.domains.passvault.PassVaultLocksmith
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.withTenantTransactionScope

class WorkspaceSecuredValueService(
    val keyVaultService: KeyVaultService,
    val workspaceSecuredValueRepository: WorkspaceSecuredValueRepository
) {

    internal fun getTenantLocksmith(tenant: Tenant): PassVaultLocksmith {
        return PassVault(keyVaultService).getTenantLocksmith(tenant)
    }

    suspend fun create(
        valueToSecure: String,
        workspaceId: Workspace.Id
    ): WorkspaceSecuredValue.Id = withTenantTransactionScope { tenant ->
        val passVault = getTenant<PERSON>ocksmith(tenant)
        val encryptedValue = passVault.encrypt(valueToSecure)
        val result =
            workspaceSecuredValueRepository.create(WorkspaceSecuredValue.EncryptedValue(encryptedValue), workspaceId, tenant);
        return@withTenantTransactionScope result
    }

    suspend fun delete(workspaceSecuredValueId: WorkspaceSecuredValue.Id) = withTenantTransactionScope {
        workspaceSecuredValueRepository.delete(workspaceSecuredValueId)
    }

    suspend fun getManyRevealed(
        workspaceSecuredValueIds: List<WorkspaceSecuredValue.Id>
    ): List<WorkspaceSecuredValue.ForServiceFullyRevealed> = withTenantTransactionScope { tenant ->
        val securedValues = workspaceSecuredValueRepository.getMany(workspaceSecuredValueIds)
        val passVault = getTenantLocksmith(tenant)

        return@withTenantTransactionScope securedValues.map { securedValue ->
            val revealedValue = passVault.decrypt(securedValue.value.encryptedValue)
            WorkspaceSecuredValue.ForServiceFullyRevealed(
                id = securedValue.id,
                value = WorkspaceSecuredValue.RevealedValue(revealedValue)
            )
        }
    }
}