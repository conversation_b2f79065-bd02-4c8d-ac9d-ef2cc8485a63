package services.oneteam.ai.shared.data

import kotlinx.serialization.json.Json
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormAnnotation
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.otSerializer
import services.oneteam.ai.shared.withTenantTransactionScope
import kotlin.coroutines.coroutineContext

class SampleDataLoader(
    private val workspaceService: WorkspaceService,
    private val workspaceVersionService: WorkspaceVersionService,
    private val foundationRepository: FoundationRepository,
    private val foundationService: FoundationService,
    private val formService: FormService,
    private val cookie: String,
    private val workspaceForCreate: Workspace.ForCreate,
    private val prefix: String,
    private val keyWs: String,
    private val requestTimeoutMs: Long
) {
    private val logger: Logger = LoggerFactory.getLogger(javaClass)
    private val json = otSerializer

    suspend fun load(): Workspace.ForApi = withTenantTransactionScope {
        val userId = User.Id(coroutineContext[RequestContext]!!.principalId()!!)

        val name = workspaceForCreate.name
        val key = workspaceForCreate.key

        // Check if workspace with the same key already exists
        workspaceService.findByKey(key)?.let {
            logger.warn("Workspace $key exists, skipping creation")
            throw BadRequestException("Workspace $key already exists")
        }

        // Nothing exists already, so we can create the workspace
        logger.info("Creating workspace $key")

        val resourcePath = "$prefix-workspace-config.json"
        val resource = this::class.java.getResource(resourcePath)
        requireNotNull(resource) { "Resource not found: $resourcePath" }

        // workspace configuration
        val workspaceForJson = json.decodeFromString(
            Workspace.ForJson.serializer(),
            resource.readText()
        )
        val workspace = workspaceService.create(
            Workspace.ForCreate(
                name = name,
                key = key,
                description = workspaceForCreate.description ?: Workspace.Description("Sample Data"),
            ),
            workspaceForJson,
            userId,
            timeoutMillis = requestTimeoutMs
        )

        val version = workspaceVersionService.createVersion(workspace.id, cookie)

        val rootFoundation = foundationService.root(workspace.id)

        // foundations

        // load json file from classpath
        val foundations = Json.decodeFromString<List<FoundationSampleData>>(
            this::class.java.getResource("$prefix-foundations.json")!!.readText()
        )
        foundations.forEach { foundation ->
            if (foundation.parentKey == null) {
                createForms(foundation, version, workspace, rootFoundation)
            }

            if (foundation.parentKey != null) {

                val parentFoundation = foundationRepository.getByKey(
                    workspace.id.value,
                    if (foundation.parentKey.value.equals(
                            keyWs,
                            ignoreCase = true
                        )
                    ) key.value else foundation.parentKey.value
                )!!
                val foundationConfiguration =
                    workspaceForJson.foundations.findNext(FoundationConfiguration.Id(parentFoundation.foundationConfigurationId))

                requireNotNull(foundationConfiguration) { "Child foundation configuration not found for parent ${parentFoundation.foundationConfigurationId}" }

                val createdFoundation = foundationService.create(
                    Foundation.ForCreate(
                        foundation.name,
                        foundation.key,
                        foundationConfiguration.id,
                        workspace.id,
                        Foundation.Id(
                            parentFoundation.id.value
                        )

                    )
                )

                createForms(foundation, version, workspace, createdFoundation)
            }
        }
        return@withTenantTransactionScope workspace
    }

    private suspend fun createForms(
        foundationData: FoundationSampleData,
        version: WorkspaceVersion.ForApi,
        workspace: Workspace.ForApi,
        foundation: Foundation.ForApi
    ) {
        foundationData.forms?.forEach { form ->

            // need to add checks to make sure the formConfigurationId and intervalId are valid
            // and that everything matches up correctly

            val formConfiguration = version.configuration.forms[form.formConfigurationId]
            val seriesId = formConfiguration?.seriesId

            var answers: FormAnswer.ForJson = formService.createDefaultFormAnswerDocument()
            var annotations: FormAnnotation.ForJson = formService.createDefaultFormAnnotationDocument()
            if (form.answers != null) {
                val path = "$prefix-answers-${form.answers}.json"
                val resource = this::class.java.getResource(path)
                if (resource != null) {
                    answers = Json.decodeFromString<FormAnswer.ForJson>(resource.readText())
                } else {
                    logger.warn("Answers file not found: $path")
                    throw BadRequestException("Answers file not found: $path")
                }
            }
            if (form.annotations != null) {
                val path = "$prefix-annotations-${form.annotations}.json"
                val resource = this::class.java.getResource(path)
                if (resource != null) {
                    annotations = Json.decodeFromString<FormAnnotation.ForJson>(resource.readText())
                } else {
                    logger.warn("Annotations file not found: $path")
                    throw BadRequestException("Annotations file not found: $path")
                }
            }

            formService.create(
                workspace.id,
                Form.ForCreate(
                    FormConfiguration.Id(formConfiguration!!.id),
                    foundation.id,
                    if (seriesId != null) SeriesConfiguration.Id(seriesId) else null,
                    form.intervalId
                ),
                answers,
                annotations
            )
        }
    }

}