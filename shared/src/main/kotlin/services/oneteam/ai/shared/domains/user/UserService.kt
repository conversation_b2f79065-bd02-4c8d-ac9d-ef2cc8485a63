package services.oneteam.ai.shared.domains.user

import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.withTenantTransactionScope

class UserService(private val userRepository: UserRepository) {

    suspend fun create(user: User.ForCreate): User.ForApi = withTenantTransactionScope { tenant ->
        return@withTenantTransactionScope userRepository.create(tenant, user).toDTO()
    }

    suspend fun getAll(): List<User.ForApi> = withTenantTransactionScope {
        return@withTenantTransactionScope userRepository.getAll().map { it.toDTO() }
    }

    suspend fun search(
        pageRequest: PageRequest, userSearchCriteria: UserSearchCriteria
    ): Page<User.ForApi> = withTenantTransactionScope {
        val searchResult = userRepository.searchByCriteria(pageRequest, userSearchCriteria)
        return@withTenantTransactionScope Page(
            searchResult.page,
            searchResult.total,
            searchResult.items.map { it.toDTO() })
    }

    suspend fun findById(userId: User.Id): User.ForApi = withTenantTransactionScope {
        val userEntity = userRepository.findOne(userId.value)
        return@withTenantTransactionScope userEntity.toDTO()
    }

}