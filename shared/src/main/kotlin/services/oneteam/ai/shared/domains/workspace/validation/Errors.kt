package services.oneteam.ai.shared.domains.workspace.validation

import kotlinx.serialization.Serializable

@Serializable
class Errors {
    private val errors = mutableListOf<ConstraintError>()

    fun add(error: ConstraintError): Errors {
        errors.add(error)
        return this
    }

    fun addAll(errors: Errors): Errors {
        this.errors.addAll(errors.getErrors())
        return this
    }

    fun hasErrors(): Boolean {
        return errors.isNotEmpty()
    }

    fun getErrors(): List<ConstraintError> {
        return errors
    }

}

@Serializable
@JvmInline
value class Field(val value: String)

@Serializable
@JvmInline
value class Type(val value: String)

@Serializable
@JvmInline
value class Path(val value: String)

@Serializable
@JvmInline
value class ConstraintDetail(val value: String?)

@Serializable
@JvmInline
value class Message(val value: String)

@Serializable
data class ConstraintError(
    val key: Field,
    val type: Type,
    val path: Path,
    val constraintDetail: ConstraintDetail?,
    val message: Message,
    val bounds: Bounds? = null
) {
    companion object {
        fun buildMinGreaterThanMaxError(path: String): ConstraintError {
            return ConstraintError(
                Field("min"),
                Type("minMax"),
                Path("$path.min"),
                ConstraintDetail("min should be less than or equal to max"),
                Message("min should be less than or equal to max")
            )
        }

        fun buildDefaultLessThanMinError(path: String): ConstraintError {
            return ConstraintError(
                Field("defaultValue"),
                Type("lessThanMin"),
                Path("$path.defaultValue"),
                ConstraintDetail("default value should be greater than or equal to min"),
                Message("default value should be greater than or equal to min")
            )
        }

        fun buildDefaultGreaterThanMaxError(path: String): ConstraintError {
            return ConstraintError(
                Field("defaultValue"),
                Type("greaterThanMax"),
                Path("$path.defaultValue"),
                ConstraintDetail("default value should be less than or equal to max"),
                Message("default value should be less than or equal to max")
            )
        }

        fun buildDuplicateIdentifierError(path: String): ConstraintError {
            return ConstraintError(
                Field("identifier"),
                Type("duplicate"),
                Path("$path.identifier"),
                ConstraintDetail("errors.common.identifier.duplicate"),
                Message("errors.common.identifier.duplicate")
            )
        }
    }
}