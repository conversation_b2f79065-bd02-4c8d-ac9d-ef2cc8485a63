package services.oneteam.ai.shared.domains

import kotlinx.serialization.Serializable
import services.oneteam.ai.shared.database.BaseLongEntity
import java.time.Instant

@Serializable
data class EntityMetadata(
    @Serializable(with = InstantSerializer::class) val createdAt: Instant,
    @Serializable(with = InstantSerializer::class) val updatedAt: Instant
) {
    companion object {
        fun <T : BaseLongEntity> from(entity: T): EntityMetadata {
            return EntityMetadata(
                createdAt = entity.createdAt, updatedAt = entity.updatedAt
            )
        }

        fun now(): EntityMetadata {
            val now = Instant.now()
            return EntityMetadata(
                createdAt = now, updatedAt = now
            )
        }
    }
}