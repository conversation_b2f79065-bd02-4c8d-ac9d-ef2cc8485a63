package services.oneteam.ai.shared

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.IntegerColumnType
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import services.oneteam.ai.shared.database.PRINCIPAL_KEY
import services.oneteam.ai.shared.database.RLS_TENANT_KEY
import services.oneteam.ai.shared.database.SYSTEM_USER_KEY
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.extensions.setValueOrNull
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.coroutineContext

val logger: Logger = LoggerFactory.getLogger("services.oneteam.ai.shared.TenantScope")
val dispatcher = Dispatchers.IO // .limitedParallelism(100)

/**
 * Executes a block of code within the context of a tenant.
 * Use this INSTEAD of `coroutineContext[RequestContext]?.tenant`
 */
suspend fun <T> withTenantScope(block: suspend (tenant: Tenant) -> T): T {
    val tenant = coroutineContext[RequestContext]?.tenant
    require(tenant != null) { "Tenant is not available in the current coroutine context" }
    return block(tenant)
}

/**
 * Executes a block of code within the context of a tenant AND a transaction.
 * If there is no current transaction, it uses [DatabaseUtils.dbQueryWithTenant] to execute the block.
 * Otherwise, it retrieves the tenant from the [RequestContext] and executes the block directly.
 *
 * It is intended that this be used in the service layer to ensure the business methods are wrapped
 * in a transaction. If one service method uses another service method, the transaction will be
 * shared, and the tenant context will be preserved.
 *
 * After the `withTenantTransactionScope` block completes, the transaction will be committed or rolled back and the
 * current transaction should be null, after which subsequent calls to `withTenantTransactionScope` will create a new transaction.
 *
 * In addition to this:
 *
 * - repositories should not create transactions themselves or use DatabaseUtils directly. They should expect a transaction to be provided by the service layer.
 * - repository methods no longer need to be suspend functions, as they will always be called within a coroutine context that provides the tenant.
 *
 * @param block The suspend function to execute with the tenant context.
 * @return The result of the block execution.
 */
suspend fun <T> withTenantTransactionScope(block: suspend (tenant: Tenant) -> T): T {
    logger.trace("withTenantTransactionScope: Current coroutine context: {}", currentCoroutineContext())
    val tenant = coroutineContext[RequestContext]!!.tenant
    val principal = coroutineContext[RequestContext]?.principal
    val principalId = toPrincipalId(principal)
    val systemUser = isSystemUser(principal)

    if (hasCurrentTransaction()) {
        // if we have a current transaction, we can just execute the block within that transaction
        logger.trace(
            "withTenantTransactionScope: Current transaction exists {}, using it for tenant {}",
            TransactionManager.currentOrNull(),
            tenant.id
        )
        return block(tenant)
    } else {
        // if we don't have a current transaction, we need to create a new one
        try {
            logger.trace(
                "withTenantTransactionScope: No current transaction, starting a new one for tenant {}, scope: {}",
                tenant.id, currentCoroutineContext()
            )

            val scope: CoroutineContext = coroutineContext[RequestContext]!! + MDCContext() + dispatcher

            // I'm not sure why we can't just inherit from the current coroutine context here, but it seems that
            // not using this scope here will mean current transaction not seen by next query (new transactions will be created when they shouldn't)
            return newSuspendedTransaction(scope) {
                try {
                    // for now, so we can debug and spot errors, we log the transaction and connection in the MDC
                    // we can remove this later when we have confidence in the implementation
                    MDC.put("transaction", TransactionManager.current().toString())
                    MDC.put("connection", TransactionManager.current().connection.toString())
                    prepareConnection(tenant, principalId, systemUser)
                    return@newSuspendedTransaction withContext(MDCContext()) {
                        return@withContext block(tenant)
                    }
                } finally {
                    MDC.remove("connection")
                    MDC.remove("transaction")
                }
            }

        } finally {
            require(!hasCurrentTransaction()) { "Transaction should NOT exist at the end of the block" }
        }
    }

}

/**
 * When running KTOR in debug mode, `TransactionManager.currentOrNull()` doesn't return null after the transaction block is closed.
 * So we need to check if the connection is closed.
 * https://slack-chats.kotlinlang.org/t/29155777/hi-all-i-m-looking-for-some-insight-into-this-issue-i-m-havi#2f581f8c-bc85-4a0d-91d3-839c77c22a9c
 */
fun hasCurrentTransaction(): Boolean {
    return TransactionManager.currentOrNull() != null && !TransactionManager.current().connection.isClosed
}

fun toPrincipalId(principal: Any?): Long? {
    // Principal may be null for operations performed by the system - or, do we want to enforce a principal and supply a system user?
    if (principal != null && principal is UserSession) {
        return principal.user.id
    }
    return null
}

fun isSystemUser(principal: Any?): Boolean {
    if (principal != null && setOf("JWTPrincipal", "ApiKeyPrincipal").contains(principal::class.simpleName)) {
        logger.trace("Using system user")
        return true
    }
    return false
}

/**
 * Sets the tenant and principal for the current transaction.
 * coroutineContext[RequestContext]!!.tenant must be defined
 *
 * See https://www.postgresql.org/docs/16/functions-admin.html#FUNCTIONS-ADMIN-SET
 * Can be retrieved in queries using
 *    SELECT CURRENT_SETTING('app.current_principal_id', TRUE)
 *
 * See docs/rls.sql for manual queries to experiment with.
 */
suspend fun prepareConnection(
    tenant: Tenant,
    principalId: Long?,
    isSystemUser: Boolean,
) {
    logger.trace("prepareConnection: {}", coroutineContext[RequestContext])
    // Principal may be null for operations performed by the system - or, do we want to enforce a principal and supply a system user?

    val conn = TransactionManager.current().connection
    val query =
        "SELECT set_config('$RLS_TENANT_KEY', ?::text, TRUE), set_config('$PRINCIPAL_KEY', ?::text, TRUE), set_config('$SYSTEM_USER_KEY', ?::text, TRUE)"

    val statement = conn.prepareStatement(query, false)
    statement[1] = tenant.id
    statement.setValueOrNull(2, principalId, IntegerColumnType())
    statement[3] = isSystemUser
    statement.executeQuery()

    logger.info("set RLS parameters for tenant: ${tenant.id}, principalId: $principalId, isSystemUser: $isSystemUser")
}
