package services.oneteam.ai.shared.domains.workspace.validation

private val ERRORS_INVALID_NAME = mapOf(
    "required" to "errors.common.name.required", "length" to "errors.common.name.length"
)

class NameValidator {
    companion object {
        fun <T> build(producer: (T) -> String?): Constraint<T> {
            return Validator<T>(
                listOf(
                    StringRequiredConstraint(
                        "name",
                        producer,
                        ERRORS_INVALID_NAME.getValue("required")
                    ),
                    StringLengthConstraint(
                        "name",
                        producer,
                        ERRORS_INVALID_NAME.getValue("length"),
                        StringBounds(2, 100)
                    )
                )
            )
        }
    }
}