package services.oneteam.ai.shared.domains.workspace.validation

import org.slf4j.Logger
import org.slf4j.LoggerFactory

class Validator<T>(private val constraints: List<Constraint<T>?>) : Constraint<T> {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override fun validate(data: T): Errors {
        val errors = Errors()
        constraints.forEach { constraint ->
            if (constraint != null) {
                try {
                    errors.addAll(constraint.validate(data))
                } catch (exception: Exception) {
                    /*
                     * This means we've had an unexpected error in the validation process that we should fix.
                     * For example, we are checking for duplicates in a property that doesn't exist so a PathNotFoundException is thrown.
                     * We could in this example configure jayway jsonpath to either suppress exceptions or not require fields when checking for duplicates.
                     * Then we could do other validation to check for required fields.
                     */
                    logger.error(
                        "Validation by ${constraint.javaClass.simpleName} could not be completed due to a system error. Error validating constraints: ${exception.message}",
                        exception
                    )
                    errors.add(
                        ConstraintError(
                            Field(""),
                            Type("system"),
                            Path(""),
                            ConstraintDetail("Validation by ${constraint.javaClass.simpleName} could not be completed due to a system error. Error validating constraints: ${exception.message}"),
                            Message("validation.system.error")
                        )
                    )
                }
            }
        }

        return errors
    }

    override fun errorKeys(): List<String> {
        // return list of all error keys from constraints
        return constraints.filterNotNull().flatMap { it.errorKeys() }
    }

    companion object {
        fun <T> conditionalConstraint(
            test: Boolean,
            constraint: Constraint<T>
        ): Constraint<T>? {
            return if (test) constraint else null
        }
    }
}