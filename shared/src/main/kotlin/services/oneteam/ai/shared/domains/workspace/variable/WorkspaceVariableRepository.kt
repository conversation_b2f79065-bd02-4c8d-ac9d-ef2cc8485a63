package services.oneteam.ai.shared.domains.workspace.variable

import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.variable.secured.WorkspaceSecuredValue

class WorkspaceVariableRepository {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun create(
        ref: String,
        value: String?,
        secured: Boolean,
        workspaceSecuredValueId: WorkspaceSecuredValue.Id?,
        workspaceId: Workspace.Id,
        tenant: Tenant
    ): WorkspaceVariable.ForApi {
        require(value == null || !secured) {
            "Cannot create a workspace variable with a value when it is secured"
        }
        val entity = WorkspaceVariableEntity.new {
            this.ref = ref
            this.secured = secured
            this.value = value
            this.securedValueId = workspaceSecuredValueId?.value
            this.workspaceId = workspaceId.value
            this.tenantId = tenant.id
        }
        return entity.toDTO();
    }

    fun delete(
        workspaceId: Workspace.Id,
        workspaceVariableId: WorkspaceVariable.Id
    ) {
        val existing = WorkspaceVariableEntity.findById(workspaceVariableId.value)
        if (existing != null) {
            require(existing.workspaceId == workspaceId.value) {
                "Workspace variable with id ${workspaceVariableId.value} does not exist"
            }
            existing.delete()
        }
    }

    fun createVersionWithIdentifiers(
        variableRefs: Set<WorkspaceVariable.Ref>,
        workspaceVersionId: WorkspaceVersion.Id,
        workspaceId: Workspace.Id,
        tenant: Tenant
    ): List<WorkspaceVariable.ForApi> {
        // Creating a version entails doing a cleanup on any unreferenced+unpublished variables
        val query = WorkspaceVariableSchema.selectAll()
        query.andWhere { WorkspaceVariableSchema.workspaceId eq workspaceId.value }
        query.andWhere { WorkspaceVariableSchema.workspaceVersionId eq null }
        val unpublishedVariables = WorkspaceVariableEntity.wrapRows(query).toList().map { it.toDTO() }
        logger.debug("Found {} unpublished variables for workspace {}", unpublishedVariables.size, workspaceId)

        val (requiredVariables, danglingVariables) = unpublishedVariables.partition { variable ->
            variableRefs.contains(variable.ref)
        }
        require(requiredVariables.size == variableRefs.size) {
            "Expected ${variableRefs.size} unpublished variables, but found ${requiredVariables.size}"
        }

        val inserted = WorkspaceVariableSchema.batchInsert(requiredVariables) {
            this[WorkspaceVariableSchema.ref] = it.ref.ref
            this[WorkspaceVariableSchema.value] = it.value
            this[WorkspaceVariableSchema.isSecured] = it.isSecured
            this[WorkspaceVariableSchema.securedValueId] = it.workspaceSecuredValueId?.value
            this[WorkspaceVariableSchema.workspaceVersionId] = workspaceVersionId.value
            this[WorkspaceVariableSchema.workspaceId] = workspaceId.value
            this[WorkspaceVariableSchema.tenantId] = tenant.id
        }
        val results = inserted.toList().map { WorkspaceVariableEntity.wrapRow(it).toDTO() }
        logger.debug(
            "Published {} variables for workspace {} and version {}",
            results.size,
            workspaceId,
            workspaceVersionId
        )

        // delete the remaining unpublished dangling variables
        val danglingVariableIds = danglingVariables.map { it.id.value }
        logger.debug("Deleting dangling variables for workspace {}: {}", workspaceId, danglingVariableIds)
        val numDeleted = WorkspaceVariableSchema.deleteWhere() {
            (WorkspaceVariableSchema.workspaceId eq workspaceId.value) and
                    (WorkspaceVariableSchema.workspaceVersionId eq null) and
                    (id inList danglingVariableIds)
        }
        logger.debug("Deleted {} dangling unpublished variables for workspace {}", numDeleted, workspaceId)

        return results
    }

    fun getAll(
        workspaceId: Workspace.Id,
        workspaceVersionId: WorkspaceVersion.Id? = null
    ): List<WorkspaceVariable.ForApi> {
        val query = WorkspaceVariableSchema.selectAll()
        query.andWhere {
            WorkspaceVariableSchema.workspaceId eq workspaceId.value
        }
        query.andWhere {
            WorkspaceVariableSchema.workspaceVersionId eq workspaceVersionId?.value
        }

        return WorkspaceVariableEntity.wrapRows(query).toList().map { it.toDTO() }
    }

    fun getMany(
        workspaceId: Workspace.Id,
        workspaceVariableIds: List<WorkspaceVariable.Id>,
    ): List<WorkspaceVariable.ForApi> {
        return WorkspaceVariableEntity.find {
            (WorkspaceVariableSchema.workspaceId eq workspaceId.value) and
                    (WorkspaceVariableSchema.id inList workspaceVariableIds.map { it.value })
        }.toList().map { it.toDTO() }
    }
}
