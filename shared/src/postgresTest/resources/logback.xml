<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%X{tenant}] [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <root level="trace">
        <appender-ref ref="STDOUT"/>
    </root>
    <logger name="org.eclipse.jetty" level="INFO"/>
    <logger name="com.zaxxer.hikari" level="INFO"/>
    <logger name="io.netty" level="INFO"/>
    <logger name="Exposed" level="DEBUG"/>
    <logger name="com.github.dockerjava" level="WARN"/>
    <logger name="org.testcontainers" level="WARN"/>
</configuration>