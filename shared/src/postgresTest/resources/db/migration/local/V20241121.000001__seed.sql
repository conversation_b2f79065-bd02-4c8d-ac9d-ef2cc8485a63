-- keep in sync with src/h2Test/resources/db/migration/local/V20241121.000001__seed.sql

INSERT INTO tenants (name, origin_url, created_at, updated_at)
VALUES ('TEST1', 'http://tenant1', NOW(), NOW());

INSERT INTO tenants (name, origin_url, created_at, updated_at)
VALUES ('TEST2', 'http://tenant2', NOW(), NOW());

insert into users (email, tenant_id, created_at, updated_at)
values ('test1@x.y', (select id from tenants where origin_url = 'http://tenant1'), NOW(), NOW());

insert into users (email, tenant_id, created_at, updated_at)
values ('test2@x.y', (select id from tenants where origin_url = 'http://tenant2'), NOW(), NOW());