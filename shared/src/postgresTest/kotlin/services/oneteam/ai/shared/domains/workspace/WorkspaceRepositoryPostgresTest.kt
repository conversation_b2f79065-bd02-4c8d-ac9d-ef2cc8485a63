package services.oneteam.ai.shared.domains.workspace

import io.kotest.common.runBlocking
import io.kotest.matchers.comparables.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import kotlinx.coroutines.withContext
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import kotlin.test.assertNotNull

class WorkspaceRepositoryPostgresTest {
    val database = TestPostgresDatabase
    val checks = Checks()
    val userRepository = UserRepository(checks)
    val workspaceRepository = WorkspaceRepository(checks, userRepository)

    @Test
    fun `should create workspace`() {
        runBlocking {
            val fixtures = Fixtures(database).initialise()
            withContext(RequestContext(tenant = fixtures.tenant2)) {
                // prepare
                // perform
                val workspace = workspaceRepository.create(
                    Workspace.ForCreate(
                        Workspace.Name("testWorkspace"), Workspace.Key("T1"),
                        Workspace.Description("test description")
                    )
                )
                // verify
                assertNotNull(workspace)
                workspace.id.value shouldBeGreaterThan (0L)
            }
        }
    }

    @Test
    fun `should get all workspaces for current tenant`() {
        runBlocking {
            // prepare
            val fixtures = Fixtures(database).initialise()

            createWorkspace(fixtures.tenant1)
            createWorkspace(fixtures.tenant2)

            // tenant 1 should only see tenant 1 workspaces
            withContext(RequestContext(tenant = fixtures.tenant1)) {
                // perform
                val workspaces = workspaceRepository.getAll()
                // verify
                assertNotNull(workspaces)
                workspaces.forEach { w ->
                    w.tenantId shouldBe fixtures.tenant1.id
                }
            }

            // tenant 2 should only see tenant 2 workspaces
            withContext(RequestContext(tenant = fixtures.tenant2)) {
                // perform
                val workspaces = workspaceRepository.getAll()
                // verify
                assertNotNull(workspaces)
                workspaces.forEach { w ->
                    w.tenantId shouldBe fixtures.tenant2.id
                }
            }

        }
    }

    suspend fun createWorkspace(tenant: Tenant): WorkspaceEntity {
        return withContext(RequestContext(tenant = tenant)) {
            // prepare
            val count = database.counter.incrementAndGet()
            // perform
            return@withContext workspaceRepository.create(
                Workspace.ForCreate(
                    Workspace.Name("N${tenant.id}-$count"),
                    Workspace.Key("K${tenant.id}-$count"),
                    Workspace.Description("D${tenant.id}-$count")
                )
            )

        }
    }
}