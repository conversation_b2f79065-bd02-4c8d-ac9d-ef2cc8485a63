package services.oneteam.ai.shared.domains.collection.form

import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.Sort
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.workspace.FormConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase

class FormRepositoryPostgresTest {
    val database = TestPostgresDatabase

    @Test
    fun testSearchForms() {
        runBlocking {
            // prepare
            val fixtures = Fixtures(database).initialise()

            withContext(RequestContext(tenant = fixtures.tenant1)) {
                val workspace = fixtures.workspaceRepository.getByKey(fixtures.Workspace1_Key)!!

                newSuspendedTransaction {
                    // insert 20 times
                    repeat(20) { index ->
                        fixtures.formRepository.create(
                            workspace, Form.ForCreate(
                                FormConfiguration.Id("config1"), Foundation.Id(1L), null
                            )
                        )

                    }
                }

                val pageRequest = PageRequest(0, 10, "", Sort(emptyList()))
                val result = fixtures.formRepository.search(
                    pageRequest, FormSearchCriteria(
                        workspaceId = Workspace.Id(workspace.id.value),
                        foundationId = Foundation.Id(1),
                        foundationParentId = null,
                        foundationConfigurationId = null,
                        formConfigurationId = FormConfiguration.Id("config1"),
                        formConfigurationIdList = null,
                        intervalIdList = null,
                        "Test"
                    )
                )

                result.shouldNotBeNull()
                result.total shouldBe (20L)
                result.page.pageNumber shouldBe 0
                result.items.size shouldBe result.page.pageSize

            }
        }

    }
}