package services.oneteam.ai.shared

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.testing.TestPostgresDatabase

class TransactionTest {
    private val database = TestPostgresDatabase

    @Test
    fun `test transaction boundaries`() = runTest {
        Fixtures(database).initialise()

        require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
        withContext(Dispatchers.IO) {
            newSuspendedTransaction {
                require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
                delay(2000) // Simulate some processing time - if I remove this delay things will work as I expect
            }
        }
        // this next line fails
        require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }

    }
}