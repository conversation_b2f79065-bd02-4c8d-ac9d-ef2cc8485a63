package services.oneteam.ai.shared.domains.workspace

import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.MockHttpClient
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariableService
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import services.oneteam.ai.shared.withTenantTransactionScope
import kotlin.test.assertNotNull

class WorkspaceUserAccessTest {

    private val database = TestPostgresDatabase
    private val checks = Checks()
    private val workspaceRepository = WorkspaceRepository(checks, UserRepository(checks))
    private val mockHttpClient = MockHttpClient()
    private val proxyService = ExternalProxyService("token", mockHttpClient.client)
    private val documentService = ApiDocumentService(
        proxyService,
        mock(FlowStepTypeConfigurationService::class.java)
    )
    private val workspaceVariableService = mockk<WorkspaceVariableService>()
    private val workspaceVersionService =
        WorkspaceVersionService(workspaceRepository, documentService, WorkspaceVersionRepository(), workspaceVariableService, Checks())

    private val workspaceText =
        this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
    private val workspaceForJson = Json.decodeFromString(Workspace.ForJson.serializer(), workspaceText)

    @Test
    fun `user should only see workspaces they have access to`() = runTest {
        val fixtures = Fixtures(database).initialise()

        // create workspace 1 for user 1
        val key1 = Workspace.Key("T1")
        val key2 = Workspace.Key("T2")



        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User1Session)) {
            withTenantTransactionScope { tenant ->

                // prepare
                val workspace = workspaceRepository.create(
                    tenant,
                    Workspace.ForCreate(
                        Workspace.Name(
                            "testWo" +
                                    "rkspaceAccess1"
                        ), key1,
                        Workspace.Description("test description"),
                        Workspace.DocumentId("workspaceDocumentId1")
                    ),
                    fixtures.tenant1User1!!
                )
                assertNotNull(workspace)
            }
        }


        // create workspace 2 for user 2

        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User2Session)) {
            withTenantTransactionScope { tenant ->
                // prepare
                val workspace = workspaceRepository.create(
                    tenant,
                    Workspace.ForCreate(
                        Workspace.Name("testWorkspaceAccess2"), key2,
                        Workspace.Description("test description"),
                        Workspace.DocumentId("workspaceDocumentId2")
                    ),
                    fixtures.tenant1User2!!
                )
                assertNotNull(workspace)
            }

        }

        // verify user 1 can only see workspace 1
        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User1Session)) {
            withTenantTransactionScope {
                assertThat(workspaceRepository.getByKey(key1)).isNotNull()
                assertThat(workspaceRepository.getByKey(key2)).isNull()
            }
        }
        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User2Session)) {
            withTenantTransactionScope {
                assertThat(workspaceRepository.getByKey(key1)).isNull()
                assertThat(workspaceRepository.getByKey(key2)).isNotNull()
            }
        }
    }
}