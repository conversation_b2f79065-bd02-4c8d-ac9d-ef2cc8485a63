package services.oneteam.ai.shared.domains.tenant

import io.kotest.matchers.shouldBe
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import kotlin.test.assertNotNull

class TenantServicePostgresTest {
    val database = TestPostgresDatabase

    @Test
    fun `should find by origin`() = runTest {
        Fixtures(database).initialise()

        TenantService(TenantRepository()).apply {
            // prepare
            // perform
            val tenant = getByInternalUrlOrOriginUrl("http://tenant1")
            // verify
            assertNotNull(tenant)
            tenant.name shouldBe "tenant1"
            println("Tenant: $tenant")
        }
    }
}