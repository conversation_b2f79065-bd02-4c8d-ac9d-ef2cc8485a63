package services.oneteam.ai.shared.domains.workspace


import io.kotest.matchers.comparables.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import services.oneteam.ai.shared.*
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.domains.workspace.validation.*
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariableService
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import kotlin.test.assertNotNull

class WorkspaceVersionServicePostgresTest {
    private val database = TestPostgresDatabase
    private val checks = Checks()
    private val workspaceRepository = WorkspaceRepository(checks, UserRepository(checks))
    private val mockHttpClient = MockHttpClient()
    private val proxyService = ExternalProxyService("token", mockHttpClient.client)
    private val documentService = ApiDocumentService(
        proxyService, mock(FlowStepTypeConfigurationService::class.java)

    )
    private val workspaceVariableService = mockk<WorkspaceVariableService>()
    private val workspaceVersionService = WorkspaceVersionService(
        workspaceRepository, documentService, WorkspaceVersionRepository(), workspaceVariableService, Checks()
    )

    private val workspaceText =
        this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
    private val workspaceForJson = otSerializer.decodeFromString(
        Workspace.ForJson.serializer(), workspaceText
    )
    private val workspaceForJsonWithErrors = workspaceForJson.copy(
        errors = listOf(
            ConstraintError(
                key = Field("key"),
                type = Type("type"),
                path = Path("path"),
                constraintDetail = ConstraintDetail("detail"),
                message = Message("error")
            )
        )
    )

    private fun getJson(): Json {
        return Json(builderAction = {
            serializersModule = variableModule
            classDiscriminator = otTypeDescrim
            prettyPrint = true
            encodeDefaults = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }

    @Test
    fun `should create a workspace version`() = runTest {
        val fixtures = Fixtures(database).initialise()

        withContext(RequestContext(tenant = fixtures.tenant2, principal = fixtures.tenant2User1Session)) {
            val workspace = withTenantTransactionScope { tenant ->
                // prepare
                return@withTenantTransactionScope workspaceRepository.create(
                    tenant, Workspace.ForCreate(
                        Workspace.Name("testWorkspace"),
                        Workspace.Key("T1"),
                        Workspace.Description("test description"),
                        Workspace.DocumentId("abc")
                    ), fixtures.tenant2User1!!
                ).let {
                    mockHttpClient.documents[it.documentId!!.value] = workspaceText
                    assertNotNull(it)
                }
            }
            assertNotNull(workspace)
            require(TransactionManager.currentOrNull() == null) {
                "Transaction should be null after dbQueryWithTenant, but is ${TransactionManager.currentOrNull()}"
            }

            coEvery { workspaceVariableService.createVersion(any(), any()) } returns emptyList()

            withTenantTransactionScope { tenant ->
                // perform
                workspaceVersionService.createVersion(Workspace.Id(workspace.id.value), "cookie").let { version ->
                    // verify
                    version.workspaceId shouldBe Workspace.Id(workspace.id.value)
                    version.id.value shouldBeGreaterThan (0L)
                    version.configuration shouldBe workspaceForJson
                }
            }

            coVerify {
                workspaceVariableService.createVersion(any(), any())
            }
        }
    }

    @Test
    fun `should not create a workspace version when errors exist`() = runTest {
        val fixtures = Fixtures(database).initialise()
        withContext(RequestContext(tenant = fixtures.tenant2, principal = fixtures.tenant2User1Session)) {
            // create a new workspace with errors and commit it
            val workspace = withTenantTransactionScope {
                // prepare
                val workspace = workspaceRepository.create(
                    fixtures.tenant2, Workspace.ForCreate(
                        Workspace.Name("testWorkspace"),
                        Workspace.Key("T1"),
                        Workspace.Description("test description"),
                        Workspace.DocumentId("abc")
                    ), fixtures.tenant2User1!!
                )
                val encodeToString =
                    getJson().encodeToString(Workspace.ForJson.serializer(), workspaceForJsonWithErrors)
                mockHttpClient.documents[workspace.documentId!!.value] = encodeToString
                assertNotNull(workspace)
                return@withTenantTransactionScope workspace
            }

            // perform
            // try to create a version with the workspace that has errors - this should throw a BadRequestException
            assertThrows(BadRequestException::class.java) {
                kotlinx.coroutines.runBlocking {
                    withContext(RequestContext(tenant = fixtures.tenant2, principal = fixtures.tenant2User1Session)) {
                        workspaceVersionService.createVersion(Workspace.Id(workspace.id.value), "cookie")
                    }
                }
            }

        }
    }
}