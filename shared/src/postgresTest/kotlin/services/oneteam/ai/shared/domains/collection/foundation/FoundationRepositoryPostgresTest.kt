package services.oneteam.ai.shared.domains.collection.foundation

import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.Sort
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormSearchCriteria
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.workspace.FormConfiguration
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase

class FoundationRepositoryPostgresTest {
    val database = TestPostgresDatabase

    @Test
    fun testGetFoundationHierarchy() {
        runBlocking {
            // prepare
            val fixtures = Fixtures(database).initialise()

            withContext(RequestContext(tenant = fixtures.tenant1)) {
                val workspace = fixtures.workspaceRepository.getByKey(fixtures.Workspace1_Key)!!
                val grandParent = fixtures.foundationRepository.getRoot(workspace.id.value)
                val hierarchies = mutableListOf<List<Long>>()
                newSuspendedTransaction {
                    repeat(3) { parentIndex ->
                        val parent = fixtures.foundationRepository.create(
                            Foundation.ForCreate(
                                workspaceId = Workspace.Id(workspace.id.value),
                                name = Foundation.Name("foundation-$parentIndex"),
                                key = Foundation.Key("foundation-$parentIndex"),
                                foundationConfigurationId = FoundationConfiguration.Id("config-$parentIndex"),
                                parentId = Foundation.Id(grandParent.id.value),
                            )
                        )
                        repeat(2) { index ->
                            val child = fixtures.foundationRepository.create(
                                Foundation.ForCreate(
                                    workspaceId = Workspace.Id(workspace.id.value),
                                    name = Foundation.Name("foundation-$parentIndex-$index"),
                                    key = Foundation.Key("foundation-$parentIndex-$index"),
                                    foundationConfigurationId = FoundationConfiguration.Id("config-$parentIndex-$index"),
                                    parentId = Foundation.Id(parent.id.value),
                                )
                            )
                            hierarchies.add(listOf(grandParent.id.value, parent.id.value, child.id.value))
                        }
                    }
                }

                hierarchies.size.shouldBe(6)
                hierarchies.forEachIndexed { index, hierarchy ->
                    hierarchies[index].size.shouldBe(3)
                    val result = fixtures.foundationRepository.getHierarchyForFoundation(
                        hierarchies[index][2]
                    )

                    result.shouldNotBeNull()
                    result[0].id.value.shouldBe(grandParent.id.value)
                    result[1].id.value.shouldBe(hierarchies[index][1])
                    result[2].id.value.shouldBe(hierarchies[index][2])
                }
            }
        }
    }
}