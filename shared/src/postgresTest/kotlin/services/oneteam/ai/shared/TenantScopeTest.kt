package services.oneteam.ai.shared

import kotlinx.coroutines.*
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase

class TenantScopeTest {
    private val database = TestPostgresDatabase

    fun main() {
        val fixtures = Fixtures(database).initialise()
        val tenant = fixtures.tenant1
        val principal = fixtures.tenant1User1Session

        runBlocking {
            launch(Dispatchers.IO + RequestContext(tenant = tenant, principal = principal)) {
//                withContext(RequestContext(tenant = tenant, principal = principal)) {
                assertThat(TransactionManager.currentOrNull()).isNull() // Ensure transaction does not exist at the start
                withTenantTransactionScope { tenant ->
                    assertThat(TransactionManager.currentOrNull()).isNotNull() // Ensure transaction exists now
                    println("delaying")
                    delay(2000) // Simulate some processing time
                    println("delayed")
                }
                assertThat(TransactionManager.currentOrNull()).isNull() // Ensure transaction does not exist at the end
//                }
            }.join()
        }
    }

    @Test
    fun `test withTenantScope boundaries`() {
        val fixtures = Fixtures(database).initialise()
        val tenant = fixtures.tenant1
        val principal = fixtures.tenant1User1Session

        runBlocking {
            launch(Dispatchers.IO + RequestContext(tenant = tenant, principal = principal)) {
//                withContext(RequestContext(tenant = tenant, principal = principal)) {
                assertThat(TransactionManager.currentOrNull()).isNull() // Ensure transaction does not exist at the start
                withTenantTransactionScope { tenant ->
                    assertThat(TransactionManager.currentOrNull()).isNotNull() // Ensure transaction exists now
                    println("delaying")
                    delay(2000) // Simulate some processing time
                    println("delayed")
                }
                assertThat(TransactionManager.currentOrNull()).isNull() // Ensure transaction does not exist at the end
//                }
            }.join()
        }
    }

    @Test
    fun `test withTenantScope when no transaction exists`() = runTest {
        val fixtures = Fixtures(database).initialise()
        val tenant = fixtures.tenant1
        val principal = fixtures.tenant1User1Session

        withContext(RequestContext(tenant = tenant, principal = principal)) {
            assertThat(TransactionManager.currentOrNull()).isNull() // Ensure transaction does not exist at the start
            withTenantTransactionScope { tenant ->
                val existingTransaction = TransactionManager.currentOrNull()
                require(existingTransaction != null) { "Transaction should exist in this context" }
                assertThat(tenant).isNotNull
                assertThat(tenant.id).isEqualTo(fixtures.tenant1.id)
                // nested withTenantScope should not create a new transaction
                withTenantTransactionScope { tenant ->
                    val currentTransaction = TransactionManager.currentOrNull()
                    require(currentTransaction != null) { "Transaction should exist in this context" }
                    assertThat(currentTransaction).isSameAs(existingTransaction) // Ensure the same transaction is reused
                    assertThat(tenant.id).isEqualTo(fixtures.tenant1.id)
                    delay(2000) // Simulate some processing time
                }
                val currentTransaction = TransactionManager.currentOrNull()
                assertThat(currentTransaction).isSameAs(existingTransaction) // Ensure the same transaction is reused
            }
            assertThat(TransactionManager.currentOrNull()).isNull() // Ensure transaction is closed after the block
        }
    }

    @Test
    fun `test withTenantScope when transaction exists`() = runTest {
        val fixtures = Fixtures(database).initialise()
        val tenant = fixtures.tenant1
        val principal = fixtures.tenant1User1Session

        withContext(RequestContext(tenant = tenant, principal = principal)) {
            assertThat(TransactionManager.currentOrNull()).isNull() // Ensure transaction does not exist at the start
            newSuspendedTransaction {
                val existingTransaction = TransactionManager.currentOrNull()
                require(existingTransaction != null) { "Transaction should exist in this context" }
                withTenantTransactionScope { tenant ->
                    assertThat(TransactionManager.currentOrNull()).isSameAs(existingTransaction) // Ensure the same transaction is reused
                    assertThat(tenant).isNotNull
                    assertThat(tenant.id).isEqualTo(fixtures.tenant1.id)
                }
                assertThat(TransactionManager.currentOrNull()).isSameAs(existingTransaction) // Ensure the same transaction is reused
            }
            assertThat(TransactionManager.currentOrNull()).isNull() // Ensure transaction is closed after the block
        }
    }

    @Test
    fun `test withTenantScope when transaction exists 2`() = runTest {
        val fixtures = Fixtures(database).initialise()
        val tenant = fixtures.tenant1
        val principal = fixtures.tenant1User1Session

        require(TransactionManager.currentOrNull() == null) { "Test requires no current transaction" }
        withContext(RequestContext(tenant = tenant, principal = principal)) {
            newSuspendedTransaction {
                val existingTransaction = TransactionManager.currentOrNull()
                require(existingTransaction != null) { "Transaction should exist in this context" }

                withTenantTransactionScope { tenant ->
                    val currentTransaction = TransactionManager.currentOrNull()
                    assertThat(currentTransaction).isNotNull
                    assertThat(currentTransaction).isSameAs(existingTransaction) // Ensure the same transaction is reused
                    assertThat(tenant).isNotNull
                    assertThat(tenant.id).isEqualTo(fixtures.tenant1.id)
                    delay(2000) // Simulate some processing time
                }

                val afterTransaction = TransactionManager.currentOrNull()
                assertThat(afterTransaction).isSameAs(existingTransaction) // Ensure transaction is still active
            }
            require(TransactionManager.currentOrNull() == null) { "Transaction should be closed by now" }
        }
    }
}