package services.oneteam.ai.shared.domains.passvault

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

abstract class KeyVaultServiceBaseTest {

    data class Spec(
        val keyVaultService: KeyVaultService,
        val vaultName: String,
        val loadedSecrets: Map<String, String>
    )

    internal abstract fun getSpec(): Spec

    @Test
    fun `can successfully retrieve key that exists`() = runTest {
        val spec = getSpec()
        val keyVaultService = spec.keyVaultService

        spec.loadedSecrets.forEach {
            (secretName, secretValue) ->
            val result = keyVaultService.retrieveSecret(secretName)
            assertEquals(
                actual = result,
                expected = secretValue,
                message = "Expected '${secretName}' to be '${secretValue}', but got '$result'"
            )
        }
    }

    @Test
    fun `throws exception when trying to retrieve that that does not exist`() = runTest {
        val spec = getSpec()
        val keyVaultService = spec.keyVaultService

        val nonExistentSecretName = "test-otai-non-existent-key"
        try {
            keyVaultService.retrieveSecret(nonExistentSecretName)
        } catch (e: IllegalArgumentException) {
            assertEquals(
                actual = e.message,
                expected = "Secret with name '${nonExistentSecretName}' does not exist in Key Vault '${spec.vaultName}'."
            )
            return@runTest
        }
        throw AssertionError("Expected IllegalArgumentException to be thrown")
    }
}