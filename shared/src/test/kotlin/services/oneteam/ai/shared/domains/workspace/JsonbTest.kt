package services.oneteam.ai.shared.domains.workspace


import org.automerge.Document
import org.automerge.ObjectId
import org.automerge.ObjectType

fun main() {
    // Create an object
    val doc = Document()

    val text: ObjectId
    val map: ObjectId
    val list: ObjectId

    doc.startTransaction().use { tx ->
        try {
            // Create a text object under the "text" key of the root map
            text = tx.set(ObjectId.ROOT, "name", ObjectType.TEXT)
            tx.spliceText(text, 0, 0, "Hello world")

            map = tx.set(ObjectId.ROOT, "map", ObjectType.MAP)
            tx.set(map, "stringKey", "String value")

            list = tx.set(ObjectId.ROOT, "list", ObjectType.LIST)
            tx.insert(list, 0, "List value")

            tx.commit()
        } catch (e: Error) {
            println(e)
            return
        }
    }

    // save the document
    val docBytes = doc.save()

    // Load the document
    val doc2 = Document.load(docBytes)
    println(doc2.text(text).get().toString()) // Prints "Hello world"

    // Modify the doc in doc2
    doc2.startTransaction().use { tx ->
        try {
            tx.spliceText(text, 5, 0, " beautiful")
            tx.commit()
        } catch (e: Error) {
            println(e)
        }
    }

    // Modify the doc in doc1
    doc.startTransaction().use { tx ->
        try {
            tx.spliceText(text, 5, 0, " there")
            tx.commit()
        } catch (e: Error) {
            println(e)
        }
    }

    // Merge the changes
    doc.merge(doc2)
    println("doc")
    println(printDoc(doc))
    println("doc2")
    println(printDoc(doc2))

    println("loading doc3")
    val doc3 = loadDoc("YqEZ7otKdymsRRWAf5i8YA8FnCu")
    if (doc3 != null) {
        println("doc3")
        println(printDoc(doc3))
    } else {
        println("doc3 is null")
    }
}
