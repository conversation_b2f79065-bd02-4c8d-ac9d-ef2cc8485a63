package services.oneteam.ai.shared

import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.*
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import kotlin.collections.dropLast
import kotlin.collections.last
import kotlin.collections.mutableMapOf
import kotlin.collections.set
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

class MockHttpClient() {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    val documents = mutableMapOf<String, String>()

    @OptIn(ExperimentalUuidApi::class)
    val mockEngine = MockEngine { request ->
        documents["1"] = """{"id": "1", "name": "Document 1"}"""

        logger.debug("REQUEST: {}", request.url)
        // document id is the last part of the url
        val id = request.url.encodedPath.split("/").last()
        // action is the second last part of the url
        val action = request.url.encodedPath.split("/").dropLast(1).last()

        when (action) {
            "create" -> {
                // read the document from the request body
                val document = request.body.toByteArray().toString()
                val documentId = Uuid.random().toString()
                documents[documentId] = document
                respond(
                    content = """{"documentId": "$documentId"}""",
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                )
            }

            "show" -> {
                val content = documents[id] ?: """{"error": "not found"}"""
                respond(
                    content = content,
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                )
            }

            else -> {
                throw Exception("Invalid action $action")
            }
        }

    }

    val client = HttpClient(mockEngine) {
        install(ContentNegotiation) {
            json(Json { ignoreUnknownKeys = true })
        }
    }
}