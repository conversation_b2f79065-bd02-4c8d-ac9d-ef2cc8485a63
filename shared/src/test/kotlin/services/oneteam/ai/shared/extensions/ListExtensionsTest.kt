package services.oneteam.ai.shared.extensions

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class ListExtensionsTest {

    @Test
    fun `toBigDecimalList should convert list of numbers and numeric strings to BigDecimal`() {
        val input = listOf(1, "2.5", 3.0, "4")
        val expected = listOf(
            BigDecimal("1.000000000000000"),
            BigDecimal("2.500000000000000"),
            BigDecimal("3.000000000000000"),
            BigDecimal("4.000000000000000")
        )
        input.toBigDecimalList() shouldBe expected
    }

    @Test
    fun `toBigDecimalList should ignore non-numeric values`() {
        val input = listOf(1, "abc", null, "2.5")
        val expected = listOf(BigDecimal("1.000000000000000"), BigDecimal("2.500000000000000"))
        input.toBigDecimalList() shouldBe expected
    }

    @Test
    fun `isNumeric should return true for numeric values`() {
        isNumeric(1) shouldBe true
        isNumeric(1.5) shouldBe true
        isNumeric("2.5") shouldBe true
    }

    @Test
    fun `isNumeric should return false for non-numeric values`() {
        isNumeric("abc") shouldBe false
        isNumeric(null) shouldBe false
        isNumeric(true) shouldBe false
    }

    @Test
    fun `toNumberOrNull should convert numeric values to BigDecimal`() {
        toNumberOrNull(1) shouldBe BigDecimal("1.000000000000000")
        toNumberOrNull(2.5) shouldBe BigDecimal("2.500000000000000")
        toNumberOrNull("3.14") shouldBe BigDecimal("3.140000000000000")
    }

    @Test
    fun `toNumberOrNull should return null for non-numeric values`() {
        toNumberOrNull("abc") shouldBe null
        toNumberOrNull(null) shouldBe null
    }
}