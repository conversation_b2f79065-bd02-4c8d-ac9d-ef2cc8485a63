package services.oneteam.ai.shared.spike

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class OptionalTest {

    // with default value
    data class PersonWithMandatoryWithDefault(val name: String, val age: Int = 20)

    data class PersonWithOptionalWithDefault(val name: String, val age: Int? = 20)

    @Test
    fun `PersonWithMandatoryWithDefault should use provided value`() {
        val person = PersonWithMandatoryWithDefault("<PERSON>", 30)
        person.age shouldBe 30
    }

    @Test
    fun `PersonWithMandatoryWithDefault should use default value`() {
        val person = PersonWithMandatoryWithDefault("<PERSON>")
        person.age shouldBe 20
    }

    @Test
    fun `PersonWithOptionalWithDefault should use default value`() {
        val person = PersonWithOptionalWithDefault("<PERSON>")
        person.age shouldBe 20
    }

    @Test
    fun `PersonWithOptionalWithDefault should use provided value`() {
        val person = PersonWithOptionalWithDefault("<PERSON>", 30)
        person.age shouldBe 30
    }
}