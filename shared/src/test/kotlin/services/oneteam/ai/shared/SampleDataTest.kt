package services.oneteam.ai.shared

import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.workspace.QuestionType
import services.oneteam.ai.shared.domains.workspace.Workspace

class SampleDataTest {

    @Test
    fun `should deserialize and serialize workspace json`() {
        // prepare
        val workspaceForJsonOriginal = otSerializer.decodeFromString<Workspace.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )

        val string = otSerializer.encodeToString<Workspace.ForJson>(workspaceForJsonOriginal)
        val workspaceForJsonCopy = otSerializer.decodeFromString<Workspace.ForJson>(string)

        workspaceForJsonCopy shouldBe workspaceForJsonOriginal
    }

    @Test
    fun `should deserialize and serialize answer json`() {
        // prepare
        val original = otSerializer.decodeFromString<FormAnswer.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-answers-cp-2025.json")!!.readText()
        )

        val string = otSerializer.encodeToString<FormAnswer.ForJson>(original)
        val copy = otSerializer.decodeFromString<FormAnswer.ForJson>(string)

        copy shouldBe original
    }

    @Test
    fun `should deserialize and serialize answer json 2`() {
        // prepare
        val original = otSerializer.decodeFromString<FormAnswer.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-answers-cp-2024.json")!!.readText()
        )

        val string = otSerializer.encodeToString<FormAnswer.ForJson>(original)
        val copy = otSerializer.decodeFromString<FormAnswer.ForJson>(string)

        copy shouldBe original

        // check that entities matches order so we know when we've got invalid data
        // this could be invalid if we deserialize from json
        original.answers.values.filter { q: FormAnswer<*> -> q.type == QuestionType.TABLE }.forEach { q ->
            val value = (q as FormAnswer.TableAnswer).value
            value.isValid().shouldBeTrue()
        }
    }
}