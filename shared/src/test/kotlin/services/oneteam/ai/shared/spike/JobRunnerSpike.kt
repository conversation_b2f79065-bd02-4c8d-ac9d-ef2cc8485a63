package services.oneteam.ai.shared.spike

import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import kotlin.random.Random
import kotlin.time.Duration.Companion.milliseconds


class RunJobsSpike {

    suspend fun run() {
        val jobs = listOf(
            suspend {
                val nextInt = Random.nextInt(100, 1000)
                println("Job 1 delay: $nextInt")
                delay(nextInt.milliseconds);
                println("Job 1")
            },
            suspend {
                val nextInt = Random.nextInt(100, 1000)
                println("Job 2 delay: $nextInt")
                delay(nextInt.milliseconds); println("Job 2")
            },
            suspend {
                val nextInt = Random.nextInt(100, 1000)
                println("Job 3 delay: $nextInt")
                delay(nextInt.milliseconds); println("Job 3")
            }
        )

        coroutineScope {
            jobs.forEach { job ->
                launch {
                    println("job starting")
                    job.invoke()
                    println("job finished")
                }
            }
        }
    }

}

@Disabled
class RunnerTest {
    @Test
    fun testJobRunner() {
        runBlocking {
            RunJobsSpike().run()
        }
    }
}