package services.oneteam.ai.shared.domains.workspace.validation

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class ValidatorTest {

    @Test
    fun `should invoke constraints`() {

        val constraint1 = object : Constraint<String> {
            override fun validate(data: String): Errors {
                return Errors().add(
                    ConstraintError(
                        Field("key1"),
                        Type("type"),
                        Path("path"),
                        ConstraintDetail("constraintDetail"),
                        Message("message")
                    )
                )
            }

            override fun errorKeys(): List<String> {
                return listOf("message")
            }
        }
        val constraint2 = object : Constraint<String> {
            override fun validate(data: String): Errors {
                return Errors().add(
                    ConstraintError(
                        Field("key2"),
                        Type("type"),
                        Path("path"),
                        ConstraintDetail("constraintDetail"),
                        Message("message")
                    )
                )
            }

            override fun errorKeys(): List<String> {
                return listOf("message")
            }
        }
        val validator = Validator(listOf(constraint1, constraint2))
        val errors = validator.validate("data")
        errors.hasErrors() shouldBe true
        errors.getErrors().size shouldBe 2
        errors.getErrors()[0].key.value shouldBe "key1"
        errors.getErrors()[1].key.value shouldBe "key2"

    }


    @Test
    fun `should return error keys`() {

        val constraint1 = object : Constraint<String> {
            override fun validate(data: String): Errors {
                return Errors().add(
                    ConstraintError(
                        Field("key1"),
                        Type("type"),
                        Path("path"),
                        ConstraintDetail("constraintDetail"),
                        Message("message")
                    )
                )
            }

            override fun errorKeys(): List<String> {
                return listOf("message1")
            }
        }
        val constraint2 = object : Constraint<String> {
            override fun validate(data: String): Errors {
                return Errors().add(
                    ConstraintError(
                        Field("key2"),
                        Type("type"),
                        Path("path"),
                        ConstraintDetail("constraintDetail"),
                        Message("message")
                    )
                )
            }

            override fun errorKeys(): List<String> {
                return listOf("message2", "message3")
            }
        }
        val validator = Validator(listOf(constraint1, constraint2))
        val errorKeys = validator.errorKeys()
        errorKeys.size shouldBe 3
        errorKeys[0] shouldBe "message1"
        errorKeys[1] shouldBe "message2"
        errorKeys[2] shouldBe "message3"
    }
}