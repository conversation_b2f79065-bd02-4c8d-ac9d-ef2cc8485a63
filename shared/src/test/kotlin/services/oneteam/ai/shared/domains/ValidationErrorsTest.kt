package services.oneteam.ai.shared.domains

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.ValidationErrors

class ValidationErrorsTest {
    @Test
    fun `should throw exception when errors are present`() {
        // Given
        val errors = ValidationErrors()

        // When
        errors.add("key", "value")

        // Then
        assertThrows<BadRequestException> {
            errors.throwIfInvalid()
        }

    }

    @Test
    fun `should not throw exception when errors are not present`() {
        // Given
        val errors = ValidationErrors()

        // When
        errors.throwIfInvalid()

        // Then
        // No exception should be thrown
    }
}