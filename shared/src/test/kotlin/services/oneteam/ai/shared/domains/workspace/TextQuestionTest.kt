package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe

class TextQuestionTest {

    private val question = BaseSection.TextQuestion(
        id = BaseSection.Id("1"),
        type = QuestionType.TEXT,
        text = "How are you today?",
        description = "description",
        identifier = "identifier",
        properties = CommonQuestionProperties.TextQuestionProperties(
            minLength = 5,
            maxLength = 10,
            required = true
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate question`() {
        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty text`() {
        val question = question.copy(text = "")
        BaseQuestionTestHelper.testEmptyText(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty identifier`() {
        val question = question.copy(identifier = "")
        BaseQuestionTestHelper.testEmptyIdentifier(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for minLength greater than maxLength`() {
        val question = question.copy(properties = question.properties?.copy(minLength = 11))

        question.validateConfiguration("testPath").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(2)
            errors.getErrors()[0].message.value.shouldBe("errors.configurationForm.question.minLength.greaterThanMaxLength")
            errors.getErrors()[0].path.value.shouldBe("testPath.properties.minLength")
            errors.getErrors()[0].key.value.shouldBe("minLength")
            errors.getErrors()[0].type.value.shouldBe("greaterThanMaxLength")
            errors.getErrors()[1].message.value.shouldBe("errors.configurationForm.question.maxLength.lessThanMinLength")
            errors.getErrors()[1].path.value.shouldBe("testPath.properties.maxLength")
            errors.getErrors()[1].key.value.shouldBe("maxLength")
            errors.getErrors()[1].type.value.shouldBe("lessThanMinLength")
        }
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for defaultValue length less than minLength`() {
        val question = question.copy(properties = question.properties?.copy(defaultValue = "a"))

        question.validateConfiguration("testPath").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(1)
            errors.getErrors()[0].message.value.shouldBe("default length should be greater than minLength")
            errors.getErrors()[0].path.value.shouldBe("testPath.properties.defaultValue")
            errors.getErrors()[0].key.value.shouldBe("defaultValue")
            errors.getErrors()[0].type.value.shouldBe("lessThanMinLength")
        }
    }

    @org.junit.jupiter.api.Test
    fun `should not return errors for empty defaultValue`() {
        val question = question.copy(properties = question.properties?.copy(defaultValue = ""))

        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for defaultValue length greater than maxLength`() {
        val question =
            question.copy(properties = question.properties?.copy(defaultValue = "This is be longer than 10 chars"))

        question.validateConfiguration("testPath").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(1)
            errors.getErrors()[0].message.value.shouldBe("default length should be less than maxLength")
            errors.getErrors()[0].path.value.shouldBe("testPath.properties.defaultValue")
            errors.getErrors()[0].key.value.shouldBe("defaultValue")
            errors.getErrors()[0].type.value.shouldBe("greaterThanMaxLength")
        }
    }
}