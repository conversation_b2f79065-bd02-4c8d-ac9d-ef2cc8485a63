package services.oneteam.ai.shared.domains.passvault

class InMemoryKeyVaultServiceTest: KeyVaultServiceBaseTest() {
    val vaultName = "otai-shared-vault-in-memory"
    val loadedSecrets = mapOf(
        "test-otai-test-secret" to "t3st-0tai-t3st-s3cr3t"
    )

    override fun getSpec(): Spec {
        val inMemoryKeyVaultService = InMemoryKeyVaultService(
            InMemoryKeyVaultService.InMemoryKeyVaultServiceConfig(
                keyVaultName = vaultName,
            )
        )
        loadedSecrets.forEach { (secretName, secretValue) ->
            inMemoryKeyVaultService.addSecret(secretName, secretValue)
        }
        return Spec(inMemoryKeyVaultService, vaultName, loadedSecrets)
    }
}