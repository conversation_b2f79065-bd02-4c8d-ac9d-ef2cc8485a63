package services.oneteam.ai.shared.domains.event

import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.mock


class EventDispatcherTest {
    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `should enqueue event`() = runTest {
        // Given
        val eventQueue = mock(InMemoryEventQueue::class.java)
        Mockito.`when`(eventQueue.size()).thenReturn(0)

        val eventDispatcher = EventDispatcher(eventQueue, StandardTestDispatcher(testScheduler))

        val event1 = mockEvent()
        val event2 = mockEvent()
        val event3 = mockEvent()

        // When
        eventDispatcher.enqueueEvent(event1, coroutineContext)
        eventDispatcher.enqueueEvent(event2, coroutineContext)
        eventDispatcher.enqueueEvent(event3, coroutineContext)
        advanceUntilIdle()

        // Then
        Mockito.verify(eventQueue, Mockito.times(1)).push(event1)
        Mockito.verify(eventQueue, Mockito.times(1)).push(event2)
        Mockito.verify(eventQueue, Mockito.times(1)).push(event3)
    }


    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `should dispatch all events to listeners`() = runTest {
        // Given
        val eventQueue = InMemoryEventQueue()
        val eventDispatcher = EventDispatcher(eventQueue, StandardTestDispatcher(testScheduler))

        val listener1 = mock(EventListener::class.java)
        val listener2 = mock(EventListener::class.java)
        val listener3 = mock(EventListener::class.java)

        val event1 = mockEvent()
        val event2 = mockEvent()
        val event3 = mockEvent()

        // When
        eventDispatcher.register(listener1)
        eventDispatcher.register(listener2)
        eventDispatcher.register(listener3)
        eventDispatcher.enqueueEvent(event1, coroutineContext)
        eventDispatcher.enqueueEvent(event2, coroutineContext)
        eventDispatcher.enqueueEvent(event3, coroutineContext)
        advanceUntilIdle()

        // Then
        Mockito.verify(listener1, Mockito.times(1)).onEvent(event1)
        Mockito.verify(listener1, Mockito.times(1)).onEvent(event2)
        Mockito.verify(listener1, Mockito.times(1)).onEvent(event3)

        Mockito.verify(listener2, Mockito.times(1)).onEvent(event1)
        Mockito.verify(listener2, Mockito.times(1)).onEvent(event2)
        Mockito.verify(listener2, Mockito.times(1)).onEvent(event3)

        Mockito.verify(listener3, Mockito.times(1)).onEvent(event1)
        Mockito.verify(listener3, Mockito.times(1)).onEvent(event2)
        Mockito.verify(listener3, Mockito.times(1)).onEvent(event3)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `should register and unregister correctly`() = runTest {
        // Given
        val eventQueue = InMemoryEventQueue()
        val eventDispatcher = EventDispatcher(eventQueue, StandardTestDispatcher(testScheduler))
        val subscriber1 = mock(EventListener::class.java)
        val event1 = mockEvent()
        val event2 = mockEvent()

        // When
        eventDispatcher.register(subscriber1)
        eventDispatcher.enqueueEvent(event1, coroutineContext)
        advanceUntilIdle()

        // Then
        Mockito.verify(subscriber1, Mockito.times(1)).onEvent(event1)

        // When
        eventDispatcher.unregister(subscriber1)
        eventDispatcher.enqueueEvent(event2, coroutineContext)
        advanceUntilIdle()

        // Then
        Mockito.verify(subscriber1, Mockito.times(0)).onEvent(event2)
    }
}