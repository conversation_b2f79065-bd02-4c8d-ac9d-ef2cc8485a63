package services.oneteam.ai.shared.domains.collection.form

import io.kotest.common.runBlocking
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.otSerializer
import kotlin.test.assertContentEquals

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FormHelperTest {


    private val documentService = mockk<IDocumentService>()
    private val uploadService = mockk<BlobService>()
    private val documentId = Form.DocumentId("doc1")
    private val questionId = BaseSection.Id("q1")
    private val itemId = ItemId("item1")
    private val itemQuestionId = BaseSection.Id("itemQ1")
    private val tenantId = 1L
    val workspace = otSerializer.decodeFromString<Workspace.ForJson>(
        this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
    )
    private val formId = Form.Id(1L)


    val itemQuestion = BaseSection.TextQuestion(
        id = itemQuestionId,
        type = QuestionType.TEXT,
        description = "",
        identifier = "NAME",
        text = "NAME",
        properties = CommonQuestionProperties.TextQuestionProperties(
            required = true
        )
    )

    val listQuestion = BaseSection.ListQuestion(
        id = questionId, properties = CommonQuestionProperties.ListQuestionProperties(
            items = listOf(itemQuestion)
        ), description = "", identifier = "EMPLOYEES", text = "Employees", type = QuestionType.LIST
    )

    val fileQuestion = BaseSection.FilesQuestion(
        id = questionId,
        type = QuestionType.FILES,
        text = "Upload your identity documents",
        description = "description",
        identifier = "identifier",
        properties = CommonQuestionProperties.FilesQuestionProperties(
            required = true,
        )
    )

    @Test
    fun `test validateAndGenerateListAnswer with SET_LIST operation`() {
        val formAnswer = FormAnswerRequestBody(
            questionId = questionId,
            listOperation = SetListAnswerOperation.SET_LIST,
            value = JsonArray(listOf(JsonPrimitive("John")))
        )

        val existingAnswer = FormAnswer.ListAnswer(
            questionId, OrderedMap(
                listOf(
                    ListAnswerItem(
                        itemId, mapOf(itemQuestionId to FormAnswer.TextAnswer(itemQuestionId, "Ada"))
                    )
                )
            )
        )

        coEvery {
            documentService.show(
                documentId.toString(), any(), FormAnswer.ForJson::class, true
            )
        } returns FormAnswer.ForJson(
            answers = mapOf(questionId to existingAnswer),
            id = Form.Id(1),
        )

        val result = runBlocking {
            validateAndGenerateListAnswer(
                listQuestion, formAnswer, documentId.toString(), documentService
            )
        }
        val expectedPath =
            listOf("answer", questionId.value)
        val expectedAnswer = FormAnswer.ListAnswer(
            questionId, OrderedMap(
                listOf(
                    ListAnswerItem(
                        itemId, mapOf(itemQuestionId to FormAnswer.TextAnswer(itemQuestionId, "John"))
                    )
                )
            )
        )

        assertEquals(1, result?.size)
        assertEquals(expectedPath, result?.first()?.path)
        assertEquals(expectedAnswer, result?.first()?.formAnswer)
    }

    @Test
    fun `test validateAndGenerateListAnswer with SET_ITEM operation`() {
        val formAnswer = FormAnswerRequestBody(
            questionId = questionId,
            listOperation = SetListAnswerOperation.SET_ITEM,
            itemIndex = 2,
            value = JsonPrimitive("John")
        )

        val existingAnswer = FormAnswer.ListAnswer(
            questionId, OrderedMap(
                listOf(
                    ListAnswerItem(itemId, mapOf(itemQuestionId to FormAnswer.TextAnswer(itemQuestionId, "Ada"))),
                    ListAnswerItem(
                        ItemId("item2"), mapOf(itemQuestionId to FormAnswer.TextAnswer(itemQuestionId, "Bob"))
                    )
                )
            )
        )

        coEvery {
            documentService.show(
                documentId.toString(), any(), FormAnswer.ForJson::class, true
            )
        } returns FormAnswer.ForJson(
            answers = mapOf(questionId to existingAnswer),
            id = Form.Id(1),
        )
        val result = runBlocking {
            validateAndGenerateListAnswer(
                listQuestion, formAnswer, documentId.toString(), documentService
            )
        }

        val expectedPath =
            listOf("answer", questionId.value, "value", "entities", "item2", "item", itemQuestionId.value)
        val expectedAnswer = FormAnswer.ListAnswer(
            questionId, OrderedMap(
                listOf(
                    ListAnswerItem(
                        ItemId("item2"), mapOf(itemQuestionId to FormAnswer.TextAnswer(itemQuestionId, "John"))
                    )
                )
            )
        )

        assertEquals(1, result?.size)
        assertEquals(expectedPath, result?.first()?.path)
        assertEquals(expectedAnswer, result?.first()?.formAnswer)
    }

    @Test
    fun `test validateAndGenerateListAnswer with REMOVE_ITEM operation`() {
        val formAnswer = FormAnswerRequestBody(
            questionId = questionId, listOperation = SetListAnswerOperation.REMOVE_ITEM, itemId = itemId.value
        )

        val existingAnswer = FormAnswer.ListAnswer(
            questionId, OrderedMap(
                listOf(
                    ListAnswerItem(itemId, mapOf(itemQuestionId to FormAnswer.TextAnswer(itemQuestionId, "Ada"))),
                    ListAnswerItem(
                        ItemId("item2"), mapOf(itemQuestionId to FormAnswer.TextAnswer(itemQuestionId, "Bob"))
                    )
                )
            )
        )

        coEvery {
            documentService.show(
                documentId.toString(), any(), FormAnswer.ForJson::class, true
            )
        } returns FormAnswer.ForJson(
            answers = mapOf(questionId to existingAnswer),
            id = Form.Id(1),
        )
        val result = runBlocking {
            validateAndGenerateListAnswer(
                listQuestion, formAnswer, documentId.toString(), documentService
            )
        }

        val expectedPath = listOf("answer", questionId.value)
        val expectedAnswer = FormAnswer.ListAnswer(
            questionId, OrderedMap(
                listOf(
                    ListAnswerItem(
                        ItemId("item2"), mapOf(itemQuestionId to FormAnswer.TextAnswer(itemQuestionId, "Bob"))
                    )
                )
            )
        )

        assertEquals(1, result?.size)
        assertEquals(expectedPath, result?.first()?.path)
        assertEquals(expectedAnswer, result?.first()?.formAnswer)
    }

    @Test
    fun `test validateAndGenerateFileAnswer with SET_FILE operation`() = runBlocking {
        val formAnswer = FormAnswerRequestBody(
            questionId = questionId, fileOperation = SetFileAnswerOperation.SET_FILE, value = JsonObject(
                mapOf(
                    "path" to JsonPrimitive("https://example.com/file.txt"), "name" to JsonPrimitive("file.txt")
                )
            )
        )
        mockkStatic("services.oneteam.ai.shared.domains.collection.form.FormHelperKt")
        coEvery {
            getBlobSasUrl(
                "https://example.com/file.txt", questionId, tenantId, workspace, formId, uploadService
            )
        } returns "upload/file"

        val result = validateAndGenerateFileAnswer(
            fileQuestion, formAnswer, documentId.toString(), documentService, tenantId, workspace, formId, uploadService
        )

        assertEquals(2, result.size)
        assertEquals("answer/q1", result[0].path.joinToString("/"))
        assertEquals("answer/q1", result[1].path.joinToString("/"))
        assertEquals("file.txt", (result[1].formAnswer as FormAnswer.FileAnswer).value.first().name)
    }

    @Test
    fun `test validateAndGenerateFileAnswer with ADD_FILE operation`() = runBlocking {
        val formAnswer = FormAnswerRequestBody(
            questionId = questionId, fileOperation = SetFileAnswerOperation.ADD_FILE, value = JsonObject(
                mapOf(
                    "path" to JsonPrimitive("https://example.com/newfile.txt"), "name" to JsonPrimitive("newfile.txt")
                )
            )
        )

        mockkStatic("services.oneteam.ai.shared.domains.collection.form.FormHelperKt")
        coEvery {
            getBlobSasUrl(
                "https://example.com/newfile.txt", questionId, tenantId, workspace, formId, uploadService
            )
        } returns "upload/file"

        val result = validateAndGenerateFileAnswer(
            fileQuestion, formAnswer, documentId.toString(), documentService, tenantId, workspace, formId, uploadService
        )

        assertEquals(1, result.size)
        assertEquals("answer/q1", result[0].path.joinToString("/"))
        assertEquals("newfile.txt", (result[0].formAnswer as FormAnswer.FileAnswer).value.first().name)
    }

    @Test
    fun `test validateAndGenerateFileAnswer with REMOVE_FILE operation`() = runBlocking {
        val formAnswer = FormAnswerRequestBody(
            questionId = questionId, fileOperation = SetFileAnswerOperation.REMOVE_FILE, fileIndex = 1
        )

        val existingAnswer = FormAnswer.FileAnswer(
            questionId, listOf(
                FileAnswerValue("upload/file1", "file1.txt"), FileAnswerValue("upload/file2", "file2.txt")
            )
        )

        coEvery {
            documentService.show(
                documentId.toString(), any(), FormAnswer.ForJson::class, true
            )
        } returns FormAnswer.ForJson(
            answers = mapOf(questionId to existingAnswer),
            id = Form.Id(1),
        )

        val result = validateAndGenerateFileAnswer(
            fileQuestion, formAnswer, documentId.toString(), documentService, tenantId, workspace, formId, uploadService
        )

        assertEquals(1, result.size)
        assertEquals("answer/q1", result[0].path.joinToString("/"))
        assertEquals("undefined", (result[0].formAnswer as FormAnswer.FileAnswer).value.first().name)
    }

    data class Spec(
        val input: JsonElement, val expected: List<FileAnswerValue>
    )

    fun normaliseInputsForFileAnswerProvider(): List<Spec> {
        return listOf(
            Spec(
                input = JsonObject(
                    mapOf(
                        "path" to JsonPrimitive("https://example.com/file.txt"), "name" to JsonPrimitive("fileName.txt")
                    )
                ), expected = listOf(
                    FileAnswerValue("https://example.com/file.txt", "fileName.txt"),
                )
            ),
            Spec(
                input = JsonArray(
                    listOf(
                        JsonObject(
                            mapOf(
                                "path" to JsonPrimitive("https://example.com/file.txt"),
                                "name" to JsonPrimitive("fileName.txt")
                            )
                        )
                    )
                ), expected = listOf(
                    FileAnswerValue("https://example.com/file.txt", "fileName.txt"),
                )
            ),
            Spec(
                input = JsonArray(
                    listOf(
                        JsonObject(
                            mapOf(
                                "path" to JsonPrimitive("https://example.com/file.txt"),
                                "name" to JsonPrimitive("fileName.txt")
                            )
                        ), JsonObject(
                            mapOf(
                                "path" to JsonPrimitive("https://example.com/file2.txt"),
                                "name" to JsonPrimitive("fileName2.txt")
                            )
                        )
                    )
                ), expected = listOf(
                    FileAnswerValue("https://example.com/file.txt", "fileName.txt"),
                    FileAnswerValue("https://example.com/file2.txt", "fileName2.txt"),
                )
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("normaliseInputsForFileAnswerProvider")
    fun `test normaliseInputsForFileAnswer`(spec: Spec) = runTest {
        val result = normaliseInputsForFileAnswer(spec.input)
        assertEquals(
            spec.expected.size, result!!.size
        )
        assertContentEquals(
            spec.expected, result.toList()
        )
    }
}