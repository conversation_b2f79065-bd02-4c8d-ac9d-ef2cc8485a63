package services.oneteam.ai.shared.spike

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode

class BigDecimalTest {
    @Test
    fun add1() {
        val a = BigDecimal("1.0")
        val b = BigDecimal("2.0")
        val result = a + b
        assertThat(result).isEqualTo(BigDecimal("3.0"))
    }

    @Test
    fun add2() {
        val a = BigDecimal("1")
        val b = BigDecimal("2.0")
        val result = a + b
        assertThat(result).isEqualTo(BigDecimal("3.0"))
    }

    @Test
    fun add3() {
        val a = BigDecimal("1.0")
        val b = BigDecimal("2")
        val result = a + b
        assertThat(result).isEqualTo(BigDecimal("3.0"))
    }

    @Test
    fun divide1() {
        val a = BigDecimal("32")
        val b = BigDecimal("6")
        val result = a / b
        assertThat(result).isEqualTo(BigDecimal("5"))
    }

    @Test
    fun divide2() {
        val a = BigDecimal("32.0").setScale(10)
        val b = BigDecimal("6.0")
        val result = a / b
        assertThat(result).isEqualTo(BigDecimal("5.3333333333"))
    }


    @Test
    fun divide3() {
        val a = BigDecimal("32.0")
        val b = BigDecimal("6.0").setScale(10)
        val result = a / b
        assertThat(result).isEqualTo(BigDecimal("5.3"))
    }

    @Test
    fun divide4() {
        val a = BigDecimal("32.0")
        val b = BigDecimal("6.0", MathContext.DECIMAL128)
        val result = a / b
        assertThat(result).isEqualTo(BigDecimal("5.3"))
    }


    @Test
    fun divide5() {
        val a = BigDecimal("32.0").setScale(10)
        val b = BigDecimal("6.0")
        val result = a / b
        assertThat(result).isEqualTo(BigDecimal("5.3333333333"))
    }


    @Test
    fun divide6() {
        val a = BigDecimal("36.0").setScale(10)
        val b = BigDecimal("6.0")
        val result = a / b
        assertThat(result.stripTrailingZeros()).isEqualTo(BigDecimal("6"))
    }

    @Test
    fun precisionAndScale() {
        val bd = BigDecimal("12345678901234567890.12345678901234567890")
        assertThat(bd.precision()).isEqualTo(40)
        assertThat(bd.scale()).isEqualTo(20)
    }

    @Test
    fun `changing precision and scale`() {
        val bd = BigDecimal("12345678901234567890.12345678901234567890")
        val bd2 = bd.setScale(10, RoundingMode.HALF_UP)
        assertThat(bd2.precision()).isEqualTo(30)
        assertThat(bd2.scale()).isEqualTo(10)
        assertThat(bd2).isEqualTo(BigDecimal("12345678901234567890.1234567890"))
    }
}