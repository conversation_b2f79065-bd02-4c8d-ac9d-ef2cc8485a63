package services.oneteam.ai.shared.spike

import kotlinx.coroutines.*
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import java.util.*
import kotlin.random.Random
import kotlin.time.Duration.Companion.milliseconds

@Disabled
class LaunchSpike {
    val collector: MutableList<String> = Collections.synchronizedList(mutableListOf<String>())

    @OptIn(DelicateCoroutinesApi::class)
    @Test
    fun test2() = runTest {
        coroutineScope {
            val j1 = GlobalScope.launch(coroutineContext) {
                supervisorScope {
                    launch {
                        println("\nRunning on custom thread pool: ${Thread.currentThread().name}")
                        add(10, "1")
                        println("\nStill on custom thread pool: ${Thread.currentThread().name}")
                    }
                }
            }
            val j2 = GlobalScope.launch(coroutineContext) {
                supervisorScope {
                    launch {
                        println("\nRunning on custom thread pool: ${Thread.currentThread().name}")
                        add(10, "2")
                        println("\nStill on custom thread pool: ${Thread.currentThread().name}")
                    }
                }
            }

            val jobs: List<Job> = listOf(j1, j2)
            jobs.joinAll()
            println(collector)
            //    [1, 2, 1, 2, 1, 2, 2, 1, 1, 2, 1, 2, 1, 2, 2, 1, 2, 1, 2, 1]
            assertThat(countChangesInValue(collector)).isGreaterThan(5)
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    @Test
    fun test3() = runTest {
        val j1 = withContext(Dispatchers.IO) {
            launch {
                println("\nRunning on custom thread pool: ${Thread.currentThread().name}")
                add(10, "1")
                println("\nStill on custom thread pool: ${Thread.currentThread().name}")
            }
        }
        println(collector)
        val j2 = withContext(Dispatchers.IO) {
            launch {
                println("\nRunning on custom thread pool: ${Thread.currentThread().name}")
                add(10, "2")
                println("\nStill on custom thread pool: ${Thread.currentThread().name}")
            }
        }

        val jobs: List<Job> = listOf(j1, j2)
        jobs.joinAll()
        println(collector)
        // [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]
        assertThat(countChangesInValue(collector)).isEqualTo(1)
    }

    @Test
    fun executeTwoCoroutinesInParallelUsingLaunch4() = runTest {
        coroutineScope {
            val j1 = async { doSomething() }
            val j2 = async { doSomethingElse() }

            println("Waiting for coroutines to finish")
            listOf(j1, j2).forEach { it.join() }
            println(collector)
            //    [1, 2, 2, 1, 2, 1, 2, 1, 2, 2, 1, 2, 2, 1, 2, 1, 1, 2, 1, 1]
            assertThat(countChangesInValue(collector)).isGreaterThan(5)
        }
    }

    @Test
    fun executeTwoCoroutinesInParallelUsingLaunch3() = runTest {
        coroutineScope {
            val j1 = coroutineScope {
                async { doSomething() }
            }
            val j2 = coroutineScope {
                async { doSomethingElse() }
            }

            println("Waiting for coroutines to finish")
            listOf(j1, j2).forEach { it.join() }
            println(collector)
        }
        //    [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]
        assertThat(countChangesInValue(collector)).isEqualTo(1)
    }

    @Test
    fun executeTwoCoroutinesInParallelUsingLaunch2() = runTest {
        val j1 = coroutineScope {
            launch { doSomething() }
        }
        val j2 = coroutineScope {
            launch { doSomethingElse() }
        }

        println("Waiting for coroutines to finish")
        listOf(j1, j2).forEach { it.join() }
        println(collector)
        //    [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]
        assertThat(countChangesInValue(collector)).isEqualTo(1)
    }

    @Test
    fun executeTwoCoroutinesInParallelUsingLaunch6() = runTest {
        val j1 = withContext(Dispatchers.IO) {
            launch { doSomething() }
        }
        val j2 = withContext(Dispatchers.Default) {
            launch { doSomethingElse() }
        }

        println("Waiting for coroutines to finish")
        listOf(j1, j2).forEach { it.join() }
        println(collector)
        // [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]
        assertThat(countChangesInValue(collector)).isEqualTo(1)
    }


    @Test
    fun executeTwoCoroutinesInParallelUsingLaunch() = runTest {
        coroutineScope {
            val j1 = launch { doSomething() }
            val j2 = launch { doSomethingElse() }
            println("Waiting for coroutines to finish")
            listOf(j1, j2).forEach { it.join() }
            println(collector)
            //    [1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 2, 1, 1, 2, 2, 1, 2, 1, 2, 1]
            assertThat(countChangesInValue(collector)).isGreaterThan(5)
        }
    }

    suspend fun doSomething() {
        val delay = Random.nextInt(10, 100)
        delay(delay.milliseconds)
        add(10, "1")
        println(" - doSomething waited for $delay milliseconds")
    }

    suspend fun doSomethingElse() {
        val delay = Random.nextInt(10, 100)
        delay(delay.milliseconds)
        add(10, "2")
        println(" - doSomethingElse waited for $delay milliseconds")
    }

    suspend fun add(int: Int, v: String) {
        for (i in 1..int) {
            add(v)
            val delay = Random.nextInt(10, 100)
            delay(delay.milliseconds)
        }
    }

    fun add(v: String) {
        collector.add(v)
    }

    /**
     * Ensure that the list is jumbled, meaning that the elements are not in a predictable order.
     */
    fun countChangesInValue(list: List<String>): Int {
        // count occurrences where the value changes
        var changes = 0
        for (i in 1 until list.size) {
            if (list[i] != list[i - 1]) {
                changes++
            }
        }
        return changes
    }
}