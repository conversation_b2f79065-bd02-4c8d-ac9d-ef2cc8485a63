package services.oneteam.ai.shared.domains.workspace.validation

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.workspace.validation.StringUpperCaseConstraint

class StringUpperCaseConstraintTest {
    @Test
    fun `when string is uppercase then no errors`() {
        val constraint = StringUpperCaseConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error"
        )
        val errors = constraint.validate(Pair("A", "B"))
        assertFalse(errors.hasErrors())
    }

    @Test
    fun `when string is uppercase and has special characters then no errors`() {
        val constraint = StringUpperCaseConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error"
        )
        val errors = constraint.validate(Pair("A", "B_C!@#"))
        assertFalse(errors.hasErrors())
    }

    @Test
    fun `when string is not uppercase then errors`() {
        val constraint = StringUpperCaseConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error"
        )
        // {"errors":[{"key":"name","type":"uppercase","path":"name","constraintDetail":"name must be upper case","message":"error"}]}
        val errors = constraint.validate(Pair("A", "b"))
        assertTrue(errors.hasErrors())
        assertEquals(1, errors.getErrors().size)
        assertEquals("uppercase", errors.getErrors()[0].type.value)
        assertEquals("name", errors.getErrors()[0].path.value)
        assertEquals("error", errors.getErrors()[0].message.value)
    }

}