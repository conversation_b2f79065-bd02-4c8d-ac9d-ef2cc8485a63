package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeTrue
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.workspace.validation.WorkspaceValidationContext
import java.time.Instant

class WorkspaceForJsonTest {
    val workspace = Workspace.ForJson(
        id = Workspace.Id(1),
        name = Workspace.Name("Workspace 1") ,
        key = Workspace.Key("workspace1"),
        foundations = OrderedMap(listOf()),
        flows = OrderedMap(listOf()),
        series = mapOf(),
        labels = mapOf(),
        forms = mapOf(
            FormConfiguration.Id("form1") to FormConfiguration.ForJson(
                id = "1",
                name = "Form",
                foundationId = "1",
                level = 1,
                metadata = EntityMetadata(
                    createdAt = Instant.now(),
                    updatedAt = Instant.now(),
                ),
                key = "key",
                content = listOf(
                    BaseSection.TextQuestion(
                        id = BaseSection.Id("1"),
                        type = QuestionType.TEXT,
                        text = "",
                        description = "description",
                        identifier = "identifier",
                        properties = CommonQuestionProperties.TextQuestionProperties(
                            maxLength = 10,
                            minLength = 1,
                            required = true
                        )
                    )
                )
            )
        ),
        metadata = EntityMetadata(
            createdAt = Instant.now(),
            updatedAt = Instant.now(),
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate forms`() {
        workspace.validateConfiguration("testPath", WorkspaceValidationContext(mapOf(), mapOf())).hasErrors().shouldBeTrue()
    }
}