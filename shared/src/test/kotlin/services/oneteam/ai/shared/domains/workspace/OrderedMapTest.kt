package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class OrderedMapTest {

    @Test
    fun `should add`() {
        val map = OrderedMap(
            listOf(
                Interval(IntervalId("1"), IntervalName("I1")),
                Interval(IntervalId("2"), IntervalName("I2"))
            )
        )
        map.add(Interval(IntervalId("3"), IntervalName("I3")))
        map.shouldBe(
            OrderedMap(
                listOf(
                    Interval(IntervalId("1"), IntervalName("I1")),
                    Interval(IntervalId("2"), IntervalName("I2")),
                    Interval(IntervalId("3"), IntervalName("I3"))
                )
            )
        )
    }

    @Test
    fun `should remove`() {
        val map = OrderedMap(
            listOf(
                Interval(IntervalId("1"), IntervalName("I1")),
                Interval(IntervalId("2"), IntervalName("I2"))
            )
        )
        map.remove(Interval(IntervalId("1"), IntervalName("I1")))
        map.shouldBe(
            OrderedMap(
                listOf(
                    Interval(IntervalId("2"), IntervalName("I2"))
                )
            )
        )
    }

    @Test
    fun `should return next value`() {
        val map = OrderedMap(
            listOf(
                Interval(IntervalId("1"), IntervalName("I1")),
                Interval(IntervalId("2"), IntervalName("I2"))
            )
        )
        map.findNext(IntervalId("1")).shouldBe(Interval(IntervalId("2"), IntervalName("I2")));
        map.findNext(IntervalId("2")).shouldBe(null);
    }

    @Test
    fun `should be valid`() {
        val map = OrderedMap(
            listOf(
                Interval(IntervalId("1"), IntervalName("I1")),
                Interval(IntervalId("2"), IntervalName("I2"))
            )
        )

        map.isValid().shouldBeTrue()
    }

    @Test
    fun `should be invalid`() {
        val map = OrderedMap.of(
            listOf(IntervalId("1")),
            mapOf(
                IntervalId("1") to Interval(IntervalId("1"), IntervalName("I1")),
                IntervalId("2") to Interval(IntervalId("2"), IntervalName("I2"))
            )
        )

        map.isValid().shouldBeFalse()
    }
}