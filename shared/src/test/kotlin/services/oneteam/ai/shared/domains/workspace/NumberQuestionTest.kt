package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe

class NumberQuestionTest {
    private val question = BaseSection.NumberQuestion(
        id = BaseSection.Id("1"),
        type = QuestionType.NUMBER,
        text = "How many days in a week?",
        description = "description",
        identifier = "identifier",
        properties = CommonQuestionProperties.NumberQuestionProperties(
            required = true,
            min = BigDecimalJson(5),
            max = BigDecimalJson(10)
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate question`() {
        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty text`() {
        val question = question.copy(text = "")
        BaseQuestionTestHelper.testEmptyText(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty identifier`() {
        val question = question.copy(identifier = "")
        BaseQuestionTestHelper.testEmptyIdentifier(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for min greater than max`() {
        val question = question.copy(properties = question.properties.copy(min = BigDecimalJson(11)))

        question.validateConfiguration("testPath").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(1)
            errors.getErrors()[0].message.value.shouldBe("min should be less than or equal to max")
            errors.getErrors()[0].path.value.shouldBe("testPath.properties.min")
            errors.getErrors()[0].key.value.shouldBe("min")
            errors.getErrors()[0].type.value.shouldBe("minMax")
        }
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for defaultValue less than minLength`() {
        val question = question.copy(properties = question.properties.copy(defaultValue = BigDecimalJson(4)))

        question.validateConfiguration("testPath").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(1)
            errors.getErrors()[0].message.value.shouldBe("default value should be greater than or equal to min")
            errors.getErrors()[0].path.value.shouldBe("testPath.properties.defaultValue")
            errors.getErrors()[0].key.value.shouldBe("defaultValue")
            errors.getErrors()[0].type.value.shouldBe("lessThanMin")
        }
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for defaultValue greater than maxLength`() {
        val question = question.copy(properties = question.properties.copy(defaultValue = BigDecimalJson(11)))

        question.validateConfiguration("testPath").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(1)
            errors.getErrors()[0].message.value.shouldBe("default value should be less than or equal to max")
            errors.getErrors()[0].path.value.shouldBe("testPath.properties.defaultValue")
            errors.getErrors()[0].key.value.shouldBe("defaultValue")
            errors.getErrors()[0].type.value.shouldBe("greaterThanMax")
        }
    }
}