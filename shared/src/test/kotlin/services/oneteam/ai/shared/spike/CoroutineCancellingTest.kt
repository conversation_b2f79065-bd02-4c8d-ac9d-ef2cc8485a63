package services.oneteam.ai.shared.spike

import kotlinx.coroutines.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Timeout
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

class CoroutineCancellingTest {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    /**
     * Sample output
     * 10:02:25.501 [Test worker @coroutine#2] DEBUG services.oneteam.ai.shared.spike.CoroutineCancellingTest -- Waiting for child thread to start
     * 10:02:25.513 [Test worker @coroutine#3] DEBUG services.oneteam.ai.shared.spike.CoroutineCancellingTest -- Starting child thread
     * 10:02:25.514 [Test worker @coroutine#3] DEBUG services.oneteam.ai.shared.spike.CoroutineCancellingTest -- <PERSON><PERSON> is active, Waiting child thread to finish
     * 10:02:25.618 [Test worker @coroutine#2] DEBUG services.oneteam.ai.shared.spike.CoroutineCancellingTest -- Pausing before cancelling parent job
     * 10:02:26.518 [Test worker @coroutine#3] DEBUG services.oneteam.ai.shared.spike.CoroutineCancellingTest -- Parent is active, Waiting child thread to finish
     * 10:02:27.121 [Test worker @coroutine#2] DEBUG services.oneteam.ai.shared.spike.CoroutineCancellingTest -- Cancelling parent job
     * 10:02:27.130 [Test worker @coroutine#2] DEBUG services.oneteam.ai.shared.spike.CoroutineCancellingTest -- Finished
     */
    @Test
    // this timeout means if the test is failing then it will fail quickly
    @Timeout(
        value = 10,
        unit = TimeUnit.SECONDS,
        threadMode = Timeout.ThreadMode.SEPARATE_THREAD
    )
    fun testParentChildCoroutineCancellationWithThread() = runBlocking {
        val childStarted = AtomicBoolean(false)
        lateinit var childThread: Thread

        val parentJob = launch {
            childThread = Thread {
                childStarted.set(true)

                while (true) {
                    // Infinite loop, no suspension
                    // this would represent a blocking operation that does not yield control
                    // and hence cannot be cancelled by coroutines
                    // In a real-world scenario,this would be a library call - eg jsonata
                }
            }
            logger.debug("Starting child thread")
            childThread.start()

            // while the parent coroutine is active, wait until the child thread finishes (never!)
            while (isActive) {
                logger.debug("Parent is active, Waiting child thread to finish")
                delay(1000)
            }

            // once the parent coroutine is cancelled, we can interrupt the child thread
            logger.debug("Interrupting child thread")
            childThread.interrupt()
        }

        // wait for child to start
        logger.debug("Waiting for child thread to start")
        while (!childStarted.get()) {
            delay(100)
        }

        // let parent run for a bit
        // this will allow the child thread to start and run for a while
        // before we cancel the parent coroutine
        logger.debug("Pausing before cancelling parent job")
        delay(1500)
        logger.debug("Cancelling parent job")
        parentJob.cancelAndJoin()
        logger.debug("Finished")
    }
}