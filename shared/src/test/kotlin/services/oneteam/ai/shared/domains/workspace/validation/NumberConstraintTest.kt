package services.oneteam.ai.shared.domains.workspace.validation

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.workspace.validation.NumberBounds
import services.oneteam.ai.shared.domains.workspace.validation.NumberConstraint

class NumberConstraintTest {
    @org.junit.jupiter.api.Test
    fun `when number is within bounds then no errors`() {
        val constraint = NumberConstraint<Pair<String, Long>>(
            "name",
            { pair -> pair.second },
            "error",
            NumberBounds(1, 10),
        )
        val errors = constraint.validate(Pair("A", 5))
        assertFalse(errors.hasErrors())
    }

    @org.junit.jupiter.api.Test
    fun `when number is below bounds then errors`() {
        val constraint = NumberConstraint<Pair<String, Long>>(
            "name",
            { pair -> pair.second },
            "error",
            NumberBounds(1, 10),
        )
        val errors = constraint.validate(Pair("A", 0))
        assertTrue(errors.hasErrors())
        assertEquals(1, errors.getErrors().size)
        assertEquals("number bounds", errors.getErrors()[0].type.value)
        assertEquals("name", errors.getErrors()[0].path.value)
        assertEquals("error", errors.getErrors()[0].message.value)
    }

    @org.junit.jupiter.api.Test
    fun `when number is above bounds then errors`() {
        val constraint = NumberConstraint<Pair<String, Long>>(
            "name",
            { pair -> pair.second },
            "error",
            NumberBounds(1, 10),
        )
        val errors = constraint.validate(Pair("A", 11))
        assertTrue(errors.hasErrors())
        assertEquals(1, errors.getErrors().size)
        assertEquals("number bounds", errors.getErrors()[0].type.value)
        assertEquals("name", errors.getErrors()[0].path.value)
        assertEquals("error", errors.getErrors()[0].message.value)
    }

    @Test
    fun `when max is not specified no errors`() {
        val constraint = NumberConstraint<Pair<String, Long>>(
            "name",
            { pair -> pair.second },
            "error",
            NumberBounds(1, null),
        )
        val errors = constraint.validate(Pair("A", 10000000))
        assertFalse(errors.hasErrors())
    }

    @Test
    fun `when min is not specified no errors`() {
        val constraint = NumberConstraint<Pair<String, Long>>(
            "name",
            { pair -> pair.second },
            "error",
            NumberBounds(null, 10),
        )
        val errors = constraint.validate(Pair("A", -10000000))
        assertFalse(errors.hasErrors())
    }

}