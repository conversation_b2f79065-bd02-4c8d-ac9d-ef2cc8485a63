package services.oneteam.ai.shared.domains.collection.form

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.collection.form.FormAnswer.TableAnswer
import services.oneteam.ai.shared.domains.workspace.BaseSection
import services.oneteam.ai.shared.domains.workspace.Workspace

class TableAnswerTest {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Disabled("Need to fix how tables are converted to JSON, using proper types")
    @Test
    fun `should convert to types`() {
        val workspace = Json.decodeFromString<Workspace.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )

        val answers = Json.decodeFromString<FormAnswer.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-answers-aqt.json")!!.readText()
        )

        val tableAnswer: TableAnswer = answers.answers.get(BaseSection.Id("VEOTahOeLj")) as TableAnswer

        logger.debug("TableAnswer: {}", tableAnswer)

        val actual = tableAnswer.toJsonElement(workspace) as JsonObject

        // {"id":"VEOTahOeLj","type":"table","answer":[{"O1REtNdy50":"aa","eALIKF2hFv":"11","_rowId":"Q7JjeX6MIGdhGaqU9n1TH","_rowIndex":1},{"O1REtNdy50":"bb","eALIKF2hFv":"22","_rowId":"ryqry17ixv4vg5DtIQrp8","_rowIndex":2},{"O1REtNdy50":"cc","eALIKF2hFv":"33","_rowId":"JEU4NFPAQpCQFFJKFXaQ1","_rowIndex":3}],"columns":{"O1REtNdy50":{"id":"O1REtNdy50","type":"text","answer":["aa","bb","cc"]},"eALIKF2hFv":{"id":"eALIKF2hFv","type":"number","answer":["11","22","33"]},"ids":["O1REtNdy50","eALIKF2hFv"]}}
        logger.debug("Actual: {}", actual)

        assertThat(actual["answer"]).isNotNull
        assertThat(((actual["answer"] as JsonArray)[0] as JsonObject)["eALIKF2hFv"]).isEqualTo(JsonPrimitive(11))
    }

}