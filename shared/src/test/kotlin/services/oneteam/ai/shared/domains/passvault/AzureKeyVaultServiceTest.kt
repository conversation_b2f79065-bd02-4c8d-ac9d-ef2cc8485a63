package services.oneteam.ai.shared.domains.passvault

import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable

// Some tests in this file require access to a REAL Azure Key Vault, which cannot be run in CI/CD pipelines.
// When running locally ensure you are authenticated with Azure CLI (`az login`) and can access the Key Vault.
// So we have a flag to skip these tests in CI/CD environments.
@EnabledIfEnvironmentVariable(
    named = "TESTS_RUN_AZURE_KEYVAULT_SERVICE",
    matches = "true"
)
class AzureKeyVaultServiceTest: KeyVaultServiceBaseTest() {
    val vaultName = "otai-shared-vault"
    val loadedSecrets = mapOf(
        "test-otai-test-secret" to "t3st-0tai-t3st-s3cr3t"
    )

    override fun getSpec(): Spec {
        val azureKeyVaultService = AzureKeyVaultService(
            AzureKeyVaultService.AzureKeyVaultServiceConfig(
                keyVaultName = vaultName,
                credentialStrategy = "AZURE_CLI_CREDENTIAL"
            )
        )
        return Spec(azureKeyVaultService, vaultName, loadedSecrets)
    }
}
