package services.oneteam.ai.shared.domains.collection.form

import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.workspace.BaseSection

class SetAnswerParamsTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun `should serialize`() {

        val body = SetAnswerParams(
            listOf("a", "b", "c"),
            FormAnswer.TextAnswer(BaseSection.Id("1"), "a"),
        )

        val json = Json.encodeToString(body)
        logger.debug(json)
        assertNotNull(json)
    }

    @Test
    fun `should deserialize`() {

        val json = """
            {"path":["a","b","c"],"formAnswer":{"type":"text","questionId":"1","value":"a"}}
        """.trimIndent()

        val body = Json.decodeFromString<SetAnswerParams>(json)
        logger.debug(body.toString())
        assertNotNull(body)
    }
}