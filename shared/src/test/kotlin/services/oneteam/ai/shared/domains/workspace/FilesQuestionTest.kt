package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse

class FilesQuestionTest {
    private val question = BaseSection.FilesQuestion(
        id = BaseSection.Id("1"),
        type = QuestionType.NUMBER,
        text = "Upload your identity documents",
        description = "description",
        identifier = "identifier",
        properties = CommonQuestionProperties.FilesQuestionProperties(
            required = true,
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate question`() {
        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should returns errors for empty text`() {
        val question = question.copy(text = "")
        BaseQuestionTestHelper.testEmptyText(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty identifier`() {
        val question = question.copy(identifier = "")
        BaseQuestionTestHelper.testEmptyIdentifier(question)
    }

}