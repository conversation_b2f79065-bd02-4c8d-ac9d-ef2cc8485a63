package services.oneteam.ai.shared.domains.flow.configuration

import kotlinx.serialization.ExperimentalSerializationApi
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.tuple
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.domains.workspace.validation.*
import services.oneteam.ai.shared.otSerializer
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import java.util.stream.Collectors

class FlowConfigurationValidationTest {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    val expectations = mapOf<String, List<ConstraintError>>(
        "flow-step-condition-without-branch.json" to listOf(
            expectation("branches", "required", "/root.steps.emhTVFhfBOggbV2rDKA0o.properties.branches")
        ),
        "flow-step-setvariables-without-variable.json" to listOf(
            expectation("variables", "required", "/root.steps.emhTVFhfBOggbV2rDKA0o.properties.variables")
        ),

        "flow-step-foreach-missing-inputs.json" to listOf(
            expectation("list", "required", "/root.steps.step1.properties.inputs.list"),
            expectation("isReturn", "required", "/root.steps.step1.properties.inputs.isReturn"),
            expectation("itemVariableName", "required", "/root.steps.step1.properties.inputs.itemVariableName")
        ),

        "flow-in-flow-step-without-flow-configuration-id.json" to listOf(
            expectation("flowConfigurationId", "required", "/root.steps.step1.properties.inputs.flowConfigurationId")
        ),

        "flow-in-flow-step-without-required-input.json" to listOf(
            expectation("foundation", "required", "/root.steps.step1.properties.inputs.foundation")
        ),
    )

    fun expectation(key: String, type: String, path: String): ConstraintError {
        return ConstraintError(Field(key), Type(type), Path(path), null, Message(""), null)
    }

    @Test
    fun `test all invalid documents`() {
        val invalidFiles = getFiles("src/test/resources/validation/flowconfiguration/invalid")
        validateFiles(invalidFiles)
    }

    @Test
    fun `test all valid documents`() {
        val validFiles = getFiles("src/test/resources/validation/flowconfiguration/valid")
        validateFiles(validFiles)
    }

    private fun getFiles(directory: String): List<File> {
        return Files.walk(Paths.get(directory)).filter { Files.isRegularFile(it) }.map { it.toFile() }
            .collect(Collectors.toList())
    }

    @OptIn(ExperimentalSerializationApi::class)
    private fun validateFiles(files: List<File>) {
        val stepTypeConfigurations = Fixtures.stepTypeConfigurations()
        val helperFlows = Fixtures.helperFlows()

        files.forEach { file ->
            val input = file.readText()
            var flowConfiguration = otSerializer.decodeFromString<FlowConfiguration.ForJson>(input)
            var errors = flowConfiguration.validateConfiguration(
                "/root", WorkspaceValidationContext(stepTypeConfigurations, helperFlows)
            )

            if (!errors.hasErrors()) {
                logger.debug("${file.name} -> VALID")
            }
            errors.getErrors().forEach {
                logger.debug("{} -> {}", file.name, it)
            }

            if (expectations.containsKey(file.name)) {
                val expectedErrors = expectations[file.name]
                assertThat(errors.getErrors()).extracting("type", "path", "key").containsExactlyInAnyOrder(
                    *expectedErrors!!.map { tuple(it.type.value, it.path.value, it.key.value) }.toTypedArray()
                )
            } else {
                assertThat(errors.hasErrors()).isFalse
            }
        }
    }

}