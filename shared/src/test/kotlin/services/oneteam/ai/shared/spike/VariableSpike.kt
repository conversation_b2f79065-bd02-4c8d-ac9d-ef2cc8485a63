package services.oneteam.ai.shared.spike

import org.junit.jupiter.api.Test

class VariableSpike {

    var timeoutValue = -1

    fun setTimeoutMillis(timeoutMillis: Int) {
        timeoutValue = timeoutMillis
    }

    fun configure(timeoutMillis: Int?) {
        if (timeoutMillis?.let { it > 0 } == true) {
            setTimeoutMillis(timeoutMillis)
        }
    }

    @Test
    fun `should set timeout when value is greater than 0`() {
        configure(1000)
        assert(timeoutValue == 1000) { "Timeout should be set to 1000" }
    }

    @Test
    fun `should not set timeout when value is null`() {
        configure(null)
        assert(timeoutValue == -1) { "Timeout should remain -1 when value is null" }
    }

    @Test
    fun `should not set timeout when value is less than or equal to 0`() {
        configure(0)
        assert(timeoutValue == -1) { "Timeout should remain -1 when value is 0" }

        configure(-100)
        assert(timeoutValue == -1) { "Timeout should remain -1 when value is negative" }
    }

}