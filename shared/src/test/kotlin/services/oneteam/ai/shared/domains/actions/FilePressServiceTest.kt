package services.oneteam.ai.shared.domains.actions;

import io.kotest.matchers.equals.shouldBeEqual
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.string.shouldContain
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.collection.form.BlobService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.workspace.Workspace

class FilePressServiceTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun `getNameForGeneratedFile fallback to template name`() = runTest {
        val name = FilePressService.getNameForGeneratedFile(
            templateUrl = "https://filepress.oneteam.services/mock-template.docx",
            filename = null,
            fileExtension = "docx"
        )
        name.shouldBeEqual("generated-mock-template.docx")
    }

    @Test
    fun `getNameForGeneratedFile used filename if provided`() = runTest {
        val name = FilePressService.getNameForGeneratedFile(
            templateUrl = "https://filepress.oneteam.services/mock-template.docx",
            filename = "myFile.docx",
            fileExtension = "docx"
        )
        name.shouldBeEqual("myFile.docx")
    }

    @Test
    fun `getNameForGeneratedFile should always use the provided fileExtension`() = runTest {
        val name = FilePressService.getNameForGeneratedFile(
            templateUrl = "https://filepress.oneteam.services/mock-template.docx",
            filename = "myFile.xlsx",
            fileExtension = "docx"
        )
        name.shouldBeEqual("myFile.docx")
    }

    @Test
    fun `returns a generated file`() = runTest {
        val uploadService = BlobService(
            "storageName",
            "thisisnotarealaccesskey",
            "upload"
        )

        val proxyService = mockk<ExternalProxyService>()
        coEvery { proxyService.call(any()) } returns ProxyService.ProxyEndpointResponse(
            response = """
        {
          "message": "File uploaded successfully to https://mock-location"
        }
    """.trimIndent(), status = ProxyService.ProxyEndpointResponseStatus.SUCCESS, error = null
        )

        mockkObject(ProxyService)
        coEvery { ProxyService.buildInternalTenantUrl(any()) } returns "http://localhost:8080/ai/api"

        val otStorageService = mockk<OTStorageService>()
        val filePressService = FilePressService(
            serviceEndpointUrl = "https://filepress.oneteam.services/",
            proxyService = proxyService,
            blobService = uploadService,
            otStorageService = otStorageService
        )

        val workspaceId = Workspace.Id(1L)
        val result = filePressService.generateDocxFromRemoteTemplate(
            tenantId = 1L,
            workspaceId = workspaceId,
            templateUrl = "https://filepress.oneteam.services/mock-template.docx",
            replacements = Json.encodeToJsonElement(mapOf("key" to "value")),
            filename = null
        )

        result.path.shouldContain(Regex("/upload/1%2F${workspaceId.value}%2Ftmp%2F.*"))
        result.name.shouldBeEqual("generated-mock-template.docx")
    }

    @Test
    fun `throws error on failure`() = runTest {
        val uploadService = BlobService(
            "storageName",
            "thisisnotarealaccesskey",
            "upload"
        )

        val proxyService = mockk<ExternalProxyService>()
        coEvery { proxyService.call(any()) } returns ProxyService.ProxyEndpointResponse(
            response = null,
            status = ProxyService.ProxyEndpointResponseStatus.FAIL,
            error = "File upload failed"
        )
        mockkObject(ProxyService)
        coEvery { ProxyService.buildInternalTenantUrl(any()) } returns "http://localhost:8080/ai/api"

        val otStorageService = mockk<OTStorageService>()
        val filePressService = FilePressService(
            serviceEndpointUrl = "https://filepress.oneteam.services/",
            proxyService = proxyService,
            blobService = uploadService,
            otStorageService = otStorageService
        )

        val workspaceId = Workspace.Id(1L)
        try {
            filePressService.generateDocxFromRemoteTemplate(
                tenantId = 1L,
                workspaceId = workspaceId,
                templateUrl = "https://filepress.oneteam.services/mock-template.docx",
                replacements = Json.encodeToJsonElement(mapOf("key" to "value")),
                filename = null
            )
        } catch (e: Exception) {
            e.message.shouldContain("File upload failed")
        }
    }
}
