package services.oneteam.ai.shared.domains.passvault

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import services.oneteam.ai.shared.domains.tenant.Tenant
import java.security.SecureRandom
import java.util.*
import javax.crypto.AEADBadTagException
import javax.crypto.spec.SecretKeySpec

class PassVaultTest {
    fun mockTenant(): Tenant {
        return Tenant(999, "otai-test", "test", "http://localhost:8000", "http://localhost:8000/sync")
    }

    @Test
    fun `can encrypt and decrypt data using a key`() {
        val key = SecureRandom().generateSeed(32)
        val encryptionKey = SecretKeySpec(key, "AES")
        val locksmith = PassVaultLocksmith(encryptionKey);

        val data = "Hello, World! {..}"
        val encryptedData = locksmith.encrypt(data)
        val decryptedData = locksmith.decrypt(encryptedData)
        assert(decryptedData == data) { "Decrypted data does not match original data" }
    }

    @Test
    fun `can encrypt and decrypt data using a key from a KV`() {
        val tenant = mockTenant()
        val kv = InMemoryKeyVaultService(
            tenant.name,
            "test"
        )
        val key = SecureRandom().generateSeed(32)
        val secretName = PassVault.dataEncryptionKeyName(tenant.name)
        kv.addSecret(secretName, Base64.getEncoder().encodeToString(key))
        val locksmith = PassVault(kv).getTenantLocksmith(tenant)

        val data = "Hello, World! {..}"
        val encryptedData = locksmith.encrypt(data)
        val decryptedData = locksmith.decrypt(encryptedData)
        assert(decryptedData == data) { "Decrypted data does not match original data" }
    }

    @Test
    fun `throws exception if tenant key is invalid encoding`() {
        val tenant = mockTenant()
        val kv = InMemoryKeyVaultService(
            tenant.name,
            "test"
        )
        val key = "this-is-an-invalid-key"
        val secretName = PassVault.dataEncryptionKeyName(tenant.name)
        kv.addSecret(secretName, key)

        val error = assertThrows<IllegalArgumentException> { PassVault(kv).getTenantLocksmith(tenant) }
        assert(error.message == "Illegal base64 character 2d") { "Expected Base64 decoding error, got: ${error.message}" }
    }

    @Test
    fun `throws exception ciphertext has been tampered`() {
        val tenant = mockTenant()
        val kv = InMemoryKeyVaultService(
            tenant.name,
            "test"
        )
        val secretName = PassVault.dataEncryptionKeyName(tenant.name)
        // If we used SecureRandom() for each test run, our tampering efforts might not work as expected. so we use a fixed key.
        kv.addSecret(secretName, "dH8z74kiARodaD3AFdBj3FdC1UubbgCESGgxF/YaCZg=")
        val locksmith = PassVault(kv).getTenantLocksmith(tenant)

        val data = "Hello, World! {..}"
        val encryptedData = locksmith.encrypt(data)
        val decryptError1 = assertThrows<IllegalArgumentException> {
            val lastCharacter = encryptedData.last();
            val replacement = if (lastCharacter == 'X') "Y" else "X"
            val tamperedData = encryptedData.dropLast(1) + replacement
            locksmith.decrypt(tamperedData)
        }
        assert(decryptError1.message == "Input byte array has wrong 4-byte ending unit") { "Expected decryption to fail, got: ${decryptError1.message}" }

        val decryptError2 = assertThrows<IllegalArgumentException> {
            val tamperedData = encryptedData.drop(1)
            locksmith.decrypt(tamperedData)
        }
        assert(decryptError2.message == "Last unit does not have enough valid bits") { "Expected decryption to fail, got: ${decryptError2.message}" }

        val decryptError3 = assertThrows<AEADBadTagException> {
            val firstCharacter = encryptedData.first();
            val replacement = if (firstCharacter == 'A') "B" else "A"
            val tamperedData = replacement + encryptedData.drop(1)
            locksmith.decrypt(tamperedData)
        }
        assert(decryptError3.message == "Tag mismatch") { "Expected decryption to fail, got: ${decryptError3.message}" }
    }

    @Test
    fun `throws exception if tenant key length is invalid when attempting to be used`() {
        val tenant = mockTenant()
        val kv = InMemoryKeyVaultService(
            tenant.name,
            "test"
        )
        val key = SecureRandom().generateSeed(64)
        val secretName = PassVault.dataEncryptionKeyName(tenant.name)
        kv.addSecret(secretName, Base64.getEncoder().encodeToString(key))

        val initError = assertThrows<IllegalArgumentException> { PassVault(kv).getTenantLocksmith(tenant) }
        assert(initError.message == "Key size must be 256 bits") { "Expected key length error, got: ${initError.message}" }
    }

    @Test
    fun `throws exception if tenant key not present`() {
        val tenant = mockTenant()
        val kv = InMemoryKeyVaultService(
            tenant.name,
            "test"
        )
        val key = "this-is-an-invalid-key"
        kv.addSecret("different-key", key)

        val error = assertThrows<IllegalArgumentException> { PassVault(kv).getTenantLocksmith(tenant) }
        assert(error.message == "Secret with name 'test-otai-test-passVault-dataEncryptionKey' does not exist in Key Vault 'otai-test'.") {
            "Expected error for missing key, got: ${error.message}"
        }
    }

    @Test
    fun `can hash and compare data using a salt`() {
        val hasher = PassVaultHasher()

        val data = "Hello, World! {..}"
        val hash = hasher.hash(data)
        assert(hasher.compareWithHash(data, hash)) { "Hash comparison should pass" }

        // Test with different data
        val differentData = "Hello, World! {..} Different"
        assert(!hasher.compareWithHash(differentData, hash)) { "Hash comparison should fail for different data" }
    }

    @Test
    fun `compareHash correctly fails if different salts are used`() {
        val hasher1 = PassVaultHasher()
        val hasher2 = PassVaultHasher()

        val data = "Hello, World! {..}"
        val hash2 = hasher2.hash(data)
        assert(hasher1.compareWithHash(data, hash2)) { "Hash comparison should pass" }

        // Test with different data
        val differentData = "Hello, World! {..} Different"
        assert(
            !hasher1.compareWithHash(
                differentData,
                hash2
            )
        ) { "Hash comparison should fail for different hashing salts" }
    }

    @Test
    fun `can hash and compare data using a hashingSalt from a KV`() {
        val tenant = mockTenant()
        val kv = InMemoryKeyVaultService(
            tenant.name,
            "test"
        )
        val hasher = PassVault(kv).getHasher()

        val data = "Hello, World! {..}"
        val hash = hasher.hash(data)
        assert(hasher.compareWithHash(data, hash)) { "Hash comparison should pass" }

        // Test with different data
        val differentData = "Hello, World! {..} Different"
        assert(!hasher.compareWithHash(differentData, hash)) { "Hash comparison should fail for different data" }
    }

}
