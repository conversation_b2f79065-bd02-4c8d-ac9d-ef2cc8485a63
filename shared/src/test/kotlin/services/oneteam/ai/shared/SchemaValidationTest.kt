package services.oneteam.ai.shared

import com.networknt.schema.InputFormat
import com.networknt.schema.JsonSchemaFactory
import com.networknt.schema.SpecVersion
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import java.util.stream.Collectors

/**
 * Most popular JSON schema validator: https://github.com/networknt/json-schema-validator
 * Easily allows you to access constraint type and details about it (e.g. for min length get detail of 2)
 **/

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SchemaValidationTest {
    private lateinit var documentSchema: com.networknt.schema.JsonSchema

    @BeforeAll
    fun setup() {
        val schemaPath = "src/main/resources/documentSchema.json"
        val jsonSchemaFactory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012)
        documentSchema = jsonSchemaFactory.getSchema(File(schemaPath).readText())
    }

    private fun getFiles(directory: String): List<File> {
        return Files.walk(Paths.get(directory))
            .filter { Files.isRegularFile(it) }
            .map { it.toFile() }
            .collect(Collectors.toList())
    }

    private fun validateFiles(files: List<File>) {
        files.forEach { file ->
            val input = file.readText()
            val assertions = documentSchema.validate(input, InputFormat.JSON)
            if (assertions.isEmpty()) {
                println("${file.name} -> VALID")
            }
            assertions.forEach {
                println("${file.name} -> $it")
            }
            if (file.absolutePath.contains("/invalid/")) {
                assert(assertions.isNotEmpty()) { "File ${file.name} does not have errors when it should" }
            } else {
                assert(assertions.isEmpty()) { "File ${file.name} has errors when it should not" }
            }
        }
    }

    @Test
    fun `test all valid documents`() {
        val validFiles = getFiles("src/test/resources/schema.validation/document/valid")
        validateFiles(
            validFiles + listOf(
                File("src/main/resources/sample-data/sgpit/sgpit-workspace-config.json"),
                File("src/main/resources/sample-data/testrunner/testrunner-workspace-config.json")
            )
        )
    }

    @Test
    fun `test all invalid documents`() {
        // TODO we need to do some maintenance on these invalid files - they have many more errors than the ones we are testing for...
        val invalidFiles = getFiles("src/test/resources/schema.validation/document/invalid")
        validateFiles(invalidFiles)
    }
}