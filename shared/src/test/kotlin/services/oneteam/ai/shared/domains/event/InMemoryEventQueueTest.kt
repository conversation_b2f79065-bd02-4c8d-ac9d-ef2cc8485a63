package services.oneteam.ai.shared.domains.event

import io.kotest.matchers.shouldBe
import kotlinx.coroutines.test.runTest
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.event.Event.ForJson
import services.oneteam.ai.shared.domains.event.Event.Id
import services.oneteam.ai.shared.domains.workspace.Workspace
import java.time.Instant

class InMemoryEventQueueTest {
    @org.junit.jupiter.api.Test
    fun `should push event into queue`() = runTest {
        val queue = InMemoryEventQueue()
        val event = mockEvent()
        queue.push(event)
        queue.size().shouldBe(1)
    }

    @org.junit.jupiter.api.Test
    fun `should poll event from queue`() = runTest {
        val queue = InMemoryEventQueue()
        val event = mockEvent()
        queue.push(event)
        queue.poll().shouldBe(event)
        queue.size().shouldBe(0)
    }

    @org.junit.jupiter.api.Test
    fun `should be FIFO`() = runTest {
        val queue = InMemoryEventQueue()
        val event1 = mockEvent()
        val event2 = mockEvent().copy(id = Id("2"))
        queue.push(event1)
        queue.push(event2)
        queue.size().shouldBe(2)
        queue.poll().shouldBe(event1)
        queue.poll().shouldBe(event2)
        queue.size().shouldBe(0)
    }
}

fun mockEvent(): ForJson {
    return ForJson(
        id = Id("1"),
        workspaceId = Workspace.Id(1),
        eventProperties = Event.EventProperties.StartFlowManuallyFromFormProperties(
            null,
            null,
            null,
        ),
        status = EventStatus.QUEUED,
        tenantId = 1,
        entityMetadata = EntityMetadata(Instant.now(), Instant.now())
    )
}