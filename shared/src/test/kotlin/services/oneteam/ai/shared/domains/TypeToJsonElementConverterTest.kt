package services.oneteam.ai.shared.domains

import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class TypeToJsonElementConverterTest {

    @Test
    fun `should convert text type to JsonPrimitive`() {
        val result = TypeToJsonElementConverter.convert(VariableDataType.TEXT, "Hello")
        assertEquals(JsonPrimitive("Hello"), result)
    }

    @Test
    fun `should convert number type to JsonPrimitive`() {
        val result = TypeToJsonElementConverter.convert(VariableDataType.NUMBER, "123.45")
        assertEquals(JsonPrimitive("123.45".toBigDecimal()), result)
    }

    @Test
    fun `should convert boolean type to JsonPrimitive`() {
        val result = TypeToJsonElementConverter.convert(VariableDataType.BOOLEAN, "true")
        assertEquals(JsonPrimitive(true), result)
    }

    @Test
    fun `should convert list type to JsonArray`() {
        val list = listOf("item1", "item2", "item3")
        val result = TypeToJsonElementConverter.convert(VariableDataType.LIST, list)
        assertEquals(JsonArray(list.map { JsonPrimitive(it) }), result)
    }

    //TODO: VARIABLE REFACTOR
//    @Test
//    fun `should throw exception for unsupported type`() {
//        val exception = assertThrows(IllegalArgumentException::class.java) {
//            TypeToJsonElementConverter.convert(VariableType.fromType("unsupported"), "value")
//        }
//        assertEquals("Unsupported type `unsupported`", exception.message)
//    }

    @Test
    fun `should return default value for text type`() {
        val result = TypeToJsonElementConverter.defaultByType(VariableDataType.TEXT)
        assertEquals(JsonPrimitive(""), result)
    }

    @Test
    fun `should return default value for number type`() {
        val result = TypeToJsonElementConverter.defaultByType(VariableDataType.NUMBER)
        assertEquals(JsonPrimitive(0), result)
    }

// TODO: VARIABLE REFACTOR
//    @Test
//    fun `should return JsonNull for unsupported default type`() {
//        val result = TypeToJsonElementConverter.defaultByType(VariableType.fromType("unsupported"))
//        assertEquals(JsonNull, result)
//    }

    @Test
    fun `should convert map to JsonObject`() {
        // Prepare
        val map = mapOf(
            "key1" to "value1", "key2" to 123, "key3" to true, "key4" to null
        )

        // Perform
        val result = TypeToJsonElementConverter.toJsonElement(map)

        // Verify
        val expected = JsonObject(
            mapOf(
                "key1" to JsonPrimitive("value1"),
                "key2" to JsonPrimitive(123),
                "key3" to JsonPrimitive(true),
                "key4" to JsonNull
            )
        )
        assertEquals(expected, result)
    }

    @Test
    fun `should convert nested map and list to JsonObject`() {
        // Prepare
        val nestedMap = mapOf(
            "key1" to "value1", "key2" to listOf(1, 2, 3), "key3" to mapOf(
                "nestedKey1" to "nestedValue1", "nestedKey2" to listOf("a", "b", "c"), "nestedKey3" to null
            ), "key4" to null
        )

        // Perform
        val result = TypeToJsonElementConverter.toJsonElement(nestedMap)

        // Verify
        val expected = JsonObject(
            mapOf(
                "key1" to JsonPrimitive("value1"),
                "key2" to JsonArray(listOf(JsonPrimitive(1), JsonPrimitive(2), JsonPrimitive(3))),
                "key3" to JsonObject(
                    mapOf(
                        "nestedKey1" to JsonPrimitive("nestedValue1"),
                        "nestedKey2" to JsonArray(listOf(JsonPrimitive("a"), JsonPrimitive("b"), JsonPrimitive("c"))),
                        "nestedKey3" to JsonNull
                    )
                ),
                "key4" to JsonNull
            )
        )
        assertEquals(expected, result)
    }
}