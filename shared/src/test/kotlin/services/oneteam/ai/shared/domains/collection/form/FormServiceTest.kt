package services.oneteam.ai.shared.domains.collection.form

import io.kotest.matchers.string.shouldContain
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.otSerializer
import java.time.Instant

class FormServiceTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun buildUrlSignature() = runTest {
        val uploadService = BlobService(
            "storageName",
            "Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==",
            "upload"
        )
        val formService = FormService(
            Mockito.mock<FormRepository>(),
            Mockito.mock<WorkspaceRepository>(),
            Mockito.mock<ApiDocumentService>(),
            Mockito.mock<WorkspaceVersionRepository>(),
            uploadService,
            Mockito.mock<FoundationService>(),
            Mockito.mock<WorkspaceVersionService>(),
            Checks()
        )

        val questionId = "aa4010"
        val rowId = "rId"
        val workspaceForJson = otSerializer.decodeFromString(
            Workspace.ForJson.serializer(),
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )


        val formEntity = Form.ForApi(
            id = Form.Id(3L),
            formConfigurationId = FormConfiguration.Id("al"),
            foundationId = Foundation.Id(5L),
            intervalId = null,
            seriesId = null,
            documentId = null,
            workspaceId = Workspace.Id(1L),
            metadata = EntityMetadata(Instant.now(), Instant.now()),
            foundation = Foundation.ForApi(
                id = Foundation.Id(6L),
                name = Foundation.Name("fname"),
                key = Foundation.Key("fkey"),
                foundationConfigurationId = FoundationConfiguration.Id("fcId"),
                parentId = null,
                workspaceId = Workspace.Id(1L),
                metadata = EntityMetadata(Instant.now(), Instant.now()),
            )
        )
        val url = formService.buildSignedUrl(1L, workspaceForJson, formEntity, questionId, rowId)
        url shouldContain Regex("https://storageName.blob.core.windows.net/upload/1%2F${workspaceForJson.id.value}%2F3%2Faa4010%2FrId%2F.*")
    }
}