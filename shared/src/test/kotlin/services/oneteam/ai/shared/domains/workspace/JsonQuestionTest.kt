package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import services.oneteam.ai.shared.domains.workspace.validation.Message

class JsonQuestionTest {
    private val question = BaseSection.JsonQuestion(
        id = BaseSection.Id("1"),
        type = QuestionType.NUMBER,
        text = "Who are your friends?",
        description = "description",
        identifier = "identifier",
        properties = CommonQuestionProperties.JsonQuestionProperties(
            required = true,
            items = listOf(
                BaseSection.TextQuestion(
                    id = BaseSection.Id("1"),
                    type = QuestionType.TEXT,
                    text = "First Name",
                    description = "first name",
                    identifier = "firstName",
                    properties = CommonQuestionProperties.TextQuestionProperties(
                        required = true
                    )
                ),
                BaseSection.TextQuestion(
                    id = BaseSection.Id("2"),
                    type = QuestionType.TEXT,
                    text = "Last Name",
                    description = "last name",
                    identifier = "lastName",
                    properties = CommonQuestionProperties.TextQuestionProperties(
                        required = true
                    )
                ),
                BaseSection.TextQuestion(
                    id = BaseSection.Id("3"),
                    type = QuestionType.TEXT,
                    text = "Age",
                    description = "age",
                    identifier = "age",
                    properties = CommonQuestionProperties.TextQuestionProperties(
                        required = true
                    )
                )
            )
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate question`() {
        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty text`() {
        val question = question.copy(text = "")
        BaseQuestionTestHelper.testEmptyText(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty identifier`() {
        val question = question.copy(identifier = "")
        BaseQuestionTestHelper.testEmptyIdentifier(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for child`() {
        val question = question.copy(
            properties = question.properties?.copy(
                items = listOf(
                    BaseSection.TextQuestion(
                        id = BaseSection.Id("1"),
                        type = QuestionType.TEXT,
                        text = "",
                        description = "first name",
                        identifier = "firstName",
                        properties = CommonQuestionProperties.TextQuestionProperties(
                            required = true
                        )
                    ),
                )
            )
        )

        question.validateConfiguration("testPath").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(1)
            errors.getErrors()[0].message.shouldBe(Message("errors.common.name.required"))
        }
    }
}