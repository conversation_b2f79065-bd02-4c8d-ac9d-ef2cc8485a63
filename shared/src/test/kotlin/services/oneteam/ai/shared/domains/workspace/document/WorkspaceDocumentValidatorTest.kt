package services.oneteam.ai.shared.domains.workspace.document

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration.*
import services.oneteam.ai.shared.domains.workspace.validation.Errors
import services.oneteam.ai.shared.domains.workspace.validation.WorkspaceConfigValidatorBuilder
import services.oneteam.ai.shared.otSerializer
import java.time.Instant
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WorkspaceDocumentValidatorTest {

    private val logger = LoggerFactory.getLogger(javaClass)
    private val objectMapper = ObjectMapper()
    private val stepTypeConfigurations = Fixtures.stepTypeConfigurations()

    fun workspacePathProvider(): Stream<String> {
        return Stream.of(
            "/sample-data/sgpit/sgpit-workspace-config.json", "/sample-data/testrunner/testrunner-workspace-config.json"
        )
    }

    @ParameterizedTest
    @MethodSource("workspacePathProvider")
    fun `workspace should be valid`(path: String) {
        val workspaceForJson = otSerializer.decodeFromString<Workspace.ForJson>(
            this::class.java.getResource(path)!!.readText()
        )
        val errors = validate(workspaceForJson).getErrors()
        logger.debug(otSerializer.encodeToString(errors))
        errors.size shouldBe 0
    }

    @Test
    fun `duplicate names on foundations should be reported`() {

        val input = emptyWorkspace().copy(
            foundations = OrderedMap(
                listOf(
                    FoundationConfiguration.ForApi(
                        Id("ID1"),
                        Name("NAME"),
                        Description(""),
                        Relationship.OneToMany,
                        EntityMetadata(Instant.now(), Instant.now()),
                        Identifier("IDENTIFIER1"),
                    ), FoundationConfiguration.ForApi(
                        Id("ID2"),
                        Name("NAME"),
                        Description(""),
                        Relationship.OneToMany,
                        EntityMetadata(Instant.now(), Instant.now()),
                        Identifier("IDENTIFIER1"),
                    )
                )
            )
        )

        val validationResult = validate(input)

        validationResult.getErrors().size shouldBe 2
        validationResult.getErrors()[0].message.value shouldBe "errors.common.name.duplicate"
        validationResult.getErrors()[0].path.value shouldBe "$.foundations.entities.ID1.identifier"
        validationResult.getErrors()[1].path.value shouldBe "$.foundations.entities.ID2.identifier"

    }


    @Test
    fun `duplicate keys on forms should be reported`() {
//
//        val input = emptyWorkspace().copy(
//            forms = listOf(
//                FormConfiguration.ForJson(
//                    FormConfiguration.Id("ID1"),
//                    FormConfiguration.Name("NAME"),
//                    FormConfiguration.Key("KEY"),
//                    FormConfiguration.Description("DESCRIPTION"),
//                    FormConfiguration.Status("STATUS"),
//                    EntityMetadata(Instant.now(), Instant.now())
//                ),
//                FormConfiguration.ForJson(
//                    FormConfiguration.Id("ID2"),
//                    FormConfiguration.Name("NAME"),
//                    FormConfiguration.Key("KEY"),
//                    FormConfiguration.Description("DESCRIPTION"),
//                    FormConfiguration.Status("STATUS"),
//                    EntityMetadata(Instant.now(), Instant.now())
//                ),
//            )
//        )
//
//        val validationResult = validate(input)
//
//        validationResult.getErrors().size shouldBe 2
//        validationResult.getErrors()[0].message.value shouldBe "errors.common.key.duplicate"
//        validationResult.getErrors()[0].path.value shouldBe "$.forms[ID1].key"
//        validationResult.getErrors()[0].type.value shouldBe "duplicate"
//        validationResult.getErrors()[1].path.value shouldBe "$.forms[ID2].key"
//        validationResult.getErrors()[1].type.value shouldBe "duplicate"

    }

    @Test
    fun `duplicate names on series should be reported`() {

        val input = emptyWorkspace().copy(
            series = mapOf(
                SeriesConfiguration.Id("ID1") to SeriesConfiguration.ForApi(
                    SeriesConfiguration.Id("ID1"),
                    SeriesConfiguration.Name("NAME"),
                    SeriesConfiguration.Description("DESCRIPTION"),
                    OrderedMap(
                        listOf(
                            Interval(
                                IntervalId("ID1"),
                                IntervalName("NAME1"),
                            )
                        )
                    ),
                    EntityMetadata(Instant.now(), Instant.now())
                ), SeriesConfiguration.Id("ID2") to SeriesConfiguration.ForApi(
                    SeriesConfiguration.Id("ID2"),
                    SeriesConfiguration.Name("NAME"),
                    SeriesConfiguration.Description("DESCRIPTION"),
                    OrderedMap(
                        listOf(
                            Interval(
                                IntervalId("ID1"),
                                IntervalName("NAME1"),
                            )
                        )
                    ),
                    EntityMetadata(Instant.now(), Instant.now())
                ), SeriesConfiguration.Id("ID3") to SeriesConfiguration.ForApi(
                    SeriesConfiguration.Id("ID3"),
                    SeriesConfiguration.Name("NAME3"),
                    SeriesConfiguration.Description("DESCRIPTION"),
                    OrderedMap(
                        listOf(
                            Interval(
                                IntervalId("ID1"),
                                IntervalName("NAME1"),
                            )
                        )
                    ),
                    EntityMetadata(Instant.now(), Instant.now())
                )
            )
        )

        val validationResult = validate(input)

        validationResult.getErrors().size shouldBe 2
        validationResult.getErrors()[0].message.value shouldBe "errors.common.name.duplicate"
        validationResult.getErrors()[0].path.value shouldBe "$.series.ID1.name"
        validationResult.getErrors()[0].type.value shouldBe "duplicate"
        validationResult.getErrors()[1].path.value shouldBe "$.series.ID2.name"
        validationResult.getErrors()[1].type.value shouldBe "duplicate"

    }

    @Test
    fun `duplicate names on labels should be reported`() {

        val input = emptyWorkspace().copy(
            labels = mapOf(
                LabelConfiguration.Id("ID1") to LabelConfiguration.ForApi(
                    LabelConfiguration.Id("ID1"),
                    LabelConfiguration.Name("NAME"),
                    LabelConfiguration.Description("DESCRIPTION"),
                    LabelConfiguration.Color("COLOR"),
                    LabelConfiguration.AvailableTo(
                        listOf(
                            LabelAvailableTo.FLOW_CONFIGURATION, LabelAvailableTo.FORM_CONFIGURATION
                        )
                    ),
                    EntityMetadata(Instant.now(), Instant.now())
                ), LabelConfiguration.Id("ID2") to LabelConfiguration.ForApi(
                    LabelConfiguration.Id("ID2"),
                    LabelConfiguration.Name("NAME"),
                    LabelConfiguration.Description("DESCRIPTION"),
                    LabelConfiguration.Color("COLOR"),
                    LabelConfiguration.AvailableTo(
                        listOf(
                            LabelAvailableTo.FLOW_CONFIGURATION, LabelAvailableTo.FORM_CONFIGURATION
                        )
                    ),
                    EntityMetadata(Instant.now(), Instant.now())
                ), LabelConfiguration.Id("ID3") to LabelConfiguration.ForApi(
                    LabelConfiguration.Id("ID3"),
                    LabelConfiguration.Name("NAME3"),
                    LabelConfiguration.Description("DESCRIPTION"),
                    LabelConfiguration.Color("COLOR"),
                    LabelConfiguration.AvailableTo(
                        listOf(
                            LabelAvailableTo.FLOW_CONFIGURATION, LabelAvailableTo.FORM_CONFIGURATION
                        )
                    ),
                    EntityMetadata(Instant.now(), Instant.now())
                )
            )
        )

        val validationResult = validate(input)

        validationResult.getErrors().size shouldBe 2
        validationResult.getErrors()[0].message.value shouldBe "errors.common.name.duplicate"
        validationResult.getErrors()[0].path.value shouldBe "$.labels.ID1.name"
        validationResult.getErrors()[0].type.value shouldBe "duplicate"
        validationResult.getErrors()[1].path.value shouldBe "$.labels.ID2.name"
        validationResult.getErrors()[1].type.value shouldBe "duplicate"

    }

    /**
     * This test checks if duplicate names on intervals are be reported
     * The test case is as follows:
     * - Create a workspace with 3 series
     * - Only the first series had intervals with duplicate names
     * - This is to make sure series id in error path is correct
     */
    @Test
    fun `duplicate errors on intervals should be reported`() {

        val input = emptyWorkspace().copy(
            series = mapOf(
                SeriesConfiguration.Id("ID1") to SeriesConfiguration.ForApi(
                    SeriesConfiguration.Id("ID1"),
                    SeriesConfiguration.Name("NAME"),
                    SeriesConfiguration.Description("DESCRIPTION"),
                    OrderedMap(
                        listOf(
                            Interval(
                                IntervalId("ID1"),
                                IntervalName("NAME"),
                            ), Interval(
                                IntervalId("ID2"),
                                IntervalName("NAME"),
                            )
                        )
                    ),
                    EntityMetadata(Instant.now(), Instant.now())
                ), SeriesConfiguration.Id("ID2") to SeriesConfiguration.ForApi(
                    SeriesConfiguration.Id("ID2"),
                    SeriesConfiguration.Name("NAME2"),
                    SeriesConfiguration.Description("DESCRIPTION"),
                    OrderedMap(
                        listOf(
                            Interval(
                                IntervalId("ID1"),
                                IntervalName("NAME1"),
                            )
                        )
                    ),
                    EntityMetadata(Instant.now(), Instant.now())
                ), SeriesConfiguration.Id("ID3") to SeriesConfiguration.ForApi(
                    SeriesConfiguration.Id("ID3"),
                    SeriesConfiguration.Name("NAME3"),
                    SeriesConfiguration.Description("DESCRIPTION"),
                    OrderedMap(
                        listOf(
                            Interval(
                                IntervalId("ID1"),
                                IntervalName("NAME1"),
                            ), Interval(
                                IntervalId("ID2"),
                                IntervalName("NAME2"),
                            )
                        )
                    ),
                    EntityMetadata(Instant.now(), Instant.now())
                )
            )
        )

        val validationResult = validate(input)

        validationResult.getErrors().size shouldBe 2
        validationResult.getErrors()[0].message.value shouldBe "errors.common.name.duplicate"
        validationResult.getErrors()[0].path.value shouldBe "$.series.ID1.intervals.entities.ID1.name"
        validationResult.getErrors()[0].type.value shouldBe "duplicate"
        validationResult.getErrors()[1].path.value shouldBe "$.series.ID1.intervals.entities.ID2.name"
        validationResult.getErrors()[1].type.value shouldBe "duplicate"
    }

    @Test
    fun `should return no errors for valid foundation`() {

        val input = emptyWorkspace().copy(
            foundations = OrderedMap(
                listOf(
                    FoundationConfiguration.ForApi(
                        Id("ID1"),
                        Name("name1"),
                        Description("description1"),
                        Relationship.OneToMany,
                        EntityMetadata(Instant.now(), Instant.now()),
                        Identifier("IDENTIFIER1"),
                    )
                )
            )
        )

        val validationResult = validate(input)

        validationResult.getErrors().size shouldBe 0
    }

    @Test
    fun `should validate foundations`() {

        val input = emptyWorkspace().copy(
            foundations = OrderedMap(
                listOf(
                    FoundationConfiguration.ForApi(
                        Id("ID1"),
                        Name(""),
                        Description(""),
                        Relationship.OneToMany,
                        EntityMetadata(Instant.now(), Instant.now()),
                        Identifier("IDENTIFIER1"),
                    )
                )
            )
        )

        val validationResult = validate(input)

        validationResult.getErrors().size shouldBe 1
        validationResult.getErrors()[0].message.value shouldBe "\$.foundations.entities.ID1.name: must be at least 2 characters long"
    }

    fun emptyWorkspace() = Workspace.ForJson(
        Workspace.Id(1),
        Workspace.Name("TEST"),
        Workspace.Key("KEY"),
        Workspace.Description("Description"),
        OrderedMap(emptyList()),
        emptyMap(),
        OrderedMap(emptyList()),
        emptyMap(),
        emptyMap(),
        emptyMap(),
        EntityMetadata(Instant.now(), Instant.now()),
        errors = emptyList()
    )

    fun validate(input: Workspace.ForJson): Errors = runBlocking {
        val json = Json { encodeDefaults = true }
        val jsonString = json.encodeToString(input)
        logger.debug(jsonString)
        val jsonDocument: JsonNode = objectMapper.readTree(jsonString)

        val querySlot = slot<Map<String, List<String>>>()
        val stepTypeConfigurationService = mockk<FlowStepTypeConfigurationService>()
        coEvery {
            stepTypeConfigurationService.getAllByQuery(
                query = capture(querySlot)
            )
        } answers {
            val query = querySlot.captured
            val identifiers = query["primaryIdentifier"] ?: emptyList()
            return@answers identifiers.map { identifier ->
                stepTypeConfigurations[identifier]
            }.filter { it != null } as List<FlowStepTypeConfiguration>
        }

        val validator = WorkspaceConfigValidatorBuilder(stepTypeConfigurationService).build(
            jsonDocument
        )

        val validationResult = validator.validate(jsonDocument)
        logger.debug(json.encodeToString(validationResult.getErrors()))
        return@runBlocking validationResult
    }
}