package services.oneteam.ai.shared.domains.event

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EventSerializationTest {
    private val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun getJson(): Json {
        return Json(builderAction = {
            prettyPrint = true
            encodeDefaults = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }

    fun jsonProvider(): Stream<String> {
        return Stream.of(
            *EventKey.entries.map {
                this::class.java.getResource("/events/${it.name.lowercase()}.json")!!.readText()
            }.toTypedArray()
        )
    }

    fun eventProvider(): Stream<Event.ForJson> {
        return Stream.of(
            Events.startFlowManuallyFromForm(),
            Events.startFlowManuallyFromFoundation(),
            Events.createCollectionFoundation(),
            Events.updateCollectionFoundation(),
            Events.deleteCollectionFoundation(),
            Events.createCollectionForm(),
            Events.updateCollectionForm(),
            Events.deleteCollectionForm(),
            Events.updateCollectionFormAnswer(),
        )
    }

    @ParameterizedTest
    @MethodSource("jsonProvider")
    fun `test deserialization ForCreate`(json: String) {
        val eventJson = getJson().decodeFromString(Event.ForCreate.serializer(), json)
        logger.debug(eventJson.toString())
        assertNotNull(eventJson)
    }

    @ParameterizedTest
    @MethodSource("eventProvider")
    fun `test serialization and deserialization ForJson`(event: Event.ForJson) {
        val serialized = getJson().encodeToString(Event.ForJson.serializer(), event)
        logger.debug(serialized)

        val deserialized = getJson().decodeFromString(Event.ForJson.serializer(), serialized)
        logger.debug(deserialized.toString())

        assertNotNull(serialized)
        assertNotNull(deserialized)
    }

    class Events {
        companion object {
            fun startFlowManuallyFromForm(): Event.ForJson {
                return Event.ForJson(
                    workspaceId = Workspace.Id(1),
                    eventProperties = Event.EventProperties.StartFlowManuallyFromFormProperties(
                        buttonLabel = "Start Flow",
                        form = Event.FormMinimal(
                            id = 1,
                            foundationId = 1,
                            formConfiguration = Event.FormConfigurationMinimal(
                                id = "formConfigurationId1",
                                key = "formConfigurationKey1",
                                name = "This is form configuration name",
                                seriesId = "seriesId1",
                            ),
                            documentId = "documentId1",
                            intervalId = "intervalId1",
                            foundation = Event.FoundationMinimal(
                                id = Foundation.Id(1),
                                name = Foundation.Name("This is foundation name"),
                                key = Foundation.Key("foundationKey1"),
                                foundationConfiguration = Event.FoundationConfigurationMinimal(
                                    id = FoundationConfiguration.Id("foundationConfigurationId1"),
                                    name = FoundationConfiguration.Name("This is foundation configuration name"),
                                    description = FoundationConfiguration.Description("This is foundation configuration description"),
                                    relationship = FoundationConfiguration.Relationship.OneToMany,
                                    parentId = FoundationConfiguration.Id("parentFoundationConfigurationId1"),
                                ),
                                parentId = Foundation.Id(2),
                            ),
                        ),
                        userId = 1,
                    ),
                    id = Event.Id("event1"),
                    status = EventStatus.QUEUED,
                    tenantId = 1,
                    entityMetadata = EntityMetadata.now(),
                )
            }

            fun startFlowManuallyFromFoundation(): Event.ForJson {
                return Event.ForJson(
                    workspaceId = Workspace.Id(1),
                    eventProperties = Event.EventProperties.StartFlowManuallyFromFoundationProperties(
                        buttonLabel = "Start Flow",
                        foundation = Event.FoundationMinimal(
                            id = Foundation.Id(1),
                            name = Foundation.Name("This is foundation name"),
                            key = Foundation.Key("foundationKey1"),
                            foundationConfiguration = Event.FoundationConfigurationMinimal(
                                id = FoundationConfiguration.Id("foundationConfigurationId1"),
                                name = FoundationConfiguration.Name("This is foundation configuration name"),
                                description = FoundationConfiguration.Description("This is foundation configuration description"),
                                relationship = FoundationConfiguration.Relationship.OneToMany,
                                parentId = FoundationConfiguration.Id("parentFoundationConfigurationId1"),
                            ),
                            parentId = Foundation.Id(2),
                        ),
                        userId = 1,
                    ),
                    id = Event.Id("event1"),
                    status = EventStatus.QUEUED,
                    tenantId = 1,
                    entityMetadata = EntityMetadata.now(),
                )
            }

            fun createCollectionFoundation(): Event.ForJson {
                return Event.ForJson(
                    workspaceId = Workspace.Id(1),
                    eventProperties = Event.EventProperties.CreateCollectionFoundationProperties(
                        foundation = Event.FoundationMinimal(
                            id = Foundation.Id(1),
                            name = Foundation.Name("This is foundation name"),
                            key = Foundation.Key("foundationKey1"),
                            foundationConfiguration = Event.FoundationConfigurationMinimal(
                                id = FoundationConfiguration.Id("foundationConfigurationId1"),
                                name = FoundationConfiguration.Name("This is foundation configuration name"),
                                description = FoundationConfiguration.Description("This is foundation configuration description"),
                                relationship = FoundationConfiguration.Relationship.OneToMany,
                                parentId = FoundationConfiguration.Id("parentFoundationConfigurationId1"),
                            ),
                            parentId = Foundation.Id(2),
                        )
                    ),
                    id = Event.Id("event1"),
                    status = EventStatus.QUEUED,
                    tenantId = 1,
                    entityMetadata = EntityMetadata.now(),
                )
            }

            fun updateCollectionFoundation(): Event.ForJson {
                return Event.ForJson(
                    workspaceId = Workspace.Id(1),
                    eventProperties = Event.EventProperties.UpdateCollectionFoundationProperties(
                        foundation = Event.FoundationMinimal(
                            id = Foundation.Id(1),
                            name = Foundation.Name("This is foundation name"),
                            key = Foundation.Key("foundationKey1"),
                            foundationConfiguration = Event.FoundationConfigurationMinimal(
                                id = FoundationConfiguration.Id("foundationConfigurationId1"),
                                name = FoundationConfiguration.Name("This is foundation configuration name"),
                                description = FoundationConfiguration.Description("This is foundation configuration description"),
                                relationship = FoundationConfiguration.Relationship.OneToMany,
                                parentId = FoundationConfiguration.Id("parentFoundationConfigurationId1"),
                            ),
                            parentId = Foundation.Id(2),
                        )
                    ),
                    id = Event.Id("event1"),
                    status = EventStatus.QUEUED,
                    tenantId = 1,
                    entityMetadata = EntityMetadata.now(),
                )
            }

            fun deleteCollectionFoundation(): Event.ForJson {
                return Event.ForJson(
                    workspaceId = Workspace.Id(1),
                    eventProperties = Event.EventProperties.DeleteCollectionFoundationProperties(
                        foundation = Event.FoundationMinimal(
                            id = Foundation.Id(1),
                            name = Foundation.Name("This is foundation name"),
                            key = Foundation.Key("foundationKey1"),
                            foundationConfiguration = Event.FoundationConfigurationMinimal(
                                id = FoundationConfiguration.Id("foundationConfigurationId1"),
                                name = FoundationConfiguration.Name("This is foundation configuration name"),
                                description = FoundationConfiguration.Description("This is foundation configuration description"),
                                relationship = FoundationConfiguration.Relationship.OneToMany,
                                parentId = FoundationConfiguration.Id("parentFoundationConfigurationId1"),
                            ),
                            parentId = Foundation.Id(2),
                        )
                    ),
                    id = Event.Id("event1"),
                    status = EventStatus.QUEUED,
                    tenantId = 1,
                    entityMetadata = EntityMetadata.now(),
                )
            }

            fun createCollectionForm(): Event.ForJson {
                return Event.ForJson(
                    workspaceId = Workspace.Id(1),
                    eventProperties = Event.EventProperties.CreateCollectionFormProperties(
                        form = Event.FormMinimal(
                            id = 1,
                            foundationId = 1,
                            formConfiguration = Event.FormConfigurationMinimal(
                                id = "formConfigurationId1",
                                key = "formConfigurationKey1",
                                name = "This is form configuration name",
                                seriesId = "seriesId1",
                            ),
                            documentId = "documentId1",
                            intervalId = "intervalId1",
                            foundation = Event.FoundationMinimal(
                                id = Foundation.Id(1),
                                name = Foundation.Name("This is foundation name"),
                                key = Foundation.Key("foundationKey1"),
                                foundationConfiguration = Event.FoundationConfigurationMinimal(
                                    id = FoundationConfiguration.Id("foundationConfigurationId1"),
                                    name = FoundationConfiguration.Name("This is foundation configuration name"),
                                    description = FoundationConfiguration.Description("This is foundation configuration description"),
                                    relationship = FoundationConfiguration.Relationship.OneToMany,
                                    parentId = FoundationConfiguration.Id("parentFoundationConfigurationId1"),
                                ),
                                parentId = Foundation.Id(2),
                            ),
                        )
                    ),
                    id = Event.Id("event1"),
                    status = EventStatus.QUEUED,
                    tenantId = 1,
                    entityMetadata = EntityMetadata.now(),
                )
            }

            fun updateCollectionForm(): Event.ForJson {
                return Event.ForJson(
                    workspaceId = Workspace.Id(1),
                    eventProperties = Event.EventProperties.UpdateCollectionFormProperties(
                        form = Event.FormMinimal(
                            id = 1,
                            foundationId = 1,
                            formConfiguration = Event.FormConfigurationMinimal(
                                id = "formConfigurationId1",
                                key = "formConfigurationKey1",
                                name = "This is form configuration name",
                                seriesId = "seriesId1",
                            ),
                            documentId = "documentId1",
                            intervalId = "intervalId1",
                            foundation = Event.FoundationMinimal(
                                id = Foundation.Id(1),
                                name = Foundation.Name("This is foundation name"),
                                key = Foundation.Key("foundationKey1"),
                                foundationConfiguration = Event.FoundationConfigurationMinimal(
                                    id = FoundationConfiguration.Id("foundationConfigurationId1"),
                                    name = FoundationConfiguration.Name("This is foundation configuration name"),
                                    description = FoundationConfiguration.Description("This is foundation configuration description"),
                                    relationship = FoundationConfiguration.Relationship.OneToMany,
                                    parentId = FoundationConfiguration.Id("parentFoundationConfigurationId1"),
                                ),
                                parentId = Foundation.Id(2),
                            ),
                        )
                    ),
                    id = Event.Id("event1"),
                    status = EventStatus.QUEUED,
                    tenantId = 1,
                    entityMetadata = EntityMetadata.now(),
                )
            }

            fun deleteCollectionForm(): Event.ForJson {
                return Event.ForJson(
                    workspaceId = Workspace.Id(1),
                    eventProperties = Event.EventProperties.DeleteCollectionFormProperties(
                        form = Event.FormMinimal(
                            id = 1,
                            foundationId = 1,
                            formConfiguration = Event.FormConfigurationMinimal(
                                id = "formConfigurationId1",
                                key = "formConfigurationKey1",
                                name = "This is form configuration name",
                                seriesId = "seriesId1",
                            ),
                            documentId = "documentId1",
                            intervalId = "intervalId1",
                            foundation = Event.FoundationMinimal(
                                id = Foundation.Id(1),
                                name = Foundation.Name("This is foundation name"),
                                key = Foundation.Key("foundationKey1"),
                                foundationConfiguration = Event.FoundationConfigurationMinimal(
                                    id = FoundationConfiguration.Id("foundationConfigurationId1"),
                                    name = FoundationConfiguration.Name("This is foundation configuration name"),
                                    description = FoundationConfiguration.Description("This is foundation configuration description"),
                                    relationship = FoundationConfiguration.Relationship.OneToMany,
                                    parentId = FoundationConfiguration.Id("parentFoundationConfigurationId1"),
                                ),
                                parentId = Foundation.Id(2),
                            ),
                        )
                    ),
                    id = Event.Id("event1"),
                    status = EventStatus.QUEUED,
                    tenantId = 1,
                    entityMetadata = EntityMetadata.now(),
                )
            }

            fun updateCollectionFormAnswer(): Event.ForJson {
                return Event.ForJson(
                    workspaceId = Workspace.Id(1),
                    eventProperties = Event.EventProperties.UpdateCollectionFormAnswerProperties(
                        form = Event.FormMinimal(
                            id = 1,
                            foundationId = 1,
                            formConfiguration = Event.FormConfigurationMinimal(
                                id = "formConfigurationId1",
                                key = "formConfigurationKey1",
                                name = "This is form configuration name",
                                seriesId = "seriesId1",
                            ),
                            documentId = "documentId1",
                            intervalId = "intervalId1",
                            foundation = Event.FoundationMinimal(
                                id = Foundation.Id(1),
                                name = Foundation.Name("This is foundation name"),
                                key = Foundation.Key("foundationKey1"),
                                foundationConfiguration = Event.FoundationConfigurationMinimal(
                                    id = FoundationConfiguration.Id("foundationConfigurationId1"),
                                    name = FoundationConfiguration.Name("This is foundation configuration name"),
                                    description = FoundationConfiguration.Description("This is foundation configuration description"),
                                    relationship = FoundationConfiguration.Relationship.OneToMany,
                                    parentId = FoundationConfiguration.Id("parentFoundationConfigurationId1"),
                                ),
                                parentId = Foundation.Id(2),
                            ),
                        ),
                        question = Event.Question(
                            answer = JsonPrimitive("This is answer value"),
                            id = "123",
                            type = "text",
                            text = "This is question text",
                            identifier = "identifier1",
                            properties = JsonObject(
                                mapOf(
                                    "required" to JsonPrimitive("true"),
                                )
                            ),
                        )
                    ),
                    id = Event.Id("event1"),
                    status = EventStatus.QUEUED,
                    tenantId = 1,
                    entityMetadata = EntityMetadata.now(),
                )
            }
        }
    }
}