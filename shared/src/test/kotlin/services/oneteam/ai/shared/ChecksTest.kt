package services.oneteam.ai.shared

import services.oneteam.ai.shared.domains.ApplicationException
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.NotFoundException
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class ChecksTest {

    private val checks = Checks()

    @Test
    fun `test sameInt with equal values`() {
        checks.sameInt(1, 1) { "Values are not the same" }
    }

    @Test
    fun `test sameInt with different values`() {
        assertFailsWith<BadRequestException> {
            checks.sameInt(1, 2) { "Values are not the same" }
        }
    }

    @Test
    fun `test exists with non-null value`() {
        val result = checks.exists("value") { "Value does not exist" }
        assertEquals("value", result)
    }

    @Test
    fun `test exists with null value`() {
        assertFailsWith<NotFoundException> {
            checks.exists(null) { "Value does not exist" }
        }
    }

    @Test
    fun `test true with true condition`() {
        checks.`true`(true) { "Condition is false" }
    }

    @Test
    fun `test true with false condition`() {
        assertFailsWith<ApplicationException> {
            checks.`true`(false) { "Condition is false" }
        }
    }
}