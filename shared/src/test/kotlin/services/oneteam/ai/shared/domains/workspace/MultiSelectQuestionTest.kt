package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse

class MultiSelectQuestionTest {
    private val question = BaseSection.MultiSelectQuestion(
        id = BaseSection.Id("1"),
        type = QuestionType.NUMBER,
        text = "What's the day you like?",
        description = "description",
        identifier = "identifier",
        properties = CommonQuestionProperties.MultiSelectQuestionProperties(
            required = true,
            options = listOf(
                CommonQuestionProperties.SelectOption(
                    label = "Monday",
                    value = "1",
                ),
                CommonQuestionProperties.SelectOption(
                    label = "Tuesday",
                    value = "2",
                )
            )
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate question`() {
        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty text`() {
        val question = question.copy(text = "")
        BaseQuestionTestHelper.testEmptyText(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty identifier`() {
        val question = question.copy(identifier = "")
        BaseQuestionTestHelper.testEmptyIdentifier(question)
    }
}