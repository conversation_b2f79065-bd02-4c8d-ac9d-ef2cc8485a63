package services.oneteam.ai.shared.domains.collection.form

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.net.URI
import kotlin.test.assertEquals

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BlobServiceTest {
    val validStorageName = "myStorage"
    val validContainerName = "myContainer"

    data class Spec(
        val input: URI,
        val expected: Boolean
    )

    fun isOtaiStorageProvider(): List<Spec> {
        return listOf(
            Spec(
                input = URI(""),
                expected = false
            ),
            Spec(
                // should fail as only http[s] protocol supported
                input = URI("ssh://myStorage.blob.core.windows.net/$validContainerName/123/abc"),
                expected = false
            ),
            Spec(
                // should fail as there is no leading slash
                input = URI("$validContainerName/123/abc"),
                expected = false
            ),
            Spec(
                input = URI("/$validContainerName/123/abc"),
                expected = true
            ),
            Spec(
                input = URI("/anotherContainer/123/abc"),
                expected = false
            ),
            Spec(
                // this looks valid but isn't since there is a host but no container name
                input = URI("https://$validStorageName.blob.core.windows.net"),
                expected = false,
            ),
            Spec(
                input = URI("https://anotherStorage.blob.core.windows.net"),
                expected = false
            ),
            Spec(
                input = URI("https://$validStorageName.blob.core.windows.net/$validContainerName/123/abc"),
                expected = true
            ),
            Spec(
                input = URI("https://$validStorageName.blob.core.windows.net/anotherContainer/123/abc"),
                expected = false
            ),
            Spec(
                input = URI("https://anotherStorage.blob.core.windows.net/$validContainerName/123/abc"),
                expected = false
            ),
            Spec(
                input = URI("https://anotherStorage.blob.core.windows.net/anotherContainer/123/abc"),
                expected = false
            )
        )
    }

    @ParameterizedTest
    @MethodSource("isOtaiStorageProvider")
    fun `isOtaiStorage`(spec: Spec) = runTest {
        val blobService = BlobService(
            storageName = validStorageName,
            accessKey = "not-a-real-key",
            containerName = validContainerName
        )

        val result = blobService.isOtaiStorage(spec.input)
        assertEquals(
            actual = result,
            expected = spec.expected,
            message = "Expected ${spec.input} to be ${spec.expected}, but got $result"
        )
    }
}