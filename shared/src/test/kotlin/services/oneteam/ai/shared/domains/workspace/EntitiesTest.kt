package services.oneteam.ai.shared.domains.workspace

import io.kotest.core.spec.style.StringSpec
import io.kotest.data.forAll
import io.kotest.data.row
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.ValidationErrors
import services.oneteam.ai.shared.domains.workspace.validation.NumberBounds
import services.oneteam.ai.shared.domains.workspace.validation.StringBounds
import java.time.Instant

class EntitiesTest : StringSpec({
    "workspace validation" {
        forAll(
            row(
                Workspace.ForCreate(
                    Workspace.Name("Valid Name"),
                    Workspace.Key("VALIDKEY1"),
                    Workspace.Description("Valid Description"),
                ),
                ValidationErrors()
            ), row(
                Workspace.ForCreate(
                    Workspace.Name(""),
                    Workspace.Key("VALIDKEY2"),
                    Workspace.Description("Valid Description"),
                    null
                ),
                ValidationErrors()
                    .add("errors.common.name.required", "name", null)
                    .add("errors.common.name.length", "name", StringBounds(2, 100))
            ), row(
                Workspace.ForUpdate(
                    Workspace.Id(-1), Workspace.Name("Valid Name"), Workspace.Key("VALIDKEY3"),
                    Workspace.Description("1"),
                    null,
                    Workspace.ForJson(
                        Workspace.Id(1),
                        Workspace.Name(""),
                        Workspace.Key("VALIDKEY3"),
                        Workspace.Description("Valid Description"),
                        foundations = OrderedMap(emptyList()),
                        forms = emptyMap(),
                        flows = OrderedMap(emptyList()),
                        series = emptyMap(),
                        labels = emptyMap(),
                        variables = emptyMap(),
                        metadata = EntityMetadata(Instant.now(), Instant.now()),
                        errors = emptyList()
                    )
                ),
                ValidationErrors().add("errors.invalidId", "id", NumberBounds(min = 0, max = null))
            )
        ) { workspace, expected ->
            workspace.validate() shouldBeEqualToComparingFields expected
        }
    }

})
