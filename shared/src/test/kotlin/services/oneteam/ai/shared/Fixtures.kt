package services.oneteam.ai.shared

import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.collection.form.BlobService
import services.oneteam.ai.shared.domains.collection.form.FormRepository
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationRepository
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.tenant.TenantRepository
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.middlewares.RequestContext
import java.nio.file.Paths
import java.util.*

class Fixtures(val testDb: TestDb) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    val checks = Checks()
    val d: ResourceBundle = ResourceBundle.getBundle("dictionary")

    val proxyService = ExternalProxyService("token", MockHttpClient().client)
    val dictionary: ResourceBundle = ResourceBundle.getBundle("dictionary")
    val tenantRepository = TenantRepository(testDb.database)
    val userRepository = UserRepository(checks)
    val workspaceRepository = WorkspaceRepository(checks, userRepository)
    val foundationRepository = FoundationRepository()

    val foundationConfigurationService = FoundationConfigurationService(checks, dictionary, foundationRepository)
    val flowStepTypeConfigurationRepository =
        FlowStepTypeConfigurationRepository()
    val flowStepTypeConfigurationService = FlowStepTypeConfigurationService(flowStepTypeConfigurationRepository)
    val formRepository = FormRepository(checks)
    val documentService = ApiDocumentService(
        proxyService,
        flowStepTypeConfigurationService
    )
    val workspaceVersionRepository = WorkspaceVersionRepository()
    val workspaceVersionService =
        WorkspaceVersionService(workspaceRepository, documentService, workspaceVersionRepository, checks)
    val foundationService = FoundationService(foundationRepository, workspaceVersionService, checks)
    val uploadService = BlobService("", "", "")
    val formService = FormService(
        formRepository,
        workspaceRepository,
        documentService,
        workspaceVersionRepository,
        uploadService,
        foundationService,
        workspaceVersionService,
        checks
    )
    val workspaceUserRepository = WorkspaceUserRepository(workspaceRepository, userRepository)
    val workspaceUserService = WorkspaceUserService(workspaceUserRepository)
    val workspaceService = WorkspaceService(
        workspaceRepository,
        foundationService,
        documentService,
        foundationConfigurationService,
        workspaceVersionService,
        workspaceUserService,
        formService,
        checks
    )

    lateinit var tenant1: Tenant
    lateinit var tenant2: Tenant

    val Workspace1_Key = Workspace.Key("WS1")
    val Workspace2_Key = Workspace.Key("WS2")


    val userId = User.Id(1L) // Assuming a user with ID 1 exists for testing purposes

    fun initialise(): Fixtures {
        logger.info("Initialising fixtures")

        testDb.drop()
        testDb.migrate()

        // these represent the tenants inserted via migrations in src/postgresTest/resources/db/migration/local/V20241121.000001__seed.sql and src/h2Test/resources/db/migration/local/V20241121.000001__seed.sql
        tenant1 = tenantRepository.getByOriginUrl("http://tenant1")!!
        tenant2 = tenantRepository.getByOriginUrl("http://tenant2")!!

        runBlocking {
            withContext(RequestContext(tenant = tenant1)) {
                workspaceService.create(
                    Workspace.ForCreate(
                        Workspace.Name("testWorkspace1"), Workspace1_Key,
                        Workspace.Description("test description")
                    ), user = userId
                )
            }
        }
        return this
    }

    companion object {
        @OptIn(ExperimentalSerializationApi::class)
        fun stepTypeConfigurations(): Map<String, FlowStepTypeConfiguration> {
            return Json.decodeFromStream<List<FlowStepType>>(this::class.java.getResourceAsStream("/StepTypeSeed.json")!!)
                .map {
                    FlowStepTypeConfiguration(
                        id = 0,
                        primaryIdentifier = it.primaryIdentifier,
                        type = it.type,
                        tenantId = 0,
                        name = it.name,
                        description = it.description,
                        properties = it.properties
                    )
                }
                .associateBy { it.primaryIdentifier }

        }

        fun helperFlows(): Map<FlowConfiguration.Id, FlowConfiguration.ForJson> {
            val file = Paths.get("src/test/resources/validation/flowconfiguration/valid/helper-flow-set-variable.json")
                .toFile()
            val input = file.readText()
            var flowConfiguration = Json.decodeFromString<FlowConfiguration.ForJson>(input)
            return mapOf(
                flowConfiguration.id to flowConfiguration
            )
        }
    }
}