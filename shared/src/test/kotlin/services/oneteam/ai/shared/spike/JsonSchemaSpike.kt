package services.oneteam.ai.shared.spike

import com.fasterxml.jackson.databind.JsonNode
import com.networknt.schema.InputFormat
import com.networknt.schema.JsonSchemaFactory
import com.networknt.schema.SpecVersion
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import org.junit.jupiter.api.Test

/**
 * https://json-schema.org/understanding-json-schema/reference/conditionals
 *
 *
 */
class JsonSchemaSpike {

    @Test
    fun `should return error`() {
        val json = """
            {
                "variant": "setVar",
                "properties": {
                    "variables": []
                }
            }
        """.trimIndent()

        val jsonSchema = """
            {
                "type": "object",
                "properties": {
                    "variant": {
                        "type": "string"
                    },
                    "properties": {
                        "type": "object",
                        "properties": {
                            "variables": {
                                "type": "array",
                                "minItems": 1
                            }
                        },
                        "required": ["variables"]
                    }
                },
                "required": ["variant", "properties"]
            }
        """.trimIndent()

        val schema = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012)
            .getSchema(jsonSchema)

        val errors = schema.validate(json, InputFormat.JSON)

        println(errors)
    }

    @Test
    fun `should return error based on variant`() {
        val json = """
            {
                "variant": "setVar",
                "properties": {
                    "variables": []
                }
            }
        """.trimIndent()

        val jsonSchema = """
{
  "type": "object",
  "properties": {
    "variant": {
      "type": "string"
    },
    "anyOf": [
      {
        "if": {
          "properties": {
            "variant": {
              "const": "setVar"
            }
          }
        },
        "then": {
          "properties": {
            "properties": {
              "variables": {
                "minItems": 1
              }
            }
          },
          "required": [
            "properties"
          ]
        }
      }
    ]
  },
  "required": [
    "variant",
    "properties"
  ]
}            
        """.trimIndent()

        val schema = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012)
            .getSchema(jsonSchema)

        val errors = schema.validate(json, InputFormat.JSON)

        println(errors)
    }
}