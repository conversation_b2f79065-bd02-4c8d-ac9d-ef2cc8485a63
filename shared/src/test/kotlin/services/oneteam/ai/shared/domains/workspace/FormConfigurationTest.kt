package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import services.oneteam.ai.shared.domains.EntityMetadata
import java.time.Instant

class FormConfigurationTest {
    private val form = FormConfiguration.ForJson(
        id = "1",
        name = "Form",
        foundationId = "1",
        level = 1,
        metadata = EntityMetadata(
            createdAt = Instant.now(),
            updatedAt = Instant.now(),
        ),
        key = "key",
        content = listOf(
            BaseSection.TextQuestion(
                id = BaseSection.Id("1"),
                type = QuestionType.TEXT,
                text = "How are you today?",
                description = "description",
                identifier = "identifier1",
                properties = CommonQuestionProperties.TextQuestionProperties(
                    maxLength = 10,
                    minLength = 1,
                    required = true
                )
            ),
            BaseSection.TextQuestion(
                id = BaseSection.Id("2"),
                type = QuestionType.TEXT,
                text = "What's your name?",
                description = "description",
                identifier = "identifier2",
                properties = CommonQuestionProperties.TextQuestionProperties(
                    maxLength = 10,
                    minLength = 1,
                    required = true
                )
            )
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate form`() {
        val form = form.copy(content = form.content?.mapIndexed() { index, question ->
            if (index == 0) {
                (question as BaseSection.TextQuestion).copy(text = "")
            } else {
                question
            }
        })
        form.validateConfiguration("form1").hasErrors().shouldBeTrue()
    }

    @org.junit.jupiter.api.Test
    fun `should not return errors for duplicate text`() {
        val form = form.copy(content = form.content?.map() { question ->
            (question as BaseSection.TextQuestion).copy(text = "How are you today?")
        })
        form.validateConfiguration("form1").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for duplicate identifier`() {
        val form = form.copy(content = form.content?.map() { question ->
            (question as BaseSection.TextQuestion).copy(identifier = "identifier1")
        })
        form.validateConfiguration("form1").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(2)
            errors.getErrors().filter { it.message.value == "errors.common.identifier.duplicate" }.size.shouldBe(2)
            errors.getErrors().filter { it.path.value == "form1.content.0.identifier" }.size.shouldBe(1)
            errors.getErrors().filter { it.path.value == "form1.content.1.identifier" }.size.shouldBe(1)
        }
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for duplicate identifier in nested structure`() {
        val form = form.copy(content = listOf(
            BaseSection.Section(
                id = BaseSection.Id("1"),
                name = BaseSection.Name("Section 1"),
                level = BaseSection.Level(0),
                content = listOf(
                    BaseSection.TextQuestion(
                        id = BaseSection.Id("2"),
                        type = QuestionType.TEXT,
                        text = "How are you today?",
                        description = "description",
                        identifier = "identifier2",
                        properties = CommonQuestionProperties.TextQuestionProperties(
                            maxLength = 10,
                            minLength = 1,
                            required = true
                        )
                    )
                )
            ),
            BaseSection.TextQuestion(
                id = BaseSection.Id("3"),
                type = QuestionType.TEXT,
                text = "What's your name?",
                description = "description",
                identifier = "identifier2",
                properties = CommonQuestionProperties.TextQuestionProperties(
                    maxLength = 10,
                    minLength = 1,
                    required = true
                )
            )
        ))

        form.validateConfiguration("form1").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(2)
            errors.getErrors().filter { it.message.value == "errors.common.identifier.duplicate" }.size.shouldBe(2)
            errors.getErrors().filter { it.path.value == "form1.content.0.content.0.identifier" }.size.shouldBe(1)
            errors.getErrors().filter { it.path.value == "form1.content.1.identifier" }.size.shouldBe(1)
        }
    }

    @org.junit.jupiter.api.Test
    fun `should not return errors for duplicate text in column question`() {
        val form = form.copy(content = listOf(
            BaseSection.TableQuestion(
                id = BaseSection.Id("1"),
                type = QuestionType.NUMBER,
                text = "Who are your friends?",
                description = "description",
                identifier = "identifier",
                properties = CommonQuestionProperties.TableQuestionProperties(
                    required = true,
                    columns = listOf(
                        BaseSection.TextQuestion(
                            id = BaseSection.Id("1"),
                            type = QuestionType.TEXT,
                            text = "First Name",
                            description = "first name",
                            identifier = "firstName",
                            properties = CommonQuestionProperties.TextQuestionProperties(
                                required = true
                            )
                        ),
                        BaseSection.TextQuestion(
                            id = BaseSection.Id("2"),
                            type = QuestionType.TEXT,
                            text = "First Name",
                            description = "last name",
                            identifier = "lastName",
                            properties = CommonQuestionProperties.TextQuestionProperties(
                                required = true
                            )
                        ),
                    )
                )
            )
        ))

        form.validateConfiguration("form1").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for duplicate identifier in column question`() {
        val form = form.copy(content = listOf(
            BaseSection.TableQuestion(
                id = BaseSection.Id("1"),
                type = QuestionType.NUMBER,
                text = "Who are your friends?",
                description = "description",
                identifier = "identifier",
                properties = CommonQuestionProperties.TableQuestionProperties(
                    required = true,
                    columns = listOf(
                        BaseSection.TextQuestion(
                            id = BaseSection.Id("1"),
                            type = QuestionType.TEXT,
                            text = "First Name",
                            description = "first name",
                            identifier = "firstName",
                            properties = CommonQuestionProperties.TextQuestionProperties(
                                required = true
                            )
                        ),
                        BaseSection.TextQuestion(
                            id = BaseSection.Id("2"),
                            type = QuestionType.TEXT,
                            text = "Last Name",
                            description = "last name",
                            identifier = "firstName",
                            properties = CommonQuestionProperties.TextQuestionProperties(
                                required = true
                            )
                        ),
                    )
                )
            )
        ))

        form.validateConfiguration("form1").let { errors ->
            errors.hasErrors().shouldBeTrue()
            errors.getErrors().size.shouldBe(2)
            errors.getErrors()[0].message.value.shouldBe("errors.common.identifier.duplicate")
            errors.getErrors()[0].path.value.shouldBe("form1.content.0.properties.columns.1.identifier")
            errors.getErrors()[0].key.value.shouldBe("identifier")
            errors.getErrors()[0].type.value.shouldBe("duplicate")
            errors.getErrors()[1].message.value.shouldBe("errors.common.identifier.duplicate")
            errors.getErrors()[1].path.value.shouldBe("form1.content.0.properties.columns.2.identifier")
            errors.getErrors()[1].key.value.shouldBe("identifier")
            errors.getErrors()[1].type.value.shouldBe("duplicate")
        }
    }
}