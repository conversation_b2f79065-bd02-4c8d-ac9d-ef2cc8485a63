package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse

class SelectQuestionTest {
    private val question = BaseSection.SelectQuestion(
        id = BaseSection.Id("1"),
        type = QuestionType.NUMBER,
        text = "What's the day today?",
        description = "description",
        identifier = "identifier",
        properties = CommonQuestionProperties.SelectQuestionProperties(
            required = true,
            options = listOf(
                CommonQuestionProperties.SelectOption(
                    label = "Monday",
                    value = "1",
                ),
                CommonQuestionProperties.SelectOption(
                    label = "Tuesday",
                    value = "2",
                )
            )
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate question`() {
        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should returns errors for empty text`() {
        val question = question.copy(text = "")
        BaseQuestionTestHelper.testEmptyText(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty identifier`() {
        val question = question.copy(identifier = "")
        BaseQuestionTestHelper.testEmptyIdentifier(question)
    }

}