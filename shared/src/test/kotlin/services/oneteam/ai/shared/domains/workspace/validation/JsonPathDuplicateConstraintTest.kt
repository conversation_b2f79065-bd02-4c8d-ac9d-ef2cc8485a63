package services.oneteam.ai.shared.domains.workspace.validation

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.workspace.validation.JsonPathContext
import services.oneteam.ai.shared.domains.workspace.validation.JsonPathDuplicateConstraint


class JsonPathDuplicateConstraintTest {

    @Test
    fun `when object has id then id should be used`() {
        val data = """
            {
                "name": "John Doe",
                "addresses": [
                {
                    "id": "ID1",
                    "city": "New York",
                    "state": "NY"
                },
                {
                    "id": "ID2",
                    "city": "Los Angeles",
                    "state": "CA"
                },
                {
                    "id": "ID3",
                    "city": "New York",
                    "state": "NY"
                }
                ]
            }
        """.trimIndent()

        val objectMapper = ObjectMapper()
        val jsonDocument: JsonNode = objectMapper.readTree(data)

        val constraint = JsonPathDuplicateConstraint(
            "$.addresses[*].city",
            "address",
            "duplicate",
            JsonPathContext().build()
        )
        val errors = constraint.validate(jsonDocument)
        errors.hasErrors() shouldBe true
        errors.getErrors().size shouldBe 2
        errors.getErrors()[0].path.value shouldBe "$.addresses[ID1].city"
        errors.getErrors()[1].path.value shouldBe "$.addresses[ID3].city"
    }

    @Test
    fun `when object doesn't have id then index should be used`() {
        val data = """
            {
                "name": "John Doe",
                "addresses": [
                {
                    "city": "New York",
                    "state": "NY"
                },
                {
                    "city": "Los Angeles",
                    "state": "CA"
                },
                {
                    "city": "New York",
                    "state": "NY"
                }
                ]
            }
        """.trimIndent()

        val objectMapper = ObjectMapper()
        val jsonDocument: JsonNode = objectMapper.readTree(data)

        val constraint = JsonPathDuplicateConstraint(
            "$.addresses[*].city",
            "address",
            "duplicate",
            JsonPathContext().build()
        )
        val errors = constraint.validate(jsonDocument)
        errors.hasErrors() shouldBe true
        errors.getErrors().size shouldBe 2
        errors.getErrors()[0].path.value shouldBe "$.addresses[0].city"
        errors.getErrors()[1].path.value shouldBe "$.addresses[2].city"
    }

}