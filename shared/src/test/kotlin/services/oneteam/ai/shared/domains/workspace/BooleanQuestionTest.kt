package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse

class BooleanQuestionTest {
    private val question = BaseSection.BooleanQuestion(
        id = BaseSection.Id("1"),
        type = QuestionType.NUMBER,
        text = "Are you married?",
        description = "description",
        identifier = "identifier",
        properties = CommonQuestionProperties.BooleanQuestionProperties(
            required = true,
        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate question`() {
        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty text`() {
        val question = question.copy(text = "")
        BaseQuestionTestHelper.testEmptyText(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty identifier`() {
        val question = question.copy(identifier = "")
        BaseQuestionTestHelper.testEmptyIdentifier(question)
    }
}