package services.oneteam.ai.shared.extensions

import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals

class BigDecimalExtensionsTest {

    @Test
    fun `test convertToNiceBigDecimal`() {
        assertEquals(BigDecimal("1"), BigDecimal("1.000").convertToNiceBigDecimal())
        assertEquals(BigDecimal("10"), BigDecimal("1E+1").convertToNiceBigDecimal())
        assertEquals(BigDecimal("0.1"), BigDecimal("0.100").convertToNiceBigDecimal())
        assertEquals(BigDecimal("0"), BigDecimal("0.000").convertToNiceBigDecimal())
        assertEquals(
            BigDecimal("12345678901234567890"),
            BigDecimal("12345678901234567890.000").convertToNiceBigDecimal()
        )
    }

}