package services.oneteam.ai.shared.domains.workspace.validation

import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Assertions.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class StringAlphaNumericConstraintTest {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @org.junit.jupiter.api.Test
    fun `when string is alphanumeric then no errors`() {
        val constraint = StringAlphaNumericConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error"
        )
        val errors = constraint.validate(Pair("A", "B"))
        assertFalse(errors.hasErrors())
    }

    @org.junit.jupiter.api.Test
    fun `when string is not alphanumeric then errors`() {
        val constraint = StringAlphaNumericConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error"
        )
        // {"errors":[{"key":"name","type":"alphanumeric","path":"name","constraintDetail":"name must be upper case","message":"error"}]}
        val errors = constraint.validate(Pair("A", "B-!"))
        logger.debug(Json.encodeToString<Errors>(Errors.serializer(), errors))
        assertTrue(errors.hasErrors())
        assertEquals(1, errors.getErrors().size)
        assertEquals("alphanumeric", errors.getErrors()[0].type.value)
        assertEquals("name", errors.getErrors()[0].path.value)
        assertEquals("error", errors.getErrors()[0].message.value)
    }

}