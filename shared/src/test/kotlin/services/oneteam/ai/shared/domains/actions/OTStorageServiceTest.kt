package services.oneteam.ai.shared.domains.actions

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class OTStorageServiceTest {
    data class Spec(
        val filePath: String,
        val expected: String
    )

    fun filePathProvider(): List<Spec> {
        return listOf(
            Spec(
                filePath = "path/to file.txt",
                expected = "https://myStorage.blob.core.windows.net/myContainer/path/to%20file.txt?mySasToken=123&sig=%2B5034Nk"
            ),
            Spec(
                filePath = "another/path/to-file.json",
                expected = "https://myStorage.blob.core.windows.net/myContainer/another/path/to-file.json?mySasToken=123&sig=%2B5034Nk"
            )
        )
    }

    @ParameterizedTest
    @MethodSource("filePathProvider")
    fun `generates valid fileUrl`(spec: Spec) = runTest {
        val service = OTStorageService(
            OTStorageService.OTStorageServiceConfig(
                storageName = "myStorage",
                containerName = "myContainer",
                sasToken = "mySasToken=123&sig=%2B5034Nk"
            )
        )
        val output = service.getFileUrl(spec.filePath)
        assert(output == spec.expected) {
            "Expected: ${spec.expected}, but got: $output"
        }
    }
}