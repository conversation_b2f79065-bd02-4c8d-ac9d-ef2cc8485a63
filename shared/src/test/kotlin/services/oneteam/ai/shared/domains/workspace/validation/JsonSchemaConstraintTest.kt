package services.oneteam.ai.shared.domains.workspace.validation

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.networknt.schema.JsonSchemaFactory
import com.networknt.schema.SpecVersion
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class JsonSchemaConstraintTest {

    private val objectMapper = ObjectMapper()

    @Test
    fun `should return no errors for valid JSON`() {
        val schemaPath = "documentSchema.json"
        val schema = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012)
            .getSchema(this::class.java.classLoader.getResourceAsStream(schemaPath).use { it!!.reader().readText() })
        val constraint = JsonSchemaConstraint.fromJsonSchema(schema)

        val valid = """

            {
              "type": "WORKSPACE_CONFIGURATION",
              "description": "",
              "errors": [],
              "flows": {
                "order": [],
                "entities": {}
              },
              "forms": {
                
              },
              "foundations": {
                "order": [
                  "2f363b74-0a20-42ce-b6de-415d36a0ba29",
                  "RNU1kLWl8agSJAwiU-moH",
                  "qUWNtCR5FTV07ZkgwD2bP"
                ],
                "entities": {
                    "2f363b74-0a20-42ce-b6de-415d36a0ba29": {
                      "description": null,
                      "id": "2f363b74-0a20-42ce-b6de-415d36a0ba29",
                      "metadata": {
                        "createdAt": "2024-11-28T05:27:35.007297Z",
                        "updatedAt": "2024-11-28T05:27:35.007297Z"
                      },
                      "name": "Workspace",
                      "relationship": "OneToMany"
                    },
                    "RNU1kLWl8agSJAwiU-moH": {
                      "description": "",
                      "id": "RNU1kLWl8agSJAwiU-moH",
                      "metadata": {
                        "createdAt": "2024-11-28T05:27:40.152Z",
                        "updatedAt": "2024-11-28T05:27:40.152Z"
                      },
                      "name": "Foundation Name",
                      "objectId": "",
                      "relationship": "OneToMany"
                    },
                    "qUWNtCR5FTV07ZkgwD2bP": {
                      "description": "",
                      "id": "qUWNtCR5FTV07ZkgwD2bP",
                      "metadata": {
                        "createdAt": "2024-11-28T05:27:41.390Z",
                        "updatedAt": "2024-11-28T05:27:41.390Z"
                      },
                      "name": "Foundation Name 1",
                      "objectId": "",
                      "relationship": "OneToMany"
                    }
                }
              },
              "id": 44,
              "key": "NEW123",
              "metadata": {
                "createdAt": "2024-11-28T05:27:34.993509Z",
                "updatedAt": "2024-11-28T05:27:34.993512Z"
              },
              "name": "new123",
              "series": {
                
              },
              "labels" : {}
            }
        """.trimIndent()
        val jsonNode: JsonNode = objectMapper.readTree(valid)

        val errors = constraint.validate(jsonNode)
        errors.hasErrors() shouldBe false
        errors.getErrors().size shouldBe 0
    }

    @Test
    fun `should return errors for invalid JSON`() {
        val schemaPath = "documentSchema.json"
        val schema = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012)
            .getSchema(this::class.java.classLoader.getResourceAsStream(schemaPath).use { it!!.reader().readText() })
        val constraint = JsonSchemaConstraint.fromJsonSchema(schema)

        val json = """
            {"id": 34, "key": "SG-PIT", "name": "SG Personal Income Tax", "flows": [], "forms": {}, "series": {}, "labels": {}, "metadata": {"createdAt": "2024-11-28T05:15:44.786617Z", "updatedAt": "2024-11-28T05:15:44.786622Z"}, "foundations": [{"id": "31a2afa6-fcbf-4a6e-900e-2c9d6a80d1c8", "name": "Workspace", "metadata": {"createdAt": "2024-11-28T05:15:44.811710Z", "updatedAt": "2024-11-28T05:15:44.811710Z"}}, {"id": "f30c2e48-9a32-473e-aa15-933a1585f0ac", "name": "Client", "metadata": {"createdAt": "2024-11-28T05:15:44.781193Z", "updatedAt": "2024-11-28T05:15:44.781193Z"}, "description": "Client"}, {"id": "890b6b35-a914-4313-8aa7-7810c2e6971b", "name": "Client", "metadata": {"createdAt": "2024-11-28T05:15:44.782613Z", "updatedAt": "2024-11-28T05:15:44.782613Z"}, "description": "Employee"}]}
        """.trimIndent()
        val jsonNode: JsonNode = objectMapper.readTree(json)

        val errors = constraint.validate(jsonNode)
        errors.hasErrors() shouldBe true
    }
}