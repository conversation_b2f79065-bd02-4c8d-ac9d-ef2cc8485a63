package services.oneteam.ai.shared.helpers

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class RegexDefinitionsTest {

    @Test
    fun `should match a function with no arguments`() {
        assertTrue(RegexDefinitions.function.matches("\$function()"))
        assertTrue(RegexDefinitions.function.matches("\$function('abd')"))
    }

    @Test
    fun `should match number`() {
        assertTrue(RegexDefinitions.number.matches("123"))
        assertTrue(RegexDefinitions.number.matches("123.5"))
        assertFalse(RegexDefinitions.number.matches("a123.5"))
    }

    @Test
    fun `should match negative number`() {
        assertTrue(RegexDefinitions.number.matches("-123"))
        assertTrue(RegexDefinitions.number.matches("-123.5"))
        assertFalse(RegexDefinitions.number.matches("-a123.5"))
    }

    @Test
    fun `should match long`() {
        assertTrue(RegexDefinitions.long.matches("123"))
        assertFalse(RegexDefinitions.long.matches("123a"))
        assertFalse(RegexDefinitions.long.matches("123.5"))
        assertFalse(RegexDefinitions.long.matches("123a"))
    }

    @Test
    fun `should match negative long`() {
        assertTrue(RegexDefinitions.long.matches("-123"))
        assertFalse(RegexDefinitions.long.matches("-123.5"))
        assertFalse(RegexDefinitions.long.matches("-123a"))
    }

    @Test
    fun `should match double`() {
        assertTrue(RegexDefinitions.double.matches("123.456"))
        assertTrue(RegexDefinitions.double.matches("123"))
        assertFalse(RegexDefinitions.double.matches("123.a"))
    }

    @Test
    fun `should match negative double`() {
        assertTrue(RegexDefinitions.double.matches("-123.456"))
        assertTrue(RegexDefinitions.double.matches("-123"))
        assertFalse(RegexDefinitions.double.matches("-123.a"))
    }

    @Test
    fun `should match date`() {
        assertTrue(RegexDefinitions.date.matches("2025-10-31"))
        assertFalse(RegexDefinitions.date.matches("2025-10-31T12:00:00"))
    }

    @Test
    fun `should match placeholder`() {
        assertTrue(RegexDefinitions.placeholder.matches("{{hello}}"))
        assertTrue(RegexDefinitions.placeholder.matches("{{hello}} {{world}}"))
        assertFalse(RegexDefinitions.placeholder.matches("{{hello}"))
    }
}