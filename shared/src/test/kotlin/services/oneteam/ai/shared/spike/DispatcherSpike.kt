package services.oneteam.ai.shared.spike

import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import kotlin.random.Random
import kotlin.time.Duration.Companion.milliseconds

@Disabled
class DispatcherSpike {
    private val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun `custom dispatcher`() {
        // Create a custom ThreadFactory to set thread names
        val threadFactory = ThreadFactory { runnable ->
            Thread(runnable, "CustomThreadName-${System.currentTimeMillis()}")
        }

        // Create a custom thread pool
        val customThreadPool = Executors.newFixedThreadPool(4, threadFactory)

        // Convert it to a CoroutineDispatcher
        val customDispatcher = customThreadPool.asCoroutineDispatcher()

        runBlocking {
            // Use the custom dispatcher
            launch(customDispatcher) {
                println("Running on custom dispatcher: ${Thread.currentThread().name}")
            }
        }

        // Shutdown the thread pool
        customThreadPool.shutdown()
    }

    @Test
    fun `limit concurrency`() {

        // used to limit concurrency and therefore resources (memory/cpu) used
        val maxConcurrentCoroutines = 6
        val semaphore = Semaphore(maxConcurrentCoroutines)

        // Create a custom ThreadFactory to set thread names
        val threadFactory = ThreadFactory { runnable ->
            Thread(runnable, "CustomThreadName-${System.currentTimeMillis()}")
        }

        // Create a custom thread pool
        val customThreadPool = Executors.newFixedThreadPool(4, threadFactory)

        // Convert it to a CoroutineDispatcher
        val customDispatcher = customThreadPool.asCoroutineDispatcher()

        runBlocking {
            // Launch multiple coroutines
            repeat(100) {
                launch(customDispatcher) {
                    semaphore.withPermit {
                        val nextInt = Random.nextInt(100, 1000)
                        logger.debug("here")
                        println("starting on thread: ${Thread.currentThread().name}")
                        delay(nextInt.milliseconds) // Simulate some work
                        println("finished on thread: ${Thread.currentThread().name}")
                    }
                }
            }
            println("All coroutines launched")
        }

        // Shutdown the thread pool
        customThreadPool.shutdown()

    }
}