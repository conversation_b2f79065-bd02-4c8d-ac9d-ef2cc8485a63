package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeFalse

class SchemaQuestionTest {
    private val question = BaseSection.SchemaQuestion(
        id = BaseSection.Id("1"),
        type = QuestionType.SCHEMA,
        text = "Expected response",
        description = "description",
        identifier = "identifier",
        properties = CommonQuestionProperties.SchemaQuestionProperties(
            required = true

        )
    )

    @org.junit.jupiter.api.Test
    fun `should validate question`() {
        question.validateConfiguration("testPath").hasErrors().shouldBeFalse()
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty text`() {
        val question = question.copy(text = "")
        BaseQuestionTestHelper.testEmptyText(question)
    }

    @org.junit.jupiter.api.Test
    fun `should return errors for empty identifier`() {
        val question = question.copy(identifier = "")
        BaseQuestionTestHelper.testEmptyIdentifier(question)
    }
}