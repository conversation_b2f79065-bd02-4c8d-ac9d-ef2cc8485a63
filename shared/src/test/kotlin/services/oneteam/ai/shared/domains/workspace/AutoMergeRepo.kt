package services.oneteam.ai.shared.domains.workspace

import org.automerge.AmValue
import org.automerge.Document
import org.automerge.ObjectId
import java.sql.DriverManager
import kotlin.time.ExperimentalTime
import kotlin.time.toKotlinInstant

typealias StorageKey = Array<String>
typealias DocumentId = String

data class Chunk(val key: StorageKey, val data: ByteArray?)

enum class ChunkType {
    SNAPSHOT, INCREMENTAL
}

fun chunkTypeFromKey(key: StorageKey): ChunkType? {
    if (key.size < 2) {
        return null
    }

    val chunkTypeStr = key[key.size - 2] // next-to-last element in key
    return when (chunkTypeStr) {
        "snapshot" -> ChunkType.SNAPSHOT
        "incremental" -> ChunkType.INCREMENTAL
        else -> null
    }
}

fun loadRange(storageKey: StorageKey): Array<Chunk> {
    val USER_NAME = "otai_superuser"
    val PASSWORD = "otai_superuser"
    val jdbcUrl = "**************************************************"
    val connection = DriverManager.getConnection(jdbcUrl, USER_NAME, PASSWORD)
    connection.prepareStatement("SELECT * FROM am WHERE key LIKE ? ORDER BY key DESC").use { stmt ->
        stmt.setString(1, storageKey.joinToString("/") + "%")
        val rs = stmt.executeQuery()
        val chunks = mutableListOf<Chunk>()
        while (rs.next()) {
            println("adding chunk for key: ${rs.getString("key")}")
            val key = rs.getString("key").split("/").toTypedArray()
            val data = rs.getBytes("data")
            chunks.add(Chunk(key, data))
        }
        connection.close()
        return chunks.toTypedArray()
    }
    return emptyArray()
}

fun mergeArrays(myArrays: List<ByteArray>): ByteArray {
    var mergedArray = ByteArray(0)
    myArrays.forEach { item ->
        mergedArray += item
    }
    return mergedArray
}

fun loadDoc(documentId: DocumentId): Document? {
    val chunks = loadRange(arrayOf(documentId))
    val binaries = mutableListOf<ByteArray>()
    for (chunk in chunks) {
        if (chunk.data == null) {
            continue
        }
        val chunkType = chunkTypeFromKey(chunk.key)
        if (chunkType == null) {
            continue
        }
        binaries.add(chunk.data)
    }
    val binary = mergeArrays(binaries)
    if (binary.isEmpty()) {
        return null
    }
    val newDoc = Document.load(binary)

    return newDoc
}

fun printDoc(doc: Document): String {
    return printMap(doc, ObjectId.ROOT)
}

@OptIn(ExperimentalTime::class)
fun printValue(doc: Document, value: AmValue): String {
    return when (value) {
        is AmValue.Bool -> "${value.value}"
        is AmValue.Counter -> "${value.value}"
        is AmValue.Int -> "${value.value}"
        is AmValue.Bytes -> "[${value.value.joinToString(",")}]"
        is AmValue.F64 -> "${value.value}"
        is AmValue.List -> printList(doc, value.id)
        is AmValue.Map -> printMap(doc, value.id)
        is AmValue.Null -> "null"
        is AmValue.Str -> "\"${value.value}\""
        is AmValue.Text -> "\"${doc.text(value.id).get()}\""
        is AmValue.Timestamp -> "\"${value.value.toInstant().toKotlinInstant()}\""
        is AmValue.UInt -> "${value.value}"
        else -> "\"other: $value\""
    }
}

fun printMap(doc: Document, objectId: ObjectId): String {
    val mapEntries = doc.mapEntries(objectId)
    if (mapEntries.isEmpty) {
        return "empty map"
    }
    return "{" + mapEntries.get().map { "\"${it.key}\": ${printValue(doc, it.value)}" }.joinToString(",\n") + "}"
}

fun printList(doc: Document, objectId: ObjectId): String {
    val listItems = doc.listItems(objectId)
    if (listItems.isEmpty) {
        println("empty map")
        return ""
    }
    return "[" + listItems.get().map { printValue(doc, it) }.joinToString(",") + "]"
}