package services.oneteam.ai.shared.spike

import kotlinx.coroutines.*
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

// https://elizarov.medium.com/futures-cancellation-and-coroutines-b5ce9c3ede3a
// https://sebi.io/posts/2023-11-24-withtimeout-doesnt-do-what-you-think-it-does/

/**
 * This test is to demonstrate the behavior of `withTimeout` and `withTimeoutOrNull` in Kotlin coroutines.
 * These functions may not behave as you might expect.
 *
 * From the documentation: https://kotlinlang.org/api/kotlinx.coroutines/kotlinx-coroutines-core/kotlinx.coroutines/with-timeout.html
 *
 * > The code that is executing inside the block is cancelled on timeout and the active or next invocation of the
 * > cancellable suspending function inside the block throws a TimeoutCancellationException.
 */
class TimeoutSpike {

    @Test
    fun `will timeout because of yielding`() {
        assertThrows(TimeoutCancellationException::class.java) {
            runBlocking {
                withTimeout(timeMillis = 5) {
                    // This block will yield
                    for (i in 1..10000) {
                        print(i)
                        delay(1) // this will yield and allow the timeout to occur before the loop completes
                    }
                }
            }
        }
    }

    @Test
    fun `will not timeout if block doesn't yield`() = runTest {
        val result = withTimeoutOrNull(timeMillis = 5) {
            // This block will not yield, so the timeout will not occur
            for (i in 1..10000) {
                print(i)
            }
            return@withTimeoutOrNull true
        }
        assertTrue(result == true) { "Result should be true" }
    }
}

