package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import services.oneteam.ai.shared.domains.workspace.validation.*

class BaseQuestionTestHelper {
    companion object {
        fun testEmptyText(question: BaseSection.BaseQuestion) {
            question.validateConfiguration("testPath").let { errors: Errors ->
                errors.hasErrors().shouldBeTrue()
                errors.getErrors().size.shouldBe(1)
                errors.getErrors()[0].message.shouldBe(Message("errors.common.name.required"))
                errors.getErrors()[0].type.shouldBe(Type("required"))
                errors.getErrors()[0].path.shouldBe(Path("testPath.text"))
            }
        }

        fun testEmptyIdentifier(question: BaseSection.BaseQuestion) {
            question.validateConfiguration("testPath").let { errors: Errors ->
                errors.hasErrors().shouldBeTrue()
                errors.getErrors().size.shouldBe(2)
                errors.getErrors()[0].message.shouldBe(Message("errors.common.identifier.required"))
                errors.getErrors()[0].key.shouldBe(Field("identifier"))
                errors.getErrors()[0].type.shouldBe(Type("required"))
                errors.getErrors()[0].path.shouldBe(Path("testPath.identifier"))
                errors.getErrors()[1].message.shouldBe(Message("errors.common.identifier.alphaNumeric"))
                errors.getErrors()[1].key.shouldBe(Field("identifier"))
                errors.getErrors()[1].type.shouldBe(Type("pattern"))
                errors.getErrors()[1].path.shouldBe(Path("testPath.identifier"))
            }
        }
    }
}