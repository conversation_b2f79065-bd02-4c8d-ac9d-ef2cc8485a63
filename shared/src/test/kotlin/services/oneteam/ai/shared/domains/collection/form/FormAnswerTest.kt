package services.oneteam.ai.shared.domains.collection.form

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.collection.form.FormAnswer.TextAnswer
import services.oneteam.ai.shared.domains.workspace.*

class FormAnswerTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun getJson(): Json {
        return Json {
            classDiscriminator = "type"
        }
    }

    @Test
    fun `test deserialization`() {
        check(deserialize<FormAnswer<TextAnswer>>(getAnswerString("text")))
        check(deserialize<FormAnswer<FormAnswer.NumberAnswer>>(getAnswerString("number")))
        check(deserialize<FormAnswer<FormAnswer.BooleanAnswer>>(getAnswerString("boolean")))
        check(deserialize<FormAnswer<FormAnswer.DateAnswer>>(getAnswerString("date")))
        check(deserialize<FormAnswer<FormAnswer.SelectAnswer>>(getAnswerString("select")))
        check(deserialize<FormAnswer<FormAnswer.MultiSelectAnswer>>(getAnswerString("multiSelect")))
        check(deserialize<FormAnswer<FormAnswer.TableAnswer>>(getAnswerString("table")))
        check(deserialize<FormAnswer<FormAnswer.JsonAnswer>>(getAnswerString("json")))
        check(deserialize<FormAnswer<FormAnswer.JsonAnswer>>(getAnswerString("files")))
//        check(deserialize<FormAnswer<FormAnswer.SchemaAnswer>>(getAnswerString("schema"))) // todo: fix in future ticket
        // TODO automatically check all types
    }

    @Test
    fun `test serialization`() {
        check(getJson().encodeToString(Answers.text()))
        check(getJson().encodeToString(Answers.number()))
        check(getJson().encodeToString(Answers.boolean()))
        check(getJson().encodeToString(Answers.date()))
        check(getJson().encodeToString(Answers.select()))
        check(getJson().encodeToString(Answers.multiSelect()))
        check(getJson().encodeToString(Answers.table()))
        check(getJson().encodeToString(Answers.json()))
        check(getJson().encodeToString(Answers.file()))
//        check(getJson().encodeToString(Answers.schema())) // todo: fix in future ticket
        // TODO automatically check all types
    }

//    @Test
//    fun `test toJsonElement`() {
//        Answers.text().toJsonElement()
//    }

    fun check(json: String) {
        logger.debug(json)
        assertNotNull(json)
    }

    fun check(answer: FormAnswer<*>) {
        logger.debug(answer.toString())
        assertNotNull(answer)
    }

    fun getAnswerString(name: String): String {
        return this::class.java.getResource("/answers/${name.lowercase()}.json")!!.readText()
    }

    inline fun <reified T : FormAnswer<*>> deserialize(json: String): T {
        return getJson().decodeFromString(json)
    }

    class Answers {
        companion object {
            fun text(): FormAnswer.TextAnswer {
                return FormAnswer.TextAnswer(BaseSection.Id("1"), "answer")
            }

            fun number(): FormAnswer.NumberAnswer {
                return FormAnswer.NumberAnswer(BaseSection.Id("1"), "1")
            }

            fun boolean(): FormAnswer.BooleanAnswer {
                return FormAnswer.BooleanAnswer(BaseSection.Id("1"), true)
            }

            fun date(): FormAnswer.DateAnswer {
                return FormAnswer.DateAnswer(BaseSection.Id("1"), "2021-01-01")
            }

            fun select(): FormAnswer.SelectAnswer {
                return FormAnswer.SelectAnswer(BaseSection.Id("1"), "option")
            }

            fun multiSelect(): FormAnswer.MultiSelectAnswer {
                return FormAnswer.MultiSelectAnswer(
                    BaseSection.Id("1"),
                    listOf(JsonPrimitive("option1"), JsonPrimitive("option2"))
                )
            }

            fun table(): FormAnswer.TableAnswer {
                return FormAnswer.TableAnswer(
                    BaseSection.Id("1"), OrderedMap(
                        listOf(
                            TableAnswerRow(
                                RowId("1"),
                                mapOf(BaseSection.Id("1") to text(), BaseSection.Id("2") to number())
                            )
                        )
                    )
                )
            }

            fun json(): FormAnswer.JsonAnswer {
                return FormAnswer.JsonAnswer(
                    BaseSection.Id("1"),
                    mapOf(BaseSection.Id("1") to text(), BaseSection.Id("2") to number())
                )
            }

            fun file(): FormAnswer.FileAnswer {
                return FormAnswer.FileAnswer(BaseSection.Id("1"), listOf(FileAnswerValue("file1", "file1.txt")))
            }

        }
    }
}