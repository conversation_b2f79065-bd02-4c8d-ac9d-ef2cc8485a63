package services.oneteam.ai.shared.domains.workspace.validation

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.workspace.validation.StringBounds
import services.oneteam.ai.shared.domains.workspace.validation.StringLengthConstraint

class StringLengthConstraintTest {

    @Test
    fun `when string is within bounds then no errors`() {
        val constraint = StringLengthConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error",
            StringBounds(1, 10),
        )
        val errors = constraint.validate(Pair("A", "B"))
        assertFalse(errors.hasErrors())
    }

    @Test
    fun `when string is below bounds then errors`() {
        val constraint = StringLengthConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error",
            StringBounds(1, 10),
        )
        val errors = constraint.validate(Pair("A", ""))
        assertTrue(errors.hasErrors())
        assertEquals(1, errors.getErrors().size)
        assertEquals("length", errors.getErrors()[0].type.value)
        assertEquals("name", errors.getErrors()[0].path.value)
        assertEquals("error", errors.getErrors()[0].message.value)
    }

    @Test
    fun `when string is above bounds then errors`() {
        val constraint = StringLengthConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error",
            StringBounds(1, 10),
        )
        val errors = constraint.validate(Pair("A", "B".repeat(11)))
        assertTrue(errors.hasErrors())
        assertEquals(1, errors.getErrors().size)
        assertEquals("length", errors.getErrors()[0].type.value)
        assertEquals("name", errors.getErrors()[0].path.value)
        assertEquals("error", errors.getErrors()[0].message.value)
    }

    @Test
    fun `when string is null then no errors`() {
        val constraint = StringLengthConstraint<Pair<String, String?>>(
            "name",
            { pair -> pair.second },
            "error",
            StringBounds(1, 10),
        )
        val errors = constraint.validate(Pair("A", null))
        assertFalse(errors.hasErrors())
    }

    @Test
    fun `when string is exactly minimum bound then no errors`() {
        val constraint = StringLengthConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error",
            StringBounds(1, 10),
        )
        val errors = constraint.validate(Pair("A", "B"))
        assertFalse(errors.hasErrors())
    }

    @Test
    fun `when string is exactly maximum bound then no errors`() {
        val constraint = StringLengthConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error",
            StringBounds(1, 10),
        )
        val errors = constraint.validate(Pair("A", "B".repeat(10)))
        assertFalse(errors.hasErrors())
    }

    @Test
    fun `when bounds are null then no errors`() {
        val constraint = StringLengthConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error",
            StringBounds(null, null),
        )
        val errors = constraint.validate(Pair("A", "Any length string"))
        assertFalse(errors.hasErrors())
    }

    @Test
    fun `when minimum bound is null then no errors`() {
        val constraint = StringLengthConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error",
            StringBounds(null, 10),
        )
        val errors = constraint.validate(Pair("A", "B"))
        assertFalse(errors.hasErrors())
    }

    @Test
    fun `when maximum bound is null then no errors`() {
        val constraint = StringLengthConstraint<Pair<String, String>>(
            "name",
            { pair -> pair.second },
            "error",
            StringBounds(1, null),
        )
        val errors = constraint.validate(Pair("A", "B"))
        assertFalse(errors.hasErrors())
    }
}