{"description": "", "endingVariables": [], "id": "_Cd9xwAYT1o9nZFi6kkZS", "metadata": {"createdAt": "2025-04-08T06:16:49.767Z", "updatedAt": "2025-04-08T06:16:49.767Z"}, "name": "test flows in flows set variable", "start": "step1", "startingVariables": [{"identifier": "foundation", "properties": {"required": true}, "type": "foundation.3e3a903a-26e4-4d2a-8924-7b513fd3929c"}], "status": "active", "steps": {"step1": {"id": "step1", "metadata": {"createdAt": "2025-04-08T06:17:17.122Z", "updatedAt": "2025-04-08T06:17:17.122Z"}, "name": "Flow", "next": "", "properties": {"inputs": {"flowConfigurationId": "", "foundation": ""}}, "variant": "flow"}}, "triggers": {"KrAaCspkB5UkUDgprMuNq": {"id": "KrAaCspkB5UkUDgprMuNq", "metadata": {"createdAt": "2025-04-08T06:16:53.287Z", "updatedAt": "2025-04-08T06:16:53.287Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "test flows in flows set variable", "foundationConfigurationId": "3e3a903a-26e4-4d2a-8924-7b513fd3929c", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}