{"description": "iterator-foreach", "id": "iterator-foreach", "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "iterator-foreach", "start": "step1", "steps": {"step1": {"id": "step1", "next": null, "variant": "iterator", "name": "iterator forEach test", "properties": {"typePrimaryIdentifier": "iteratorForEach", "inputs": {}, "configuration": {"start": "subStep1", "startingVariables": [{"identifier": "iterator_step1_item", "type": "json", "properties": {"required": true}}, {"identifier": "iterator_step1_index", "type": "number", "properties": {"required": true}}, {"identifier": "iterator_step1_output", "type": "number", "properties": {"required": false}}], "steps": {"subStep1": {"id": "subStep1", "variant": "setVariables", "name": "iterator test", "properties": {"typePrimaryIdentifier": "TODO: add type identifier here", "variables": [{"identifier": "iterator_step1_output", "type": "string", "value": "{{iterator_step1_index}}"}]}}}}}}}, "triggers": {"trigger1": {"id": "trigger2", "name": "Manual trigger", "properties": {"typePrimaryIdentifier": "manualTriggerFromFoundation", "inputs": {"buttonLabel": "Run foundation flow", "foundationConfigurationId": "emp"}}}}}