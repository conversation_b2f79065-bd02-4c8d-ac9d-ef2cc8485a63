{"description": "", "endingVariables": [{"identifier": "set_variable_foundation_id", "type": "text"}], "id": "oYxeBOXstIMHOGqFUMbwX", "metadata": {"createdAt": "2025-04-08T06:17:28.930Z", "updatedAt": "2025-04-08T06:17:28.930Z"}, "name": "test helper set variable", "start": "WYCHF6GrBCVsDEbDpxWA9", "startingVariables": [{"identifier": "foundation", "properties": {"required": true}, "type": "foundation.3e3a903a-26e4-4d2a-8924-7b513fd3929c"}], "status": "active", "steps": {"WYCHF6GrBCVsDEbDpxWA9": {"id": "WYCHF6GrBCVsDEbDpxWA9", "metadata": {"createdAt": "2025-04-09T04:33:29.771Z", "updatedAt": "2025-04-09T04:33:29.771Z"}, "name": "Set variable(s)", "next": null, "properties": {"variables": [{"identifier": "set_variable_foundation_id", "type": "text", "value": "{{foundation.id}}"}]}, "variant": "setVariables"}}, "triggers": {"542UVmNduYBZmmZKScw-X": {"id": "542UVmNduYBZmmZKScw-X", "metadata": {"createdAt": "2025-04-08T06:17:34.407Z", "updatedAt": "2025-04-08T06:17:34.407Z"}, "name": "Manually trigger from a foundation", "next": null, "properties": {"inputs": {"buttonLabel": "test helper set variable", "foundationConfigurationId": "3e3a903a-26e4-4d2a-8924-7b513fd3929c", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}