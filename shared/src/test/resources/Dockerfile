# there is an existing docker file for this sync server but its not publically accessable :,) https://github.com/automerge/automerge-repo-sync-server/issues/8

FROM alpine:3.14
LABEL authors="sebastian.whiffen"

RUN apk add --no-cache curl

RUN apk add --no-cache git
RUN git clone https://github.com/automerge/automerge-repo-sync-server.git /app

RUN apk add --no-cache nodejs-current npm

WORKDIR /app
RUN npm install

HEALTHCHECK CMD curl --fail http://localhost:3030/ || exit 1

EXPOSE 3030
CMD ["sh", "-c", "DEBUG=* npm start"]