{"description": "Tax return document", "flows": {"order": [], "entities": {}}, "forms": [{"description": "Personal Information", "id": "12345", "key": "PI@#^&$*&@#$", "metadata": {"createdAt": "2024-11-25T02:46:32.044Z", "updatedAt": "2024-11-25T02:46:32.044Z"}, "name": "personal_info"}, {"description": "Income Details", "id": "67890", "key": "I   D", "metadata": {"createdAt": "2024-11-25T02:46:49.296Z", "updatedAt": "2024-11-25T02:46:49.296Z"}, "name": "income_details"}, {"description": "Deductions", "id": "11223", "key": "DD", "metadata": {"createdAt": "2024-11-25T02:46:52.821Z", "updatedAt": "2024-11-25T02:46:52.822Z"}, "name": "deductions"}, {"description": "Tax Summary", "id": "44556", "key": "TS", "metadata": {"createdAt": "2024-11-25T02:46:57.468Z", "updatedAt": "2024-11-25T02:46:57.468Z"}, "name": "tax_summary"}], "foundations": [{"description": "Taxpayer Information", "id": "caf4a93b-93b1-4c44-8151-2563e2ec8ac7", "metadata": {"createdAt": "2024-11-25T01:40:56.762790Z", "updatedAt": "2024-11-25T01:40:56.762790Z"}, "name": "taxpayer_info", "relationship": "OneToMany"}, {"description": "Employer Information", "id": "HHWCYzyNq9uVbtZsLnqEl", "metadata": {"createdAt": "2024-11-25T02:35:19.812Z", "updatedAt": "2024-11-25T02:35:19.812Z"}, "name": "employer_info", "objectId": "E12345", "relationship": "OneToMany", "workspaceId": 1}], "id": 1, "key": "TR1334987 S&##", "metadata": {"createdAt": "2024-11-25T01:40:56.749007Z", "updatedAt": "2024-11-25T01:40:56.749009Z"}, "name": "tax_return_2024", "series": {}, "labels": {}}