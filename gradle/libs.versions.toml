[versions]
kotlinVersion = "2.1.21"
logbackVersion = "1.5.18"
postgresVersion = "42.7.6"
h2Version = "2.3.232"
exposedVersion = "0.61.0"
hikaricpVersion = "6.3.0"
koinVersion = "4.0.4"
flywayVersion = "11.9.1"
kotestVersion = "5.9.1"
testContainerPostgresqlVersion = "1.21.1"
dateTimeVersion = "0.6.2"
mockitoVersion = "5.18.0"
mockkVersion = "1.14.2"
jsonSchemaValidatorVersion = "1.5.7"
jsonPathVersion = "2.9.0"
ktorVersion = "3.1.3"
sonarqubeVersion = "6.2.0.5505"
azureVersion = "1.2.35"
dependencyCheckVersion = "12.1.2"
nanoidKotlinVersion = "1.0.1"
jsonataVersion = "0.9.8"
gsonVersion = "2.13.1"
commonsTextVersion = "1.13.1"
assertjVersion = "3.27.3"
dataframeVersion = "0.15.0"
opentelemetryKtor = "2.13.3-alpha"
azureWebPubsubVersion = "1.4.1"
byteBuddyVersion = "1.17.5"
objenesisVersion = "3.4"

[libraries]

junit-jupiter-params = { module = "org.junit.jupiter:junit-jupiter-params" }

# plugins for buildSrc/build.gradle.kts
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlinVersion" }
serialization-gradle-plugin = { module = "org.jetbrains.kotlin.plugin.serialization:org.jetbrains.kotlin.plugin.serialization.gradle.plugin", version.ref = "kotlinVersion" }
sonarqube-gradle-plugin = { module = "org.sonarqube:org.sonarqube.gradle.plugin", version.ref = "sonarqubeVersion" }

assertj-core = { module = "org.assertj:assertj-core", version.ref = "assertjVersion" }
commons-text = { module = "org.apache.commons:commons-text", version.ref = "commonsTextVersion" }
gson = { module = "com.google.code.gson:gson", version.ref = "gsonVersion" }
jsonata = { module = "com.dashjoin:jsonata", version.ref = "jsonataVersion" }

postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresVersion" }
h2 = { module = "com.h2database:h2", version.ref = "h2Version" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logbackVersion" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "dateTimeVersion" }

exposed-core = { module = "org.jetbrains.exposed:exposed-core", version.ref = "exposedVersion" }
exposed-dao = { module = "org.jetbrains.exposed:exposed-dao", version.ref = "exposedVersion" }
exposed-jdbc = { module = "org.jetbrains.exposed:exposed-jdbc", version.ref = "exposedVersion" }
exposed-json = { module = "org.jetbrains.exposed:exposed-json", version.ref = "exposedVersion" }
exposed-java-time = { module = "org.jetbrains.exposed:exposed-java-time", version.ref = "exposedVersion" }

hikari = { module = "com.zaxxer:HikariCP", version.ref = "hikaricpVersion" }

koin-bom = { module = "io.insert-koin:koin-bom", version.ref = "koinVersion" }

flyway-core = { module = "org.flywaydb:flyway-core", version.ref = "flywayVersion" }
flyway-database-postgresql = { module = "org.flywaydb:flyway-database-postgresql", version.ref = "flywayVersion" }

kotest = { module = "io.kotest:kotest-assertions-core-jvm", version.ref = "kotestVersion" }
kotest-properties = { module = "io.kotest:kotest-property", version.ref = "kotestVersion" }
kotest-runner = { module = "io.kotest:kotest-runner-junit5", version.ref = "kotestVersion" }
mockito = { module = "org.mockito:mockito-core", version.ref = "mockitoVersion" }
mockk = { module = "io.mockk:mockk", version.ref = "mockkVersion" }

tcpostgresql = { module = "org.testcontainers:postgresql", version.ref = "testContainerPostgresqlVersion" }
test-containers = { module = "org.testcontainers:testcontainers", version.ref = "testContainerPostgresqlVersion" }

automerge = { module = "org.automerge:automerge", version = "0.0.7" }

json-schema-validator = { module = "com.networknt:json-schema-validator", version.ref = "jsonSchemaValidatorVersion" }
json-path = { module = "com.jayway.jsonpath:json-path", version.ref = "jsonPathVersion" }

ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktorVersion" }
ktor-client-cio = { module = "io.ktor:ktor-client-cio", version.ref = "ktorVersion" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktorVersion" }
ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktorVersion" }
ktor-client-mock = { module = "io.ktor:ktor-client-mock", version.ref = "ktorVersion" }

azure-sdk-bom = { module = "com.azure:azure-sdk-bom", version.ref = "azureVersion" }
azure-storage-blob = { module = "com.azure:azure-storage-blob" }
nanoid = { module = "io.viascom.nanoid:nanoid", version.ref = "nanoidKotlinVersion" }
opentelemetry-ktor = { module = "io.opentelemetry.instrumentation:opentelemetry-ktor-3.0", version.ref = "opentelemetryKtor" }

dataframe = { module = "org.jetbrains.kotlinx:dataframe", version.ref = "dataframeVersion" }

azure-web-pubsub = { module = "com.azure:azure-messaging-webpubsub", version.ref = "azureWebPubsubVersion" }
bytebuddy = { module = "net.bytebuddy:byte-buddy", version.ref = "byteBuddyVersion" }
objenesis = { module = "org.objenesis:objenesis", version.ref = "objenesisVersion" }

[bundles]
exposed = ["exposed-core", "exposed-dao", "exposed-jdbc", "exposed-json", "exposed-java-time"]
kotest = ["kotest", "kotest-properties", "kotest-runner"]
flyway = ["flyway-core", "flyway-database-postgresql"]
ktor-client = ["ktor-client-core", "ktor-client-cio", "ktor-client-content-negotiation", "ktor-serialization-kotlinx-json"]

[plugins]
ktor = { id = "io.ktor.plugin", version.ref = "ktorVersion" }

dependencyCheck = { id = "org.owasp.dependencycheck", version.ref = "dependencyCheckVersion" }