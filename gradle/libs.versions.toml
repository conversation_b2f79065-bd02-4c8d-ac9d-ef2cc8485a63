[versions]
kotlinVersion = "2.2.0"
logbackVersion = "1.5.18"
logbackJacksonVersion = "0.1.5"
postgresVersion = "42.7.7"
h2Version = "2.3.232"
exposedVersion = "0.61.0"
hikaricpVersion = "6.3.1"
koinVersion = "4.1.0"
flywayVersion = "11.10.3"
kotestVersion = "5.9.1"
testContainerPostgresqlVersion = "1.21.3"
dateTimeVersion = "0.7.1-0.6.x-compat"
mockitoVersion = "5.18.0"
mockkVersion = "1.14.5"
jsonSchemaValidatorVersion = "1.5.8"
jsonPathVersion = "2.9.0"
sonarqubeVersion = "6.2.0.5505"
azureVersion = "1.2.36"
dependencyCheckVersion = "12.1.3"
nanoidKotlinVersion = "1.0.1"
jsonataVersion = "0.9.8"
gsonVersion = "2.13.1"
commonsTextVersion = "1.13.1"
assertjVersion = "3.27.3"
dataframeVersion = "0.15.0"
opentelemetryKtor = "2.13.3-alpha"
benchmarkVersion = "0.4.14"
azureWebPubsubVersion = "1.5.0"
byteBuddyVersion = "1.17.6"
objenesisVersion = "3.4"
logstashVersion = "8.1"

[libraries]

junit-jupiter-params = { module = "org.junit.jupiter:junit-jupiter-params" }

# plugins for buildSrc/build.gradle.kts
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlinVersion" }
serialization-gradle-plugin = { module = "org.jetbrains.kotlin.plugin.serialization:org.jetbrains.kotlin.plugin.serialization.gradle.plugin", version.ref = "kotlinVersion" }
sonarqube-gradle-plugin = { module = "org.sonarqube:org.sonarqube.gradle.plugin", version.ref = "sonarqubeVersion" }

benchmarks = { module = "org.jetbrains.kotlinx:kotlinx-benchmark-runtime", version.ref = "benchmarkVersion" }

assertj-core = { module = "org.assertj:assertj-core", version.ref = "assertjVersion" }
commons-text = { module = "org.apache.commons:commons-text", version.ref = "commonsTextVersion" }
gson = { module = "com.google.code.gson:gson", version.ref = "gsonVersion" }
jsonata = { module = "com.dashjoin:jsonata", version.ref = "jsonataVersion" }

postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresVersion" }
h2 = { module = "com.h2database:h2", version.ref = "h2Version" }

logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logbackVersion" }
logback-json-classic = { module = "ch.qos.logback.contrib:logback-json-classic", version.ref = "logbackJacksonVersion" }
logback-jackson = { module = "ch.qos.logback.contrib:logback-jackson", version.ref = "logbackJacksonVersion" }

kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "dateTimeVersion" }

exposed-core = { module = "org.jetbrains.exposed:exposed-core", version.ref = "exposedVersion" }
exposed-dao = { module = "org.jetbrains.exposed:exposed-dao", version.ref = "exposedVersion" }
exposed-jdbc = { module = "org.jetbrains.exposed:exposed-jdbc", version.ref = "exposedVersion" }
exposed-json = { module = "org.jetbrains.exposed:exposed-json", version.ref = "exposedVersion" }
exposed-java-time = { module = "org.jetbrains.exposed:exposed-java-time", version.ref = "exposedVersion" }

hikari = { module = "com.zaxxer:HikariCP", version.ref = "hikaricpVersion" }

koin-bom = { module = "io.insert-koin:koin-bom", version.ref = "koinVersion" }

flyway-core = { module = "org.flywaydb:flyway-core", version.ref = "flywayVersion" }
flyway-database-postgresql = { module = "org.flywaydb:flyway-database-postgresql", version.ref = "flywayVersion" }

kotest = { module = "io.kotest:kotest-assertions-core-jvm", version.ref = "kotestVersion" }
kotest-properties = { module = "io.kotest:kotest-property", version.ref = "kotestVersion" }
kotest-runner = { module = "io.kotest:kotest-runner-junit5", version.ref = "kotestVersion" }
mockito = { module = "org.mockito:mockito-core", version.ref = "mockitoVersion" }
mockk = { module = "io.mockk:mockk", version.ref = "mockkVersion" }

tcpostgresql = { module = "org.testcontainers:postgresql", version.ref = "testContainerPostgresqlVersion" }
test-containers = { module = "org.testcontainers:testcontainers", version.ref = "testContainerPostgresqlVersion" }

automerge = { module = "org.automerge:automerge", version = "0.0.7" }

json-schema-validator = { module = "com.networknt:json-schema-validator", version.ref = "jsonSchemaValidatorVersion" }
json-path = { module = "com.jayway.jsonpath:json-path", version.ref = "jsonPathVersion" }

azure-sdk-bom = { module = "com.azure:azure-sdk-bom", version.ref = "azureVersion" }
azure-storage-blob = { module = "com.azure:azure-storage-blob" }
azure-security-keyvault-secrets = { module = "com.azure:azure-security-keyvault-secrets" }
azure-identity = { module = "com.azure:azure-identity" }
azure-web-pubsub = { module = "com.azure:azure-messaging-webpubsub", version.ref = "azureWebPubsubVersion" }

nanoid = { module = "io.viascom.nanoid:nanoid", version.ref = "nanoidKotlinVersion" }
opentelemetry-ktor = { module = "io.opentelemetry.instrumentation:opentelemetry-ktor-3.0", version.ref = "opentelemetryKtor" }

dataframe = { module = "org.jetbrains.kotlinx:dataframe", version.ref = "dataframeVersion" }

bouncycastle = { module = "org.bouncycastle:bcpkix-jdk18on", version = "1.81" }
bytebuddy = { module = "net.bytebuddy:byte-buddy", version.ref = "byteBuddyVersion" }
objenesis = { module = "org.objenesis:objenesis", version.ref = "objenesisVersion" }
logstash = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logstashVersion" }

[bundles]
exposed = ["exposed-core", "exposed-dao", "exposed-jdbc", "exposed-json", "exposed-java-time"]
kotest = ["kotest", "kotest-properties", "kotest-runner"]
flyway = ["flyway-core", "flyway-database-postgresql"]
logback = ["logback-classic", "logback-json-classic", "logback-jackson"]

[plugins]
allopen = { id = "org.jetbrains.kotlin.plugin.allopen", version.ref = "kotlinVersion" }
benchmark = { id = "org.jetbrains.kotlinx.benchmark", version.ref = "benchmarkVersion" }