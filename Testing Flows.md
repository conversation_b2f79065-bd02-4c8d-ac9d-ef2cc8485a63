## Testing Flows

In the DEV environment, <PERSON> maintains a workspace called `Test Runner` which is used to exercise the system and verify
behaviour.

We maintain a copy of this workspace in sample data called `testrunner`:
`shared/src/main/resources/sample-data/testrunner`

> We need to keep this up to date as changes are made in DEV.
> We need to configure any of the TEMPLATE foundations and forms, and any answers for the TEMPLATE forms.

You can use this workspace locally:

- ensure your bruno variables are set up correctly so unique keys are generated
    - bru.setVar("initials", 'PXR');
    - bru.setVar("discriminator", 3000);
    - bru.setVar("workspaceNumber",3000)

![bruno-set-vars.png](docs/images/bruno-set-vars.png)

- use bruno to run `create sample data testrunner`
  ![bruno-testrunner.png](docs/images/bruno-testrunner.png)


- load this new workspace in your browser
- go to the workspace form `Test Runner`
  ![testrunner-workspace-form.png](docs/images/testrunner-workspace-forms.png)
- run the actions one after the other (wait for each to complete before running the next one)
    - Set up tests
    - Start testing
    - Run Demo <PERSON>

![testrunner-workspace-form-actions.png](docs/images/testrunner-workspace-form-actions.png)