Question: Why is `TransactionManager.currentOrNull()` not returning null after a `newSuspendedTransaction` block in
Ktor?

> SPOILER: It turns out that this occurs when running ktor in debug mode - turning off debug mode resolves the issue.
> https://slack-chats.kotlinlang.org/t/29155777/hi-all-i-m-looking-for-some-insight-into-this-issue-i-m-havi#daa273fe-48cb-461e-bd0c-4cb28f9df8b4
> https://slack-chats.kotlinlang.org/t/29155777/hi-all-i-m-looking-for-some-insight-into-this-issue-i-m-havi#2f581f8c-bc85-4a0d-91d3-839c77c22a9c

I’m looking for some insight into this issue I’m having with my Ktor/exposed application…If I do the following in a KTOR
route, after the newSuspendedTransaction block is close, TransactionManager.currentOrNull() returns a not null value - I
would expect it to be null.If I remove the delay() call, it will work as expected.

```kotlin
get("/") {
    require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
    newSuspendedTransaction {
        require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
        delay(2000) // Simulate some processing time - if I remove this delay things will work as I expect
    }
    // this next line fails 
    require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
    call.respondText("OK")
}
```

To make it even more mysterious I cannot get this scenario to fail in a test - the following code works fine, and
TransactionManager.currentOrNull() returns null after the newSuspendedTransaction scope closes.

```kotlin
suspend fun main() {
    val database = TestPostgresDatabase

    val fixtures = Fixtures(database).initialise()

    coroutineScope {
        launch(Dispatchers.IO) {
            require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
            newSuspendedTransaction {
                require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
                delay(2000) // Simulate some processing time
            }
            require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
            println("Transaction completed successfully ${TransactionManager.currentOrNull()}")
        }.join()
    }
}
```

Can anyone tell me what I’m doing wrong or missing please? What I'm trying to do is test if a transaction is already in
progress or not using TransactionManager.currentOrNull() so I know when to initialise my connection with row level
security parameters. For the most part its working well but there is this one condition where there is a network call -
which must result in yielding similar to the delay above, and then I get left with a closed connection...

----

Answer:

This is a classic issue with Kotlin coroutines and thread-local storage in Ktor applications. The problem you're
experiencing is related to how `TransactionManager.currentOrNull()` uses `ThreadLocal` storage and how Ktor's coroutine
dispatcher works.

## The Root Cause

When you call `delay()` inside the `newSuspendedTransaction` block, the coroutine gets suspended and may resume on a
different thread. However, the transaction state in Exposed is stored in a `ThreadLocal` variable. Here's what happens:

**Before delay**: Transaction runs on Thread A, `ThreadLocal` contains the transaction
**During delay**: Coroutine suspends, transaction completes, but `ThreadLocal` on Thread A still holds the reference
**After delay**: Coroutine resumes on Thread B, but Thread B's `ThreadLocal` may still contain a stale transaction
reference from a previous operation

## Why It Works in Tests

Your test works because:
You're explicitly using `Dispatchers.IO`
The coroutine context is more controlled
There might be less thread switching happening in the test environment

## Solutions

### Solution 1: Use Explicit Dispatcher (Recommended)

```kotlin:ktor-route-fix.kt
get("/") {
    require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
    
    withContext(Dispatchers.IO) {
        newSuspendedTransaction {
            require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
            delay(2000) // This should now work correctly
        }
    }
    
    require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
    call.respondText("OK")
}
```

### Solution 2: Clear Transaction Context Explicitly

```kotlin:explicit-cleanup.kt
get("/") {
    require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
    
    try {
        newSuspendedTransaction {
            require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
            delay(2000)
        }
    } finally {
        // Explicitly clear the transaction context
        TransactionManager.resetCurrent(null)
    }
    
    require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
    call.respondText("OK")
}
```

### Solution 3: Use a Custom Transaction Wrapper

```kotlin:transaction-wrapper.kt
suspend fun <T> safeTransaction(block: suspend Transaction.() -> T): T {
    return withContext(Dispatchers.IO) {
        try {
            newSuspendedTransaction(block = block)
        } finally {
            TransactionManager.resetCurrent(null)
        }
    }
}

// Usage in your route
get("/") {
    require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
    
    safeTransaction {
        require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
        delay(2000)
    }
    
    require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
    call.respondText("OK")
}
```

### Solution 4: Configure Ktor with Proper Dispatcher

```kotlin:ktor-config.kt
fun Application.configureDatabases() {
    // Configure your database connection
    
    // Use a dedicated dispatcher for database operations
    install(CallLogging)
    
    routing {
        route("/api") {
            // Wrap all database routes with IO dispatcher
            intercept(ApplicationCallPipeline.Call) {
                withContext(Dispatchers.IO) {
                    proceed()
                }
            }
            
            get("/") {
                require(TransactionManager.currentOrNull() == null) { "1 - transaction should not exist at the start" }
                
                newSuspendedTransaction {
                    require(TransactionManager.currentOrNull() != null) { "2 - transaction should exist now" }
                    delay(2000)
                }
                
                require(TransactionManager.currentOrNull() == null) { "3 - transaction should not exist at the end" }
                call.respondText("OK")
            }
        }
    }
}
```

## For Your Row Level Security Use Case

Since you mentioned you're trying to detect if a transaction is already in progress for row level security, here's a
more robust approach:

```kotlin:rls-helper.kt
class DatabaseContext {
    companion object {
        private val transactionContext = ThreadLocal<Boolean>()
        
        fun isInTransaction(): Boolean = transactionContext.get() ?: false
        
        suspend fun <T> withRLS(userId: String, block: suspend () -> T): T {
            return if (isInTransaction()) {
                // Already in transaction, just execute
                block()
            } else {
                // Start new transaction with RLS
                withContext(Dispatchers.IO) {
                    newSuspendedTransaction {
                        transactionContext.set(true)
                        try {
                            // Set RLS parameters
                            exec("SET row_security.user_id = '$userId'")
                            block()
                        } finally {
                            transactionContext.set(false)
                        }
                    }
                }
            }
        }
    }
}

// Usage
get("/user-data") {
    val userId = call.principal<UserPrincipal>()?.userId ?: throw UnauthorizedException()
    
    DatabaseContext.withRLS(userId) {
        // Your database operations here
        delay(2000) // Network calls, etc.
        // RLS will be properly maintained
    }
    
    call.respondText("OK")
}
```

## Key Takeaways

**Always use `Dispatchers.IO`** for database operations in Ktor
**Be aware of thread-local storage limitations** with coroutines
**Consider explicit cleanup** when dealing with stateful thread-local resources
**Test in realistic conditions** that match your production environment

The first solution using `withContext(Dispatchers.IO)` should resolve your immediate issue and is the most
straightforward fix.
