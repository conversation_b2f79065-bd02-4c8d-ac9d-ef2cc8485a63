-- switch transactions to manual mode

SELECT set_config('app.current_tenant_id', 1::text, TRUE),
       set_config('app.current_principal_id', 1::text, TRUE),
       set_config('app.is_system_user', false::text, TRUE);

SELECT *
FROM workspaces
WHERE (current_setting('app.is_system_user'::text, true)::boolean = true)
   OR id IN (SELECT workspace_users.workspace_id
             FROM workspace_users
             WHERE workspace_users.user_id =
                   COALESCE(NULLIF(current_setting('app.current_principal_id', TRUE), ''), '-1')::int);

select NULLIF(current_setting('app.current_principal_id', TRUE), '');
select COALESCE(NULLIF(current_setting('app.current_principal_id', TRUE), ''), '-1')::int;

ALTER TABLE workspaces
    ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspaces
    DISABLE ROW LEVEL SECURITY;

-- auto-generated definition
create policy tenant_isolation_policy on workspaces
    as restrictive
    for all
    using (tenant_id = (current_setting('app.current_tenant_id'::text, true))::integer);



INSERT INTO workspaces (created_at, updated_at, id, "name", "key", description, document_id, tenant_id)
VALUES ('2025-06-11T01:54:18.601286', '2025-06-11T01:54:18.601286', 355, '554', '554', '554', NULL, 1);

select workspace_id
from workspace_users
where workspace_users.user_id = current_setting('app.current_principal_id', TRUE)::int;

-- is_system_user is true OR the user is part of the workspace
-- This query checks if the current user is a system user or if they are part of the workspace

select *
from workspaces
where ((current_setting('app.is_system_user'::text, true))::boolean = true)
   OR id in (select workspace_users.workspace_id
             from workspace_users
             where workspace_users.user_id =
                   coalesce(current_setting('app.current_principal_id', TRUE)::text, '-1'));



select coalesce(current_setting('app.current_principal_id', TRUE)::text, '-1');
select current_setting('app.current_principal_id', TRUE);

select *
from workspaces
where id = 26
  and deleted = false;


SELECT foundations.id,
       foundations.created_at,
       foundations.updated_at,
       foundations."name",
       foundations."key",
       foundations.parent_id,
       foundations.foundation_configuration_id,
       foundations.workspace_id,
       foundations.tenant_id
FROM foundations
WHERE (foundations.workspace_id = 307)
  AND (foundations.parent_id IS NULL);

select *
from workspace_users
where workspace_id = 307;

rollback;


delete
from workspace_users
where workspace_id < 300;