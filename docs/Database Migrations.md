# Database Migrations

Migrations are used for managing the schema and data. The database and user setup is in the [scripts](../../scripts)
folder - see the "preseed" sql files. These should be applied when provisioning a new database.

Migrations are applied using Flyway.

- https://documentation.red-gate.com/flyway/flyway-cli-and-api/concepts/migrations

The SQL migrations are located in
the [shared/src/main/resources/db/migration](../../shared/src/main/resources/db/migration)
directory.

```sql
-- Sample: src/main/resources/db/migration/V7__add-sample.sql

CREATE TABLE sample
(
    id          BIGSERIAL PRIMARY KEY,
    name        VARCHAR(20) NOT NULL,
    description TEXT        NOT NULL
);
```

Migrations can also be implemented in Kotlin (in [shared/src/main/kotlin/db/migration]()):

```kotlin
// Sample: src/main/kotlin/db/migration/V5__Sample.kt

package db.migration

import org.flywaydb.core.api.migration.BaseJavaMigration
import org.flywaydb.core.api.migration.Context

class V5__Sample : BaseJavaMigration() {
    override fun migrate(context: Context?) {
        val statement = context?.connection?.prepareStatement(
            """
        CREATE TABLE article (
          id bigserial primary key,
          name varchar(20) NOT NULL,
          description text NOT NULL
        );
      """
        )
        statement.use { it?.execute() }
    }
}
```

## Configuration

Configuration for Flyway is in [src/main/resources/application.yaml](../../src/main/resources/application.yaml):

```yaml
flyway:
    enabled: true
    locations: [ "classpath:db/migration/postgres-only", "classpath:db/migration/common" ]
    # properties keyed off the environment variable WEBSITE_SITE_NAME
    properties: {
        "local": {
            "domain": "localhost",
            "protocol": "http"
        },
        "otai-dev": {
            "domain": "innovation.dev.oneteam.services",
            "protocol": "https"
        }
    }
```

The application must be run with a WEBSITE_SITE_NAME environment variable so that it knows which set of flyway
properties to use from `application.yaml`.

## Rebuilding the database locally

If you want to start from a clean slate, you can drop the database and recreate it by running the following command:

```shell
./scripts/db-recreate-local.mjs
```

When you start the application migrations will be applied to the database leaving it in a ready state.

## More Information:

* https://github.com/JetBrains/Exposed/tree/main/samples/exposed-migration
* https://documentation.red-gate.com/fd/migrations-184127470.html
* https://documentation.red-gate.com/fd/flyway-cli-and-api-183306238.html
* https://www.red-gate.com/blog/organising-your-migrations
