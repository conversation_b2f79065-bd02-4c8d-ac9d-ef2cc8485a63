# Jsonata Functions

## Parameters

For our functions that support arrays/pairwise operations as follows.

Assuming our function is SUM:

### When multiple values are passed

```text
$SUM(1,2,3) returns the sum of the numbers, which is 6.
```

### When a single list is passed

```text
$SUM([1,2,3]) returns the sum of the values in the array, which is 6.
```

### When multiple lists are passed

```text
$SUM([1,2,3], [4,5,6]) returns the sum of the pairwise values in the arrays, which is [5, 7, 9].
```

See [Pairwise](pairwise/Pairwise.md) for more details on how pairwise operations work.

## Handling invalid numbers and nulls

For numerical operations, we need to handle invalid numbers and nulls gracefully.
If a value is not a valid number or null, it should be treated as 0 in the context of addition/subtraction and 1 for
multiplication/divide. 
