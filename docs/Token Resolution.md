# Placeholder Resolution

We have jsonata expressions that have placeholders. For example:

```json
{
  "id": "step1",
  "name": "Literal addition",
  "next": null,
  "properties": {
    "variables": [
      {
        "identifier": "value_1",
        "type": "number",
        "value": "1 + 1"
      },
      {
        "identifier": "value_1Times10",
        "type": "number",
        "value": "{{value_1}} * 10"
      }
    ]
  },
  "variant": "setVariables"
}
```

We want to process each variable one at a time, setting the value of the variable in the flow context.

To do this we need to:

1. Build a map containing the values of the placeholders present in the expression.
    1. Since building this may be expensive we only want to do it for the placeholders that are referenced in the
       expression
        1. Look at the expression we are going to process and find the placeholders.
        2. Use this list of placeholders to filter the list of variables we include in the json object.
2. Once we have the map of values we can use it as the jsonata context for the expression

Example:

```json title="set variables block"
{
  "identifier": "value_1",
  "type": "number",
  "value": "1 + 1"
}
```

Here there are no placeholders so we can just evaluate the expression as is.

```text title="jsonata expression"
1 + 1
```

Evaluated by jsonata returns

```text title="jsonata result"
2
```

And this gets set in the flow context under the variable `value_1`.
So now the flow context looks like:

```json title="flow context"
{
  "event": ...,
  "global": ...,
  "variables": {
    "value_1": {
      "identifier": "value_1",
      "type": "number",
      "value": 2
    }
  }
}
```

Now we can process the next variable:

```json
{
  "identifier": "value_1Times10",
  "type": "number",
  "value": "{{value_1}} * 10"
}
```

We need to build the jsonata context for this expression.
`value_1` is the only placeholder so we need to include this in the jsonata context.

```json title="jsonata context"
{
  "value_1": 2
}
```

We need to remove the `{{` and `}}` from the expression so we can evaluate it with jsonata.

Now when we run the expression we get:

```text title="jsonata expression"
value_1 * 10
```

Evaluated by jsonata returns

```text title="jsonata result"
20
```

To build this map for jsonata, we use MapBuilder instance. We have FormJsonMapBuilder, FoundationJsonMapBuilder, and
DefaultJsonMapBuilder.
These let us represent the objects in convenient ways so we can use various json paths to access the values.

If we have a form:

![Pet form completed](images/pet-form-completed.png)

Then we'll turn it into a map like this:

```json title="map"
{
  "id": 8844,
  "formConfigurationId": "a0ZjxHUe06Mm0VZSwKfSE",
  "foundationId": 4367,
  "foundation": {
    "id": 4367,
    "name": "PXR SGPIT 4013",
    "key": "PXRSGPIT4013",
    "foundationConfigurationId": "wor",
    "foundationConfiguration": {
      "id": "wor",
      "name": "Workspace",
      "relationship": "OneToMany",
      "description": null
    },
    "parentId": null
  },
  "seriesId": null,
  "intervalId": "aa2025",
  "documentId": "449mCG8zdcDKhxQbRVnDnetWBU7z",
  "annotationDocumentId": "44NNQHEpNLQMZHirv1PU6S3pF3GV",
  "formConfiguration": {
    "id": "a0ZjxHUe06Mm0VZSwKfSE",
    "key": "SIMPLEFORM",
    "name": "Pet Form",
    "seriesId": "years"
  },
  "aYgCq4JRAI": {
    "id": "aYgCq4JRAI",
    "type": "TEXT",
    "text": "Describe your favourite pet",
    "identifier": "DescribeYourFavouritePet",
    "description": "",
    "answer": "Puppies!"
  },
  "atepF23vts": {
    "id": "atepF23vts",
    "type": "table",
    "answer": [
      {
        "axhXIrL7sC": "Catto",
        "az3aG1NYmI": "cat",
        "_rowId": "azKYhr7VkPN0dq21iRnpz",
        "_rowIndex": 1
      },
      {
        "axhXIrL7sC": "Doggo",
        "az3aG1NYmI": "dog",
        "_rowId": "aIiEhgrIC7jICcWRVfWEr",
        "_rowIndex": 2
      }
    ],
    "columns": {
      "axhXIrL7sC": {
        "id": "axhXIrL7sC",
        "type": "text",
        "answer": [
          "Catto",
          "Doggo"
        ]
      },
      "az3aG1NYmI": {
        "id": "az3aG1NYmI",
        "type": "select",
        "answer": [
          "cat",
          "dog"
        ]
      },
      "ids": [
        "axhXIrL7sC",
        "az3aG1NYmI"
      ]
    }
  }
}
```

Then we can use json paths like

- `atepF23vts.columns.axhXIrL7sC.answer` to get the values of the first column in the table
- `atepF23vts.answer[0]` to get the first row of the table

So this isn't just a simple serialization of the answers, but a more complex transformation that
may repeat data in different places to make it easier to access. In the case of the answers to the table
we have the answers in the `atepF23vts.answer` array by row, but we also have the columns in `atepF23vts.columns`.


