# Pairwise operations

Our goal here is to support pairwise operations that match excel operations.
And also support multiple dimensions.

See

- https://docs.google.com/spreadsheets/d/19UGbngFmR0wV6hE_J1NakaTiO9TKTDdbB-FmIrerbKQ/edit?gid=0#gid=0
- https://oneteam-services.atlassian.net/browse/OA-1897
- https://help.hcl-software.com/dom_designer/12.0.0/basic/H_OPERATIONS_ON_LISTS.html
- https://www.calculator.net/matrix-calculator.html

- https://www.wolframalpha.com/input/?i=matrix+addition
- https://www.wolframalpha.com/input?i2d=true&i=%7B%7B1%2C2%7D%2C%7B3%2C4%7D%7D%2B%7B%7B5%2C6%7D%2C%7B7%2C8%7D%7D

----

Simple example:

```text
[1,2,3] + 4 + [[5,6]]
```

We move to adding a pair at a time:

```text
[1,2,3] + 4
```

The pair must be the same dimension, so we need to uplift 4 from dimension 0 to dimension 1.
When we uplift 4, we need to duplicate it out to the same length as the other vector.

```text
[1,2,3] + [4,4,4]
```

Then we can add them together:

```text
[1+4,2+4,3+4]
```

Giving

```text
[5,6,7]
```

Then we can add the next pair:

```text
[5,6,7] + [[5,6]]
```

We need to uplift [5,6,7] to the same dimension as [[5,6]].

```text
[[5,6,7]] + [[5,6]]
```

In both values dimension 2 has length of 1.
But dimension 1 has length of 3 and 2 respectively.
So we need to pad out [[5,6]] to be 3 long.

```text
[[5,6,7]] + [[5,6,0]]
```

Then we can add them together:

```text
[[5+5, 6+6, 7+0]]
```

Giving a final result of:

```text
[[10,12,7]]
```

----

### Background

Given

```
[
  [
    [ 1, 2, 3, 4],
    [ 5, 6, 7, 8]
  ],
  [
    [ 9,10,11,12],
    [13,14,15,16]
  ],
  [
    [17,18,19,20],
    [21,22,23,24]
  ]
]
```

Then

- Dimension 1 length = 4 (columns)
- Dimension 2 length = 2 (columns and rows - a single 2d table)
- Dimension 3 length = 3 (3 x 2d tables)

If we had ragged dimensions where not all vector lengths were the same, we would need to pad them out to the
highest dimension length. For example, if we had:

```
[
  [
    [ 1, 2, 3, 4],
    [ 5, 6, 7, 8]
  ],
  [
    [ 9,10,11,12],
    [13,14,15,16]
  ],
  [
    [17,18,19,20]
  ]
]
```

Then we would need to pad the last vector with zeros to make it the same length as the others.

Example where dimension 1 is ragged:

```text
[
  [
    [ 1, 2, 3, 4],
    [ 5, 6, 7]
  ]
]
```

Would pad out to

```text
[
  [
    [ 1, 2, 3, 4],
    [ 5, 6, 7, 0]
  ]
]
```

Example where dimension 2 is ragged:

```text
[
  [
    [ 1, 2, 3, 4],
    [ 5, 6, 7, 8]
  ],
  [
    [ 8, 9,10,11]
  ]
]
```

Would pad out to

```text
[
  [
    [ 1, 2, 3, 4],
    [ 5, 6, 7, 8]
  ],
  [
    [ 8, 9,10,11],
    [ 0, 0, 0, 0]
  ]
]
```

What value we pad with perhaps depends on the operation we are doing.
For addition, we would pad with zeros.
For multiplication, we would pad with ones.
For text values we would pad with empty strings?

We want to be able to support pairwise operations for numbers:

- addition
- subtraction
- multiplication
- division

And for text:

- concatenation

