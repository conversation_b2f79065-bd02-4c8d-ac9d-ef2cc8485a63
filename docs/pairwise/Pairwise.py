import numpy as np

# expand dimensions

# 1. `np.expand_dims(a, axis)` expands the dimensions of `a` at the specified `axis`.
# 2. `np.expand_dims(a, axis=0)` adds a new dimension at the beginning of `a`.
# 3. `np.expand_dims(a, axis=1)` adds a new dimension at the second position of `a`.

print(np.expand_dims(4, axis=0))  # [4]
print(np.expand_dims(4, axis=0) + np.zeros((1, 3), dtype=int))  # [[4 4 4]]
print(np.expand_dims(np.array([4]), axis=1))  # [[4]]

# Extend the length using broadcasting
print(np.expand_dims(np.array([4]), axis=1) + np.zeros((len(np.array([4])), 3), dtype=int))  # [[4]]

print(np.expand_dims(np.array([1, 2, 3]), axis=1))  # [[1][2][3]]
print(np.expand_dims(np.array([[5, 6]]), axis=2))  # [[[5][6]]]
print("8", np.expand_dims(np.array([[5, 6], [7, 8]]), axis=2))  # [ [[5][6]] [[7][8]] ]

print("example:", np.array([1, 2, 3]) + np.array([4]))  # [5 6 7]

### Explanation:
# 1. `[5, 6, 7]` has dimension 1.
# 2. `[[5, 6]]` has dimension 2.
# 3. Raise `[5, 6, 7]` to dimension 2: `[[5], [6], [7]]`.
# 4. Pad `[[5, 6]]` to match the length of `[[5], [6], [7]]`: `[[5, 6], [0, 0], [0, 0]]`.
# 5. Perform pairwise addition:
# - `[5] + [5, 6] = [10, 11]`
# - `[6] + [0, 0] = [6, 6]`
# - `[7] + [0, 0] = [7, 7]`.

print("example:", np.array([[5], [6], [7]]) + np.array([[5, 6]]))  # [[10 11] [11 12] [12 13] ]

#  [1, 2, 3] + 4 + [[5, 6]]
#
# [[1],[2],[3]] + [[4],[4],[4]] + [[5,6],[0,0],[0,0]]

print("example:", np.array([[1], [2], [3]]) + np.array([[4], [4], [4]]) + np.array([[5, 6], [0, 0], [0, 0]]))
# [[10 11]  [ 6  6]  [ 7  7]]

print("example:", np.array([[1], [2], [3]]) + np.array([[4], [4], [4]]))
# [[5] [6] [7]]

print("example:", np.array([[5], [6], [7]]) + np.array([[5, 6], [0, 0], [0, 0]]))
# [[10 11] [ 6  6] [ 7  7]]
