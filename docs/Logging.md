# Logging

We use logback for logging in our application.

## Servers

Servers run with the configuration in [app/src/main/resources/logback.xml](../../app/src/main/resources/logback.xml).
We use JSON formatting for the logs so that they can be easily parsed by Azure Application Insights - multiline logs
such as stacktraces then correctly read as a single log entry.

We use the logstash logback encoder to configure the stacktraces to be smaller.
This is required because Azure truncates the log messages at 16385 characters and stacktraces easily puts us over this.
See https://github.com/logfellow/logstash-logback-encoder#readme

Server log entry looks like the following (subject to change)

Web Request example:

```json
{
  "@timestamp": "2025-07-08T23:58:04.9252637Z",
  "@version": "1",
  "message": "SELECT users.id, users.created_at, users.updated_at, users.email, users.tenant_id, users.properties FROM users WHERE users.id = 753",
  "logger_name": "Exposed",
  "thread_name": "DefaultDispatcher-worker-1",
  "level": "DEBUG",
  "level_value": 10000,
  "requestId": "aoEn0426BuFiDpnbJs9M3",
  "tenant": "OT",
  "trace_id": "eb3e489a59aaec2523e7c151c6d9640f",
  "trace_flags": "01",
  "span_id": "aa6b82face1d49bf"
}
```

Flow execution example::

```json
{
  "@timestamp": "2025-07-09T01:27:15.410291106Z",
  "@version": "1",
  "message": "Step finished: Simple Condition",
  "logger_name": "services.oneteam.ai.flow.execution.listeners.FlowListenerAutomergeDocumentUpdater",
  "thread_name": "flow-runner-2",
  "level": "DEBUG",
  "level_value": 10000,
  "flowExecutionId": "7900",
  "flowConfigurationId": "conditiona1",
  "requestId": "aBzk2SQzfwZmwwziA36dq",
  "tenant": "OT",
  "trace_id": "41d035b0059eff29942fc8233c2826cb",
  "trace_flags": "01",
  "span_id": "9fb4fa518614f904"
}
```

With an error:

```json
{
  "@timestamp": "2025-07-09T01:02:38.11157415Z",
  "@version": "1",
  "message": "Validation by JsonPathDuplicateConstraint could not be completed due to a system error. Error validating constraints: Missing property in path $['labels']",
  "logger_name": "services.oneteam.ai.shared.domains.workspace.validation.Validator",
  "thread_name": "DefaultDispatcher-worker-4",
  "level": "ERROR",
  "level_value": 40000,
  "stack_trace": "com.jayway.jsonpath.PathNotFoundException: Missing property in path $['labels']\n",
  "requestId": "acT1BEJymyu0pKMWjS132",
  "tenant": "OT",
  "trace_id": "973a9c8d1bd9a611725e6c1ea8f22000",
  "trace_flags": "00",
  "span_id": "a0955bf6bc575bf8"
}
```

To query for logs in Azure Application Insights, you can use the following KQL query - this is an all encompassing query
that you can modify to filter for specific logs.

```kql
AppServiceConsoleLogs 
| extend data = extract_all(@"(?P<json>.*)", dynamic(['json']), ResultDescription)
| extend json = data[0]
| extend jsonP = dynamic_to_json(json)
// common log data
| extend timestamp = todatetime(extract_json("$['@timestamp']", jsonP))
| extend level = extract_json("$.level", jsonP)
| extend levelValue = toint(extract_json("$.level_value", jsonP))
| extend thread = extract_json("$.thread_name", jsonP)
| extend logger = extract_json("$.logger_name", jsonP)
| extend message = extract_json("$.message", jsonP)
| extend tenant = extract_json("$.tenant", jsonP)
| extend stackTrace = extract_json("$.stack_trace", jsonP)
// flow runner
| extend flowExecutionId = toint(extract_json("$.flowExecutionId", jsonP))
| extend flowConfigurationId = extract_json("$.flowConfigurationId", jsonP)
// tracing
| extend requestId = extract_json("$.requestId", jsonP)
| extend traceId = extract_json("$.traceId", jsonP)
| extend spanId = extract_json("$.spanId", jsonP)
// columns to show
| project timestamp, message, tenant, stackTrace, requestId, flowExecutionId, flowConfigurationId, traceId, spanId, level, levelValue, thread,  TimeGenerated, jsonP
| where 1==1 
// and tenant == 'OT' // exclude non-tenant related logs
// and timestamp >= ago(15m) 
// and thread contains "flow-runner-" // isolate flow runner output
// and flowConfigurationId == "conditiona1" // isolate flow runs for given flow configuration
// and flowExecutionId == 7901 // isolate a given flow run
// and levelValue > 10000 // eg above debug - DEBUG = 10000 INFO = 20000 WARN = 30000 etc
// and stackTrace contains ("labels")
// and level == "ERROR" // by level TRACE/DEBUG/INFO/WARN/ERROR
| order by timestamp desc
```

You can find this query in the Azure Queries hub:

![](./images/Azure%20queries%20hub.png)

## Local development

For local development, we run the application with a parameter to use a developer specific logback configuration file:

```
-Dlogback.configurationFile=app/src/main/resources/logback-local-$OSUser$.xml
```

When running the `OneTeam AI` run configuration in Intellij it will automatically create this file if it does not exist.

You can then customize it for your needs.

This configuration doesn't use json formatting to make it easier to read in the console.

![](./images/OTAI-BE%20run%20config.png)