{"name": "@automerge/automerge-repo-sync-server", "version": "0.2.8", "main": "src/index.js", "license": "MIT", "type": "module", "scripts": {"start": "node ./src/index.js", "test": "mocha --exit", "prettier": "prettier -c ."}, "files": ["src/index.js", "src/server.js"], "bin": "./src/index.js", "dependencies": {"@automerge/automerge": "^2.2.8", "@automerge/automerge-repo": "^2.0.0-alpha.23", "@automerge/automerge-repo-network-websocket": "^2.0.0-alpha.23", "@automerge/automerge-repo-storage-nodefs": "^1.2.0", "express": "^4.18.1", "glob": "10.3.10", "npx": "^10.2.2", "ws": "^8.7.0"}, "devDependencies": {"@types/express": "^4.17.17", "mocha": "^10.2.0", "prettier": "^3.0.0", "ts-node": "^10.9.1", "typescript": "^4.7.4"}, "prettier": {"semi": false}, "publishConfig": {"access": "public"}}