// @ts-check
import fs from "fs"
import express from "express"
import {WebSocketServer} from "ws"
import {Repo} from "@automerge/automerge-repo"
import {NodeWSServerAdapter} from "@automerge/automerge-repo-network-websocket"
import {NodeFSStorageAdapter} from "@automerge/automerge-repo-storage-nodefs"
import os from "os"


export class Server {
    /** @type WebSocketServer */
    #socket

    /** @type ReturnType<import("express").Express["listen"]> */
    #server

    /** @type {((value: any) => void)[]} */
    #readyResolvers = []

    #isReady = false

    /** @type Repo */
    #repo

    constructor() {
        const dir = process.env.DATA_DIR !== undefined ? process.env.DATA_DIR : ".amrg"
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir)
        }

        var hostname = os.hostname()

        this.#socket = new WebSocketServer({noServer: true})

        const PORT = process.env.PORT !== undefined ? parseInt(process.env.PORT) : 3030
        const app = express()
        app.use(express.static("public"))

        const config = {
            network: [new NodeWSServerAdapter(this.#socket)],
            storage: new NodeFSStorageAdapter(dir),
            /** @ts-ignore @type {(import("@automerge/automerge-repo").PeerId)}  */
            peerId: `storage-server-${hostname}`, // Since this is a server, we don't share generously — meaning we only sync documents they already
            // know about and can ask for by ID.
            sharePolicy: async () => true,
        }
        this.#repo = new Repo(config)

        app.get("/", (req, res) => {
            const handle = this.#repo.create()
            handle.whenReady()
            res.send(`👍 @automerge/automerge-repo-sync-server is running`)
        })

        app.post("/create-foo-bar", (req, res) => {

            const handle = this.#repo.create()
            handle.whenReady().then(() => {
                handle.change(h => h.foo = "bar")
            })

            res.send(handle.documentId)
        })

        app.post("/cleanup", (req, res) => {

            let handles = []

            for (let handlesKey in this.#repo.handles) {
                handles.push(handlesKey)
                this.#repo.delete(handlesKey)
            }

            res.send(handles)
        })

        app.get("/find/:id", async (req, res) => {
            const handle = await this.#repo.find(req.params.id)
            handle.whenReady().then(() => {
                res.send(handle.doc())
            })
        })

        this.#server = app.listen(PORT, () => {
            console.log(`Listening on port ${PORT}`)
            this.#isReady = true
            this.#readyResolvers.forEach((resolve) => resolve(true))
        })

        this.#server.on("upgrade", (request, socket, head) => {
            console.log("upgrade")
            this.#socket.handleUpgrade(request, socket, head, (socket) => {
                console.log("handleUpgrade")
                this.#socket.emit("connection", socket, request)
                this.#socket.on("message", (data) => {
                    console.log("Message received: ", data)
                })
            })
        })
    }

    async ready() {
        if (this.#isReady) {
            return true
        }

        return new Promise((resolve) => {
            this.#readyResolvers.push(resolve)
        })
    }

    close() {
        this.#socket.close()
        this.#server.close()
    }
}