import automergeRepo.src.main.kotlin.Repo
import automergeRepo.src.main.kotlin.RepoOptions
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import network.WebSocketClientAdapter
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.condition.DisabledIfEnvironmentVariable
import kotlin.test.Test
import org.testcontainers.containers.GenericContainer
import kotlin.test.Ignore

@Disabled
class WebsocketRepoTests {

    private val defaultServerPort = 3030
    private var serverPort: Int = defaultServerPort
    private val container =
        GenericContainer("oneteamdev.azurecr.io/test-containers/sync-server:latest").withExposedPorts(defaultServerPort)

    init {
        container.start()
        serverPort = container.getMappedPort(defaultServerPort)
    }

    private fun cleanUp() {
        val handles =
            container.execInContainer("curl", "-X", "POST", "http://localhost:$defaultServerPort/cleanup").stdout

        println("$handles has/have been cleaned up from sync server")
    }

    @Test
    fun `changes are replicated from server to client`(): Unit = runBlocking {

        //create and change doc on server
        val docId = container.execInContainer(
            "curl", "-X", "POST", "http://localhost:$defaultServerPort/create-foo-bar"
        ).stdout

        val options =
            RepoOptions().apply { networkAdapters = listOf(WebSocketClientAdapter("ws://localhost:$serverPort", "")) }

        val client = Repo(options)


        val handle = client.find<TestDoc>(docId).whenReady()
        assert(handle.document().foo == "bar")

        cleanUp()
    }


    @Test
    fun `can find a created document from sync server`() {

        val options =
            RepoOptions().apply { networkAdapters = listOf(WebSocketClientAdapter("ws://localhost:$serverPort", "")) }

        val client = Repo(options)

        val testDoc = TestDoc(foo = "bar", bar = "baz")

        val handle = client.create(testDoc)

        //check local works
        assert(handle.document().foo == "bar")
        assert(handle.document().bar == "baz")

        //check remote works
        val docJson = container.execInContainer(
            "curl", "-X", "GET", "http://localhost:$defaultServerPort/find/${handle.docId}"
        ).stdout

        val serverDoc = Json.decodeFromString<TestDoc>(docJson)

        assert(serverDoc.foo == "bar")
        assert(serverDoc.bar == "baz")

        cleanUp()
    }

    @Test
    fun `client changes are replicated on server`() = runTest {

        val client = Repo(
            RepoOptions(
                networkAdapters = listOf(WebSocketClientAdapter("ws://localhost:$serverPort", "")),
            )
        )

        val handle = client.create(TestDoc(foo = "bar", bar = "baz"))

        handle.change { it.foo = "baarg!!!" }

        val handle2 = client.find<TestDoc>(handle.docId).whenReady()

        val w = handle2.document().foo

        assert(w == "baarg!!!")

        val docJson = container.execInContainer(
            "curl", "-X", "GET", "http://localhost:$defaultServerPort/find/${handle.docId}"
        ).stdout

        val serverDoc = Json.decodeFromString<TestDoc>(docJson)

        assert(serverDoc.foo == "baarg!!!")

        cleanUp()
    }
}

@Serializable
open class TestDoc(var foo: String = "", var bar: String = "", var baz: String = "")