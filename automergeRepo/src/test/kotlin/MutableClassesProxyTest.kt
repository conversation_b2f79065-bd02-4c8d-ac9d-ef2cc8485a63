//import DocumentProxyTest.SomeClass
//import automergeRepo.src.main.kotlin.documentProxy.DocumentProxyBuilder
//import automergeRepo.src.main.kotlin.documentProxy.ProxyStrategy
//import kotlinx.serialization.Serializable
//import org.automerge.Document
//import org.junit.jupiter.api.assertThrows
//import org.junit.jupiter.params.ParameterizedTest
//import org.junit.jupiter.params.provider.MethodSource
//import java.util.stream.Stream
//import kotlin.use
//
//class MutableClassesProxyTest {
//    @Serializable
//    open class MutableListContainer {
//        @Serializable
//        open val list = mutableListOf<SomeClass>()
//    }
//
//    @Serializable
//    open class MutableMapContainer {
//        @Serializable
//        open val map = mutableMapOf<String, SomeClass>()
//    }
//
//    @Serializable
//    open class MapInMap {
//        @Serializable
//        open val map = mapOf<String, MutableMap<String, Int>>("string" to mutableMapOf())
//    }
//
//    @Serializable
//    open class MapInMap2 {
//        @Serializable
//        open val map = mapOf<String, MutableMapContainer>("string" to MutableMapContainer())
//    }
//
//    @Serializable
//    open class ListInList {
//        @Serializable
//        open val list = listOf<MutableList<Int>>(mutableListOf(1))
//    }
//
//    @Serializable
//    open class ListInList2 {
//        @Serializable
//        open val list = listOf<MutableListContainer>(MutableListContainer(), MutableListContainer())
//    }
//
//    companion object {
//        @JvmStatic
//        fun <T : Any> mutableClassObjectsProvider(): Stream<T> {
//            return Stream.of(
//                MutableListContainer(),
//                MutableMapContainer(),
////                mutableListOf<Int>(1),
////                mutableMapOf<String, Int>("string" to 1),
//                MapInMap(),
//                MapInMap2(),
//                ListInList(),
//                ListInList2()
//            ) as Stream<T>
//        }
//    }
//
//    @ParameterizedTest
//    @MethodSource("mutableClassObjectsProvider")
//    fun <T : Any> `mutable map cannot be proxied`(classObject: T) {
//        val doc = Document()
//
//        var proxy2: T? = null
//
//        assertThrows<UnsupportedOperationException> {
//            doc.startTransaction().use { tx ->
//                proxy2 = DocumentProxyBuilder {
//                    it.proxyStrategy = ProxyStrategy.PROXY_FIRST
//                }.create(
//                    target = classObject, doc = doc, tx = tx, parent = null
//                )
//                tx.commit()
//            }
//        }
//    }
//}