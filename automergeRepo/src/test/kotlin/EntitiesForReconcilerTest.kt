import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.NotFoundException
import services.oneteam.ai.shared.domains.ValidationErrors
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.validation.*


@Serializable
sealed class Workspace {
    abstract val name: Name
    abstract val key: Key
    abstract val description: Description?

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    @JvmInline
    value class DocumentId(val value: String)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Key(val value: String)

    @Serializable
    @JvmInline
    value class Description(val value: String)

    @Serializable
    data class ForCreate(
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        val documentId: DocumentId? = null,
    ) : Workspace()

    @Serializable
    data class ForUpdate(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        val documentId: DocumentId?,
        val configuration: ForJson
    ) : Workspace()

    @Serializable
    data class ForUpdateDetails(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
    ) : Workspace()

    @Serializable
    data class ForApi(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        val documentId: DocumentId?,
        val metadata: EntityMetadata
    ) : Workspace()

    @Serializable
    @OptIn(ExperimentalSerializationApi::class)
    data class ForJson(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        @EncodeDefault
        val foundations: services.oneteam.ai.shared.domains.workspace.OrderedMap<FoundationConfiguration.Id, FoundationConfiguration.ForApi>,
        @EncodeDefault
        val forms: Map<FormConfiguration.Id, FormConfiguration.ForJson> = mapOf(),
        @EncodeDefault
        val flows: services.oneteam.ai.shared.domains.workspace.OrderedMap<FlowConfiguration.Id, FlowConfiguration.ForJson> = services.oneteam.ai.shared.domains.workspace.OrderedMap(
            emptyList()
        ),
        @EncodeDefault
        val series: Map<SeriesConfiguration.Id, SeriesConfiguration.ForApi> = mapOf(),
        @EncodeDefault
        val labels: Map<LabelConfiguration.Id, LabelConfiguration.ForApi> = mapOf(),
        val metadata: EntityMetadata,
        val errors: List<ConstraintError>? = emptyList()
    ) : Workspace(), CollaborationDocument, BaseConfigValidator {
        @EncodeDefault
        override val type = CollaborationDocumentType.WORKSPACE_CONFIGURATION

        override fun descriptor(): String {
            return name.value
        }

        fun validateConfiguration(path: String, context: WorkspaceValidationContext): Errors {
            val errors = super.validateConfiguration(path)
            forms.forEach { (id, form) ->
                form.validateConfiguration("$.forms.${id.value}").let { formErrors ->
                    errors.addAll(formErrors)
                }
            }
            flows.entities.forEach { (id, flow) ->
                flow.validateConfiguration("$.flows.entities.${id.value}", context).let { flowErrors ->
                    errors.addAll(flowErrors)
                }
            }
            return errors
        }

        fun findFlow(id: FlowConfiguration.Id): FlowConfiguration.ForJson {
            return flows.entities[id]
                ?: throw NotFoundException("No flow configuration found with ID ${id.value}")
        }

        fun findForm(id: FormConfiguration.Id): FormConfiguration.ForJson {
            return forms[id]
                ?: throw NotFoundException("No form configuration found with ID ${id.value}")
        }

        fun findInterval(seriesId: SeriesConfiguration.Id, intervalId: IntervalId): Interval {
            return series[seriesId]?.intervals?.entities?.get(intervalId)
                ?: throw NotFoundException("No interval found for ID ${intervalId.value} in series configuration ${seriesId.value}")
        }

        fun findSeries(id: SeriesConfiguration.Id): SeriesConfiguration.ForApi {
            return series[id] ?: throw NotFoundException("No series configuration found with ID ${id.value}")
        }
    }


    fun validate(): ValidationErrors {
        val errors = Validator<Workspace>(
            listOf(
                NameValidator.build { obj: Workspace -> obj.name.value },
                KeyValidator.build { obj: Workspace -> obj.key.value },
                Validator.conditionalConstraint(
                    (this is ForUpdate),
                    IdValidator.build<Workspace> { obj -> (obj as ForUpdate).id.value }
                )
            )
        ).validate(this)

        return ValidationErrors.from(errors)
    }
}


@Serializable
data class OrderedMap<ID, T : HasId<ID>> constructor(val order: MutableList<ID>, val entities: MutableMap<ID, T>) {

    companion object {
        fun <ID, T : HasId<ID>> empty(): OrderedMap<ID, T> {
            return OrderedMap(mutableListOf(), mutableMapOf())
        }

        fun <ID, T : HasId<ID>> of(
            order: List<ID>, entities: Map<ID, T>
        ): OrderedMap<ID, T> {
            return OrderedMap(order.toMutableList(), entities.toMutableMap())
        }

    }

    constructor(values: List<T>) : this(
        order = values.map { it.id }.toMutableList(),
        entities = values.associateBy { it.id }.toMutableMap()
    )

    fun add(entity: T) {
        order += entity.id
        entities[entity.id] = entity
    }

    fun remove(entity: T) {
        order -= entity.id
        entities.remove(entity.id)
    }

    fun remove(id: ID) {
        order -= id
        entities.remove(id)
    }

    fun toList(): List<T> {
        return order.map { entities[it]!! }
    }

    fun findNext(id: ID): T? {
        order.indexOf(id).let {
            require(it != -1) { "ID not found in ordered list" }
            if (it == order.size - 1) {
                return null
            }
            val idOfNext = order[it + 1]
            return entities[idOfNext] ?: throw NotFoundException("ID not found in entities")
        }
    }

    fun isValid(): Boolean {
        // check that order matches all of the values in entities
        return entities.keys.size == order.size && entities.keys.containsAll(order) && order.containsAll(entities.keys)
    }

}