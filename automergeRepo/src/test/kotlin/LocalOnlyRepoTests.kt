import automergeRepo.src.main.kotlin.Repo
import automergeRepo.src.main.kotlin.RepoOptions
import automergeRepo.src.main.kotlin.generateAutomergeUrl
import automergeRepo.src.main.kotlin.network.NoOpAdapter
import automergeRepo.src.main.kotlin.repoExeptions.DocumentUnavailableException
import automergeRepo.src.main.kotlin.repoExeptions.InvalidUrlException
import io.kotest.common.runBlocking
import kotlinx.coroutines.*
import kotlinx.serialization.Serializable
import network.INetworkAdapter
import org.automerge.AmValue
import org.automerge.ObjectId
import org.junit.jupiter.api.assertThrows
import kotlin.test.Ignore
import kotlin.test.Test
import kotlin.test.assertEquals

@Ignore
class LocalOnlyRepoTests {

    @Test
    fun `can instantiate a repo`() {
        setup().repo
    }

    @Test
    fun `can create a document`() {
        val repo = setup().repo
        val handle = repo.create<TestObject>()
        assert(handle.isReady())
    }

    @Test
    fun `can create a document with an initial value`() {
        val repo = setup().repo
        val handle = repo.create<TestObject>(TestObject("bar"))

        assert((handle.doc().get(ObjectId.ROOT, "foo").get() as AmValue.Str).value == "bar")
    }

    @Test
    fun `can find a document by document id`() = runBlocking {
        val repo = setup().repo

        val handle = repo.create<TestObject>(TestObject("bar"))
        handle.getProgress()

        val handle2 = repo.find<TestObject>(handle.docId).whenReady()
        handle2.getProgress()

        val value = (handle.doc().get(ObjectId.ROOT, "foo").get() as AmValue.Str).value
        val value2 = (handle2.doc().get(ObjectId.ROOT, "foo").get() as AmValue.Str).value

        assert(value == value2)
    }

    @Test
    fun `can change a document`() {
        val repo = setup().repo
        val handle = repo.create<TestObject>(TestObject("bar"))

        handle.change {
            it.foo = "baz"
        }

        assert(handle.isReady())
        assert((handle.doc().get(ObjectId.ROOT, "foo").get() as AmValue.Str).value == "baz")
    }

//    @Test
//    fun `can clone a document`() {
//        val repo = setup().repo
//        val handle = repo.create<TestObject>(TestObject("bar"))
//
//        val handle2 = repo.clone<TestObject>(handle)
//
//        assert(handle2.isReady())
//        assert(handle.docId != handle2.docId)
//        assert((handle2.doc().get(ObjectId.ROOT, "foo").get() as AmValue.Str).value == "bar")
//    }

    @Test
    fun `throws an error if we try to find a handle with an invalid AutomergeUrl`(): Unit = runBlocking {
        val id = "invalid-url"
        val repo = setup().repo
        assertThrows<InvalidUrlException>("Invalid Automerge URL: $id") { repo.find<TestObject>(id) }
    }

    @Test
    fun `doesnt find a document that doesnt exist`(): Unit = runBlocking {
        val id = generateAutomergeUrl()
        val repo = setup().repo
        assertThrows<DocumentUnavailableException>("Document $id is unavailable") {
            repo.find<TestObject>(id).whenReady()
        }
    }

    @Test
    fun `doesn't mark a document as unavailable until network adapters are ready`(): Unit = runBlocking {
        coroutineScope {
            val setup = setup(false)
            val repo = setup.repo
            val networkAdapter = setup.networkAdapter
            val url = generateAutomergeUrl()

            //ensuring that whenReady causes execution to pause, similar to how 'await' would
            val res = withTimeoutOrNull(50) {
                repo.find<TestObject>(url).whenReady()
            }

            assert(res == null)

            networkAdapter.forceReady()

            assertThrows<DocumentUnavailableException>("Document $url is unavailable") {
                repo.find<TestObject>(url).whenReady()
            }
        }
    }

    @Test
    fun `can find a created document`(): Unit = runBlocking {
        val repo = setup().repo
        val handle = repo.create<TestObject>(TestObject("bar"))

        assert(handle.isReady())

        val handle2 = repo.find<TestObject>(handle.docId).whenReady()

        assertEquals(handle, handle2)

        assert((handle.doc().get(ObjectId.ROOT, "foo").get() as AmValue.Str).value == "bar")
        assert((handle2.doc().get(ObjectId.ROOT, "foo").get() as AmValue.Str).value == "bar")
    }

    @Serializable
    open class TestObject(
        var foo: String = "foo",
    )

    data class SetupInfo(val repo: Repo, val networkAdapter: INetworkAdapter)

    fun setup(startReady: Boolean = true): SetupInfo {

        val networkAdapter = NoOpAdapter(startReady)
        val options = RepoOptions().apply { networkAdapters = listOf(networkAdapter) }

        val repo = Repo(options)

        return SetupInfo(repo, networkAdapter)

    }
}

