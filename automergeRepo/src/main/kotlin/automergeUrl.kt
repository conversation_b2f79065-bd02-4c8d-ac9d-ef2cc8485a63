package automergeRepo.src.main.kotlin

import java.math.BigInteger
import java.nio.ByteBuffer
import java.security.MessageDigest
import java.util.UUID

const val urlPrefix = "automerge:"
typealias DocumentId = String

data class ParsedAutomergeUrl(
    val binaryDocumentId: ByteArray,
    val documentId: String,
    val heads: List<String>? = null,
    val hexHeads: List<String>? = null
)

data class UrlOptions(val documentId: Any, val heads: List<String>? = null)

object BS58Check {
    private const val ALPHABET = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
    private val INDEXES = IntArray(128) { -1 }
    init {
        for (i in ALPHABET.indices) {
            INDEXES[ALPHABET[i].toInt()] = i
        }
    }
    private fun sha256(input: ByteArray): ByteArray {
        return MessageDigest.getInstance("SHA-256").digest(input)
    }
    fun encode(input: ByteArray): String {
        val checksum = sha256(sha256(input)).copyOfRange(0, 4)
        val extended = input + checksum
        var num = BigInteger(1, extended)
        val sb = StringBuilder()
        while (num > BigInteger.ZERO) {
            val remainder = num.mod(BigInteger.valueOf(58))
            num = num.divide(BigInteger.valueOf(58))
            sb.insert(0, ALPHABET[remainder.toInt()])
        }
        for (b in extended) {
            if (b.toInt() == 0) sb.insert(0, ALPHABET[0]) else break
        }
        return sb.toString()
    }
    fun decode(input: String): ByteArray {
        return decodeUnsafe(input) ?: throw IllegalArgumentException("Invalid Base58Check string")
    }
    fun decodeUnsafe(input: String): ByteArray? {
        if (input.isEmpty()) return ByteArray(0)
        var num = BigInteger.ZERO
        for (char in input) {
            val digit = if (char.toInt() < 128) INDEXES[char.toInt()] else -1
            if (digit < 0) return null
            num = num.multiply(BigInteger.valueOf(58)).add(BigInteger.valueOf(digit.toLong()))
        }
        val numBytes = num.toByteArray()
        val bytes = if (numBytes.isNotEmpty() && numBytes[0] == 0.toByte()) numBytes.drop(1).toByteArray() else numBytes
        var leadingZeros = 0
        for (char in input) {
            if (char == ALPHABET[0]) leadingZeros++ else break
        }
        val decoded = ByteArray(leadingZeros + bytes.size)
        System.arraycopy(bytes, 0, decoded, leadingZeros, bytes.size)
        if (decoded.size < 4) return null
        val data = decoded.copyOfRange(0, decoded.size - 4)
        val checksum = decoded.copyOfRange(decoded.size - 4, decoded.size)
        val hash = sha256(sha256(data)).copyOfRange(0, 4)
        if (!checksum.contentEquals(hash)) return null
        return data
    }
}

fun uint8ArrayFromHexString(hex: String): ByteArray {
    val cleaned = if (hex.startsWith("0x")) hex.substring(2) else hex
    return cleaned.chunked(2).map { it.toInt(16).toByte() }.toByteArray()
}

fun uint8ArrayToHexString(array: ByteArray): String {
    return array.joinToString("") { "%02x".format(it) }
}

fun parseAutomergeUrl(url: String): ParsedAutomergeUrl {
    val parts = url.split("#")
    if (parts.size > 2) throw IllegalArgumentException("Invalid URL: contains multiple heads sections")
    val baseUrl = parts[0]
    val headsSection = if (parts.size == 2) parts[1] else null
    val regex = Regex("^${Regex.escape(urlPrefix)}(\\w+)$")
    val match = regex.find(baseUrl) ?: throw IllegalArgumentException("Invalid document URL: $url")
    val documentId = match.groupValues[1]
    val binaryDocumentId = documentIdToBinary(documentId)
        ?: throw IllegalArgumentException("Invalid document URL: $url")
    if (headsSection == null) return ParsedAutomergeUrl(binaryDocumentId, documentId)
    val heads = if (headsSection.isEmpty()) emptyList() else headsSection.split("|")
    val hexHeads = heads.map { head ->
        try { uint8ArrayToHexString(BS58Check.decode(head)) }
        catch (e: Exception) { throw IllegalArgumentException("Invalid head in URL: $head") }
    }
    return ParsedAutomergeUrl(binaryDocumentId, documentId, heads, hexHeads)
}

fun stringifyAutomergeUrl(arg: Any): String = when (arg) {
    is ByteArray -> urlPrefix + binaryToDocumentId(arg)
    is String -> if (arg.startsWith(urlPrefix)) arg else urlPrefix + arg
    is UrlOptions -> {
        val docId = arg.documentId
        val encodedDocumentId = when (docId) {
            is ByteArray -> binaryToDocumentId(docId)
            is String -> if (docId.startsWith(urlPrefix)) docId.removePrefix(urlPrefix) else docId
            else -> throw IllegalArgumentException("Invalid documentId: $docId")
        }
        var url = urlPrefix + encodedDocumentId
        arg.heads?.let { heads ->
            heads.forEach { head ->
                try { BS58Check.decode(head) } catch (e: Exception) { throw IllegalArgumentException("Invalid head: $head") }
            }
            url += "#" + heads.joinToString("|")
        }
        url
    }
    else -> throw IllegalArgumentException("Invalid argument")
}

fun getHeadsFromUrl(url: String): List<String>? = parseAutomergeUrl(url).heads

fun anyDocumentIdToAutomergeUrl(id: Any): String? = when {
    isValidAutomergeUrl(id) -> id as String
    isValidDocumentId(id) -> stringifyAutomergeUrl(when (id) {
        is String -> UrlOptions(id)
        is ByteArray -> UrlOptions(id)
        else -> id
    })
    isValidUuid(id) -> parseLegacyUUID(id as String)
    else -> null
}

fun isValidAutomergeUrl(str: Any?): Boolean {
    if (str !is String || str.isEmpty() || !str.startsWith(urlPrefix)) return false
    return try {
        val parsed = parseAutomergeUrl(str)
        if (!isValidDocumentId(parsed.documentId)) return false
        parsed.heads?.forEach { head ->
            try { BS58Check.decode(head) } catch (e: Exception) { return false }
        }
        true
    } catch (e: Exception) { false }
}

fun isValidDocumentId(str: Any?): Boolean {
    if (str !is String) return false
    val binary = documentIdToBinary(str) ?: return false
    return try { uuidFromBytes(binary); true } catch (e: Exception) { false }
}

fun isValidUuid(str: Any?): Boolean {
    if (str !is String) return false
    return try { UUID.fromString(str); true } catch (e: Exception) { false }
}

fun generateAutomergeUrl(): String {
    val uuid = UUID.randomUUID()
    val bytes = uuidToBytes(uuid)
    return stringifyAutomergeUrl(bytes)
}

fun documentIdToBinary(docId: String): ByteArray? = BS58Check.decodeUnsafe(docId)

fun binaryToDocumentId(docId: ByteArray): String = BS58Check.encode(docId)

fun encodeHeads(heads: List<String>): List<String> = heads.map { BS58Check.encode(uint8ArrayFromHexString(it)) }

fun decodeHeads(heads: List<String>): List<String> = heads.map { uint8ArrayToHexString(BS58Check.decode(it)) }

fun parseLegacyUUID(str: String): String? {
    if (!isValidUuid(str)) return null
    val uuid = UUID.fromString(str)
    val bytes = uuidToBytes(uuid)
    return stringifyAutomergeUrl(bytes)
}

fun interpretAsDocumentId(id: Any): String = when (id) {
    is ByteArray -> binaryToDocumentId(id)
    is String -> when {
        isValidAutomergeUrl(id) -> parseAutomergeUrl(id).documentId
        isValidDocumentId(id) -> id
        isValidUuid(id) -> {
            println("Future versions will not support UUIDs as document IDs; use Automerge URLs instead.")
            val uuid = UUID.fromString(id)
            binaryToDocumentId(uuidToBytes(uuid))
        }
        else -> throw IllegalArgumentException("Invalid AutomergeUrl: '$id'")
    }
    else -> throw IllegalArgumentException("Invalid AutomergeUrl: '$id'")
}

fun uuidToBytes(uuid: UUID): ByteArray {
    val bb = ByteBuffer.allocate(16)
    bb.putLong(uuid.mostSignificantBits)
    bb.putLong(uuid.leastSignificantBits)
    return bb.array()
}

fun uuidFromBytes(bytes: ByteArray): UUID {
    if (bytes.size != 16) throw IllegalArgumentException("Invalid UUID byte array")
    val bb = ByteBuffer.wrap(bytes)
    return UUID(bb.long, bb.long)
}
