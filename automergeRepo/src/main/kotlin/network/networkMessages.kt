package automergeRepo.src.main.kotlin.network

import automergeRepo.src.main.kotlin.*
import kotlinx.serialization.*
import kotlinx.serialization.cbor.Cbor
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.cbor.CBORFactory
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.module.SimpleModule
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.builtins.serializer
import kotlinx.serialization.descriptors.*
import kotlinx.serialization.encoding.*
import kotlinx.serialization.json.*
import java.util.Base64

typealias StorageId = String

@Serializable
enum class MessageType {
    @SerialName("join")
    JOIN,

    @SerialName("peer")
    PEER_MESSAGE,

    @SerialName("error")
    ERROR,

    @SerialName("sync")
    SYNC,

    @SerialName("doc-unavailable")
    DOC_UNAVAILABLE,

    @SerialName("ephemeral")
    EPHEMERAL,

    @SerialName("request")
    REQUEST,

    @SerialName("remote-subscription-change")
    REMOTE_SUBSCRIPTION_CHANGE,

    @SerialName("remote-heads-changed")
    REMOTE_HEADS_CHANGED
}

open class TraceableMessage {
    val meta: Meta = Meta()

    class Meta {
        var traceparent: String? = null
        var tracestate: String? = null
    }
}

@Serializable
abstract sealed class Message : TraceableMessage() {
    abstract val type: MessageType
    abstract var senderId: PeerId?
    abstract val targetId: PeerId?
    abstract val data: ByteArray?
    abstract val documentId: DocumentId?

    @Serializable
    abstract sealed class DocMessage : Message() {
        @Serializable
        data class SyncMessage(
            override val type: MessageType = MessageType.SYNC,
            override var senderId: PeerId? = null,
            override val targetId: PeerId,
            override val data: ByteArray,
            override val documentId: DocumentId
        ) : DocMessage()

        @Serializable
        data class EphemeralMessage(
            override val type: MessageType = MessageType.EPHEMERAL,
            override var senderId: PeerId?,
            override val targetId: PeerId,
            override val data: ByteArray,
            override val documentId: DocumentId,
            val count: Int,
            val sessionId: SessionId
        ) : DocMessage()

        @Serializable
        data class DocumentUnavailableMessage(
            override val type: MessageType = MessageType.DOC_UNAVAILABLE,
            override var senderId: PeerId? = null,
            override val targetId: PeerId,
            override val documentId: DocumentId,
            override val data: ByteArray? = null
        ) : DocMessage()

        @Serializable
        data class RequestMessage(
            override val type: MessageType = MessageType.REQUEST,
            override var senderId: PeerId? = null,
            override val targetId: PeerId,
            override val documentId: DocumentId,
            override val data: ByteArray? = null
        ) : DocMessage()

        @Serializable
        abstract sealed class RepoMessage : DocMessage() {
            @Serializable
            data class RemoteSubscriptionChangeMessage(
                override val type: MessageType = MessageType.REMOTE_SUBSCRIPTION_CHANGE,
                override var senderId: PeerId?,
                override val targetId: PeerId,
                val add: List<StorageId>? = null,
                val remove: List<StorageId>? = null,
                override val documentId: DocumentId? = null,
                override val data: ByteArray? = null
            ) : RepoMessage()

            @Serializable
            data class RemoteHeadsChangedMessage(
                override val type: MessageType = MessageType.REMOTE_HEADS_CHANGED,
                override var senderId: PeerId?,
                override val targetId: PeerId,
                override val documentId: DocumentId?,
                override val data: ByteArray? = null
            ) : RepoMessage() {
                @Serializable
                data class NewHead(val heads: List<String>, val timestamp: Long)
            }
        }
    }

    @Serializable
    abstract sealed class FromClientMessage : Message() {
        abstract val peerMetadata: PeerMetaData
        abstract val selectedProtocolVersions: Array<String>

        @Serializable
        data class JoinMessage(
            override val type: MessageType = MessageType.JOIN,
            override var senderId: PeerId?,
            override val peerMetadata: PeerMetaData,
            override val selectedProtocolVersions: Array<String> = arrayOf(ProtocolV1),
            override val targetId: PeerId? = null,
            override val data: ByteArray? = null,
            override val documentId: DocumentId? = null
        ) : FromClientMessage()
    }

    @Serializable
    abstract sealed class FromServerMessage : Message() {
        @Serializable
        data class PeerMessage(
            override val targetId: PeerId,
            val selectedProtocolVersions: Array<String> = arrayOf(ProtocolV1),
            val peerMetadata: PeerMetaData,
            override val type: MessageType = MessageType.PEER_MESSAGE,
            override var senderId: PeerId?,
            override val data: ByteArray? = null,
            override val documentId: DocumentId? = null
        ) : FromServerMessage()

        @Serializable
        data class ErrorMessage(
            override val targetId: PeerId,
            val message: String,
            override val type: MessageType = MessageType.ERROR,
            override var senderId: PeerId?,
            override val data: ByteArray? = null,
            override val documentId: DocumentId? = null
        ) : FromServerMessage()
    }
}

object DualByteArraySerializer : KSerializer<ByteArray> {
    override val descriptor: SerialDescriptor = ListSerializer(Int.serializer()).descriptor
    override fun serialize(encoder: Encoder, value: ByteArray) {
        val composite = encoder.beginCollection(descriptor, value.size)
        for (i in value.indices) {
            composite.encodeIntElement(descriptor, i, value[i].toInt() and 0xFF)
        }
        composite.endStructure(descriptor)
    }

    override fun deserialize(decoder: Decoder): ByteArray {
        val jsonDecoder = decoder as? JsonDecoder
        return if (jsonDecoder != null) {
            val element = jsonDecoder.decodeJsonElement()
            when {
                element is JsonArray -> element.map { (it as JsonPrimitive).int }.map { it.toByte() }.toByteArray()
                element is JsonPrimitive && element.isString -> Base64.getDecoder().decode(element.content)
                else -> throw IllegalStateException("Unexpected JSON token for ByteArray")
            }
        } else {
            val composite = decoder.beginStructure(descriptor)
            val list = mutableListOf<Int>()
            while (true) {
                val index = composite.decodeElementIndex(descriptor)
                if (index == CompositeDecoder.DECODE_DONE) break
                list.add(composite.decodeIntElement(descriptor, index))
            }
            composite.endStructure(descriptor)
            list.map { it.toByte() }.toByteArray()
        }
    }
}

@Serializable
data class MessageDto(
    val type: MessageType,
    val senderId: PeerId? = null,
    val targetId: PeerId? = null,
    @Serializable(with = DualByteArraySerializer::class) val data: ByteArray? = null,
    val documentId: DocumentId? = null,
    val count: Int? = null,
    val sessionId: SessionId? = null,
    val peerMetadata: PeerMetaData? = null,
    val message: String? = null,
    val add: List<StorageId>? = null,
    val remove: List<StorageId>? = null
)

val cbor = Cbor { encodeDefaults = true; ignoreUnknownKeys = true }

private fun convertDtoToMessage(dto: MessageDto): Message = when (dto.type) {
    MessageType.JOIN -> Message.FromClientMessage.JoinMessage(
        senderId = dto.senderId!!,
        peerMetadata = dto.peerMetadata!!,
        selectedProtocolVersions = arrayOf(ProtocolV1),
        targetId = dto.targetId,
        data = dto.data,
        documentId = dto.documentId
    )

    MessageType.PEER_MESSAGE -> Message.FromServerMessage.PeerMessage(
        targetId = dto.targetId!!,
        selectedProtocolVersions = arrayOf(ProtocolV1),
        peerMetadata = dto.peerMetadata!!,
        senderId = dto.senderId!!,
        data = dto.data,
        documentId = dto.documentId
    )

    MessageType.ERROR -> Message.FromServerMessage.ErrorMessage(
        targetId = dto.targetId!!,
        message = dto.message!!,
        senderId = dto.senderId!!,
        data = dto.data,
        documentId = dto.documentId
    )

    MessageType.SYNC -> Message.DocMessage.SyncMessage(
        senderId = dto.senderId!!,
        targetId = dto.targetId!!,
        data = dto.data!!,
        documentId = dto.documentId!!
    )

    MessageType.DOC_UNAVAILABLE -> Message.DocMessage.DocumentUnavailableMessage(
        senderId = dto.senderId!!,
        targetId = dto.targetId!!,
        documentId = dto.documentId!!,
        data = dto.data
    )

    MessageType.EPHEMERAL -> Message.DocMessage.EphemeralMessage(
        senderId = dto.senderId!!,
        targetId = dto.targetId!!,
        data = dto.data!!,
        documentId = dto.documentId!!,
        count = dto.count!!,
        sessionId = dto.sessionId!!
    )

    MessageType.REQUEST -> Message.DocMessage.RequestMessage(
        senderId = dto.senderId!!,
        targetId = dto.targetId!!,
        documentId = dto.documentId!!,
        data = dto.data
    )

    MessageType.REMOTE_SUBSCRIPTION_CHANGE -> Message.DocMessage.RepoMessage.RemoteSubscriptionChangeMessage(
        senderId = dto.senderId!!,
        targetId = dto.targetId!!,
        add = dto.add,
        remove = dto.remove,
        documentId = dto.documentId!!,
        data = dto.data
    )

    MessageType.REMOTE_HEADS_CHANGED -> Message.DocMessage.RepoMessage.RemoteHeadsChangedMessage(
        senderId = dto.senderId!!,
        targetId = dto.targetId!!,
        documentId = dto.documentId,
        data = dto.data
    )
}

class RawByteArraySerializer : JsonSerializer<ByteArray>() {
    override fun serialize(value: ByteArray, gen: JsonGenerator, serializers: SerializerProvider) {
        gen.writeStartArray()
        value.forEach { gen.writeNumber(it.toInt() and 0xFF) }
        gen.writeEndArray()
    }
}

val module = SimpleModule().apply {
    addSerializer(ByteArray::class.java, RawByteArraySerializer())
}

val jsonMapper = ObjectMapper().registerModule(module)

fun ByteArray.toMessage(): Message {
    val cborMapper = ObjectMapper(CBORFactory())
    val tree = cborMapper.readTree(this)
    val json = jsonMapper.writeValueAsString(tree)
    val dto = Json { ignoreUnknownKeys = true }.decodeFromString<MessageDto>(json)
    return convertDtoToMessage(dto)
}

fun Message.toMessageDto(): ByteArray {
    return when (this) {
        is Message.DocMessage.DocumentUnavailableMessage -> cbor.encodeToByteArray(
            MessageDto(
                senderId = senderId,
                targetId = targetId,
                documentId = documentId,
                type = MessageType.DOC_UNAVAILABLE
            )
        )

        is Message.DocMessage.EphemeralMessage -> cbor.encodeToByteArray(
            MessageDto(
                senderId = senderId,
                targetId = targetId,
                data = data,
                documentId = documentId,
                count = count,
                sessionId = sessionId,
                type = MessageType.EPHEMERAL
            )
        )

        is Message.DocMessage.RepoMessage.RemoteHeadsChangedMessage -> cbor.encodeToByteArray(
            MessageDto(
                senderId = senderId,
                targetId = targetId,
                documentId = documentId,
                data = data,
                type = MessageType.REMOTE_HEADS_CHANGED
            )
        )

        is Message.DocMessage.RepoMessage.RemoteSubscriptionChangeMessage -> cbor.encodeToByteArray(
            MessageDto(
                senderId = senderId,
                targetId = targetId,
                documentId = documentId,
                type = MessageType.REMOTE_SUBSCRIPTION_CHANGE
            )
        )

        is Message.DocMessage.RequestMessage -> cbor.encodeToByteArray(
            MessageDto(
                senderId = senderId,
                targetId = targetId,
                documentId = documentId,
                data = data,
                type = MessageType.REQUEST
            )
        )

        is Message.DocMessage.SyncMessage -> cbor.encodeToByteArray(
            MessageDto(
                senderId = senderId,
                targetId = targetId,
                data = data,
                documentId = documentId,
                type = MessageType.SYNC
            )
        )

        is Message.FromClientMessage.JoinMessage -> cbor.encodeToByteArray(
            MessageDto(
                senderId = senderId,
                peerMetadata = peerMetadata,
                targetId = targetId,
                data = data,
                documentId = documentId,
                type = MessageType.JOIN
            )
        )

        is Message.FromServerMessage.ErrorMessage -> cbor.encodeToByteArray(
            MessageDto(
                senderId = senderId,
                targetId = targetId,
                message = message,
                data = data,
                documentId = documentId,
                type = MessageType.ERROR
            )
        )

        is Message.FromServerMessage.PeerMessage -> cbor.encodeToByteArray(
            MessageDto(
                senderId = senderId,
                targetId = targetId,
                peerMetadata = peerMetadata,
                data = data,
                documentId = documentId,
                type = MessageType.PEER_MESSAGE
            )
        )
    }
}
