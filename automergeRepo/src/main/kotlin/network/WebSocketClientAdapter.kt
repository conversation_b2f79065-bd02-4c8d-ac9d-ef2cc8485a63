package network

import automergeRepo.src.main.kotlin.PeerId
import automergeRepo.src.main.kotlin.PeerMetaData
import automergeRepo.src.main.kotlin.events.AdapterStatus
import automergeRepo.src.main.kotlin.events.EventEmitter
import automergeRepo.src.main.kotlin.events.NetworkAdapterProgress
import automergeRepo.src.main.kotlin.network.Message
import automergeRepo.src.main.kotlin.network.MessageType
import automergeRepo.src.main.kotlin.network.toMessage
import automergeRepo.src.main.kotlin.network.toMessageDto
import io.ktor.client.*
import io.ktor.client.plugins.websocket.*
import io.ktor.websocket.*
import kotlinx.coroutines.*
import org.slf4j.LoggerFactory
import io.ktor.client.plugins.auth.*
import io.ktor.client.plugins.auth.providers.*
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.context.Context
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlin.time.Duration.Companion.seconds

class WebSocketClientAdapter(val url: String, bearerToken: String) : INetworkAdapter,
    EventEmitter<NetworkAdapterEvent>() {
    var socket: DefaultClientWebSocketSession? = null
    override lateinit var repoScope: CoroutineScope

    private var remotePeerId: PeerId? = null
    private val logger = LoggerFactory.getLogger(javaClass)


    private val client = HttpClient {
        install(WebSockets) {
            pingInterval = 3.seconds
        }
        install(Auth) {
            bearer {
                loadTokens {
                    BearerTokens(bearerToken, "refreshToken")
                }
            }
        }
    }

    private val readyResolver = CompletableDeferred<Unit>()

    override fun isReady(): Boolean {
        return readyResolver.isCompleted
    }

    override var peerId: PeerId? = null
    override var peerMetaData: PeerMetaData = PeerMetaData()


    override fun connect(peerId: String, peerMetaData: PeerMetaData?) {
        this.peerId = peerId
        this.peerMetaData = peerMetaData ?: PeerMetaData()
        if (socket == null) {
            repoScope.launch {
                client.webSocket(url) {
                    socket = this
                    join()
                    for (frame in incoming) {
                        receiveMessage(frame.readBytes())
                    }
                }
            }
        } else {
            if (peerId != this.peerId) {
                throw IllegalArgumentException("cannot re-assign peer ID")
            }
        }

        // Mark this adapter as ready if we haven't received an ack in 1 second.
        // We might hear back from the other end at some point but we shouldn't
        // hold up marking things as unavailable for any longer
        repoScope.launch {
            delay(1000)
            forceReady()
        }
    }


    fun receiveMessage(bytes: ByteArray) {
        val message: Message = bytes.toMessage()

        when (message.type) {

            MessageType.PEER_MESSAGE -> {
                logger.debug("peer: ${message.senderId}")
                message.senderId ?: throw IllegalArgumentException("Message has no sender ID")
                peerCandidate(message.senderId!!)
            }

            MessageType.ERROR -> {
                logger.error("Error message: ${message.data}")
            }

            else -> {
                emit(
                    NetworkAdapterEvent.Message(
                        payload = message
                    )
                )
            }
        }
    }

    private suspend fun join() {
        if (peerId == null) throw IllegalArgumentException("Adapter has no peer ID")
        if (socket == null) throw IllegalArgumentException("Adapter has no socket")
        send(
            Message.FromClientMessage.JoinMessage(
                senderId = peerId!!, peerMetadata = PeerMetaData()
            )
        )
    }

    override suspend fun send(message: Message) {
        if (peerId == null) throw IllegalArgumentException("Adapter has no peer ID")
        logger.trace("Sending message")

        val propagator = GlobalOpenTelemetry.getPropagators().textMapPropagator
        val messageHeaders = mutableMapOf<String, String>()
        propagator.inject(
            Context.current(), messageHeaders, { carrier, key, value -> carrier?.set(key, value) })


        val mes = message.toMessageDto()
        socket?.send(mes)
    }

    fun peerCandidate(remotePeerId: PeerId, payload: PeerMetaData? = null) {
        if (this.socket == null) throw IllegalStateException("Socket not initialised while attempting to connect remote peer")
        this.remotePeerId = remotePeerId
        this.forceReady()
        emit(NetworkAdapterEvent.PeerCandidate(peerId = remotePeerId))
    }

    override fun forceReady() {
        this.readyResolver.complete(Unit)
    }

    override suspend fun getProgress(): NetworkAdapterProgress {
        val that = this

        val flow = MutableSharedFlow<AdapterStatus>(1)

        repoScope.launch {
            flow.emit(AdapterStatus.Loading(that))
            readyResolver.await()
            flow.emit(AdapterStatus.Ready(that))
        }

        return NetworkAdapterProgress(flow)
    }

    override fun disconnect() {
        socket?.cancel("Disconnecting")
        client.close()
    }
}
