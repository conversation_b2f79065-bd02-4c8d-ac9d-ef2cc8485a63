package automergeRepo.src.main.kotlin.network

import automergeRepo.src.main.kotlin.PeerId
import automergeRepo.src.main.kotlin.PeerMetaData
import automergeRepo.src.main.kotlin.events.AdapterStatus
import automergeRepo.src.main.kotlin.events.NetworkAdapterProgress
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.produce
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import network.NetworkAdapterEvent
import network.INetworkAdapter

class NoOpAdapter(startReady: Boolean = true) : INetworkAdapter {

    private val readyResolver = CompletableDeferred<Unit>()

    init {
        if (startReady) {
            forceReady()
        }
    }

    override lateinit var repoScope: CoroutineScope

    override fun forceReady() {
        readyResolver.complete(Unit)
    }

    override var peerId: PeerId? = null

    override fun isReady(): Boolean {
        return readyResolver.isCompleted
    }

    override var peerMetaData: PeerMetaData = PeerMetaData()

    override fun connect(peerId: String, peerMetaData: PeerMetaData?) {}

    override suspend fun send(message: Message) {}

    override suspend fun getProgress(): NetworkAdapterProgress {
        val that = this

        val flow = MutableSharedFlow<AdapterStatus>(1)

        repoScope.launch {
            flow.emit(AdapterStatus.Loading(that))
            readyResolver.await()
            flow.emit(AdapterStatus.Ready(that))
        }

        return NetworkAdapterProgress(flow)
    }

    override fun <T : NetworkAdapterEvent> on(eventType: Class<T>, listener: (T) -> Unit) {}

    override fun emit(event: NetworkAdapterEvent) {}

    override fun disconnect() {}
}
