package network

import automergeRepo.src.main.kotlin.PeerId
import automergeRepo.src.main.kotlin.PeerMetaData
import automergeRepo.src.main.kotlin.events.EventEmitterInterface
import automergeRepo.src.main.kotlin.events.NetworkAdapterProgress
import automergeRepo.src.main.kotlin.network.Message
import kotlinx.coroutines.CoroutineScope

interface INetworkAdapter : EventEmitterInterface<NetworkAdapterEvent> {
    var repoScope: CoroutineScope
    val peerMetaData: PeerMetaData
    val peerId: PeerId?
    fun isReady(): Boolean
    fun forceReady()
    fun connect(peerId: PeerId, peerMetaData: PeerMetaData? = null)
    suspend fun send(message: Message)
    fun disconnect()
    suspend fun getProgress(): NetworkAdapterProgress
}

sealed class NetworkAdapterEvent {
    data object Close : NetworkAdapterEvent()
    data class PeerCandidate(val peerId: PeerId, val peerMetadata: PeerMetaData? = null) : NetworkAdapterEvent()
    data class PeerDisconnected(val payload: PeerDisconnectedPayload) : NetworkAdapterEvent()
    data class Message(val payload: automergeRepo.src.main.kotlin.network.Message) : NetworkAdapterEvent()
}

