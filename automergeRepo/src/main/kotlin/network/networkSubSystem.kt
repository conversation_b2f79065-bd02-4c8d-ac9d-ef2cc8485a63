package network

import automergeRepo.src.main.kotlin.HandleState
import automergeRepo.src.main.kotlin.PeerId
import automergeRepo.src.main.kotlin.PeerMetaData
import automergeRepo.src.main.kotlin.events.EventEmitter
import automergeRepo.src.main.kotlin.events.NetworkSubSystemProgress
import automergeRepo.src.main.kotlin.events.SubSystemStatus
import automergeRepo.src.main.kotlin.network.Message
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.produce
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.serialization.Serializable


class NetworkSubSystem(
    private val repoScope: CoroutineScope, adapters: List<INetworkAdapter>, val peerId: PeerId
) : EventEmitter<NetworkSubsystemEvents>() {

    private val adaptersByPeer: MutableMap<PeerId, INetworkAdapter> = mutableMapOf()
    private val adapters = mutableListOf<INetworkAdapter>()

    init {
        adapters.forEach { addNetworkAdapter(it) }
    }

    fun getProgress(): NetworkSubSystemProgress {
        val that = this

        val flow = MutableSharedFlow<SubSystemStatus>(1)

        repoScope.launch {
            flow.emit(SubSystemStatus.Loading(that))
            adapters.map { it.getProgress() }.forEach { it.whenReady() }
            flow.emit(SubSystemStatus.Ready(that))
        }

        return NetworkSubSystemProgress(flow)
    }

    suspend fun send(message: Message) {
        val peer = adaptersByPeer[message.targetId] ?: throw Exception("No peer found for targetId ${message.targetId}")
        //extremely sneaky, more sneaky appending of values if this happens to be ephemeral. but for now not needed
        message.senderId = this.peerId
        peer.send(message)
    }

     fun addNetworkAdapter(adapter: INetworkAdapter) {
        adapter.repoScope = this.repoScope
        adapters.add(adapter)
        adapter.on(NetworkAdapterEvent.PeerCandidate::class.java) { event ->
            if (event.peerId !in adaptersByPeer) {
                adaptersByPeer[event.peerId] = adapter
            }
            emit(NetworkSubsystemEvents.Peer(event.peerId, event.peerMetadata))
        }
        adapter.on(NetworkAdapterEvent.PeerDisconnected::class.java) { event ->
            adaptersByPeer.remove(event.payload.peerId)
        }
        adapter.on(NetworkAdapterEvent.Close::class.java) {
            adaptersByPeer.entries.removeIf { it.value == adapter }
        }
        adapter.on(NetworkAdapterEvent.Message::class.java) { event ->
            this.emit(NetworkSubsystemEvents.Message(event.payload))
        }

        adapter.connect(peerId)
    }
}

sealed class NetworkSubsystemEvents {
    data class Peer(val peerId: PeerId, val peerMetadata: PeerMetaData? = null) : NetworkSubsystemEvents()
    data class PeerDisconnected(val payload: PeerDisconnectedPayload) : NetworkSubsystemEvents()
    data class Message(val message: automergeRepo.src.main.kotlin.network.Message) : NetworkSubsystemEvents()
}

@Serializable
data class PeerDisconnectedPayload(val peerId: PeerId)