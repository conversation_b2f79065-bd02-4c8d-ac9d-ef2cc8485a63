package automergeRepo.src.main.kotlin

import com.tinder.StateMachine
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import org.automerge.Document

class ObservableStateMachine<State : HandleState, Event : HandleEvent, SideEffect : Any>(
    machineFactory: (ObservableStateMachine<State, Event, SideEffect>) -> StateMachine<State, Event, SideEffect>,
    var doc: Document = Document(),
    val repoScope: CoroutineScope
) {

    private val _stateFlow = MutableSharedFlow<StateContext>(
        replay = 1,
    )

    val stateFlow: MutableSharedFlow<StateContext> get() = _stateFlow

    private val machine: StateMachine<State, Event, SideEffect> = machineFactory(this)

    val currentState get() = machine.state

    fun transition(event: Event) = machine.transition(event)


    data class StateContext(
        val doc: Document, val docId: DocumentId, val state: HandleState
    )
}

sealed class HandleEvent {
    data object Begin : HandleEvent()
    data object Request : HandleEvent()
    data object DocReady : HandleEvent()
    data object DocUnavailable : HandleEvent()
    data object Reload : HandleEvent()
    data object Delete : HandleEvent()
    data object Create : HandleEvent()
    data object Find : HandleEvent()
    data object Unload : HandleEvent()
    data class Update(val callback: (Document) -> Document) : HandleEvent()
}

sealed class HandleState {
    /** The handle has been created but not yet loaded or requested */
    data object IDLE : HandleState()

    /** We are waiting for storage to finish loading */
    data object LOADING : HandleState()

    /** We are waiting for someone in the network to respond to a sync request */
    data object REQUESTING : HandleState()

    /** The document is available */
    data object READY : HandleState()

    /** The document has been unloaded from the handle, to free memory usage */
    data object UNLOADED : HandleState()

    /** The document has been deleted from the repo */
    data object DELETED : HandleState()

    /** The document was not available in storage or from any connected peers */
    data object UNAVAILABLE : HandleState()
}