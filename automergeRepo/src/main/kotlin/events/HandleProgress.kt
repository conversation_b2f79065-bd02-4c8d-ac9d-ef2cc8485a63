package automergeRepo.src.main.kotlin.events

import automergeRepo.src.main.kotlin.HandleState
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.first

class HandleProgress internal constructor(

    private val flow: SharedFlow<HandleState>,
) : IProgress<Unit> {

    override suspend fun whenReady(allowedStates: List<HandleState>) {
        flow.first { it in allowedStates }
    }
}
