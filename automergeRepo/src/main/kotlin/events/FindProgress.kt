package automergeRepo.src.main.kotlin.events

import automergeRepo.src.main.kotlin.DocHandle
import automergeRepo.src.main.kotlin.HandleState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

sealed class FindProgressState(val handle: <PERSON><PERSON>andle<*>) {
    class READY(handle: <PERSON><PERSON>and<PERSON><*>) : FindProgressState(handle)
    class UNAVAILABLE(handle: DocHandle<*>) : FindProgressState(handle)
    class REQUESTING(handle: <PERSON>Handle<*>) : FindProgressState(handle)
    class LOADING(handle: <PERSON><PERSON>andle<*>) : FindProgressState(handle)
    class DELETED(handle: DocHandle<*>) : FindProgressState(handle)
}

class FindProgress<T : DocHandle<*>> internal constructor(
    val flow: SharedFlow<FindProgressState>,
    val repoScope: CoroutineScope,
    val flowDelegate: suspend () -> Unit,
    val handle: T
) : IProgress<T> {

    override suspend fun whenReady(allowedStates: List<HandleState>): T {

        repoScope.launch { flowDelegate() }

        val res = flow.first {
            it.handle.state() in allowedStates
        }

        val t = res.handle as T

        return t
    }
}