package automergeRepo.src.main.kotlin.events

import automergeRepo.src.main.kotlin.HandleState
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.first
import network.NetworkSubSystem
import java.lang.Exception

sealed class SubSystemStatus {
    data class Ready(val subSystem: NetworkSubSystem) : SubSystemStatus()
    data class Loading(val subSystem: NetworkSubSystem) : SubSystemStatus()
    data class Failed(val error: Throwable) : SubSystemStatus()
}

class NetworkSubSystemProgress(
    private val flow: SharedFlow<SubSystemStatus>,
) : IProgress<Unit> {

    override suspend fun whenReady(allowedStates: List<HandleState>) {
        flow.first {
            it is SubSystemStatus.Ready
        }
        return
    }
}