package automergeRepo.src.main.kotlin.events

import automergeRepo.src.main.kotlin.HandleState
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.first
import network.INetworkAdapter

sealed class AdapterStatus {
    data class Ready(val subSystem: INetworkAdapter) : AdapterStatus()
    data class Loading(val subSystem: INetworkAdapter) : AdapterStatus()
    data class Failed(val error: Throwable) : AdapterStatus()
}

class NetworkAdapterProgress(
    private val flow: SharedFlow<AdapterStatus>
) : IProgress<Unit> {

    override suspend fun whenReady(allowedStates: List<HandleState>) {
        flow.first { it is AdapterStatus.Ready }
        return
    }
}