package automergeRepo.src.main.kotlin.events

open class EventEmitter<E> {
    private val listeners = mutableMapOf<Class<out E>, MutableList<(E) -> Unit>>()

    fun <T : E> on(eventType: Class<T>, listener: (T) -> Unit) {
        val list = listeners.getOrPut(eventType) { mutableListOf() }
        list.add(listener as (E) -> Unit)
    }

    fun emit(event: E) {
        listeners[event!!::class.java]?.forEach { it(event) }
    }
}

interface EventEmitterInterface<E> {
    fun <T : E> on(eventType: Class<T>, listener: (T) -> Unit)
    fun emit(event: E)
}