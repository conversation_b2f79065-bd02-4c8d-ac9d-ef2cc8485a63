package automergeRepo.src.main.kotlin

//import automergeRepo.src.main.kotlin.documentProxy.DocumentProxyBuilder
import automergeRepo.src.main.kotlin.events.EventEmitter
import automergeRepo.src.main.kotlin.events.FindProgress
import automergeRepo.src.main.kotlin.events.FindProgressState
import automergeRepo.src.main.kotlin.network.Message
import automergeRepo.src.main.kotlin.network.MessageType
import automergeRepo.src.main.kotlin.network.NoOpAdapter
import automergeRepo.src.main.kotlin.reconciler.reconcile
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.produce
import kotlinx.coroutines.flow.*
import synchronizer.CollectionSynchronizer
import synchronizer.SynchronizerEvent
import network.*
import org.automerge.Document

data class RepoOptions(
    var networkAdapters: List<INetworkAdapter> = listOf(NoOpAdapter()),
)

class Repo(options: RepoOptions = RepoOptions()) : EventEmitter<RepoEvents>() {

    val repoScope = CoroutineScope(
        SupervisorJob() + singleThreadDispatcher + repoCoroutineExceptionHandler
    )

    private val cacheLock = Any()
    val handleCache = mutableMapOf<DocumentId, DocHandle<*>>()
    private val synchronizer = CollectionSynchronizer(repoScope, this)
    val networkSubSystem: NetworkSubSystem = NetworkSubSystem(repoScope, options.networkAdapters, randomPeerId())

    init {

        this.on(RepoEvents.DeleteDocument::class.java) { }
        this.synchronizer.on(SynchronizerEvent.Message::class.java) { event ->
            repoScope.launch { networkSubSystem.send(event.message) }
        }
        this.networkSubSystem.on(NetworkSubsystemEvents.Peer::class.java) { event ->
            synchronizer.addPeer(event.peerId)
        }
        this.networkSubSystem.on(NetworkSubsystemEvents.PeerDisconnected::class.java) { event ->
            repoScope.launch { synchronizer.removePeer(event.payload.peerId) }
        }
        this.networkSubSystem.on(NetworkSubsystemEvents.Message::class.java) { event ->
            receiveMessage(event.message)
        }
    }

    private fun receiveMessage(message: Message) {
        when (message.type) {
            MessageType.PEER_MESSAGE, MessageType.ERROR, MessageType.REMOTE_SUBSCRIPTION_CHANGE, MessageType.REMOTE_HEADS_CHANGED -> {
                TODO()
            }

            MessageType.SYNC, MessageType.REQUEST, MessageType.JOIN, MessageType.EPHEMERAL, MessageType.DOC_UNAVAILABLE -> repoScope.launch {
                synchronizer.receiveMessage(
                    message
                )
            }
        }
    }

    private fun randomPeerId(): PeerId =
        "peer-ai-backend-" + (Math.random() * Long.MAX_VALUE).toLong().toString(36).substring(2, 6)


    fun <T : Any> find(
        documentId: DocumentId, clazz: Class<T>
    ): FindProgress<DocHandle<T>> {
        val progress = findWithProgress(documentId, clazz)

        registerHandleWithSubSystems(progress.handle)

        return progress as FindProgress<DocHandle<T>>
    }

    inline fun <reified T : Any> find(
        documentId: DocumentId
    ): FindProgress<DocHandle<T>> {
        val progress = findWithProgress(documentId, T::class.java)

        registerHandleWithSubSystems(progress.handle)

        return progress as FindProgress<DocHandle<T>>
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    fun <T : Any> findWithProgress(
        documentId: DocumentId, clazz: Class<T>
    ): FindProgress<DocHandle<*>> {
        val flow = MutableSharedFlow<FindProgressState>(replay = 1)

        val docId = if (isValidAutomergeUrl(documentId)) {
            parseAutomergeUrl(documentId).documentId
        } else {
            interpretAsDocumentId(documentId)
        }

        handleCache[docId]?.let { handle ->
            //check all the terminal states first. force early return
            if (handle.state() == HandleState.UNAVAILABLE) {
                return FindProgress(flow, repoScope, { flow.emit(FindProgressState.UNAVAILABLE(handle)) }, handle)
            }

            if (handle.state() == HandleState.READY) {
                return FindProgress(flow, repoScope, { flow.emit(FindProgressState.READY(handle)) }, handle)
            }
        }

        val flowDelegate = suspend {

            val handle = getHandle(docId, clazz)

            flow.emit(FindProgressState.LOADING(handle))

            networkSubSystem.getProgress().whenReady()

            //should move this handle into ready state inside checkForChanges()
            handle.request()

            flow.emit(FindProgressState.LOADING(handle))

            registerHandleWithSubSystems(handle)

            handle.getProgress().whenReady(listOf(HandleState.READY, HandleState.UNAVAILABLE))

            if (handle.state() == HandleState.UNAVAILABLE) {
                flow.emit(FindProgressState.UNAVAILABLE(handle))
            }

            if (handle.state() == HandleState.DELETED) {
                flow.emit(FindProgressState.DELETED(handle))
            }

            flow.emit(FindProgressState.READY(handle))
        }
        val handle = getHandle(docId, clazz)
        return FindProgress(flow, repoScope, flowDelegate, handle)
    }

    inline fun <reified T : Any> create(
        initialValue: T? = null
    ): DocHandle<T> {

        val url = generateAutomergeUrl()
        val documentId = parseAutomergeUrl(url).documentId
        val handle = getHandle(documentId, T::class.java)
        registerHandleWithSubSystems(handle)

        handle.update {
            val doc = Document()
            if (initialValue != null) {
                doc.reconcile(initialValue)
                return@update doc
            }
            doc
        }

        handle.doneLoading()
        return handle
    }

    fun <T : Any> create(
        initialValue: T? = null, clazz: Class<T>
    ): DocHandle<T> {

        val url = generateAutomergeUrl()
        val documentId = parseAutomergeUrl(url).documentId
        val handle = getHandle(documentId, clazz)
        registerHandleWithSubSystems(handle)

        handle.update {
            val doc = Document()
            if (initialValue != null) {
                doc.reconcile(initialValue, clazz.kotlin)
                return@update doc
            }
            doc
        }

        handle.doneLoading()
        return handle
    }

//    inline fun <reified T : Any> clone(handleToClone: DocHandle<*>): DocHandle<*> {
//        if (!handleToClone.isReady()) {
//            throw IllegalStateException("Document is not ready, call handle.whenReady() first")
//        }
//        val clonedDoc = Document.load(handleToClone.doc().save())
//        val newHandle = this.create<T>()
//
//        newHandle.update { clonedDoc }
//
//        return newHandle
//    }

    fun <T : Any> getHandle(docId: DocumentId, clazz: Class<T>): DocHandle<T> {
        synchronized(cacheLock) {
            if (handleCache.containsKey(docId)) {
                return handleCache[docId] as DocHandle<T>
            }
            val handle = DocHandle(docId, clazz.kotlin, repoScope)
            handleCache[docId] = handle
            return handle
        }
    }

    fun registerHandleWithSubSystems(handle: DocHandle<*>) {
        synchronizer.addDocument(handle)
    }
}

sealed class RepoEvents {
    data class Document(val handle: DocHandle<*>, val isNew: Boolean) : RepoEvents()
    data class DeleteDocument(val documentId: DocumentId) : RepoEvents()
    data class UnavailableDocument(val documentId: DocumentId) : RepoEvents()
}
