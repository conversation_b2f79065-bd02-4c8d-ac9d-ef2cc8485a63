package automergeRepo.src.main.kotlin

import kotlinx.datetime.toJavaInstant
import kotlinx.serialization.KSerializer
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonPrimitive
import org.automerge.*
import java.lang.reflect.Field
import java.time.temporal.Temporal
import java.util.*
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.time.ExperimentalTime
import kotlin.time.Instant
import kotlin.time.toJavaInstant

fun printDoc(doc: Document): String {
    return printMap(doc, ObjectId.ROOT)
}

fun printValue(doc: Document, value: AmValue): String {
    return when (value) {
        is AmValue.Bool -> "${value.value}"
        is AmValue.Counter -> "${value.value}"
        is AmValue.Int -> "${value.value}"
        is AmValue.Bytes -> "[${value.value.joinToString(",")}]"
        is AmValue.F64 -> "${value.value}"
        is AmValue.List -> printList(doc, value.id)
        is AmValue.Map -> printMap(doc, value.id)
        is AmValue.Null -> "null"
        is AmValue.Str -> "\"${value.value.escapeJson()}\""
        is AmValue.Text -> {
            "\"${doc.text(value.id).get().escapeJson()}\""
        }

        is AmValue.Timestamp -> "\"${value.value.toInstant()}\""
        is AmValue.UInt -> "${value.value}"
        else -> "\"other: $value\""
    }
}

fun String.escapeJson(): String {
    val sb = StringBuilder()
    for (c in this) {
        when (c) {
            '\\' -> sb.append("\\\\")
            '\"' -> sb.append("\\\"")
            '\b' -> sb.append("\\b")
            '\u000C' -> sb.append("\\f")
            '\n' -> sb.append("\\n")
            '\r' -> sb.append("\\r")
            '\t' -> sb.append("\\t")
            else -> {
                if (c < ' ') {
                    sb.append(String.format("\\u%04x", c.code))
                } else {
                    sb.append(c)
                }
            }
        }
    }
    return sb.toString()
}

fun printMap(doc: Document, objectId: ObjectId): String {

    val mapEntries = doc.mapEntries(objectId)
    if (mapEntries.isEmpty) {
        return "empty map"
    }
    return "{" + mapEntries.get().map { "\"${it.key}\":${printValue(doc, it.value)}" }.joinToString(",\n") + "}"
}

fun printList(doc: Document, objectId: ObjectId): String {
    val listItems = doc.listItems(objectId)
    if (listItems.isEmpty) {
        println("empty map")
        return ""
    }
    return "[" + listItems.get().map { printValue(doc, it) }.joinToString(",") + "]"
}