package automergeRepo.src.main.kotlin

/**
 * Decodes an Automerge sync state message from a [ByteArray] and returns an object containing the decoded heads.
 * This corresponds to the Rust `State::decode` for sync state, identified by a leading 0x43 byte
 *
 * The binary format is:
 * - Byte 0: header byte 0x43 indicating a sync state record
 * - Next bytes: a LEB128-encoded length N of the heads list
 * - Next: N change hashes (each 32 bytes) representing the heads
 *
 * The returned [SyncState.heads] contains the list of change hash bytes (each 32 bytes).
 * If decoding fails (e.g. wrong header byte or malformed data), a [SyncDecodeException] is thrown.
 */
@Throws(SyncDecodeException::class)
fun decodeSyncMessage(input: ByteArray): SyncState {
    if (input.isEmpty()) {
        throw NotEnoughInputException()
    }
    // First byte should be 0x43 (SYNC_STATE_TYPE)
    // 0x42 is for ephemeral sync messages
    val header = input[0]
    if (header != 0x43.toByte() && header != 0x42.toByte()) {
        throw WrongTypeException(0x43.toByte(), header)
    }
    // Read length of heads (N) as unsigned LEB128 starting from byte
    val (countLong, afterLenIndex) = input.readUnsignedLeb128(offset = 1)
    if (countLong > Int.MAX_VALUE.toLong()) {
        // Too many heads to handle in a list (likely invalid data or overflow)
        throw BadFormatException("Heads count too large")
    }
    val count = countLong.toInt()
    // Ensure there are enough bytes for all heads (32 bytes each)
    val bytesAvailable = input.size - afterLenIndex
    if (count.toLong() * 32L > bytesAvailable.toLong()) {
        throw NotEnoughInputException()
    }
    // Parse each 32-byte change hash and collect into list
    val headsList = ArrayList<ByteArray>(count)
    var index = afterLenIndex
    repeat(count) {
        // Each hash is 32 bytes (SHA-256 hash)
        val endIndex = index + 32
        // (bounds check not strictly needed due to prior check, but for safety)
        if (endIndex > input.size) throw NotEnoughInputException()
        headsList.add(input.copyOfRange(index, endIndex))
        index = endIndex
    }
    return SyncState(headsList)
}

/** Holds the decoded sync state information (currently only the heads list). */
data class SyncState(val heads: List<ByteArray>) {
    /**
     * Optional convenience: get heads as hex strings (e.g. for logging or comparison).
     */
    fun headsHex(): List<String> = heads.map { it.toHexString() }
}

/** Base class for exceptions thrown by [decodeSyncMessage] on decode failure. */
sealed class SyncDecodeException(message: String) : Exception(message)

/** Thrown if the input does not begin with the expected header byte (0x43). */
class WrongTypeException(expected: Byte, found: Byte) : SyncDecodeException(
    "Wrong type: expected 0x%02x but found 0x%02x".format(expected.toInt() and 0xFF, found.toInt() and 0xFF)
)

/** Thrown if the byte array ends unexpectedly (insufficient data to decode). */
class NotEnoughInputException : SyncDecodeException("Not enough input")

/** Thrown if the data is malformed (e.g. invalid LEB128 encoding or other format errors). */
class BadFormatException(reason: String) : SyncDecodeException(reason)

/**
 * Reads an unsigned LEB128-encoded number from this [ByteArray] starting at the given offset.
 * Returns the decoded value and the new offset (position immediately after the LEB128 sequence).
 * Throws [NotEnoughInputException] if the bytes end prematurely, or [BadFormatException] on invalid encoding.
 */
@Throws(SyncDecodeException::class)
private fun ByteArray.readUnsignedLeb128(offset: Int): Pair<Long, Int> {
    var value = 0L
    var position = offset
    // LEB128 can use at most 10 bytes for a 64-bit value (7 bits per byte * 10 = 70 bits)
    for (i in 0 until 10) {
        if (position >= this.size) {
            throw NotEnoughInputException()
        }
        val byte = this[position].toInt() and 0xFF
        position += 1
        // Extract lower 7 bits of this byte
        val chunk = byte and 0x7F
        // If this is the 10th byte (i == 9), check for overflow or invalid encoding
        if (i == 9) {
            // In a valid encoding, the 10th byte can only have at most 1 as its value (to represent up to 2^63) and must end the sequence
            if ((byte and 0x80) != 0 || chunk > 1) {
                throw BadFormatException("Invalid LEB128 encoding")
            }
        }
        // Accumulate this chunk into the result
        value = value or (chunk.toLong() shl (7 * i))
        // If the high bit is not set, this byte terminates the LEB128 sequence
        if ((byte and 0x80) == 0) {
            // If the value is negative in signed 64-bit (i.e., bit 63 set), treat as overflow (too large for positive number)
            if (value < 0) {
                throw BadFormatException("LEB128 value overflow")
            }
            return Pair(value, position)
        }
    }
    // If we exit the loop without returning, the LEB128 sequence was too long (more than 10 bytes)
    throw BadFormatException("Invalid LEB128 encoding")
}

/** Convert a [ByteArray] to a lowercase hex string representation. */
private fun ByteArray.toHexString(): String {
    return this.joinToString("") { "%02x".format(it) }
}
