package synchronizer

import automergeRepo.src.main.kotlin.*
import automergeRepo.src.main.kotlin.synchronizer.DocSynchronizer
import automergeRepo.src.main.kotlin.network.Message
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.automerge.SyncState

class CollectionSynchronizer(private val repoScope: CoroutineScope, val repo: Repo) : Synchronizer() {

    private val peers = mutableSetOf<PeerId>()
    private val docSynchronizers = mutableMapOf<DocumentId, DocSynchronizer>()
    private val docSetUp = mutableMapOf<DocumentId, Boolean>()

    override suspend fun receiveMessage(message: Message) {
        if (message.documentId == null) throw Exception("DocumentId was null")

        this.docSetUp[message.documentId!!] = true;
        val handle = repo.find<Any>(message.documentId!!)
            .whenReady(listOf(HandleState.READY, HandleState.UNAVAILABLE, HandleState.REQUESTING))
        val docSynchronizer = fetchDocSynchronizer(handle as DocHandle<*>)
        docSynchronizer.receiveMessage(message)
        docSynchronizer.beginSync(peers)
    }

    fun addDocument(handle: DocHandle<*>) {
        // HACK: this is a hack to prevent us from adding the same document twice
        if (this.docSetUp[handle.docId] == true) {
            return;
        }
        val sync = fetchDocSynchronizer(handle)
        repoScope.launch { sync.beginSync(peers) }
    }

    fun addPeer(peerId: PeerId) {
        if (peers.contains(peerId)) {
            return
        }

        if (peers.add(peerId)) {
            for (sync in docSynchronizers.values) {
                repoScope.launch { sync.beginSync(setOf(peerId)) }
            }
        }
    }

    suspend fun removePeer(peerId: PeerId) {
        peers.remove(peerId)
        for (sync in docSynchronizers.values) {
            sync.endSync(peerId)
        }
    }

    private fun fetchDocSynchronizer(handle: DocHandle<*>): DocSynchronizer {
        if (handle.docId !in docSynchronizers) {
            docSynchronizers[handle.docId] = initDocSynchronizer(handle)
        }
        return docSynchronizers[handle.docId]!!
    }

    private fun initDocSynchronizer(handle: DocHandle<*>): DocSynchronizer {
        val onLoadSyncState: suspend (peerId: PeerId) -> SyncState? = { _ -> null }
        val docSync = DocSynchronizer(this.repoScope, handle, repo.networkSubSystem.peerId, onLoadSyncState)
        docSync.on(SynchronizerEvent.Message::class.java) { event -> this.emit(event) }
        docSync.on(SynchronizerEvent.OpenDoc::class.java) { event -> this.emit(event) }
        docSync.on(SynchronizerEvent.SyncState::class.java) { event -> this.emit(event) }
        return docSync
    }
}
