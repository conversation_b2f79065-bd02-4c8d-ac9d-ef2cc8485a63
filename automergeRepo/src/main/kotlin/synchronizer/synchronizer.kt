package synchronizer

import automergeRepo.src.main.kotlin.DocumentId
import automergeRepo.src.main.kotlin.PeerId
import automergeRepo.src.main.kotlin.events.EventEmitter

sealed class SynchronizerEvent {
    data class Message(val message: automergeRepo.src.main.kotlin.network.Message) : SynchronizerEvent()
    data class SyncState(val peerId: PeerId, val documentId: DocumentId, val syncState: org.automerge.SyncState) : SynchronizerEvent()
    data class OpenDoc(val peerId: PeerId, val documentId: DocumentId) : SynchronizerEvent()
}

abstract class Synchronizer : EventEmitter<SynchronizerEvent>() {
    abstract suspend fun receiveMessage(message: automergeRepo.src.main.kotlin.network.Message)
}