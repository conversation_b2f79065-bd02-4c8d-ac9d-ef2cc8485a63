package automergeRepo.src.main.kotlin.synchronizer

import automergeRepo.src.main.kotlin.*
import automergeRepo.src.main.kotlin.network.Message
import automergeRepo.src.main.kotlin.network.MessageType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.automerge.Document
import org.automerge.SyncState
import synchronizer.Synchronizer
import synchronizer.SynchronizerEvent
import java.util.Date

class DocSynchronizer(
    val repoScope: CoroutineScope,
    private val handle: DocHandle<*>,
    val peerId: PeerId,
    val onLoadSyncState: suspend (peerId: PeerId) -> SyncState?
) : Synchronizer() {

    private var syncStarted: Boolean = false
    private val peerDocumentStatuses = mutableMapOf<PeerId, PeerDocumentStatus>()
    private val peers = mutableSetOf<PeerId>()
    private val syncStates = mutableMapOf<PeerId, SyncState>()
    private val pendingSyncMessages = mutableListOf<PendingMessage>()
    private val pendingSyncStateCallbacks = mutableMapOf<PeerId, MutableList<(SyncState) -> Unit>>()

    private fun processAllPendingSyncMessages() {
        this.pendingSyncMessages.forEach { processSyncMessage(it.message) }
        this.pendingSyncMessages.clear()
    }

    private fun processSyncMessage(message: Message) {
        if (message.type == MessageType.REQUEST) {
            this.peerDocumentStatuses[message.senderId!!] = PeerDocumentStatus.WANTS
        }
        this.checkDocUnavailable()

        // if the message has heads, then the peer has the document
        if (decodeSyncMessage(message.data!!).heads.isNotEmpty()) {
            this.peerDocumentStatuses[message.senderId!!] = PeerDocumentStatus.HAS
        }

        this.withSyncState(message.senderId!!) { syncState ->
            this.handle.update { doc ->
                doc.receiveSyncMessage(syncState, message.data!!);

                this.setSyncState(message.senderId!!, syncState)
                // respond to just this peer
                sendSyncMessage(message.senderId!!, doc)
                doc
            }
            this.checkDocUnavailable()
        }
    }

    private fun checkDocUnavailable() {
        if (this.syncStarted && this.handle.inState(listOf(HandleState.REQUESTING)) && this.peers.all { peerId ->
                this.peerDocumentStatuses[peerId] == PeerDocumentStatus.UNAVAILABLE || this.peerDocumentStatuses[peerId] == PeerDocumentStatus.WANTS
            }) {
            this.peers.filter { peerId -> this.peerDocumentStatuses[peerId] == PeerDocumentStatus.WANTS }
                .forEach { peerId ->
                    this.emit(
                        SynchronizerEvent.Message(
                            message = Message.DocMessage.DocumentUnavailableMessage(
                                targetId = peerId,
                                documentId = this.handle.docId,
                            )
                        )
                    )
                }
            this.handle.unavailable()
        }
    }

    override suspend fun receiveMessage(message: Message) {
        when (message.type) {
            MessageType.SYNC, MessageType.REQUEST -> {
                this.receiveSyncMessage(message)
            }

            MessageType.DOC_UNAVAILABLE -> {
                this.peerDocumentStatuses[message.senderId!!] = PeerDocumentStatus.UNAVAILABLE
                this.checkDocUnavailable()
            }

            MessageType.EPHEMERAL -> {
//                if (message.documentId != this.handle.docId) {
//                    throw IllegalArgumentException("channelId doesn't match documentId")
//                }


            }

            else -> {
                throw IllegalArgumentException("Unknown message type: ${message.type}")
            }
        }
    }

    private suspend fun receiveSyncMessage(message: Message) {
        if (message.documentId != handle.docId) {
            throw IllegalArgumentException("channelId doesn't match documentId")
        }

        if (!this.handle.inState(listOf(HandleState.REQUESTING, HandleState.READY, HandleState.UNAVAILABLE))) {
            this.pendingSyncMessages.add(
                PendingMessage(
                    message = message,
                    received = Date(),
                )
            )
            return
        }

        this.processAllPendingSyncMessages()
        this.processSyncMessage(message)
    }

    fun hasPeer(peerId: PeerId): Boolean = peerId in peers

    fun endSync(peerId: PeerId) {
        peers.remove(peerId)
    }

    suspend fun beginSync(peerIds: Set<PeerId>) {
        repoScope.launch {
            try {
                handle.getProgress().whenReady(
                    listOf(
                        HandleState.READY, HandleState.REQUESTING, HandleState.UNAVAILABLE
                    )
                )
            } finally {
                syncStarted = true
                checkDocUnavailable()
            }
        }

        if (peerIds.any { peerDocumentStatuses[it] == PeerDocumentStatus.HAS }) {
            handle.getProgress().whenReady()
        }

        for (peerId in peerIds) {
            withSyncState(peerId) { syncState ->
                repoScope.launch {
                    setSyncState(peerId, syncState)
                    handle.getProgress()
                        .whenReady(listOf(HandleState.READY, HandleState.REQUESTING, HandleState.UNAVAILABLE))
                    val doc = if (handle.isReady()) handle.doc() else Document()
                    val noPeersWithDocument = peers.all { pid ->
                        listOf(
                            PeerDocumentStatus.UNAVAILABLE, PeerDocumentStatus.WANTS
                        ).contains(peerDocumentStatuses[pid])
                    }
                    if (!noPeersWithDocument) sendSyncMessage(peerId, doc)
                }
            }
        }
    }

    private fun sendSyncMessage(peerId: PeerId, document: Document = Document()) {
        withSyncState(peerId) { syncState ->
            val syncMessage = document.generateSyncMessage(syncState)
            if (!syncMessage.isEmpty) {
                setSyncState(peerId, syncState)
                val isNew = document.heads.isEmpty()
                if (!handle.isReady() && isNew && peerDocumentStatuses.any { it.value == PeerDocumentStatus.HAS } && peerDocumentStatuses[peerId] == PeerDocumentStatus.UNKNOWN) {
                    this.emit(
                        SynchronizerEvent.Message(
                            message = Message.DocMessage.RequestMessage(
                                targetId = peerId,
                                data = syncMessage.get(),
                                documentId = handle.docId,
                            )
                        )
                    )
                } else {
                    this.emit(
                        SynchronizerEvent.Message(
                            message = Message.DocMessage.SyncMessage(
                                targetId = peerId, data = syncMessage.get(), documentId = handle.docId
                            )
                        )
                    )

                }
                if (!isNew) {
                    peerDocumentStatuses[peerId] = PeerDocumentStatus.HAS
                }
            }
        }
    }

    private fun withSyncState(peerId: PeerId, callback: (syncState: SyncState) -> Unit) {
        this.addPeer(peerId)
        if (peerId !in peerDocumentStatuses) {
            peerDocumentStatuses[peerId] = PeerDocumentStatus.UNKNOWN
        }

        val syncState = syncStates[peerId]
        if (syncState != null) {
            callback(syncState)
            return
        }

        if (pendingSyncStateCallbacks[peerId] == null) {
            pendingSyncStateCallbacks[peerId] = mutableListOf(callback)
            this.initSyncState(peerId, SyncState())
        }
    }

    private fun initSyncState(peerId: PeerId, syncState: SyncState) {
        pendingSyncStateCallbacks[peerId]?.forEach { callback ->
            callback(syncState)
        }

        this.pendingSyncStateCallbacks.remove(peerId)
        syncStates[peerId] = syncState
    }

    private fun addPeer(peerId: PeerId) {
        if (peers.add(peerId)) {
            this.emit(
                SynchronizerEvent.OpenDoc(
                    handle.docId, peerId
                )
            )
        }
    }

    private fun setSyncState(peerId: PeerId, syncState: SyncState) {
        syncStates[peerId] = syncState
        this.emit(
            SynchronizerEvent.SyncState(
                peerId = peerId, documentId = handle.docId, syncState = syncState
            )
        )
    }
}

enum class PeerDocumentStatus {
    UNKNOWN, HAS, UNAVAILABLE, WANTS
}

data class PendingMessage(val message: Message, val received: Date)