package automergeRepo.src.main.kotlin

import automergeRepo.src.main.kotlin.ObservableStateMachine.StateContext
import automergeRepo.src.main.kotlin.events.EventEmitter
import automergeRepo.src.main.kotlin.events.HandleProgress
import automergeRepo.src.main.kotlin.reconciler.reconcile
import com.tinder.StateMachine
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import kotlinx.serialization.InternalSerializationApi
import kotlinx.serialization.json.Json
import kotlinx.serialization.serializer
import org.automerge.Document
import org.automerge.Patch
import kotlin.reflect.KClass

class DocHandle<T : Any> constructor(
    val docId: DocumentId,
    private val clazz: KClass<T>,
    private val repoScope: CoroutineScope,
) : EventEmitter<DocHandleEvents>() {
    private var prevDocState = Document()

    val handleJsonizer = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    //TODO: Make this configurable
    private val timeoutDelayMs: Long = 1000 * 5
    val machine = createDocHandleStateMachine()

    val handleJson = Json { encodeDefaults = true; }

    fun state() = machine.currentState

    @OptIn(InternalSerializationApi::class)
    fun document(): T {
        val json = printDoc(machine.doc)
        val newObj = Json.decodeFromString(clazz.serializer(), json)
        return newObj
    }

    @OptIn(InternalSerializationApi::class)
    fun <T : Any> document(clazz: Class<T>): T {

        val w = printDoc(machine.doc)

        return handleJsonizer.decodeFromString(clazz.kotlin.serializer(), w)
    }

    init {
        machine.transition(event = HandleEvent.Begin)
    }

    fun unavailable() {
        machine.transition(
            event = HandleEvent.DocUnavailable
        )
    }

    private fun checkForChanges(before: Document, after: Document) {
        if (!(before.heads contentEquals after.heads)) {
            val patches = after.diff(before.heads, after.heads)
            if (patches.size > 0) {
                this.emit(
                    DocHandleEvents.Change(
                        DocHandleChangePayload(
                            handle = this, doc = after, patches = patches, PatchInfo(before, after, PatchSource.CHANGE)
                        )
                    )
                )
            }
            // If we didn't have the document yet, signal that we now do
            if (!this.isReady()) {
                this.machine.transition(HandleEvent.DocReady)
            }
        }
        this.prevDocState = after
    }

    fun request() {
        if (this.state() == HandleState.LOADING) {
            machine.transition(
                event = HandleEvent.Request
            )
        }
    }

    fun doneLoading() {
        machine.transition(
            event = HandleEvent.DocReady
        )
    }

    @OptIn(InternalSerializationApi::class)
    fun change(callback: (T) -> Unit) {
        this.machine.transition(
            event = HandleEvent.Update(
                callback = { doc ->
                    val obj = handleJson.decodeFromString(clazz.serializer(), printDoc(doc))
                    callback(obj)
                    doc.reconcile(obj, clazz)
                    doc
                })
        )
    }

    fun update(callback: (Document) -> Document) {
        machine.transition(
            event = HandleEvent.Update(
                callback = callback
            )
        )
    }

    fun getProgress(): HandleProgress {

        val flow = MutableSharedFlow<HandleState>(
            replay = 1,
        )

        repoScope.launch {
            withTimeout(timeoutDelayMs) {
                //will throw timeout exception upon completion
                //listen to state machines state changes
                machine.stateFlow.collect { context ->
                    //pass forward any state changes to anyone listening to this handle's changes
                    flow.emit(context.state)
                }
            }
        }

        return HandleProgress(flow)
    }

    fun isReady() = inState(listOf(HandleState.READY))

    fun inState(states: List<HandleState>) = states.contains(machine.currentState)

    fun doc(): Document {
        if (!isReady()) {
            throw IllegalStateException("Document is not ready")
        }
        return machine.doc
    }

    private fun createDocHandleStateMachine(): ObservableStateMachine<HandleState, HandleEvent, Unit> {
        return ObservableStateMachine({ self ->
            StateMachine.create {
                initialState(HandleState.IDLE)

                onTransition { transition ->
                    val event = transition.event

                    val before = Document(prevDocState.save())

                    if (event is HandleEvent.Update) {
                        self.doc = event.callback(self.doc)

                    }

                    checkForChanges(before, self.doc)

                    repoScope.launch { self.stateFlow.emit(StateContext(self.doc, docId, self.currentState)) }
                }

                state<HandleState.IDLE> {
                    on<HandleEvent.Begin> { transitionTo(HandleState.LOADING) }
                    on<HandleEvent.Unload> { transitionTo(HandleState.UNLOADED) }
                    on<HandleEvent.Delete> { transitionTo(HandleState.DELETED) }
                }

                state<HandleState.LOADING> {
                    on<HandleEvent.Request> { transitionTo(HandleState.REQUESTING) }
                    on<HandleEvent.DocReady> { transitionTo(HandleState.READY) }
                    on<HandleEvent.Unload> { transitionTo(HandleState.UNLOADED) }
                    on<HandleEvent.Delete> { transitionTo(HandleState.DELETED) }
                }
                state<HandleState.REQUESTING> {
                    on<HandleEvent.DocUnavailable> { transitionTo(HandleState.UNAVAILABLE) }
                    on<HandleEvent.DocReady> { transitionTo(HandleState.READY) }
                    on<HandleEvent.Unload> { transitionTo(HandleState.UNLOADED) }
                    on<HandleEvent.Delete> { transitionTo(HandleState.DELETED) }
                }
                state<HandleState.UNAVAILABLE> {
                    onEnter { }
                    on<HandleEvent.DocReady> { transitionTo(HandleState.READY) }
                    on<HandleEvent.Unload> { transitionTo(HandleState.UNLOADED) }
                    on<HandleEvent.Delete> { transitionTo(HandleState.DELETED) }
                }
                state<HandleState.READY> {
                    on<HandleEvent.Unload> { transitionTo(HandleState.UNLOADED) }
                    on<HandleEvent.Delete> { transitionTo(HandleState.DELETED) }
                }
                state<HandleState.UNLOADED> {
                    onEnter { }
                    on<HandleEvent.Reload> { transitionTo(HandleState.LOADING) }
                }
                state<HandleState.DELETED> {
                    onEnter { }
                }
            }
        }, repoScope = repoScope)
    }


}


sealed class DocHandleEvents {
    //    data class HeadsChanged<T>(val payload: DocHandleEncodedChangePayload<T>) : DocHandleEvents()
    data class Change<T : Any>(val payload: DocHandleChangePayload<T>) : DocHandleEvents()
//    data class Delete<T>(val payload: DocHandleDeletePayload<T>) : DocHandleEvents()
//    data class EphemeralMessage<T>(val payload: DocHandleEphemeralMessagePayload<T>) : DocHandleEvents()
//    data class EphemeralMessageOutbound<T>(val payload: DocHandleOutboundEphemeralMessagePayload<T>) : DocHandleEvents()
//    data class RemoteHeads(val payload: DocHandleRemoteHeadsPayload) : DocHandleEvents()
//    data class Unavailable<T>(val payload: DocHandleUnavailablePayload<T>) : DocHandleEvents()
}

//data class DocHandleEncodedChangePayload<T>(
//    val handle: DocHandle<T>, val doc: Document
//)

data class DocHandleChangePayload<T : Any>(
    val handle: DocHandle<T>, val doc: Document, val patches: List<Patch>, val patchInfo: PatchInfo<T>
)

enum class PatchSource {
    FROM, EMPTY_CHANGE, CHANGE, CHANGE_AT, MERGE, LOAD_INCREMENTAL, APPLY_CHANGES, RECEIVE_SYNC_MESSAGE
}

data class PatchInfo<T>(
    val before: Document, val after: Document, val source: PatchSource
)

//data class DocHandleDeletePayload<T>(
//    val handle: DocHandle<T>
//)
//
//data class DocHandleUnavailablePayload<T>(
//    val handle: DocHandle<T>
//)
//
//data class DocHandleEphemeralMessagePayload<T>(
//    val handle: DocHandle<T>, val senderId: PeerId, val message: Any
//)
//
//data class DocHandleOutboundEphemeralMessagePayload<T>(
//    val handle: DocHandle<T>, val data: ByteArray
//)

data class DocHandleRemoteHeadsPayload(
//    val storageId: StorageId,
    val heads: List<String>
)
