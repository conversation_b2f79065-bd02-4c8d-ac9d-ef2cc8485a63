package automergeRepo.src.main.kotlin.reconciler.`object`

import automergeRepo.src.main.kotlin.reconciler.insertToMap
import automergeRepo.src.main.kotlin.reconciler.traverse
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import org.automerge.AmValue
import org.automerge.ObjectId
import org.automerge.Transaction

fun JsonObject.traverse(tx: Transaction, current: AmValue, currentSetter: (newValue: JsonElement) -> ObjectId?) {
    val currentId = try {
        (current as AmValue.Map).id
    } catch (e: ClassCastException) {
        //if the current is not a map, we need to create it
        //this also returns the id of the new element
        // !! is safe here because only primitive values return null. and a primitive cannot be a parent
        currentSetter(this)!!
    }

    val oldMap = tx.mapEntries(currentId).get()
    val newMap = this.entries

    //check and remove entries
    oldMap.filter { it.key !in newMap.map { entry -> entry.key } }.forEach { removed ->
        tx.delete(currentId, removed.key)
    }

    //check and add entries
    //primitives will be added with their value
    //lists and maps will only have their key and type added
    this.entries.forEach { added ->
        if (oldMap.none { it.key == added.key }) {
            insertToMap(tx, currentId, added.key, added.value)
        }
    }

    // recurse each member's value
    this.entries.forEachIndexed { i, entry ->
        val newParentId = currentId
        val newCurrent = tx.get(currentId, entry.key).get()

        val newCurrentSetter: (newValue: JsonElement) -> ObjectId? = { newValue ->
            insertToMap(tx, currentId, entry.key, newValue)
        }

        entry.value.traverse(tx, newCurrent, newParentId, entry.key, newCurrentSetter)
    }
}


fun JsonObject.traverseRoot(tx: Transaction) {
    val oldMap = tx.mapEntries(ObjectId.ROOT).get()
    val newMap = this.entries

    //check and remove entries
    oldMap.filter { it.key !in newMap.map { entry -> entry.key } }.forEach { removed ->
        tx.delete(ObjectId.ROOT, removed.key)
    }

    //check and add entries
    //primitives will be added with their value
    //lists and maps will only have their key and type added
    this.entries.forEach { added ->
        if (oldMap.none { it.key == added.key }) {
            insertToMap(tx, ObjectId.ROOT, added.key, added.value)
        }
    }

    // recurse each member's value
    this.entries.forEachIndexed { i, entry ->
        val newCurrent = tx.get(ObjectId.ROOT, entry.key).get()

        val newCurrentSetter: (newValue: JsonElement) -> ObjectId? = { newValue ->
            insertToMap(tx, ObjectId.ROOT, entry.key, newValue)!!
        }

        entry.value.traverse(tx, newCurrent, ObjectId.ROOT, entry.key, newCurrentSetter)
    }
}