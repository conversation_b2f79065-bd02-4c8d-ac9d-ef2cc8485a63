package automergeRepo.src.main.kotlin.reconciler.primitive

import automergeRepo.src.main.kotlin.reconciler.valueOrNull
import kotlinx.serialization.json.*
import org.automerge.AmValue
import org.automerge.NewValue
import org.automerge.ObjectId
import org.automerge.Transaction

fun JsonPrimitive.traverse(
    tx: Transaction,
    current: AmValue,
    parentId: ObjectId,
    index: Int,
    currentSetter: (newValue: JsonElement) -> ObjectId?
) {
    val i = index.toLong()

    if (current is AmValue.Null) {
        tx.insert(parentId, i, NewValue.NULL)
        return
    }

    when (val value = this.valueOrNull) {

        is String -> {
            when (current) {
                is AmValue.Str -> {
                    if (value != current.value) {
                        tx.insert(parentId, i, value)
                        return
                    }
                }

                is AmValue.Text -> {
                    if (value != tx.text(current.id).get()) {
                        tx.insert(parentId, i, value)
                        return
                    }
                }
            }
        }

        is Int -> {
            try {
                if (value != (current as AmValue.Int).value.toInt()) {
                    tx.insert(parentId, i, value)
                    return
                }
            } catch (e: ClassCastException) {
                currentSetter(this)
                return
            }
        }

        //suspect. long to double conversion, keep an eye on this.
        is Long -> {
            try {
                if (value != (current as AmValue.F64).value.toLong()) {
                    tx.insert(parentId, i, value.toDouble())
                    return
                }
            } catch (e: ClassCastException) {
                currentSetter(this)
                return
            }
        }

        is Double -> {
            try {
                if (value != (current as AmValue.F64).value) {
                    tx.insert(parentId, i, value)
                    return
                }
            } catch (e: ClassCastException) {
                currentSetter(this)
                return
            }
        }

        is Boolean -> {
            try {
                if (value != (current as AmValue.Bool).value) {
                    tx.insert(parentId, i, value)
                    return
                }
            } catch (e: ClassCastException) {
                currentSetter(this)
                return
            }
        }

        null -> {
            tx.insert(parentId, i, NewValue.NULL)
            return
        }

        else -> throw IllegalArgumentException("Unsupported primitive type in list: $current")

    }
}

fun JsonPrimitive.traverse(
    tx: Transaction,
    current: AmValue,
    parentId: ObjectId,
    key: String,
    currentSetter: (newValue: JsonElement) -> ObjectId?
) {
    if (current is AmValue.Null) {
        tx.set(parentId, key, NewValue.NULL)
        return
    }

    when (val value = this.valueOrNull) {

        null -> {
            tx.set(parentId, key, NewValue.NULL)
            return
        }

        is String -> {
            when (current) {
                is AmValue.Str -> {
                    if (value != current.value) {
                        tx.set(parentId, key, value)
                        return
                    }
                }

                is AmValue.Text -> {
                    if (value != tx.text(current.id).get()) {
                        tx.set(parentId, key, value)
                        return
                    }
                }
            }
        }

        is Int -> {
            try {
                if (value != (current as AmValue.Int).value.toInt()) {
                    tx.set(parentId, key, value)
                    return
                }
            } catch (e: ClassCastException) {
                currentSetter(this)
                return
            }
        }

        //suspect. long to double conversion, keep an eye on this.
        is Long -> {
            try {
                if (value != (current as AmValue.F64).value.toLong()) {
                    tx.set(parentId, key, value.toDouble())
                    return
                }
            } catch (e: ClassCastException) {
                currentSetter(this)
                return
            }
        }

        is Double -> {
            try {
                if (value != (current as AmValue.F64).value) {
                    tx.set(parentId, key, value)
                    return
                }
            } catch (e: ClassCastException) {
                currentSetter(this)
                return
            }
        }

        is Boolean -> {
            try {
                if (value != (current as AmValue.Bool).value) {
                    tx.set(parentId, key, value)
                    return
                }
            } catch (e: ClassCastException) {
                currentSetter(this)
                return
            }
        }


        else -> throw IllegalArgumentException("Unsupported primitive type in map: $current")
    }
}