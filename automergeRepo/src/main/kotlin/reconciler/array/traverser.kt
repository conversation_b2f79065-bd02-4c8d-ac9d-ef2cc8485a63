package automergeRepo.src.main.kotlin.reconciler.array

import automergeRepo.src.main.kotlin.reconciler.insertToList
import automergeRepo.src.main.kotlin.reconciler.traverse
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import org.automerge.AmValue
import org.automerge.ObjectId
import org.automerge.Transaction

fun JsonArray.traverse(tx: Transaction, current: AmValue, currentSetter: (newValue: JsonElement) -> ObjectId?) {

    val currentId = try {
        (current as AmValue.List).id
    } catch (e: ClassCastException) {
        //if the current is not a list, we need to create it
        //this also returns the id of the new element
        // !! is safe here because only primitive values return null. and a primitive cannot be a parent
        currentSetter(this)!!
    }

    //remove items that exceed the length of the new array
    val oldItems = tx.listItems(currentId).get()
    val newSize = this.size
    for (i in oldItems.size - 1 downTo newSize) {
        tx.delete(currentId, i.toLong())
    }

    //traverse the rest.
    for (e in this.withIndex()) {
        val i = e.index

        //ensure created
        if (i >= oldItems.size || oldItems[i] == null) {
            insertToList(tx, currentId, i, e.value)
        }

        val newParentSetter: (newValue: JsonElement) -> ObjectId = { newValue ->
            // !! is safe here because only primitive values return null. and a primitive cannot be a parent
            insertToList(tx, currentId, i, newValue)!!
        }

        e.value.traverse(
            tx, tx.listItems(currentId).get()[i], currentId, i, newParentSetter
        )
    }
}
