package automergeRepo.src.main.kotlin.reconciler

import kotlinx.serialization.json.*
import org.automerge.*

fun insertToMap(tx: Transaction, id: ObjectId, key: String, element: JsonElement): ObjectId? {
    when (element) {
        is JsonNull -> tx.set(id, key, NewValue.NULL)
        is JsonArray -> return tx.set(id, key, ObjectType.LIST)
        is JsonObject -> return tx.set(id, key, ObjectType.MAP)
        is JsonPrimitive -> insertToMap(tx, id, key, element)
    }
    //primitives and nulls have no object id
    return null
}

fun insertToMap(tx: Transaction, parentId: ObjectId, key: String, value: JsonPrimitive): Unit {
    when (val unwrappedValue = value.valueOrNull) {
        is String -> {
            tx.set(parentId, key, unwrappedValue)
        }

        is Int -> {
            tx.set(parentId, key, unwrappedValue)
        }

        is Long -> {
            tx.set(parentId, key, unwrappedValue.toDouble())
        }

        is Double -> {
            tx.set(parentId, key, unwrappedValue)
        }

        is Boolean -> {
            tx.set(parentId, key, unwrappedValue)
        }

        else -> throw IllegalArgumentException()
    }
}


fun insertToList(tx: Transaction, id: ObjectId, index: Int, element: JsonElement): ObjectId? {
    val lIndex = index.toLong()
    when (element) {
        is JsonNull -> tx.insert(id, lIndex, NewValue.NULL)
        is JsonArray -> return tx.insert(id, lIndex, ObjectType.LIST)
        is JsonObject -> return tx.insert(id, lIndex, ObjectType.MAP)
        is JsonPrimitive -> insertToList(tx, id, lIndex, element)
    }
    //primitives and nulls have no object id
    return null
}

fun insertToList(tx: Transaction, parentId: ObjectId, index: Long, element: JsonPrimitive): Unit {
    when (val unwrappedValue = element.valueOrNull) {
        is String -> {
            tx.insert(parentId, index, unwrappedValue)
        }

        is Int -> {
            tx.insert(parentId, index, unwrappedValue)
        }

        is Long -> {
            tx.insert(parentId, index, unwrappedValue.toDouble())
        }

        is Double -> {
            tx.insert(parentId, index, unwrappedValue)
        }

        is Boolean -> {
            tx.insert(parentId, index, unwrappedValue)
        }

        else -> throw IllegalArgumentException("Unsupported primitive type in list: $element")
    }
}

// https://github.com/Kotlin/kotlinx.serialization/issues/1298#issuecomment-934427378
private val INT_REGEX = Regex("""^-?\d+$""")
private val DOUBLE_REGEX = Regex("""^-?\d+\.\d+(?:E-?\d+)?$""")

val JsonPrimitive.valueOrNull: Any?
    get() = when {
        this is JsonNull -> null
        this.isString -> this.content
        else -> this.content.toBooleanStrictOrNull() ?: when {
            INT_REGEX.matches(this.content) -> this.int
            DOUBLE_REGEX.matches(this.content) -> this.double
            else -> throw IllegalArgumentException("Unknown type for JSON value: ${this.content}")
        }
    }