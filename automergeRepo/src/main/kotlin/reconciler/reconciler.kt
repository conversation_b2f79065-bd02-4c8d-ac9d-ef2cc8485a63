package automergeRepo.src.main.kotlin.reconciler

import automergeRepo.src.main.kotlin.reconciler.array.traverse
import kotlinx.serialization.json.*
import automergeRepo.src.main.kotlin.reconciler.`object`.traverse
import automergeRepo.src.main.kotlin.reconciler.`object`.traverseRoot
import org.automerge.*
import automergeRepo.src.main.kotlin.reconciler.primitive.traverse
import kotlinx.serialization.InternalSerializationApi
import kotlinx.serialization.serializer
import kotlin.reflect.KClass

val reconcilerJson = Json {
    encodeDefaults = true
}

inline fun <reified T> Document.reconcile(new: T): Unit = this.startTransaction().use { tx ->

    val json = reconcilerJson.encodeToJsonElement(new).jsonObject

    //need to ensure the top layer is initialized. we can't enter the recursive function without a 'current'
    json.traverseRoot(tx)

    tx.commit()
}

@OptIn(InternalSerializationApi::class)
fun <T : Any> Document.reconcile(new: T, clazz: KClass<T>): Unit = this.startTransaction().use { tx ->

    val json = reconcilerJson.encodeToJsonElement(clazz.serializer(), new).jsonObject

    //need to ensure the top layer is initialized. we can't enter the recursive function without a 'current'
    json.traverseRoot(tx)

    tx.commit()
}


//these functions are just for routing a more specific json type
//parent is list
fun JsonElement.traverse(
    tx: Transaction,
    current: AmValue,
    parentId: ObjectId,
    index: Int,
    currentSetter: (newValue: JsonElement) -> ObjectId?
) {

    when (this) {
        is JsonArray -> {
            this.traverse(tx, current, currentSetter)
        }

        is JsonObject -> {
            this.traverse(tx, current, currentSetter)
        }

        is JsonPrimitive -> {
            this.traverse(tx, current, parentId, index, currentSetter)
        }

        JsonNull -> {
            tx.set(parentId, index.toLong(), NewValue.NULL)
        }
    }
}

//parent is map
fun JsonElement.traverse(
    tx: Transaction,
    current: AmValue,
    parentId: ObjectId,
    key: String,
    currentSetter: (newValue: JsonElement) -> ObjectId?
) {

    when (this) {
        is JsonArray -> {
            this.traverse(tx, current, currentSetter)
        }

        is JsonObject -> {
            this.traverse(tx, current, currentSetter)
        }

        is JsonPrimitive -> {
            this.traverse(tx, current, parentId, key, currentSetter)
        }

        JsonNull -> {
            tx.set(parentId, key, NewValue.NULL)
        }
    }
}





