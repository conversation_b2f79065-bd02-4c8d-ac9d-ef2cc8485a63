plugins {
    id("project.conventions")
}

group = "services.oneteam.ai.automergeRepo"
version = "0.0.1"

dependencies {

    //general
    implementation(libs.gson)
    implementation(libs.jsonata)
    implementation(libs.commons.text)
    implementation(libs.kotlinx.datetime)

    //test
    implementation(libs.opentelemetry.ktor)
    testImplementation(kotlin("test"))
    testImplementation(libs.bundles.kotest)
    testImplementation(libs.mockito)
    testImplementation(libs.mockk)
    testImplementation(libs.assertj.core)
    testImplementation(libs.test.containers)
    implementation("io.micrometer:micrometer-registry-otlp:1.15.0")
    implementation("io.ktor:ktor-metrics-micrometer:1.6.8")
    testImplementation(libs.junit.jupiter.params)
    testImplementation(project(":shared"))

    //repo
    api(libs.automerge)
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.10.2")
    implementation("io.ktor:ktor-serialization-kotlinx-json-jvm:3.1.3")
    api(libs.bundles.ktor.client)
    implementation("io.ktor:ktor-client-auth:3.1.3")
    implementation("com.tinder.statemachine:statemachine:0.2.0")
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-cbor:1.8.1")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.19.0")
    implementation("net.bytebuddy:byte-buddy:1.17.5")
    implementation("org.objenesis:objenesis:3.4")
    implementation("org.jetbrains.kotlin:kotlin-reflect:2.1.21")

}