
dependencies {

    //general
    implementation(libs.gson)
    implementation(libs.jsonata)
    implementation(libs.commons.text)
    implementation(libs.kotlinx.datetime)

    //test
    implementation(libs.opentelemetry.ktor)
    testImplementation(kotlin("test"))
    testImplementation(libs.bundles.kotest)
    testImplementation(libs.mockito)
    testImplementation(libs.mockk)
    testImplementation(libs.assertj.core)
    testImplementation(libs.test.containers)
    implementation("io.micrometer:micrometer-registry-otlp:1.15.2")
    implementation(ktorLibs.server.metrics.micrometer)
    testImplementation(libs.junit.jupiter.params)
    testImplementation(project(":shared"))

    //repo
    api(libs.automerge)
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.10.2")
    implementation(ktorLibs.serialization.kotlinx.json)

    implementation(ktorLibs.client.core)
    implementation(ktorLibs.client.auth)
    implementation(ktorLibs.client.cio)
    implementation(ktorLibs.client.contentNegotiation)
    implementation(ktorLibs.client.serialization)
    implementation(ktorLibs.client.mock)

    implementation("com.tinder.statemachine:statemachine:0.2.0")
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-cbor:1.9.0")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.19.2")
    implementation("net.bytebuddy:byte-buddy:1.17.6")
    implementation("org.objenesis:objenesis:3.4")
    implementation("org.jetbrains.kotlin:kotlin-reflect:2.2.0")

}