#!/usr/bin/env zx
import { $ } from "zx";

const SCRIPTS_DIR = __dirname;
const REPOSITORY_ROOT_DIR = `${SCRIPTS_DIR}/..`;

function printUsageHelp() {
  console.log(`usage: ./licenseCheker.mjs [--help]

  Run this script in the repo you want to get the licenses information for.
  It will generate a bunch of artifacts in the /tmp directory.

  Example:
    ./licenseChecker.mjs
  `);
}

if (argv.help) {
  printUsageHelp();
  process.exit(0);
}


const repoName = "oneteam-ai-be";
const currentDate = new Date().toISOString().split('T')[0].replace(/-/g, '');
const workingDir = `/tmp/${repoName}`;
const filenameBase = `${currentDate}-${repoName}`;

await $`mkdir -p ${workingDir}`;

function workingPathToFile(file) {
  return `${workingDir}/${file}`;
}

console.log("Running license checker for the project...");
const licenseReport = await getLicenseReport();
await outputDirectDetailedCsv(licenseReport);
await outputSummary(licenseReport);
await $`echo "These files were generated by the oneteam-be/scripts/licenseChecker.mjs script." > ${workingPathToFile("README.md")}`;

console.log("License check complete. Artifacts outputted to:");
console.log(chalk.yellow(`open ${workingDir}`));

// ---


/**
 * @typedef {Object<string, string>} LicenseReportItem
 * @property {string} dependency
 * @property {string} version
 * @property {Array<{license: string, license_url: string}>} licenses
 * @property {string} [url]
 * @property {string} [description]
 * @property {Array<{name: string, email: string}>} [developers]
 * @property {string} [year]
 * @property {string} [project]
 * 
 * @returns {Promise<LicenseReportItem[]>}
 */
async function getLicenseReport() {
  await $`cd ${REPOSITORY_ROOT_DIR} && ./gradlew licenseReport`;
  const licenseReportPath = `${REPOSITORY_ROOT_DIR}/app/build/reports/licenses/licenseReport.json`;

  const licenseReport = JSON.parse(await $`cat ${licenseReportPath}`);
  return licenseReport;
}

async function outputDirectDetailedCsv(licenseReport) {
  const outputLines = licenseReport.map(reportItem => (
    [
      `"${reportItem.dependency}"`,
      `"${reportItem.version}"`,
      `"${reportItem.licenses[0].license}"`,
      `"${reportItem.licenses[0].license_url}"`,
    ].join(',')
  ));
  const outputContent = ['"Package Name","Version","License","URL"'].concat(outputLines).join('\n');
  const outputTsvFilename = `${filenameBase}-direct-detailed.csv`;
  await $`echo ${outputContent} > ${workingPathToFile(outputTsvFilename)}`;
  console.log(chalk.green(`TSV output written to ${workingPathToFile(outputTsvFilename)}`));
}

// Outputs things like this
// ├─ Apache License, Version 2.0: 5
// ├─ GNU General Public License v3.0: 3
// └─ BSD*: 1
async function outputSummary(licenseReport) {
  const summaryOutputPath = workingPathToFile(`${filenameBase}-summary.txt`);

  const aggregate = licenseReport.reduce((acc, item) => {
    const license = item.licenses[0].license;
    acc[license] ??= 0;
    acc[license]++;
    return acc;
  }, {});

  const sorted = Object.entries(aggregate).toSorted(([, countA], [, countB]) => countB - countA);
  const result = sorted.map(([license, count], idx) => {
    const prefix = idx !== sorted.length - 1 ? "├─" : "└─";
    return `${prefix} ${license}: ${count}`;
  });
  const summaryContent = result.join('\n');
  await $`echo ${summaryContent} > ${summaryOutputPath}`;
  console.log(chalk.green(`Summary written to ${summaryOutputPath}`));
}
