import { glob } from "glob";
import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";
import { execSync } from 'child_process';
import { existsSync, rmSync, writeFileSync, readFileSync } from "fs";
import { join } from "path";

export interface Metric {
    score: number;
    scoreError: string;
    scoreConfidence: [string, string];
    scorePercentiles: Record<string, number>;
    scoreUnit: string;
    rawData: number[][];
}

export interface BenchmarkReport {
    jmhVersion: string;
    benchmark: string;
    mode: string;
    threads: number;
    forks: number;
    jvm: string;
    jvmArgs: string[];
    jdkVersion: string;
    vmName: string;
    vmVersion: string;
    warmupIterations: number;
    warmupTime: string;
    warmupBatchSize: number;
    measurementIterations: number;
    measurementTime: string;
    measurementBatchSize: number;
    primaryMetric: Metric;
    secondaryMetrics: Record<string, Metric>;
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export function getReportPath() {
    const reportsDir = path.join(__dirname, "..", "..", "..", "benchmarks", "build", "reports");
    const [reportPath] = glob.sync(path.join(reportsDir, "*.json"));
    return reportPath

}

export function getReportFromBenchmark(): BenchmarkReport[] {
    const content = fs.readFileSync(getReportPath(), "utf8");
    return JSON.parse(content) as BenchmarkReport[];
}

//I wanted to just upload a static html file for the report to the google drive,
//it would be easy if you could access files through fetch in the browser
//this setup is a workaround to ensure the json can be accessed by report.html
export function buildJmhReport(
    jsonFiles: string[],
    tempDir = "./jmh-temp"
): string {

    //cloning so we can npm build into static files
    execSync(
        `git clone https://github.com/jzillmann/jmh-visualizer.git ${tempDir}`,
        { stdio: "inherit" }
    );
    const env = { ...process.env, NODE_OPTIONS: "--openssl-legacy-provider" };
    execSync("npm install", { cwd: tempDir, stdio: "inherit", env });
    execSync("npm run release", { cwd: tempDir, stdio: "inherit", env });

    const buildDir = join(tempDir, "build");

    const indexPath = join(buildDir, "index.html");
    let indexHtml = readFileSync(indexPath, "utf8");

    //putting the required json inside the report file so it doesnt have to fetch from fs
    const override = `<script>
    const _reports = [${jsonFiles.join(",")}];
    const _origFetch = window.fetch;
    window.fetch = (url, opts) => url.endsWith(".json")
    ? Promise.resolve(new Response(JSON.stringify(_reports.shift()), {
      status:200, headers:{"Content-Type":"application/json"}
        }))
    : _origFetch(url, opts);
    </script>`;
    indexHtml = indexHtml.replace(
        /<head>/,
        `<head>${override}`
    );
    writeFileSync(indexPath, indexHtml, "utf8");

    const sources = jsonFiles.map((_, i) => `report${i + 1}.json`).join(",");
    const reportHtml = `<!DOCTYPE html>
    <html>
        <head>
            <meta charset="utf-8"/>
            <title>JMH Visualizer</title>
            <meta http-equiv="refresh" content="0;url=./index.html?sources=${sources}"/>
        </head>
        <body></body>
    </html>`;
    writeFileSync(join(buildDir, "report.html"), reportHtml, "utf8");

    return buildDir;
}