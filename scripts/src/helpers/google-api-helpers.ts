import { drive_v3, google } from "googleapis";
import fs from "fs";
import path from "path";
import { scriptConstants } from "./constants.ts";
import { BenchmarkReport } from "./benchmark-helpers.ts";

export const getDrive = (): drive_v3.Drive => {
    const jwt = new google.auth.JWT({
        email: scriptConstants.serviceAccountEmail,
        key: scriptConstants.serviceAccountPrivateKey,
        scopes: ["https://www.googleapis.com/auth/drive"],
    });

    return google.drive({ version: "v3", auth: jwt });
};

export async function uploadFileToBenchmark(filePath: string, reportFolderName: string, mimeType: string): Promise<string> {
    const drive = getDrive();
    //root folder where reports will be placed, 
    const parentId = await findOrCreateBenchmarkFolder(drive);
    //dated report
    const dateFolderid = await findOrCreateFolder(drive, parentId, reportFolderName);

    const fileName = path.basename(filePath);
    const res = await drive.files.create({
        requestBody: {
            name: fileName,
            parents: [dateFolderid],
        },
        media: {
            mimeType: mimeType,
            body: fs.createReadStream(filePath),
        },
        supportsAllDrives: true,
        fields: "id",
    });

    return res.data.id!;
}



export async function getLatestReport(): Promise<BenchmarkReport|null> {
    const drive = getDrive();

    const rootRes = await drive.files.list({
        q: [
          "mimeType='application/vnd.google-apps.folder'",
          `name='${scriptConstants.benchmarkFolderName}'`,
          `'${scriptConstants.benchmarkParentFolderId}' in parents`,
        ].join(' and '),
        fields: 'files(id)',
        pageSize: 1,
        supportsAllDrives: true,
        includeItemsFromAllDrives: true,
    });
    const rootId = rootRes.data.files?.[0]?.id; if (!rootId) return null;

    const datedRes = await drive.files.list({
        q: [
          "mimeType='application/vnd.google-apps.folder'",
          `'${rootId}' in parents`,
        ].join(' and '),
        fields: 'files(id,name)',
        orderBy: 'name desc',
        pageSize: 1,
        supportsAllDrives: true,
        includeItemsFromAllDrives: true,
    });
    const folderId = datedRes.data.files?.[0]?.id; if (!folderId) return null;

    const jsonRes = await drive.files.list({
        q: `'${folderId}' in parents and name='benchmark-results.json'`,
        fields: 'files(id)',
        pageSize: 1,
        supportsAllDrives: true,
        includeItemsFromAllDrives: true,
    });
    const fileId = jsonRes.data.files?.[0]?.id; if (!fileId) return null;

    const { data: stream } = await drive.files.get(
      { fileId, alt: 'media', supportsAllDrives: true },
      { responseType: 'stream' }
    );

    let content = '';
    for await (const chunk of stream as NodeJS.ReadableStream) {
      content += chunk.toString();
    }
    return JSON.parse(content) as BenchmarkReport;
}


export async function findOrCreateBenchmarkFolder(drive: drive_v3.Drive): Promise<string> {
    const list = await drive.files.list({
        q: `mimeType='application/vnd.google-apps.folder' and name='${scriptConstants.benchmarkFolderName}' and '${scriptConstants.benchmarkParentFolderId}' in parents`,
        fields: "files(id)",
        pageSize: 1,
        supportsAllDrives: true,
        includeItemsFromAllDrives: true,
    });

    if (list.data.files?.length) {
        return list.data.files[0].id!;
    }

    const created = await drive.files.create({
        requestBody: {
            name: scriptConstants.benchmarkFolderName,
            mimeType: "application/vnd.google-apps.folder",
            parents: [scriptConstants.benchmarkParentFolderId],
        },
        fields: "id",
        supportsAllDrives: true,
    });

    return created.data.id!;
}

async function findOrCreateFolder(
    drive: drive_v3.Drive,
    parentId: string,
    name: string
): Promise<string> {
    const list = await drive.files.list({
        q: `mimeType='application/vnd.google-apps.folder' and name='${name}' and '${parentId}' in parents`,
        fields: "files(id)",
        pageSize: 1,
        supportsAllDrives: true,
        includeItemsFromAllDrives: true,
    });

    if (list.data.files?.length) {
        return list.data.files[0].id!;
    }

    const created = await drive.files.create({
        requestBody: {
            name: name,
            mimeType: "application/vnd.google-apps.folder",
            parents: [parentId],
        },
        fields: "id",
        supportsAllDrives: true,
    });

    return created.data.id!;
}