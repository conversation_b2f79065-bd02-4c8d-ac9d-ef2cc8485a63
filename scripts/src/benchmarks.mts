import { BenchmarkReport, buildJmhReport, getReportPath } from "./helpers/benchmark-helpers.ts";
import { $ } from "zx";
import process from "node:process";
import { zipFolder } from "./helpers/helpers";
import { findOrCreateBenchmarkFolder, getDrive, getLatestReport, uploadFileToBenchmark } from "./helpers/google-api-helpers";
import fs from "fs";
import { drive_v3 } from "googleapis";
import { scriptConstants } from "./helpers/constants.ts";

$.verbose = false;

const printUsageHelp = () => {
    console.log(`
  ./gradlew benchmark

  This will generate ./build/reports/benchmarks/main/{date}/{name}.json files.

  Before running the script, make sure to set the environment variables.

  Run:
    ./scripts/uploadOwaspResults.mjs [--help]
  `);
};

const zipPath = "./jmh-temp/benchmarks.zip";

if (process.argv.includes("--help")) {
    printUsageHelp();
    process.exit(0);
}

const previousReport = await getLatestReport()

const latestReport = JSON.parse(fs.readFileSync(getReportPath(), "utf8")) as BenchmarkReport[];
const pathToReport = buildJmhReport([JSON.stringify(previousReport), JSON.stringify(latestReport)]);

await zipFolder(pathToReport, zipPath);

const now = new Date().toISOString();
await uploadFileToBenchmark(getReportPath(), now, "application/json");
await uploadFileToBenchmark(zipPath, now, "application/zip");
