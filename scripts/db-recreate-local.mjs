#!/usr/bin/env zx
$.verbose = true;

const SCRIPT_DIR = __dirname;

if (argv.help) {
    console.log(`
    "Usage: db-recreate-local.mjs [--no-docker] [--help]"

    Options:
    --no-docker            Use local psql instead of docker
    --help                 Display this help message
  `);
    process.exit(0);
}
const useDocker = argv.docker ?? true;

console.log(`
  Recreating database
  This will delete all data in the database
  Make sure you have no active connections to the database
`);

// # run sql without copying file into container
// # https://stackoverflow.com/a/61879481
if (useDocker) {
    // restart the database so it won't have any active connections
    await $`docker restart psql`;
    // wait for the database to be ready https://stackoverflow.com/a/63011266
    await $`until docker exec psql pg_isready ; do sleep 2 ; done`;
    await $`< ${SCRIPT_DIR}/db/db-destroy.sql docker exec -i psql psql development development`;
    // create the database
    await $`< ${SCRIPT_DIR}/db/db-create.sql docker exec -i psql psql development development`;
    // now connect to the database and run the configure script
    await $`< ${SCRIPT_DIR}/db/db-configure.sql docker exec -i psql psql development -d "otai-dev-database"`;
} else {
    await $`< ${SCRIPT_DIR}/db/db-destroy.sql psql development development`;
    await $`< ${SCRIPT_DIR}/db/db-create.sql psql development development`;
    await $`< ${SCRIPT_DIR}/db/db-configure.sql psql development -d "otai-dev-database"`;
}

console.log(`
  Database recreate
  Check there are no errors in the output above before continuing
  RESTART YOUR SERVER TO CONNECT TO THE NEW DATABASE AND RUN THE MIGRATIONS
`);
