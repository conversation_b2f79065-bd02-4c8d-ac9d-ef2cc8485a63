#!/bin/zsh

# current script directory
SCRIPT_DIR=$(dirname "$0")

# this script checks to see if you already have a local developer configuration for logback.
# If not, it will create a default one for you.

LOGBACK_CONFIG_FILE="$SCRIPT_DIR/../app/src/main/resources/logback-local.xml"
DEV_LOGBACK_CONFIG_FILE="$SCRIPT_DIR/../app/src/main/resources/logback-local-$USER.xml"

if [ -f $DEV_LOGBACK_CONFIG_FILE ]; then
  echo "Local logback configuration already exists"
else
  echo "Creating local logback configuration"
  cp $LOGBACK_CONFIG_FILE $DEV_LOGBACK_CONFIG_FILE
  echo "Local logback configuration created at $DEV_LOGBACK_CONFIG_FILE"
fi
