#!/usr/bin/env zx
import fs from 'node:fs';
// <reference 'zx/globals' />

const SCRIPT_DIR = __dirname;

if (argv.help) {
    console.log(`
    Usage: env.mjs [--help]
    
    Write .env.local file at the top level of the project. 
    The .env.local file won't be in source control to avoid affecting CI/CD.

    Options:
    --help                 Display this help message
  `);
    process.exit(0);
}

const content = `
#USE_EXECUTION_STEP_FACTORY_V1
#FILE_PRESS_URL
#STORAGE_ACCOUNT_NAME
#STORAGE_ACCESS_KEY
#LOAD_SAMPLE_DATA
#TELEMETRY_ENABLED
#SERVICE_JWT_TOKEN
#JWT_PUBLIC_KEY
#TIMEOUT # cookie timeout

FRONTEND_URL=
WEBSITE_SITE_NAME=local
CORS_HOSTS=http://localhost:8000;http://localhost:8001;http://localhost:8002
DEVELOPMENT=true
SESSION_ENCRYPT=00112233445566778899aabbccddeeff
SESSION_SIGN=6819b57a326945c1968f45236589
LOAD_SAMPLE_DATA=true
JWT_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
                MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqxPdT2kgzHnfxsVOUZgB
                2f9KlO/nIi5ZE95br1365jToyXJpKQTLRBeIY/+5dRHIJe0D1HQe4dZihNRXajOd
                PGqot5jsRH0JH/IenMOtxRJf35AXNTeyqEvdwKxL8HsY9MPonZKcy7lQpHeauWQs
                amJpm20zOEnlx+41P5c7230oanHtYxkig+L+cG1It55NgzcvIwqqmJOf0yP4bkjE
                U4F2qMTh+VdAAABb9XhwqQszXFqttUshec2nN7tkS2g+ErSscGLZbXwKJj771gmX
                vyvKxUSzmpianOAJolmu9IY+9Z93TR2vEyAAkqr91EqLMmefF054OVQS45L04gHS
                pQIDAQAB
                -----END PUBLIC KEY-----"

PASS_VAULT_KEY_VAULT_PROVIDER=IN_MEMORY
PASS_VAULT_KEY_VAULT_NAME=localdev-vault
PASS_VAULT_KEY_VAULT_SECRET_NAME_PREFIX=localdev
PASS_VAULT_KEY_VAULT_MEMORY_LOAD_SEED_DATA=true
`;

const envLocalOutputPath = `${SCRIPT_DIR}/../.env.local`;
echo(`Writing ${envLocalOutputPath}`);

fs.writeFile(envLocalOutputPath, content, err => {
    if (err) {
        echo("Failed to write .env.local");
        echo(err);
    } else {
        echo('.env.local written successfully');
    }
});
