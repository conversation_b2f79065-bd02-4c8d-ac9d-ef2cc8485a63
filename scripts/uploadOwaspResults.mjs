#!/usr/bin/env zx
import { createReadStream, readFileSync } from "fs";
import { google } from "googleapis";
import path from "path";

$.verbose = false;

const printUsageHelp = () => {
  console.log(`
    ./gradlew dependencyCheckAnalyze

    This will generate dependency-check-report.html/json file in the build/reports directory.

    Before running the script, make sure to set the following environment variables:
    - SERVICE_ACCOUNT_EMAIL: Google Service Account Email
    - SERVICE_ACCOUNT_PRIVATE_KEY: Google Service Account Private Key
    - CONFLUENCE_AUTH: Confluence Basic Auth (username:password)
    - NVD_API_KEY: NVD API Key (Request from https://nvd.nist.gov/developers/request-an-api-key)

    Run the script from the root of project by running the following command:
    ./scripts/uploadOwaspResults.mjs [--help] [--no-google-drive] [--no-confluence]
  `);
};

if (argv.help) {
  printUsageHelp();
  process.exit(0);
}

const report = JSON.parse(
  readFileSync(
    path.join(
      __dirname,
      "..",
      "build",
      "reports",
      "dependency-check-report.json"
    ),
    "utf-8"
  )
);

const date = new Date();
const todayDate = date.toISOString().split("T")[0];
const doGoogleDrive = argv["google-drive"] ?? true;
const doConfluence = argv["confluence"] ?? true;

// Google Drive API Constants
const folderName = `OWASP Dependency Check Scan ${todayDate}`;
const fileName = `OneTeam AI BE Scan ${todayDate}.html`;
const parentFolderId = "1wadp3i7J5xpUR3o0sMOXEwWaheiyWU5_"; //Code Vulnerability Sanning Folder ID
const serviceAccountEmail = process.env.SERVICE_ACCOUNT_EMAIL;
const serviceAccountprivateKey =
  process.env.SERVICE_ACCOUNT_PRIVATE_KEY.replace(/\\n/g, "\n");

// Confluence API Constants
const severities = ["CRITICAL", "HIGH", "MEDIUM", "LOW"];
const domain = "oneteam-services.atlassian.net";
const pageId = "**********";
const spaceId = "**********";
const auth = process.env.CONFLUENCE_AUTH; // {username}:{password}
const apiURL = `https://${domain}/wiki/api/v2/pages/${pageId}`;

async function findOrCreateGDriveFolderId(drive) {
  const res = await drive.files.list({
    q: `mimeType='application/vnd.google-apps.folder' and name='${folderName}' and '${parentFolderId}' in parents`,
    fields: "files(id, name)",
    supportsAllDrives: true,
    includeItemsFromAllDrives: true
  });

  if (res.data.files.length) {
    const folderId = res.data.files[0].id;
    console.log("Folder already exists with id: ", folderId);
    return folderId;
  }

  const folderMetadata = {
    name: folderName,
    mimeType: "application/vnd.google-apps.folder",
    parents: [parentFolderId]
  };
  const folder = await drive.files.create({
    resource: folderMetadata,
    fields: "id",
    supportsAllDrives: true
  });

  console.log("Folder created with id: ", folder.data.id);
  return folder.data.id;
}

async function uploadFileToFolder({ folderId, drive }) {
  const fileMetadata = {
    name: fileName,
    parents: [folderId]
  };
  const media = {
    mimeType: "text/html",
    body: createReadStream(
      path.join(
        __dirname,
        "..",
        "build",
        "reports",
        "dependency-check-report.html"
      )
    )
  };

  const file = await drive.files.create({
    resource: fileMetadata,
    media: media,
    fields: "id",
    supportsAllDrives: true
  });

  console.log("File uploaded with id: ", file.data.id);
}

async function uploadFile() {
  const jwtClient = new google.auth.JWT(
    serviceAccountEmail,
    null, // keyFile
    serviceAccountprivateKey,
    ["https://www.googleapis.com/auth/drive"] //scope
  );

  const drive = google.drive({ version: "v3", auth: jwtClient });

  jwtClient.authorize(async authErr => {
    if (authErr) {
      console.log(authErr);
      return;
    }

    try {
      const folderId = await findOrCreateGDriveFolderId(drive);
      await uploadFileToFolder({ folderId, drive });
    } catch (err) {
      console.error(err);
    }
  });
}

const getVulnerabilitiesCount = severity => {
  return report.dependencies
    .map(dependency =>
      dependency.vulnerabilities?.filter(
        vulnerability =>
          vulnerability.cvssv3?.baseSeverity === severity ||
          vulnerability.severity === severity
      )
    )
    .filter(a => a)
    .flat().length;
};

const [critical, high, medium, low] = severities.map(getVulnerabilitiesCount);
const total = critical + high + medium + low;

async function getCurrentPage() {
  const response = await fetch(`${apiURL}?body-format=storage`, {
    method: "GET",
    headers: {
      Authorization: `Basic ${Buffer.from(auth).toString("base64")}`,
      Accept: "application/json"
    }
  });
  const { version, body, title } = await response.json();
  return {
    version: version.number,
    body: body.storage.value,
    title
  };
}

async function getUpdatedPageHTML(currentPage) {
  const scanDate = date.toLocaleString("en-AU", {
    day: "numeric",
    month: "short",
    year: "2-digit"
  });
  const newRow = `<tr>
        <td><p>${scanDate.replaceAll(" ", "-")}</p></td>
        <td><p>${total}</p></td>
        <td><p>${critical}</p></td>
        <td><p>${high}</p></td>
        <td><p>${medium}</p></td>
      </tr>`;
  const closingTag = "</tbody></table>";
  /**
   * The page structure is as follows:
   * ...Heading content...
   * <table><tbody>
   * | Scan | Vulnerabilities Found | Critical | High | Medium | (Headers)
   * First Table Original Content
   * ----- For backend new row goes here -----
   * </table></tbody> (Closing tag)
   *
   * <table><tbody>
   * Second Table Original Content
   * | Scan | Vulnerabilities Found | Critical | High | Medium | (Headers)
   * ----- For frontend new row goes here -----
   * </table></tbody> (Closing tag)
   *
   * <table><tbody>
   * Third Table Original Content
   * | Scan | Vulnerabilities Found | Critical | High | Medium | (Headers)
   * ----- For OTAI frontend new row goes here -----
   * </table></tbody> (Closing tag)
   *
   * <table><tbody>
   * Fourth Table Original Content
   * | Scan | Vulnerabilities Found | Critical | High | Medium | (Headers)
   * ----- For OTAI backend new row goes here -----
   * </table></tbody> (Closing tag)
   *
   * <table><tbody>
   * Fifth Table Original Content
   * | Scan | Vulnerabilities Found | Critical | High | Medium | (Headers)
   * ----- For OTAI Sync Server new row goes here -----
   * </table></tbody> (Closing tag)
   */
  const [table1, table2, table3, table4, table5, ...rest] =
    currentPage.body.split(closingTag);
  const updatedPage =
    table1 +
    closingTag +
    table2 +
    closingTag +
    table3 +
    closingTag +
    table4 +
    newRow +
    closingTag +
    table5 +
    closingTag +
    rest;
  return updatedPage.replaceAll(/\n/g, "").replaceAll('"', '\\"');
}

async function updateConfluencePage() {
  try {
    const currentPage = await getCurrentPage();
    const updatedPage = await getUpdatedPageHTML(currentPage);

    const response = await fetch(apiURL, {
      method: "PUT",
      headers: {
        Authorization: `Basic ${Buffer.from(auth).toString("base64")}`,
        Accept: "application/json",
        "Content-Type": "application/json"
      },
      body: `{
        "id": ${pageId},
        "status": "current",
        "title": "${currentPage.title}",
        "spaceId": ${spaceId},
        "body": {
          "representation": "storage",
          "value": "${String.raw`${updatedPage}`}"
        },
        "version": {
          "number": ${currentPage.version + 1},
          "message": "Updated from AI BE pipeline script"
        }
      }`
    });
    const resJSON = await response.json();
    if (resJSON.id) {
      console.log(
        `https://${domain}/wiki/spaces/O/pages/${resJSON.id} updated successfully at ${new Date().toISOString()}.`
      );
    } else {
      throw new Error(resJSON);
    }
  } catch (error) {
    console.error(error);
  }
}

if (doGoogleDrive) {
  await uploadFile();
}
if (doConfluence) {
  await updateConfluencePage();
}
