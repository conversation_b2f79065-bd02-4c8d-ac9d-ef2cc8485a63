{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "strict": true, "outDir": "dist", "rootDir": "src", "types": ["node"], "allowImportingTsExtensions": true}, "include": ["*.ts", "*.mts", "node_modules/zx/build/globals.d.ts"]}