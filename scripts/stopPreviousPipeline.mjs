#! /usr/bin/env node
import axios from "axios";
import { <PERSON><PERSON><PERSON> } from "buffer";
import process from "process";

const {
  BITBUCKET_CLIENT_ID: clientId,
  BITBUCKET_CLIENT_SECRET: clientSecret,
  BITBUCKET_REPO_FULL_NAME: repoFullName,
  BITBUCKET_BRANCH: branch,
  BITBUCKET_PR_ID: prId,
  BITBUCKET_PIPELINE_UUID: currentPipelineUuid
} = process.env;

async function stopOldPipelines(pipelines, config, type) {
  console.log(pipelines);
  for (let i = 0; i < pipelines.length - 1; i++) {
    const pipeline = pipelines[i];
    if (pipeline.uuid === currentPipelineUuid) {
      break;
    }
    if (
      pipeline.target.type === "pipeline_pullrequest_target" &&
      type !== "PR"
    ) {
      console.log(`Skipping PR pipeline, handling only ${type} types`);
      continue;
    }
    console.log(
      `Stopping older pipeline on branch '${branch}': ${pipeline.uuid}`
    );
    if (pipeline.target.type === "pipeline_ref_target" && type !== "BRANCH") {
      console.log(`Skipping branch pipeline, handling only ${type} types`);
      continue;
    }
    try {
      await axios.post(
        `https://api.bitbucket.org/2.0/repositories/${repoFullName}/pipelines/${pipeline.uuid}/stopPipeline`,
        {},
        config
      );
    } catch (err) {
      console.error("Error stopping old branch pipelines:", err, pipeline);
    }
  }
}

//  Retrieve an OAuth 2.0 access token for Bitbucket using client_credentials flow.

async function getAccessToken(clientId, clientSecret) {
  const tokenUrl = "https://bitbucket.org/site/oauth2/access_token";
  const authHeader = Buffer.from(`${clientId}:${clientSecret}`).toString(
    "base64"
  );

  const resp = await fetch(tokenUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization: "Basic " + authHeader
    },
    body: new URLSearchParams({
      grant_type: "client_credentials",
      // You can request various scopes depending on needs
      scopes: "repository"
    })
  });

  if (!resp.ok) {
    console.error(
      `Failed to fetch access token. Status: ${resp.status} - ${resp.statusText}`
    );
    process.exit(1);
  }

  const data = await resp.json();
  return data.access_token;
}

async function handleOldPipelines(branch, type) {
  const token = await getAccessToken(clientId, clientSecret);
  console.log(`Got access token`);
  const config = {
    headers: {
      Authorization: `Bearer ${token}`
    }
  };

  try {
    // Get pipelines for a branch that are currently BUILDING or PENDING
    const res = await axios.get(
      `https://api.bitbucket.org/2.0/repositories/${repoFullName}/pipelines?target.branch=${branch}&status=BUILDING&status=PENDING`,
      config
    );

    const pipelines = res.data.values;
    if (pipelines.length < 2) {
      console.log(
        `Only one or none pipeline is running on branch '${branch}'. No need to stop anything.`
      );
      return;
    }
    stopOldPipelines(pipelines, config, type);
  } catch (err) {
    console.error("Error stopping old branch pipelines:", err);
    process.exit(1);
  }
}

(async function main() {
  if (!clientId || !clientSecret || !repoFullName) {
    console.error("Missing required environment variables");
    process.exit(1);
  }
  console.log(`BITBUCKET_BRANCH: ${branch}`);
  console.log(`BITBUCKET_PR_ID: ${prId}`);
  if (prId && branch) {
    console.log(`Handling PR pipelines for PR ID: ${prId}`);
    await handleOldPipelines(branch, "PR");
  } else if (branch) {
    console.log(`Handling branch pipelines for branch: ${branch}`);
    await handleOldPipelines(branch, "BRANCH");
  } else {
    console.log(
      "Neither BITBUCKET_BRANCH nor BITBUCKET_PR_ID is set. Nothing to do..."
    );
  }
})();
