image: gradle:8.14-jdk21-jammy
options:
  size: 4x
  max-time: 20
definitions:
  services:
    docker-for-sonar-pipe:
      memory: 4096
      type: docker
  steps:
    - step: &stop-previous-pipeline
        name: Stop previous Pipeline
        image: node:22.18.0-bullseye
        script:
          - npm install -g npm@10.9.2
          - npm i zx axios --no-save
          - ./scripts/stopPreviousPipeline.mjs
    - step: &build
        name: build
        caches:
          - gradle
        script:
          # https://java.testcontainers.org/supported_docker_environment/continuous_integration/bitbucket_pipelines/
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - gradle build
        services:
          - docker
        artifacts:
          - app/build/libs/**
          - app/build/reports/**
          - flows/build/reports/**
          - shared/build/reports/**
    - step: &ignore-test-and-analysis-failures
        name: Ignore failures from test and code analysis
        script:
          - touch .ot-meta-ignore-failures
        artifacts:
          - .ot-meta-ignore-failures
    - step: &run-test
        name: Run Tests
        caches:
          - gradle
        script:
          - if [ -e .ot-meta-ignore-failures ]; then
          - set +e
          - fi
          # https://java.testcontainers.org/supported_docker_environment/continuous_integration/bitbucket_pipelines/
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - gradle check
        services:
          - docker
        artifacts:
          - app/build/reports/**
          - flows/build/reports/**
          - shared/build/reports/**
    - step: &code-analysis
        name: SonarCloud Scan and Analysis
        clone:
          depth: full # SonarCloud scanner needs the full history to assign issues properly
        caches:
          - gradle
        services:
          - docker-for-sonar-pipe
        script:
          - gradle sonar
    - step: &benchmark
        name: run benchmarks
        caches:
          - gradle
        image: gradle:8.14-jdk21-jammy
        script:
          - ./gradlew :benchmarks:mainBenchmarkJar :benchmarks:benchmarkWithGC
        artifacts:
          - benchmarks/build/reports/**
    - step: &upload-benchmarks
        name: Upload Benchmarks
        image: node:22.18.0-bullseye
        script:
          - npm --prefix ./scripts install
          - npm --prefix ./scripts run upload-benchmark
    - step: &version
        name: version
        clone:
          depth: full # to get correct git commit count
        script:
          - |
            LAST_TAG=$(git describe --tags $(git rev-list --tags --max-count=1))
            if [ -n "$BITBUCKET_TAG" ]; then
              export VERSION="$BITBUCKET_TAG"
              echo "Tag build for $VERSION"
            else
              HASH=$(echo $BITBUCKET_COMMIT | cut -c 1-7)
              COMMIT_COUNT=$(git rev-list --count HEAD)
              if [ -n "$LAST_TAG" ]; then
                export VERSION="$LAST_TAG-$COMMIT_COUNT"
              else
                echo "No tags found"
                exit 1
              fi
              echo "Commit build for $VERSION"
            fi
          - echo $VERSION > version.txt
          - echo $VERSION
        artifacts:
          - version.txt
    - step: &docker-build-push
        name: Docker Build and Push
        script:
          - export VERSION=$(cat version.txt)
          - ./scripts/makeBuildJson.sh
          - gradle app:publishImage
    - step: &deploy
        name: Deploy To App Service
        script:
          - export VERSION=$(cat version.txt)
          - export IMAGE="$ACR_HOST/$ACR_PROJECT:$VERSION"
          - pipe: atlassian/azure-cli-run:1.2.4
            variables:
              AZURE_TENANT_ID: ${AZURE_TENANT_ID}
              AZURE_APP_ID: ${AZURE_APP_ID}
              AZURE_PASSWORD: ${AZURE_PASSWORD}
              CLI_COMMAND: >
                az webapp config container set
                --subscription oneteam_dev
                --resource-group otai_dev_innovation
                --name otai-dev-innovation
                --docker-custom-image-name ${IMAGE}
              DEBUG: "true" # Optional

    - step: &owasp-dependency-check
        name: Run OWASP Dependency Check
        max-time: 40 # Allow more time for the dependency check
        image: node:22.18.0-bullseye
        script:
          - npm install --global npm@10.9.2 zx axios
          - npm install googleapis@148.0.0
          - wget https://download.oracle.com/java/21/latest/jdk-21_linux-x64_bin.deb
          - dpkg -i jdk-21_linux-x64_bin.deb
          - ls /usr/lib/jvm/
          - export JVM_VERSION=$(ls /usr/lib/jvm/ | grep "jdk-21.*-oracle-x64") # so it can pick up whatever version is installed e.g: jdk-21.0.7-oracle-x64
          - export JAVA_HOME=/usr/lib/jvm/$JVM_VERSION
          - export PATH=$JAVA_HOME/bin:$PATH
          - java -version
          - ./gradlew -Dorg.gradle.jvmargs="-Xmx1536m -XX:MaxMetaspaceSize=1024m" dependencyCheckAggregate
          - ./scripts/uploadOwaspResults.mjs

pipelines:
  tags:
    "*.*.*":
      - step: *run-test
      - step: *code-analysis
      - step: *build
      - step: *version
      - step: *docker-build-push
  branches:
    "main": # deploy to dev
      - step: *version
      - step: *docker-build-push
      - step: *deploy
      - step: *ignore-test-and-analysis-failures
      - step: *run-test
      - step: *code-analysis
  pull-requests:
    "**":
      - parallel:
          - step: *stop-previous-pipeline
          - step: *build
      - step: *code-analysis
      - step: *version
  custom:
    owasp-dependency-check:
      - step: *owasp-dependency-check
    run-and-upload-benchmarks:
      - step: *benchmark
      - step: *upload-benchmarks
