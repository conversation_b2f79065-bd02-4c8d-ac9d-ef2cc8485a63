#!/bin/bash

# JSON file containing the ID mappings
id_mapping_file="id_mapping.json"

# Directory containing JSON files
json_dir="../shared/src/main/resources/sample-data/testrunner/"

# Loop through each object in the JSON array
jq -c '.[]' "$id_mapping_file" | while IFS= read -r line; do
  # Extract original_id and updated_id using jq
  original_id=$(echo "$line" | jq -r '.original_id')
  updated_id=$(echo "$line" | jq -r '.updated_id')

  # Skip if original_id equals updated_id
  if [[ "$original_id" == "$updated_id" ]]; then
    continue
  fi

  echo "Original ID: $original_id"
  echo "Updated ID: $updated_id"

  # Replace the original ID with the updated ID in all JSON files
  for json_file in "$json_dir"/*.json; do
    if [[ -f "$json_file" ]]; then
      sed -i "" "s/$original_id/$updated_id/g" "$json_file"
    fi
  done
done

echo "ID replacement completed."