// Usage: node tools/compare-workspace-config.mjs original.json updated.json
// eg: node tools/compare-workspace-config.mjs shared/src/main/resources/sample-data/sgpit/sgpit-workspace-config.json updated.json
// Note: code from copilot
// Why we need this script:
// Every time I want to update sgpit / testrunner workspace config, I make changes in UI,
// and then copy over the workspace config doc into otai-be repo
// But all the entities in the maps (json objects) are reordered so diff is large and hard to verify
// This script reorders the keys in the JSON file to match the original order
// so that the diff is easier to read and verify
import fs from 'fs';

function readJson(filePath) {
    return JSON.parse(fs.readFileSync(filePath, 'utf-8'));
}

function reorderJson(reference, target) {
    if (reference && typeof reference === 'object' && !Array.isArray(reference) &&
        target && typeof target === 'object' && !Array.isArray(target)) {
        const ordered = {};
        // Add keys in reference order
        for (const key of Object.keys(reference)) {
            if (key in target) {
                ordered[key] = reorderJson(reference[key], target[key]);
            }
        }
        // Add extra keys from target
        for (const key of Object.keys(target)) {
            if (!(key in reference)) {
                ordered[key] = target[key];
            }
        }
        return ordered;
    } else if (Array.isArray(reference) && Array.isArray(target)) {
        // Optionally, handle list reordering if needed
        return target;
    } else {
        return target;
    }
}

if (process.argv.length < 4) {
    console.error('Usage: node compare-workspace-config.mjs <fileOriginal> <fileUpdated>');
    process.exit(1);
}

const fileOriginal = process.argv[2];
const fileUpdated = process.argv[3];

const sourceJson = readJson(fileOriginal);
const targetJson = readJson(fileUpdated);

const reordered = reorderJson(sourceJson, targetJson);

fs.writeFileSync(fileOriginal, JSON.stringify(reordered, null, 2), {encoding: 'utf8', flag: 'w'});
