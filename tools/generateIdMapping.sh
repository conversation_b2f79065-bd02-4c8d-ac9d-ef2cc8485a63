#!/bin/bash

# Directory containing JSON files
json_dir="../shared/src/main/resources/sample-data/testrunner/"

# Output file for ID mapping
id_mapping_file="id_mapping.txt"

# Ensure the output file is empty
> "$id_mapping_file"

# Temporary file to store all IDs
temp_file=$(mktemp)

# Loop through all JSON files in the directory
for json_file in "$json_dir"/*.json; do
  if [[ -f "$json_file" ]]; then
    echo "Processing file: $json_file"
    # Extract IDs and append to the temporary file
    grep -oE '"id"\s*:\s*"[^"]+"' "$json_file" | sed -E 's/"id":\s*"([^"]+)"/\1/' >> "$temp_file"
  fi
done

# Process unique IDs
sort "$temp_file" | uniq | while read -r id; do
  # Replace _ and - with "a"
  updated_id=$(echo "$id" | sed -E 's/[_-]/a/g')
  # Replace the first digit with "a" if the ID starts with a digit
  updated_id=$(echo "$updated_id" | sed -E 's/^[0-9]/a/')

  # Changing the name
  updated_id=$(echo "$updated_id" | sed -E 's/"id"/"updated_id"/g')
  id=$(echo "$id" | sed -E 's/"id"/"original_id"/g')
  echo "$id $updated_id" >> "$id_mapping_file"
done

sed -i '' -E 's/("updated_id": ")[0-9]/\1a/g' $id_mapping_file

# Clean up temporary file
rm "$temp_file"

echo "ID mapping file generated at: $id_mapping_file"